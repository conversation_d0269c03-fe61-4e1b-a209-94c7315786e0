{"c": ["app/layout", "app/page", "app/statistics/page", "app/dispatch/page", "app/orders/page", "app/orders/[id]/page", "app/archive/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@firebase/app/dist/esm/index.esm2017.js", "(app-pages-browser)/./node_modules/@firebase/auth/dist/esm2017/index-8e6e89cb.js", "(app-pages-browser)/./node_modules/@firebase/auth/dist/esm2017/index.js", "(app-pages-browser)/./node_modules/@firebase/component/dist/esm/index.esm2017.js", "(app-pages-browser)/./node_modules/@firebase/firestore/dist/index.esm2017.js", "(app-pages-browser)/./node_modules/@firebase/logger/dist/esm/index.esm2017.js", "(app-pages-browser)/./node_modules/@firebase/storage/dist/index.esm2017.js", "(app-pages-browser)/./node_modules/@firebase/util/dist/index.esm2017.js", "(app-pages-browser)/./node_modules/@firebase/util/dist/postinstall.mjs", "(app-pages-browser)/./node_modules/@firebase/webchannel-wrapper/dist/bloom-blob/esm/bloom_blob_es2018.js", "(app-pages-browser)/./node_modules/@firebase/webchannel-wrapper/dist/webchannel-blob/esm/webchannel_blob_es2018.js", "(app-pages-browser)/./node_modules/firebase/app/dist/esm/index.esm.js", "(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js", "(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js", "(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js", "(app-pages-browser)/./node_modules/idb/build/index.js", "(app-pages-browser)/./node_modules/idb/build/wrap-idb-value.js", "(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs", "(app-pages-browser)/./src/lib/firebase.ts"]}