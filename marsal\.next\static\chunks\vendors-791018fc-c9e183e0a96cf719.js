"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2900],{667:(t,r,e)=>{e.d(r,{$:()=>D});var n=e(39778),o=e(68883),i=e(42563),a=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),s=function(t){function r(r){return t.call(this,r)||this}return a(r,t),r.prototype.encodeCompressedGtin=function(t,r){t.append("(01)");var e=t.length();t.append("9"),this.encodeCompressedGtinWithoutAI(t,r,e)},r.prototype.encodeCompressedGtinWithoutAI=function(t,e,n){for(var o=0;o<4;++o){var i=this.getGeneralDecoder().extractNumericValueFromBitArray(e+10*o,10);i/100==0&&t.append("0"),i/10==0&&t.append("0"),t.append(i)}r.appendCheckDigit(t,n)},r.appendCheckDigit=function(t,r){for(var e=0,n=0;n<13;n++){var o=t.charAt(n+r).charCodeAt(0)-48;e+=(1&n)==0?3*o:o}10==(e=10-e%10)&&(e=0),t.append(e)},r.GTIN_SIZE=40,r}(i.A),u=e(1933),c=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),f=function(t){function r(r){return t.call(this,r)||this}return c(r,t),r.prototype.parseInformation=function(){var t=new u.A;t.append("(01)");var e=t.length(),n=this.getGeneralDecoder().extractNumericValueFromBitArray(r.HEADER_SIZE,4);return t.append(n),this.encodeCompressedGtinWithoutAI(t,r.HEADER_SIZE+4,e),this.getGeneralDecoder().decodeAllCodes(t,r.HEADER_SIZE+44)},r.HEADER_SIZE=4,r}(s),h=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),l=function(t){function r(r){return t.call(this,r)||this}return h(r,t),r.prototype.parseInformation=function(){var t=new u.A;return this.getGeneralDecoder().decodeAllCodes(t,r.HEADER_SIZE)},r.HEADER_SIZE=5,r}(i.A),p=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),A=function(t){function r(r){return t.call(this,r)||this}return p(r,t),r.prototype.encodeCompressedWeight=function(t,r,e){var n=this.getGeneralDecoder().extractNumericValueFromBitArray(r,e);this.addWeightCode(t,n);for(var o=this.checkWeight(n),i=1e5,a=0;a<5;++a)o/i==0&&t.append("0"),i/=10;t.append(o)},r}(s),_=e(438),d=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),E=function(t){function r(r){return t.call(this,r)||this}return d(r,t),r.prototype.parseInformation=function(){if(this.getInformation().getSize()!==r.HEADER_SIZE+A.GTIN_SIZE+r.WEIGHT_SIZE)throw new _.A;var t=new u.A;return this.encodeCompressedGtin(t,r.HEADER_SIZE),this.encodeCompressedWeight(t,r.HEADER_SIZE+A.GTIN_SIZE,r.WEIGHT_SIZE),t.toString()},r.HEADER_SIZE=5,r.WEIGHT_SIZE=15,r}(A),g=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),y=function(t){function r(r){return t.call(this,r)||this}return g(r,t),r.prototype.addWeightCode=function(t,r){t.append("(3103)")},r.prototype.checkWeight=function(t){return t},r}(E),I=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),v=function(t){function r(r){return t.call(this,r)||this}return I(r,t),r.prototype.addWeightCode=function(t,r){r<1e4?t.append("(3202)"):t.append("(3203)")},r.prototype.checkWeight=function(t){return t<1e4?t:t-1e4},r}(E),w=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),N=function(t){function r(r){return t.call(this,r)||this}return w(r,t),r.prototype.parseInformation=function(){if(this.getInformation().getSize()<r.HEADER_SIZE+s.GTIN_SIZE)throw new _.A;var t=new u.A;this.encodeCompressedGtin(t,r.HEADER_SIZE);var e=this.getGeneralDecoder().extractNumericValueFromBitArray(r.HEADER_SIZE+s.GTIN_SIZE,r.LAST_DIGIT_SIZE);t.append("(392"),t.append(e),t.append(")");var n=this.getGeneralDecoder().decodeGeneralPurposeField(r.HEADER_SIZE+s.GTIN_SIZE+r.LAST_DIGIT_SIZE,null);return t.append(n.getNewString()),t.toString()},r.HEADER_SIZE=8,r.LAST_DIGIT_SIZE=2,r}(s),T=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),R=function(t){function r(r){return t.call(this,r)||this}return T(r,t),r.prototype.parseInformation=function(){if(this.getInformation().getSize()<r.HEADER_SIZE+s.GTIN_SIZE)throw new _.A;var t=new u.A;this.encodeCompressedGtin(t,r.HEADER_SIZE);var e=this.getGeneralDecoder().extractNumericValueFromBitArray(r.HEADER_SIZE+s.GTIN_SIZE,r.LAST_DIGIT_SIZE);t.append("(393"),t.append(e),t.append(")");var n=this.getGeneralDecoder().extractNumericValueFromBitArray(r.HEADER_SIZE+s.GTIN_SIZE+r.LAST_DIGIT_SIZE,r.FIRST_THREE_DIGITS_SIZE);n/100==0&&t.append("0"),n/10==0&&t.append("0"),t.append(n);var o=this.getGeneralDecoder().decodeGeneralPurposeField(r.HEADER_SIZE+s.GTIN_SIZE+r.LAST_DIGIT_SIZE+r.FIRST_THREE_DIGITS_SIZE,null);return t.append(o.getNewString()),t.toString()},r.HEADER_SIZE=8,r.LAST_DIGIT_SIZE=2,r.FIRST_THREE_DIGITS_SIZE=10,r}(s),P=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),m=function(t){function r(r,e,n){var o=t.call(this,r)||this;return o.dateCode=n,o.firstAIdigits=e,o}return P(r,t),r.prototype.parseInformation=function(){if(this.getInformation().getSize()!==r.HEADER_SIZE+r.GTIN_SIZE+r.WEIGHT_SIZE+r.DATE_SIZE)throw new _.A;var t=new u.A;return this.encodeCompressedGtin(t,r.HEADER_SIZE),this.encodeCompressedWeight(t,r.HEADER_SIZE+r.GTIN_SIZE,r.WEIGHT_SIZE),this.encodeCompressedDate(t,r.HEADER_SIZE+r.GTIN_SIZE+r.WEIGHT_SIZE),t.toString()},r.prototype.encodeCompressedDate=function(t,e){var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e,r.DATE_SIZE);if(38400!==n){t.append("("),t.append(this.dateCode),t.append(")");var o=n%32,i=(n/=32)%12+1,a=n/=12;a/10==0&&t.append("0"),t.append(a),i/10==0&&t.append("0"),t.append(i),o/10==0&&t.append("0"),t.append(o)}},r.prototype.addWeightCode=function(t,r){t.append("("),t.append(this.firstAIdigits),t.append(r/1e5),t.append(")")},r.prototype.checkWeight=function(t){return t%1e5},r.HEADER_SIZE=8,r.WEIGHT_SIZE=20,r.DATE_SIZE=16,r}(A);function D(t){try{if(t.get(1))return new f(t);if(!t.get(2))return new l(t);switch(o.A.extractNumericValueFromBitArray(t,1,4)){case 4:return new y(t);case 5:return new v(t)}switch(o.A.extractNumericValueFromBitArray(t,1,5)){case 12:return new N(t);case 13:return new R(t)}switch(o.A.extractNumericValueFromBitArray(t,1,7)){case 56:return new m(t,"310","11");case 57:return new m(t,"320","11");case 58:return new m(t,"310","13");case 59:return new m(t,"320","13");case 60:return new m(t,"310","15");case 61:return new m(t,"320","15");case 62:return new m(t,"310","17");case 63:return new m(t,"320","17")}}catch(r){throw console.log(r),new n.A("unknown decoder: "+t)}}},2605:(t,r,e)=>{e.d(r,{A:()=>y});var n=e(25969),o=e(79886),i=e(438),a=e(69071),s=e(322),u=e(37391),c=e(13997),f=e(87932),h=e(16067),l=e(22868),p=function(){function t(){}return t.buildBitArray=function(t){var r=2*t.length-1;null==t[t.length-1].getRightChar()&&(r-=1);for(var e=12*r,n=new l.A(e),o=0,i=t[0].getRightChar().getValue(),a=11;a>=0;--a)(i&1<<a)!=0&&n.set(o),o++;for(var a=1;a<t.length;++a){for(var s=t[a],u=s.getLeftChar().getValue(),c=11;c>=0;--c)(u&1<<c)!=0&&n.set(o),o++;if(null!==s.getRightChar())for(var f=s.getRightChar().getValue(),c=11;c>=0;--c)(f&1<<c)!=0&&n.set(o),o++}return n},t}(),A=e(667),_=function(){function t(t,r,e,n){this.leftchar=t,this.rightchar=r,this.finderpattern=e,this.maybeLast=n}return t.prototype.mayBeLast=function(){return this.maybeLast},t.prototype.getLeftChar=function(){return this.leftchar},t.prototype.getRightChar=function(){return this.rightchar},t.prototype.getFinderPattern=function(){return this.finderpattern},t.prototype.mustBeLast=function(){return null==this.rightchar},t.prototype.toString=function(){return"[ "+this.leftchar+", "+this.rightchar+" : "+(null==this.finderpattern?"null":this.finderpattern.getValue())+" ]"},t.equals=function(r,e){return r instanceof t&&t.equalsOrNull(r.leftchar,e.leftchar)&&t.equalsOrNull(r.rightchar,e.rightchar)&&t.equalsOrNull(r.finderpattern,e.finderpattern)},t.equalsOrNull=function(r,e){return null===r?null===e:t.equals(r,e)},t.prototype.hashCode=function(){return this.leftchar.getValue()^this.rightchar.getValue()^this.finderpattern.getValue()},t}(),d=function(){function t(t,r,e){this.pairs=t,this.rowNumber=r,this.wasReversed=e}return t.prototype.getPairs=function(){return this.pairs},t.prototype.getRowNumber=function(){return this.rowNumber},t.prototype.isReversed=function(){return this.wasReversed},t.prototype.isEquivalent=function(t){return this.checkEqualitity(this,t)},t.prototype.toString=function(){return"{ "+this.pairs+" }"},t.prototype.equals=function(r,e){return r instanceof t&&this.checkEqualitity(r,e)&&r.wasReversed===e.wasReversed},t.prototype.checkEqualitity=function(t,r){var e;if(t&&r)return t.forEach(function(t,n){r.forEach(function(r){t.getLeftChar().getValue()===r.getLeftChar().getValue()&&t.getRightChar().getValue()===r.getRightChar().getValue()&&t.getFinderPatter().getValue()===r.getFinderPatter().getValue()&&(e=!0)})}),e},t}(),E=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),g=function(t){var r="function"==typeof Symbol&&Symbol.iterator,e=r&&t[r],n=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")};let y=function(t){function r(){var e=null!==t&&t.apply(this,arguments)||this;return e.pairs=Array(r.MAX_PAIRS),e.rows=[],e.startEnd=[2],e}return E(r,t),r.prototype.decodeRow=function(t,e,n){this.pairs.length=0,this.startFromEven=!1;try{return r.constructResult(this.decodeRow2pairs(t,e))}catch(t){}return this.pairs.length=0,this.startFromEven=!0,r.constructResult(this.decodeRow2pairs(t,e))},r.prototype.reset=function(){this.pairs.length=0,this.rows.length=0},r.prototype.decodeRow2pairs=function(t,r){for(var e,n=!1;!n;)try{this.pairs.push(this.retrieveNextPair(r,this.pairs,t))}catch(t){if(t instanceof i.A){if(!this.pairs.length)throw new i.A;n=!0}}if(this.checkChecksum())return this.pairs;if(e=!!this.rows.length,this.storeRow(t,!1),e){var o=this.checkRowsBoolean(!1);if(null!=o||null!=(o=this.checkRowsBoolean(!0)))return o}throw new i.A},r.prototype.checkRowsBoolean=function(t){if(this.rows.length>25)return this.rows.length=0,null;this.pairs.length=0,t&&(this.rows=this.rows.reverse());var r=null;try{r=this.checkRows([],0)}catch(t){console.log(t)}return t&&(this.rows=this.rows.reverse()),r},r.prototype.checkRows=function(t,e){for(var n,o,a=e;a<this.rows.length;a++){var s=this.rows[a];this.pairs.length=0;try{for(var u=(n=void 0,g(t)),c=u.next();!c.done;c=u.next()){var f=c.value;this.pairs.push(f.getPairs())}}catch(t){n={error:t}}finally{try{c&&!c.done&&(o=u.return)&&o.call(u)}finally{if(n)throw n.error}}if(this.pairs.push(s.getPairs()),r.isValidSequence(this.pairs)){if(this.checkChecksum())return this.pairs;var h=Array(t);h.push(s);try{return this.checkRows(h,a+1)}catch(t){console.log(t)}}}throw new i.A},r.isValidSequence=function(t){var e,n;try{for(var o=g(r.FINDER_PATTERN_SEQUENCES),i=o.next();!i.done;i=o.next()){var a=i.value;if(!(t.length>a.length)){for(var s=!0,u=0;u<t.length;u++)if(t[u].getFinderPattern().getValue()!==a[u]){s=!1;break}if(s)return!0}}}catch(t){e={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(e)throw e.error}}return!1},r.prototype.storeRow=function(t,e){for(var n=0,o=!1,i=!1;n<this.rows.length;){var a=this.rows[n];if(a.getRowNumber()>t){i=a.isEquivalent(this.pairs);break}o=a.isEquivalent(this.pairs),n++}i||o||r.isPartialRow(this.pairs,this.rows)||(this.rows.push(n,new d(this.pairs,t,e)),this.removePartialRows(this.pairs,this.rows))},r.prototype.removePartialRows=function(t,r){var e,n,o,i,a,s;try{for(var u=g(r),c=u.next();!c.done;c=u.next()){var f=c.value;if(f.getPairs().length!==t.length)try{for(var h=(o=void 0,g(f.getPairs())),l=h.next();!l.done;l=h.next()){var p=l.value,A=!1;try{for(var d=(a=void 0,g(t)),E=d.next();!E.done;E=d.next()){var y=E.value;if(_.equals(p,y))break}}catch(t){a={error:t}}finally{try{E&&!E.done&&(s=d.return)&&s.call(d)}finally{if(a)throw a.error}}}}catch(t){o={error:t}}finally{try{l&&!l.done&&(i=h.return)&&i.call(h)}finally{if(o)throw o.error}}}}catch(t){e={error:t}}finally{try{c&&!c.done&&(n=u.return)&&n.call(u)}finally{if(e)throw e.error}}},r.isPartialRow=function(t,r){var e,n,o,i,a,s;try{for(var u=g(r),c=u.next();!c.done;c=u.next()){var f=c.value,h=!0;try{for(var l=(o=void 0,g(t)),p=l.next();!p.done;p=l.next()){var A=p.value,_=!1;try{for(var d=(a=void 0,g(f.getPairs())),E=d.next();!E.done;E=d.next()){var y=E.value;if(A.equals(y)){_=!0;break}}}catch(t){a={error:t}}finally{try{E&&!E.done&&(s=d.return)&&s.call(d)}finally{if(a)throw a.error}}if(!_){h=!1;break}}}catch(t){o={error:t}}finally{try{p&&!p.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}if(h)return!0}}catch(t){e={error:t}}finally{try{c&&!c.done&&(n=u.return)&&n.call(u)}finally{if(e)throw e.error}}return!1},r.prototype.getRows=function(){return this.rows},r.constructResult=function(t){var r=p.buildBitArray(t),e=(0,A.$)(r).parseInformation(),o=t[0].getFinderPattern().getResultPoints(),i=t[t.length-1].getFinderPattern().getResultPoints(),s=[o[0],o[1],i[0],i[1]];return new a.A(e,null,null,s,n.A.RSS_EXPANDED,null)},r.prototype.checkChecksum=function(){var t=this.pairs.get(0),r=t.getLeftChar(),e=t.getRightChar();if(null===e)return!1;for(var n=e.getChecksumPortion(),o=2,i=1;i<this.pairs.size();++i){var a=this.pairs.get(i);n+=a.getLeftChar().getChecksumPortion(),o++;var s=a.getRightChar();null!=s&&(n+=s.getChecksumPortion(),o++)}return 211*(o-4)+(n%=211)===r.getValue()},r.getNextSecondBar=function(t,r){var e;return t.get(r)?(e=t.getNextUnset(r),e=t.getNextSet(e)):(e=t.getNextSet(r),e=t.getNextUnset(e)),e},r.prototype.retrieveNextPair=function(t,e,n){var o,a,s=e.length%2==0;this.startFromEven&&(s=!s);var u=!0,c=-1;do this.findNextPair(t,e,c),null===(o=this.parseFoundFinderPattern(t,n,s))?c=r.getNextSecondBar(t,this.startEnd[0]):u=!1;while(u);var f=this.decodeDataCharacter(t,o,s,!0);if(!this.isEmptyPair(e)&&e[e.length-1].mustBeLast())throw new i.A;try{a=this.decodeDataCharacter(t,o,s,!1)}catch(t){a=null,console.log(t)}return new _(f,a,o,!0)},r.prototype.isEmptyPair=function(t){return 0===t.length},r.prototype.findNextPair=function(t,e,n){var o,a=this.getDecodeFinderCounters();a[0]=0,a[1]=0,a[2]=0,a[3]=0;var s=t.getSize();o=n>=0?n:this.isEmptyPair(e)?0:e[e.length-1].getFinderPattern().getStartEnd()[1];var u=e.length%2!=0;this.startFromEven&&(u=!u);for(var c=!1;o<s&&(c=!t.get(o));)o++;for(var f=0,h=o,l=o;l<s;l++)if(t.get(l)!==c)a[f]++;else{if(3===f){if(u&&r.reverseCounters(a),r.isFinderPattern(a)){this.startEnd[0]=h,this.startEnd[1]=l;return}u&&r.reverseCounters(a),h+=a[0]+a[1],a[0]=a[2],a[1]=a[3],a[2]=0,a[3]=0,f--}else f++;a[f]=1,c=!c}throw new i.A},r.reverseCounters=function(t){for(var r=t.length,e=0;e<r/2;++e){var n=t[e];t[e]=t[r-e-1],t[r-e-1]=n}},r.prototype.parseFoundFinderPattern=function(t,e,n){if(n){for(var o,i,a,u,c=this.startEnd[0]-1;c>=0&&!t.get(c);)c--;c++,o=this.startEnd[0]-c,i=c,a=this.startEnd[1]}else i=this.startEnd[0],o=(a=t.getNextUnset(this.startEnd[1]+1))-this.startEnd[1];var h=this.getDecodeFinderCounters();s.A.arraycopy(h,0,h,1,h.length-1),h[0]=o;try{u=this.parseFinderValue(h,r.FINDER_PATTERNS)}catch(t){return null}return new f.A(u,[i,a],i,a,e)},r.prototype.decodeDataCharacter=function(t,e,n,a){for(var s=this.getDataCharacterCounters(),u=0;u<s.length;u++)s[u]=0;if(a)r.recordPatternInReverse(t,e.getStartEnd()[0],s);else{r.recordPattern(t,e.getStartEnd()[1],s);for(var f=0,l=s.length-1;f<l;f++,l--){var p=s[f];s[f]=s[l],s[l]=p}}var A=o.A.sum(new Int32Array(s))/17,_=(e.getStartEnd()[1]-e.getStartEnd()[0])/15;if(Math.abs(A-_)/_>.3)throw new i.A;for(var d=this.getOddCounts(),E=this.getEvenCounts(),g=this.getOddRoundingErrors(),y=this.getEvenRoundingErrors(),f=0;f<s.length;f++){var I=s[f]/A,v=I+.5;if(v<1){if(I<.3)throw new i.A;v=1}else if(v>8){if(I>8.7)throw new i.A;v=8}var w=f/2;(1&f)==0?(d[w]=v,g[w]=I-v):(E[w]=v,y[w]=I-v)}this.adjustOddEvenCounts(17);for(var N=4*e.getValue()+2*!n+ +!a-1,T=0,R=0,f=d.length-1;f>=0;f--){if(r.isNotA1left(e,n,a)){var P=r.WEIGHTS[N][2*f];R+=d[f]*P}T+=d[f]}for(var m=0,f=E.length-1;f>=0;f--)if(r.isNotA1left(e,n,a)){var P=r.WEIGHTS[N][2*f+1];m+=E[f]*P}var D=R+m;if((1&T)!=0||T>13||T<4)throw new i.A;var S=(13-T)/2,F=r.SYMBOL_WIDEST[S],C=h.A.getRSSvalue(d,F,!0),b=h.A.getRSSvalue(E,9-F,!1),L=r.EVEN_TOTAL_SUBSET[S],G=r.GSUM[S];return new c.A(C*L+b+G,D)},r.isNotA1left=function(t,r,e){return!(0===t.getValue()&&r&&e)},r.prototype.adjustOddEvenCounts=function(t){var e=o.A.sum(new Int32Array(this.getOddCounts())),n=o.A.sum(new Int32Array(this.getEvenCounts())),a=!1,s=!1;e>13?s=!0:e<4&&(a=!0);var u=!1,c=!1;n>13?c=!0:n<4&&(u=!0);var f=e+n-t,h=(1&e)==1,l=(1&n)==0;if(1===f)if(h){if(l)throw new i.A;s=!0}else{if(!l)throw new i.A;c=!0}else if(-1===f)if(h){if(l)throw new i.A;a=!0}else{if(!l)throw new i.A;u=!0}else if(0===f){if(h){if(!l)throw new i.A;e<n?(a=!0,c=!0):(s=!0,u=!0)}else if(l)throw new i.A}else throw new i.A;if(a){if(s)throw new i.A;r.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(s&&r.decrement(this.getOddCounts(),this.getOddRoundingErrors()),u){if(c)throw new i.A;r.increment(this.getEvenCounts(),this.getOddRoundingErrors())}c&&r.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},r.SYMBOL_WIDEST=[7,5,4,3,1],r.EVEN_TOTAL_SUBSET=[4,20,52,104,204],r.GSUM=[0,348,1388,2948,3988],r.FINDER_PATTERNS=[Int32Array.from([1,8,4,1]),Int32Array.from([3,6,4,1]),Int32Array.from([3,4,6,1]),Int32Array.from([3,2,8,1]),Int32Array.from([2,6,5,1]),Int32Array.from([2,2,9,1])],r.WEIGHTS=[[1,3,9,27,81,32,96,77],[20,60,180,118,143,7,21,63],[189,145,13,39,117,140,209,205],[193,157,49,147,19,57,171,91],[62,186,136,197,169,85,44,132],[185,133,188,142,4,12,36,108],[113,128,173,97,80,29,87,50],[150,28,84,41,123,158,52,156],[46,138,203,187,139,206,196,166],[76,17,51,153,37,111,122,155],[43,129,176,106,107,110,119,146],[16,48,144,10,30,90,59,177],[109,116,137,200,178,112,125,164],[70,210,208,202,184,130,179,115],[134,191,151,31,93,68,204,190],[148,22,66,198,172,94,71,2],[6,18,54,162,64,192,154,40],[120,149,25,75,14,42,126,167],[79,26,78,23,69,207,199,175],[103,98,83,38,114,131,182,124],[161,61,183,127,170,88,53,159],[55,165,73,8,24,72,5,15],[45,135,194,160,58,174,100,89]],r.FINDER_PAT_A=0,r.FINDER_PAT_B=1,r.FINDER_PAT_C=2,r.FINDER_PAT_D=3,r.FINDER_PAT_E=4,r.FINDER_PAT_F=5,r.FINDER_PATTERN_SEQUENCES=[[r.FINDER_PAT_A,r.FINDER_PAT_A],[r.FINDER_PAT_A,r.FINDER_PAT_B,r.FINDER_PAT_B],[r.FINDER_PAT_A,r.FINDER_PAT_C,r.FINDER_PAT_B,r.FINDER_PAT_D],[r.FINDER_PAT_A,r.FINDER_PAT_E,r.FINDER_PAT_B,r.FINDER_PAT_D,r.FINDER_PAT_C],[r.FINDER_PAT_A,r.FINDER_PAT_E,r.FINDER_PAT_B,r.FINDER_PAT_D,r.FINDER_PAT_D,r.FINDER_PAT_F],[r.FINDER_PAT_A,r.FINDER_PAT_E,r.FINDER_PAT_B,r.FINDER_PAT_D,r.FINDER_PAT_E,r.FINDER_PAT_F,r.FINDER_PAT_F],[r.FINDER_PAT_A,r.FINDER_PAT_A,r.FINDER_PAT_B,r.FINDER_PAT_B,r.FINDER_PAT_C,r.FINDER_PAT_C,r.FINDER_PAT_D,r.FINDER_PAT_D],[r.FINDER_PAT_A,r.FINDER_PAT_A,r.FINDER_PAT_B,r.FINDER_PAT_B,r.FINDER_PAT_C,r.FINDER_PAT_C,r.FINDER_PAT_D,r.FINDER_PAT_E,r.FINDER_PAT_E],[r.FINDER_PAT_A,r.FINDER_PAT_A,r.FINDER_PAT_B,r.FINDER_PAT_B,r.FINDER_PAT_C,r.FINDER_PAT_C,r.FINDER_PAT_D,r.FINDER_PAT_E,r.FINDER_PAT_F,r.FINDER_PAT_F],[r.FINDER_PAT_A,r.FINDER_PAT_A,r.FINDER_PAT_B,r.FINDER_PAT_B,r.FINDER_PAT_C,r.FINDER_PAT_D,r.FINDER_PAT_D,r.FINDER_PAT_E,r.FINDER_PAT_E,r.FINDER_PAT_F,r.FINDER_PAT_F]],r.MAX_PAIRS=11,r}(u.A)},10659:(t,r,e)=>{e.d(r,{A:()=>I});var n=e(37391),o=e(13997),i=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),a=function(t){function r(r,e,n){var o=t.call(this,r,e)||this;return o.count=0,o.finderPattern=n,o}return i(r,t),r.prototype.getFinderPattern=function(){return this.finderPattern},r.prototype.getCount=function(){return this.count},r.prototype.incrementCount=function(){this.count++},r}(o.A),s=e(69071),u=e(79417),c=e(438),f=e(1933),h=e(25969),l=e(56595),p=e(87932),A=e(79886),_=e(16067),d=e(322),E=e(22152),g=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),y=function(t){var r="function"==typeof Symbol&&Symbol.iterator,e=r&&t[r],n=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")};let I=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.possibleLeftPairs=[],r.possibleRightPairs=[],r}return g(r,t),r.prototype.decodeRow=function(t,e,n){var o,i,a,s,u=this.decodePair(e,!1,t,n);r.addOrTally(this.possibleLeftPairs,u),e.reverse();var f=this.decodePair(e,!0,t,n);r.addOrTally(this.possibleRightPairs,f),e.reverse();try{for(var h=y(this.possibleLeftPairs),l=h.next();!l.done;l=h.next()){var p=l.value;if(p.getCount()>1)try{for(var A=(a=void 0,y(this.possibleRightPairs)),_=A.next();!_.done;_=A.next()){var d=_.value;if(d.getCount()>1&&r.checkChecksum(p,d))return r.constructResult(p,d)}}catch(t){a={error:t}}finally{try{_&&!_.done&&(s=A.return)&&s.call(A)}finally{if(a)throw a.error}}}}catch(t){o={error:t}}finally{try{l&&!l.done&&(i=h.return)&&i.call(h)}finally{if(o)throw o.error}}throw new c.A},r.addOrTally=function(t,r){if(null!=r){var e,n,o=!1;try{for(var i=y(t),a=i.next();!a.done;a=i.next()){var s=a.value;if(s.getValue()===r.getValue()){s.incrementCount(),o=!0;break}}}catch(t){e={error:t}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(e)throw e.error}}o||t.push(r)}},r.prototype.reset=function(){this.possibleLeftPairs.length=0,this.possibleRightPairs.length=0},r.constructResult=function(t,r){for(var e=new String(4537077*t.getValue()+r.getValue()).toString(),n=new f.A,o=13-e.length;o>0;o--)n.append("0");n.append(e);for(var i=0,o=0;o<13;o++){var a=n.charAt(o).charCodeAt(0)-48;i+=(1&o)==0?3*a:a}10==(i=10-i%10)&&(i=0),n.append(i.toString());var u=t.getFinderPattern().getResultPoints(),c=r.getFinderPattern().getResultPoints();return new s.A(n.toString(),null,0,[u[0],u[1],c[0],c[1]],h.A.RSS_14,new Date().getTime())},r.checkChecksum=function(t,r){var e=(t.getChecksumPortion()+16*r.getChecksumPortion())%79,n=9*t.getFinderPattern().getValue()+r.getFinderPattern().getValue();return n>72&&n--,n>8&&n--,e===n},r.prototype.decodePair=function(t,r,e,n){try{var o=this.findFinderPattern(t,r),i=this.parseFoundFinderPattern(t,e,r,o),s=null==n?null:n.get(u.A.NEED_RESULT_POINT_CALLBACK);if(null!=s){var c=(o[0]+o[1])/2;r&&(c=t.getSize()-1-c),s.foundPossibleResultPoint(new l.A(c,e))}var f=this.decodeDataCharacter(t,i,!0),h=this.decodeDataCharacter(t,i,!1);return new a(1597*f.getValue()+h.getValue(),f.getChecksumPortion()+4*h.getChecksumPortion(),i)}catch(t){return null}},r.prototype.decodeDataCharacter=function(t,e,n){for(var i=this.getDataCharacterCounters(),a=0;a<i.length;a++)i[a]=0;if(n)E.A.recordPatternInReverse(t,e.getStartEnd()[0],i);else{E.A.recordPattern(t,e.getStartEnd()[1]+1,i);for(var s=0,u=i.length-1;s<u;s++,u--){var f=i[s];i[s]=i[u],i[u]=f}}for(var h=n?16:15,l=A.A.sum(new Int32Array(i))/h,p=this.getOddCounts(),d=this.getEvenCounts(),g=this.getOddRoundingErrors(),y=this.getEvenRoundingErrors(),s=0;s<i.length;s++){var I=i[s]/l,v=Math.floor(I+.5);v<1?v=1:v>8&&(v=8);var w=Math.floor(s/2);(1&s)==0?(p[w]=v,g[w]=I-v):(d[w]=v,y[w]=I-v)}this.adjustOddEvenCounts(n,h);for(var N=0,T=0,s=p.length-1;s>=0;s--)T*=9,T+=p[s],N+=p[s];for(var R=0,P=0,s=d.length-1;s>=0;s--)R*=9,R+=d[s],P+=d[s];var m=T+3*R;if(n){if((1&N)!=0||N>12||N<4)throw new c.A;var D=(12-N)/2,S=r.OUTSIDE_ODD_WIDEST[D],F=9-S,C=_.A.getRSSvalue(p,S,!1),b=_.A.getRSSvalue(d,F,!0),L=r.OUTSIDE_EVEN_TOTAL_SUBSET[D],G=r.OUTSIDE_GSUM[D];return new o.A(C*L+b+G,m)}if((1&P)!=0||P>10||P<4)throw new c.A;var D=(10-P)/2,S=r.INSIDE_ODD_WIDEST[D],F=9-S,C=_.A.getRSSvalue(p,S,!0),b=_.A.getRSSvalue(d,F,!1),O=r.INSIDE_ODD_TOTAL_SUBSET[D],G=r.INSIDE_GSUM[D];return new o.A(b*O+C+G,m)},r.prototype.findFinderPattern=function(t,r){var e=this.getDecodeFinderCounters();e[0]=0,e[1]=0,e[2]=0,e[3]=0;for(var o=t.getSize(),i=!1,a=0;a<o&&r!==(i=!t.get(a));)a++;for(var s=0,u=a,f=a;f<o;f++)if(t.get(f)!==i)e[s]++;else{if(3===s){if(n.A.isFinderPattern(e))return[u,f];u+=e[0]+e[1],e[0]=e[2],e[1]=e[3],e[2]=0,e[3]=0,s--}else s++;e[s]=1,i=!i}throw new c.A},r.prototype.parseFoundFinderPattern=function(t,e,n,o){for(var i=t.get(o[0]),a=o[0]-1;a>=0&&i!==t.get(a);)a--;a++;var s=o[0]-a,u=this.getDecodeFinderCounters(),c=new Int32Array(u.length);d.A.arraycopy(u,0,c,1,u.length-1),c[0]=s;var f=this.parseFinderValue(c,r.FINDER_PATTERNS),h=a,l=o[1];return n&&(h=t.getSize()-1-h,l=t.getSize()-1-l),new p.A(f,[a,o[1]],h,l,e)},r.prototype.adjustOddEvenCounts=function(t,r){var e=A.A.sum(new Int32Array(this.getOddCounts())),o=A.A.sum(new Int32Array(this.getEvenCounts())),i=!1,a=!1,s=!1,u=!1;t?(e>12?a=!0:e<4&&(i=!0),o>12?u=!0:o<4&&(s=!0)):(e>11?a=!0:e<5&&(i=!0),o>10?u=!0:o<4&&(s=!0));var f=e+o-r,h=(1&e)==+!!t,l=(1&o)==1;if(1===f)if(h){if(l)throw new c.A;a=!0}else{if(!l)throw new c.A;u=!0}else if(-1===f)if(h){if(l)throw new c.A;i=!0}else{if(!l)throw new c.A;s=!0}else if(0===f){if(h){if(!l)throw new c.A;e<o?(i=!0,u=!0):(a=!0,s=!0)}else if(l)throw new c.A}else throw new c.A;if(i){if(a)throw new c.A;n.A.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(a&&n.A.decrement(this.getOddCounts(),this.getOddRoundingErrors()),s){if(u)throw new c.A;n.A.increment(this.getEvenCounts(),this.getOddRoundingErrors())}u&&n.A.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},r.OUTSIDE_EVEN_TOTAL_SUBSET=[1,10,34,70,126],r.INSIDE_ODD_TOTAL_SUBSET=[4,20,48,81],r.OUTSIDE_GSUM=[0,161,961,2015,2715],r.INSIDE_GSUM=[0,336,1036,1516],r.OUTSIDE_ODD_WIDEST=[8,6,4,3,1],r.INSIDE_ODD_WIDEST=[2,4,6,8],r.FINDER_PATTERNS=[Int32Array.from([3,8,2,1]),Int32Array.from([3,5,5,1]),Int32Array.from([3,3,7,1]),Int32Array.from([3,1,9,1]),Int32Array.from([2,7,4,1]),Int32Array.from([2,5,6,1]),Int32Array.from([2,3,8,1]),Int32Array.from([1,5,7,1]),Int32Array.from([1,3,9,1])],r}(n.A)},13997:(t,r,e)=>{e.d(r,{A:()=>n});let n=function(){function t(t,r){this.value=t,this.checksumPortion=r}return t.prototype.getValue=function(){return this.value},t.prototype.getChecksumPortion=function(){return this.checksumPortion},t.prototype.toString=function(){return this.value+"("+this.checksumPortion+")"},t.prototype.equals=function(r){return r instanceof t&&this.value===r.value&&this.checksumPortion===r.checksumPortion},t.prototype.hashCode=function(){return this.value^this.checksumPortion},t}()},16067:(t,r,e)=>{e.d(r,{A:()=>o});var n=function(t){var r="function"==typeof Symbol&&Symbol.iterator,e=r&&t[r],n=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")};let o=function(){function t(){}return t.getRSSvalue=function(r,e,o){var i,a,s=0;try{for(var u=n(r),c=u.next();!c.done;c=u.next()){var f=c.value;s+=f}}catch(t){i={error:t}}finally{try{c&&!c.done&&(a=u.return)&&a.call(u)}finally{if(i)throw i.error}}for(var h=0,l=0,p=r.length,A=0;A<p-1;A++){var _=void 0;for(_=1,l|=1<<A;_<r[A];_++,l&=~(1<<A)){var d=t.combins(s-_-1,p-A-2);if(o&&0===l&&s-_-(p-A-1)>=p-A-1&&(d-=t.combins(s-_-(p-A),p-A-2)),p-A-1>1){for(var E=0,g=s-_-(p-A-2);g>e;g--)E+=t.combins(s-_-g-1,p-A-3);d-=E*(p-1-A)}else s-_>e&&d--;h+=d}s-=_}return h},t.combins=function(t,r){t-r>r?(n=r,e=t-r):(n=t-r,e=r);for(var e,n,o=1,i=1,a=t;a>e;a--)o*=a,i<=n&&(o/=i,i++);for(;i<=n;)o/=i,i++;return o},t}()},37391:(t,r,e)=>{e.d(r,{A:()=>u});var n=e(79886),o=e(438),i=e(22152),a=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),s=function(t){var r="function"==typeof Symbol&&Symbol.iterator,e=r&&t[r],n=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")};let u=function(t){function r(){var r=t.call(this)||this;return r.decodeFinderCounters=new Int32Array(4),r.dataCharacterCounters=new Int32Array(8),r.oddRoundingErrors=[,,,,],r.evenRoundingErrors=[,,,,],r.oddCounts=Array(r.dataCharacterCounters.length/2),r.evenCounts=Array(r.dataCharacterCounters.length/2),r}return a(r,t),r.prototype.getDecodeFinderCounters=function(){return this.decodeFinderCounters},r.prototype.getDataCharacterCounters=function(){return this.dataCharacterCounters},r.prototype.getOddRoundingErrors=function(){return this.oddRoundingErrors},r.prototype.getEvenRoundingErrors=function(){return this.evenRoundingErrors},r.prototype.getOddCounts=function(){return this.oddCounts},r.prototype.getEvenCounts=function(){return this.evenCounts},r.prototype.parseFinderValue=function(t,e){for(var n=0;n<e.length;n++)if(i.A.patternMatchVariance(t,e[n],r.MAX_INDIVIDUAL_VARIANCE)<r.MAX_AVG_VARIANCE)return n;throw new o.A},r.count=function(t){return n.A.sum(new Int32Array(t))},r.increment=function(t,r){for(var e=0,n=r[0],o=1;o<t.length;o++)r[o]>n&&(n=r[o],e=o);t[e]++},r.decrement=function(t,r){for(var e=0,n=r[0],o=1;o<t.length;o++)r[o]<n&&(n=r[o],e=o);t[e]--},r.isFinderPattern=function(t){var e,n,o=t[0]+t[1],i=o+t[2]+t[3],a=o/i;if(a>=r.MIN_FINDER_PATTERN_RATIO&&a<=r.MAX_FINDER_PATTERN_RATIO){var u=Number.MAX_SAFE_INTEGER,c=Number.MIN_SAFE_INTEGER;try{for(var f=s(t),h=f.next();!h.done;h=f.next()){var l=h.value;l>c&&(c=l),l<u&&(u=l)}}catch(t){e={error:t}}finally{try{h&&!h.done&&(n=f.return)&&n.call(f)}finally{if(e)throw e.error}}return c<10*u}return!1},r.MAX_AVG_VARIANCE=.2,r.MAX_INDIVIDUAL_VARIANCE=.45,r.MIN_FINDER_PATTERN_RATIO=9.5/12,r.MAX_FINDER_PATTERN_RATIO=12.5/14,r}(i.A)},42563:(t,r,e)=>{e.d(r,{A:()=>o});var n=e(68883);let o=function(){function t(t){this.information=t,this.generalDecoder=new n.A(t)}return t.prototype.getInformation=function(){return this.information},t.prototype.getGeneralDecoder=function(){return this.generalDecoder},t}()},68883:(t,r,e)=>{e.d(r,{A:()=>E});var n=e(71534),o=e(39778),i=e(1933),a=function(){function t(t,r){r?this.decodedInformation=null:(this.finished=t,this.decodedInformation=r)}return t.prototype.getDecodedInformation=function(){return this.decodedInformation},t.prototype.isFinished=function(){return this.finished},t}(),s=function(){function t(t){this.newPosition=t}return t.prototype.getNewPosition=function(){return this.newPosition},t}(),u=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),c=function(t){function r(r,e){var n=t.call(this,r)||this;return n.value=e,n}return u(r,t),r.prototype.getValue=function(){return this.value},r.prototype.isFNC1=function(){return this.value===r.FNC1},r.FNC1="$",r}(s),f=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),h=function(t){function r(r,e,n){var o=t.call(this,r)||this;return n?(o.remaining=!0,o.remainingValue=o.remainingValue):(o.remaining=!1,o.remainingValue=0),o.newString=e,o}return f(r,t),r.prototype.getNewString=function(){return this.newString},r.prototype.isRemaining=function(){return this.remaining},r.prototype.getRemainingValue=function(){return this.remainingValue},r}(s),l=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function n(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),p=function(t){function r(r,e,o){var i=t.call(this,r)||this;if(e<0||e>10||o<0||o>10)throw new n.A;return i.firstDigit=e,i.secondDigit=o,i}return l(r,t),r.prototype.getFirstDigit=function(){return this.firstDigit},r.prototype.getSecondDigit=function(){return this.secondDigit},r.prototype.getValue=function(){return 10*this.firstDigit+this.secondDigit},r.prototype.isFirstDigitFNC1=function(){return this.firstDigit===r.FNC1},r.prototype.isSecondDigitFNC1=function(){return this.secondDigit===r.FNC1},r.prototype.isAnyFNC1=function(){return this.firstDigit===r.FNC1||this.secondDigit===r.FNC1},r.FNC1=10,r}(s),A=e(438),_=function(t){var r="function"==typeof Symbol&&Symbol.iterator,e=r&&t[r],n=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")},d=function(){function t(){}return t.parseFieldsInGeneralPurpose=function(r){if(!r)return null;if(r.length<2)throw new A.A;var e,n,o,i,a,s,u,c,f=r.substring(0,2);try{for(var h=_(t.TWO_DIGIT_DATA_LENGTH),l=h.next();!l.done;l=h.next()){var p=l.value;if(p[0]===f){if(p[1]===t.VARIABLE_LENGTH)return t.processVariableAI(2,p[2],r);return t.processFixedAI(2,p[1],r)}}}catch(t){e={error:t}}finally{try{l&&!l.done&&(n=h.return)&&n.call(h)}finally{if(e)throw e.error}}if(r.length<3)throw new A.A;var d=r.substring(0,3);try{for(var E=_(t.THREE_DIGIT_DATA_LENGTH),g=E.next();!g.done;g=E.next()){var p=g.value;if(p[0]===d){if(p[1]===t.VARIABLE_LENGTH)return t.processVariableAI(3,p[2],r);return t.processFixedAI(3,p[1],r)}}}catch(t){o={error:t}}finally{try{g&&!g.done&&(i=E.return)&&i.call(E)}finally{if(o)throw o.error}}try{for(var y=_(t.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH),I=y.next();!I.done;I=y.next()){var p=I.value;if(p[0]===d){if(p[1]===t.VARIABLE_LENGTH)return t.processVariableAI(4,p[2],r);return t.processFixedAI(4,p[1],r)}}}catch(t){a={error:t}}finally{try{I&&!I.done&&(s=y.return)&&s.call(y)}finally{if(a)throw a.error}}if(r.length<4)throw new A.A;var v=r.substring(0,4);try{for(var w=_(t.FOUR_DIGIT_DATA_LENGTH),N=w.next();!N.done;N=w.next()){var p=N.value;if(p[0]===v){if(p[1]===t.VARIABLE_LENGTH)return t.processVariableAI(4,p[2],r);return t.processFixedAI(4,p[1],r)}}}catch(t){u={error:t}}finally{try{N&&!N.done&&(c=w.return)&&c.call(w)}finally{if(u)throw u.error}}throw new A.A},t.processFixedAI=function(r,e,n){if(n.length<r)throw new A.A;var o=n.substring(0,r);if(n.length<r+e)throw new A.A;var i=n.substring(r,r+e),a=n.substring(r+e),s="("+o+")"+i,u=t.parseFieldsInGeneralPurpose(a);return null==u?s:s+u},t.processVariableAI=function(r,e,n){var o,i=n.substring(0,r);o=n.length<r+e?n.length:r+e;var a=n.substring(r,o),s=n.substring(o),u="("+i+")"+a,c=t.parseFieldsInGeneralPurpose(s);return null==c?u:u+c},t.VARIABLE_LENGTH=[],t.TWO_DIGIT_DATA_LENGTH=[["00",18],["01",14],["02",14],["10",t.VARIABLE_LENGTH,20],["11",6],["12",6],["13",6],["15",6],["17",6],["20",2],["21",t.VARIABLE_LENGTH,20],["22",t.VARIABLE_LENGTH,29],["30",t.VARIABLE_LENGTH,8],["37",t.VARIABLE_LENGTH,8],["90",t.VARIABLE_LENGTH,30],["91",t.VARIABLE_LENGTH,30],["92",t.VARIABLE_LENGTH,30],["93",t.VARIABLE_LENGTH,30],["94",t.VARIABLE_LENGTH,30],["95",t.VARIABLE_LENGTH,30],["96",t.VARIABLE_LENGTH,30],["97",t.VARIABLE_LENGTH,3],["98",t.VARIABLE_LENGTH,30],["99",t.VARIABLE_LENGTH,30]],t.THREE_DIGIT_DATA_LENGTH=[["240",t.VARIABLE_LENGTH,30],["241",t.VARIABLE_LENGTH,30],["242",t.VARIABLE_LENGTH,6],["250",t.VARIABLE_LENGTH,30],["251",t.VARIABLE_LENGTH,30],["253",t.VARIABLE_LENGTH,17],["254",t.VARIABLE_LENGTH,20],["400",t.VARIABLE_LENGTH,30],["401",t.VARIABLE_LENGTH,30],["402",17],["403",t.VARIABLE_LENGTH,30],["410",13],["411",13],["412",13],["413",13],["414",13],["420",t.VARIABLE_LENGTH,20],["421",t.VARIABLE_LENGTH,15],["422",3],["423",t.VARIABLE_LENGTH,15],["424",3],["425",3],["426",3]],t.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH=[["310",6],["311",6],["312",6],["313",6],["314",6],["315",6],["316",6],["320",6],["321",6],["322",6],["323",6],["324",6],["325",6],["326",6],["327",6],["328",6],["329",6],["330",6],["331",6],["332",6],["333",6],["334",6],["335",6],["336",6],["340",6],["341",6],["342",6],["343",6],["344",6],["345",6],["346",6],["347",6],["348",6],["349",6],["350",6],["351",6],["352",6],["353",6],["354",6],["355",6],["356",6],["357",6],["360",6],["361",6],["362",6],["363",6],["364",6],["365",6],["366",6],["367",6],["368",6],["369",6],["390",t.VARIABLE_LENGTH,15],["391",t.VARIABLE_LENGTH,18],["392",t.VARIABLE_LENGTH,15],["393",t.VARIABLE_LENGTH,18],["703",t.VARIABLE_LENGTH,30]],t.FOUR_DIGIT_DATA_LENGTH=[["7001",13],["7002",t.VARIABLE_LENGTH,30],["7003",10],["8001",14],["8002",t.VARIABLE_LENGTH,20],["8003",t.VARIABLE_LENGTH,30],["8004",t.VARIABLE_LENGTH,30],["8005",6],["8006",18],["8007",t.VARIABLE_LENGTH,30],["8008",t.VARIABLE_LENGTH,12],["8018",18],["8020",t.VARIABLE_LENGTH,25],["8100",6],["8101",10],["8102",2],["8110",t.VARIABLE_LENGTH,70],["8200",t.VARIABLE_LENGTH,70]],t}();let E=function(){function t(t){this.buffer=new i.A,this.information=t}return t.prototype.decodeAllCodes=function(t,r){for(var e=r,n=null;;){var o=this.decodeGeneralPurposeField(e,n),i=d.parseFieldsInGeneralPurpose(o.getNewString());if(null!=i&&t.append(i),n=o.isRemaining()?""+o.getRemainingValue():null,e===o.getNewPosition())break;e=o.getNewPosition()}return t.toString()},t.prototype.isStillNumeric=function(t){if(t+7>this.information.getSize())return t+4<=this.information.getSize();for(var r=t;r<t+3;++r)if(this.information.get(r))return!0;return this.information.get(t+3)},t.prototype.decodeNumeric=function(t){if(t+7>this.information.getSize()){var r=this.extractNumericValueFromBitArray(t,4);return 0===r?new p(this.information.getSize(),p.FNC1,p.FNC1):new p(this.information.getSize(),r-1,p.FNC1)}var e=this.extractNumericValueFromBitArray(t,7);return new p(t+7,(e-8)/11,(e-8)%11)},t.prototype.extractNumericValueFromBitArray=function(r,e){return t.extractNumericValueFromBitArray(this.information,r,e)},t.extractNumericValueFromBitArray=function(t,r,e){for(var n=0,o=0;o<e;++o)t.get(r+o)&&(n|=1<<e-o-1);return n},t.prototype.decodeGeneralPurposeField=function(t,r){this.buffer.setLengthToZero(),null!=r&&this.buffer.append(r),this.current.setPosition(t);var e=this.parseBlocks();return null!=e&&e.isRemaining()?new h(this.current.getPosition(),this.buffer.toString(),e.getRemainingValue()):new h(this.current.getPosition(),this.buffer.toString())},t.prototype.parseBlocks=function(){do{var t,r,e=this.current.getPosition();if(t=this.current.isAlpha()?(r=this.parseAlphaBlock()).isFinished():this.current.isIsoIec646()?(r=this.parseIsoIec646Block()).isFinished():(r=this.parseNumericBlock()).isFinished(),e===this.current.getPosition()&&!t)break}while(!t);return r.getDecodedInformation()},t.prototype.parseNumericBlock=function(){for(;this.isStillNumeric(this.current.getPosition());){var t=this.decodeNumeric(this.current.getPosition());if(this.current.setPosition(t.getNewPosition()),t.isFirstDigitFNC1()){var r=void 0;return new a(!0,r=t.isSecondDigitFNC1()?new h(this.current.getPosition(),this.buffer.toString()):new h(this.current.getPosition(),this.buffer.toString(),t.getSecondDigit()))}if(this.buffer.append(t.getFirstDigit()),t.isSecondDigitFNC1()){var r=new h(this.current.getPosition(),this.buffer.toString());return new a(!0,r)}this.buffer.append(t.getSecondDigit())}return this.isNumericToAlphaNumericLatch(this.current.getPosition())&&(this.current.setAlpha(),this.current.incrementPosition(4)),new a(!1)},t.prototype.parseIsoIec646Block=function(){for(;this.isStillIsoIec646(this.current.getPosition());){var t=this.decodeIsoIec646(this.current.getPosition());if(this.current.setPosition(t.getNewPosition()),t.isFNC1())return new a(!0,new h(this.current.getPosition(),this.buffer.toString()));this.buffer.append(t.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setAlpha()),new a(!1)},t.prototype.parseAlphaBlock=function(){for(;this.isStillAlpha(this.current.getPosition());){var t=this.decodeAlphanumeric(this.current.getPosition());if(this.current.setPosition(t.getNewPosition()),t.isFNC1())return new a(!0,new h(this.current.getPosition(),this.buffer.toString()));this.buffer.append(t.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setIsoIec646()),new a(!1)},t.prototype.isStillIsoIec646=function(t){if(t+5>this.information.getSize())return!1;var r=this.extractNumericValueFromBitArray(t,5);if(r>=5&&r<16)return!0;if(t+7>this.information.getSize())return!1;var e=this.extractNumericValueFromBitArray(t,7);if(e>=64&&e<116)return!0;if(t+8>this.information.getSize())return!1;var n=this.extractNumericValueFromBitArray(t,8);return n>=232&&n<253},t.prototype.decodeIsoIec646=function(t){var r,e=this.extractNumericValueFromBitArray(t,5);if(15===e)return new c(t+5,c.FNC1);if(e>=5&&e<15)return new c(t+5,"0"+(e-5));var o=this.extractNumericValueFromBitArray(t,7);if(o>=64&&o<90)return new c(t+7,""+(o+1));if(o>=90&&o<116)return new c(t+7,""+(o+7));switch(this.extractNumericValueFromBitArray(t,8)){case 232:r="!";break;case 233:r='"';break;case 234:r="%";break;case 235:r="&";break;case 236:r="'";break;case 237:r="(";break;case 238:r=")";break;case 239:r="*";break;case 240:r="+";break;case 241:r=",";break;case 242:r="-";break;case 243:r=".";break;case 244:r="/";break;case 245:r=":";break;case 246:r=";";break;case 247:r="<";break;case 248:r="=";break;case 249:r=">";break;case 250:r="?";break;case 251:r="_";break;case 252:r=" ";break;default:throw new n.A}return new c(t+8,r)},t.prototype.isStillAlpha=function(t){if(t+5>this.information.getSize())return!1;var r=this.extractNumericValueFromBitArray(t,5);if(r>=5&&r<16)return!0;if(t+6>this.information.getSize())return!1;var e=this.extractNumericValueFromBitArray(t,6);return e>=16&&e<63},t.prototype.decodeAlphanumeric=function(t){var r,e=this.extractNumericValueFromBitArray(t,5);if(15===e)return new c(t+5,c.FNC1);if(e>=5&&e<15)return new c(t+5,"0"+(e-5));var n=this.extractNumericValueFromBitArray(t,6);if(n>=32&&n<58)return new c(t+6,""+(n+33));switch(n){case 58:r="*";break;case 59:r=",";break;case 60:r="-";break;case 61:r=".";break;case 62:r="/";break;default:throw new o.A("Decoding invalid alphanumeric value: "+n)}return new c(t+6,r)},t.prototype.isAlphaTo646ToAlphaLatch=function(t){if(t+1>this.information.getSize())return!1;for(var r=0;r<5&&r+t<this.information.getSize();++r)if(2===r){if(!this.information.get(t+2))return!1}else if(this.information.get(t+r))return!1;return!0},t.prototype.isAlphaOr646ToNumericLatch=function(t){if(t+3>this.information.getSize())return!1;for(var r=t;r<t+3;++r)if(this.information.get(r))return!1;return!0},t.prototype.isNumericToAlphaNumericLatch=function(t){if(t+1>this.information.getSize())return!1;for(var r=0;r<4&&r+t<this.information.getSize();++r)if(this.information.get(t+r))return!1;return!0},t}()},87932:(t,r,e)=>{e.d(r,{A:()=>o});var n=e(56595);let o=function(){function t(t,r,e,o,i){this.value=t,this.startEnd=r,this.value=t,this.startEnd=r,this.resultPoints=[],this.resultPoints.push(new n.A(e,i)),this.resultPoints.push(new n.A(o,i))}return t.prototype.getValue=function(){return this.value},t.prototype.getStartEnd=function(){return this.startEnd},t.prototype.getResultPoints=function(){return this.resultPoints},t.prototype.equals=function(r){return r instanceof t&&this.value===r.value},t.prototype.hashCode=function(){return this.value},t}()}}]);