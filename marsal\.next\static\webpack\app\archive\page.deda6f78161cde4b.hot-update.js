"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/archive/page",{

/***/ "(app-pages-browser)/./src/components/receipt-template.tsx":
/*!*********************************************!*\
  !*** ./src/components/receipt-template.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ReceiptTemplate = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = (param, ref)=>{\n    let { order } = param;\n    // Generate barcode data (simple implementation)\n    const generateBarcode = (trackingNumber)=>{\n        // This is a simple barcode representation based on tracking number\n        // In a real app, you'd use a proper barcode library\n        const barcodePattern = trackingNumber.split('').map(()=>'|||').join(' ');\n        return \"||||| \".concat(barcodePattern, \" |||||\");\n    };\n    const getStatusText = (status)=>{\n        const statusMap = {\n            pending: \"في الانتظار\",\n            assigned: \"مسند للمندوب\",\n            \"out-for-delivery\": \"خارج للتوصيل\",\n            delivered: \"تم التسليم\",\n            returned: \"راجع للمرسل\",\n            cancelled: \"ملغي\",\n            postponed: \"مؤجل\"\n        };\n        return statusMap[status] || status;\n    };\n    // Format price in English as required\n    const formatPrice = (amount)=>{\n        return amount.toLocaleString('en-US', {\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        });\n    };\n    // Get current date and time\n    const getCurrentDateTime = ()=>{\n        const now = new Date();\n        return {\n            date: now.toLocaleDateString('ar-IQ'),\n            time: now.toLocaleTimeString('ar-IQ', {\n                hour: '2-digit',\n                minute: '2-digit'\n            })\n        };\n    };\n    const { date, time } = getCurrentDateTime();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: \"receipt-template\",\n        style: {\n            width: \"110mm\",\n            height: \"130mm\",\n            padding: \"5mm\",\n            fontFamily: \"'Arial', 'Tahoma', sans-serif\",\n            fontSize: \"11px\",\n            lineHeight: \"1.3\",\n            color: \"#000\",\n            backgroundColor: \"#fff\",\n            border: \"2px solid #000\",\n            boxSizing: \"border-box\",\n            direction: \"rtl\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"8px\",\n                    borderBottom: \"2px solid #000\",\n                    paddingBottom: \"5px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: \"14px\",\n                            fontWeight: \"bold\",\n                            margin: \"0 0 3px 0\",\n                            color: \"#000\",\n                            letterSpacing: \"0.5px\",\n                            lineHeight: \"1.1\"\n                        },\n                        children: \"مكتب علي الشيباني للتوصيل السريع فرع الحي\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            margin: \"0\",\n                            fontSize: \"8px\",\n                            color: \"#666\",\n                            fontStyle: \"italic\"\n                        },\n                        children: \"خدمة توصيل سريعة وموثوقة\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"10px\",\n                    border: \"2px solid #000\",\n                    padding: \"6px\",\n                    backgroundColor: \"#f0f8ff\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"9px\",\n                            color: \"#666\",\n                            marginBottom: \"2px\"\n                        },\n                        children: \"رقم الوصل\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"18px\",\n                            fontWeight: \"bold\",\n                            color: \"#000\",\n                            letterSpacing: \"1px\"\n                        },\n                        children: order.trackingNumber\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"6px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"رقم هاتف الزبون:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"12px\",\n                            color: \"#0066cc\",\n                            direction: \"ltr\"\n                        },\n                        children: order.recipientPhone\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"6px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"الحالة:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"11px\",\n                            color: order.status === \"delivered\" ? \"#28a745\" : \"#ffc107\",\n                            padding: \"2px 6px\",\n                            borderRadius: \"3px\",\n                            backgroundColor: order.status === \"delivered\" ? \"#d4edda\" : \"#fff3cd\"\n                        },\n                        children: getStatusText(order.status)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"8px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"اسم المندوب:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"11px\",\n                            color: \"#333\"\n                        },\n                        children: order.assignedTo || \"غير محدد\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"8px\",\n                    border: \"3px solid #000\",\n                    padding: \"8px\",\n                    backgroundColor: \"#fff3cd\",\n                    borderRadius: \"4px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"10px\",\n                            color: \"#666\",\n                            marginBottom: \"3px\",\n                            fontWeight: \"bold\"\n                        },\n                        children: \"Amount Required - المبلغ المطلوب\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"20px\",\n                            fontWeight: \"bold\",\n                            color: \"#000\",\n                            letterSpacing: \"1px\",\n                            direction: \"ltr\",\n                            fontFamily: \"Arial, sans-serif\",\n                            textShadow: \"1px 1px 2px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            formatPrice(order.amount),\n                            \" IQD\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"8px\",\n                    border: \"1px solid #000\",\n                    padding: \"6px\",\n                    backgroundColor: \"#fff\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"8px\",\n                            color: \"#666\",\n                            marginBottom: \"3px\"\n                        },\n                        children: \"الباركود\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontFamily: \"monospace\",\n                            fontSize: \"10px\",\n                            letterSpacing: \"1px\",\n                            color: \"#000\",\n                            backgroundColor: \"#fff\",\n                            padding: \"3px\"\n                        },\n                        children: generateBarcode(order.trackingNumber)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"8px\",\n                            color: \"#666\",\n                            marginTop: \"2px\"\n                        },\n                        children: order.trackingNumber\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    fontSize: \"7px\",\n                    color: \"#666\",\n                    borderTop: \"1px solid #ddd\",\n                    paddingTop: \"3px\",\n                    marginTop: \"auto\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            marginBottom: \"1px\"\n                        },\n                        children: \"شكراً لاختياركم خدماتنا\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            fontSize: \"6px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"التاريخ: \",\n                                    date\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"الوقت: \",\n                                    time\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n        lineNumber: 53,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = ReceiptTemplate;\nReceiptTemplate.displayName = \"ReceiptTemplate\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReceiptTemplate);\nvar _c, _c1;\n$RefreshReg$(_c, \"ReceiptTemplate$forwardRef\");\n$RefreshReg$(_c1, \"ReceiptTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/receipt-template.tsx\n"));

/***/ })

});