(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[872],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(5155);t(2115);var a=t(9708),n=t(2085),i=t(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:n,asChild:l=!1,...o}=e,c=l?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:t,size:n,className:r})),...o})}},2138:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2318:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(5155);t(2115);var a=t(9434);function n(e){let{className:r,type:t,...n}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...n})}},4229:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4475:(e,r,t)=>{Promise.resolve().then(t.bind(t,7706))},5695:(e,r,t)=>{"use strict";var s=t(8999);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i});var s=t(5155);t(2115);var a=t(9434);function n(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function i(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function d(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...t})}function l(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...t})}function o(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...t})}},7706:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(5155),a=t(2115),n=t(6695),i=t(285),d=t(2523),l=t(2318),o=t(2138),c=t(4229),u=t(9434),m=t(6874),x=t.n(m),p=t(5695);function g(){let e=(0,p.useRouter)(),[r,t]=(0,a.useState)({name:"",username:"",password:"",phone:"",role:"courier"}),[m,g]=(0,a.useState)(!1),[h,v]=(0,a.useState)({}),[f,b]=(0,a.useState)(null),[y,j]=(0,a.useState)("idle"),N=()=>{(r.name.trim()||r.username.trim()||r.phone.trim())&&(j("saving"),localStorage.setItem("newUserDraft",JSON.stringify(r)),setTimeout(()=>{j("saved"),setTimeout(()=>j("idle"),2e3)},500))};(0,a.useEffect)(()=>{let e=localStorage.getItem("newUserDraft");if(e)try{let r=JSON.parse(e);t(r)}catch(e){console.error("Error loading draft:",e)}},[]);let w=e=>{let{name:r,value:s}=e.target;t(e=>({...e,[r]:s})),h[r]&&v(e=>({...e,[r]:""})),f&&clearTimeout(f),b(setTimeout(()=>{N()},2e3))},k=()=>{let e={};return r.name.trim()||(e.name="الاسم مطلوب"),r.username.trim()?r.username.length<3?e.username="اسم المستخدم يجب أن يكون 3 أحرف على الأقل":/^[a-zA-Z0-9_]+$/.test(r.username)||(e.username="اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط"):e.username="اسم المستخدم مطلوب",r.password.trim()?r.password.length<6&&(e.password="كلمة المرور يجب أن تكون 6 أحرف على الأقل"):e.password="كلمة المرور مطلوبة",r.phone.trim()?(0,u.zC)(r.phone)||(e.phone="رقم الهاتف غير صحيح (يجب أن يبدأ بـ 07)"):e.phone="رقم الهاتف مطلوب",r.role||(e.role="الدور مطلوب"),v(e),0===Object.keys(e).length},S=async t=>{if(t.preventDefault(),k()){g(!0);try{let t={id:Date.now().toString(),name:r.name.trim(),username:r.username.trim(),password:r.password.trim(),phone:r.phone.trim(),role:r.role,isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},s=JSON.parse(localStorage.getItem("users")||"[]");if(s.some(e=>e.username===t.username)){alert("اسم المستخدم موجود بالفعل! يرجى اختيار اسم مستخدم آخر."),g(!1);return}s.push(t),localStorage.setItem("users",JSON.stringify(s)),localStorage.removeItem("newUserDraft"),alert("تم إضافة الموظف بنجاح!"),e.push("/users")}catch(e){console.error("Error creating user:",e),alert("حدث خطأ أثناء إضافة الموظف. يرجى المحاولة مرة أخرى.")}finally{g(!1)}}};return(0,s.jsx)("div",{className:"min-h-screen bg-background p-4 md:p-6 lg:p-8",dir:"rtl",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-primary flex items-center gap-2",children:[(0,s.jsx)(l.A,{className:"h-8 w-8"}),"موظف جديد","saving"===y&&(0,s.jsx)("span",{className:"text-sm text-yellow-600 bg-yellow-100 px-2 py-1 rounded-full",children:"جاري الحفظ..."}),"saved"===y&&(0,s.jsx)("span",{className:"text-sm text-green-600 bg-green-100 px-2 py-1 rounded-full",children:"تم الحفظ تلقائياً"})]}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:"إضافة موظف جديد إلى النظام - يتم الحفظ التلقائي للمسودة"})]}),(0,s.jsx)(x(),{href:"/users",children:(0,s.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),"العودة للموظفين"]})})]}),(0,s.jsxs)("form",{onSubmit:S,className:"space-y-6",children:[(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"المعلومات الشخصية"}),(0,s.jsx)(n.BT,{children:"البيانات الأساسية للموظف"})]}),(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"الاسم الكامل *"}),(0,s.jsx)(d.p,{name:"name",value:r.name,onChange:w,placeholder:"أدخل الاسم الكامل",className:h.name?"border-red-500":""}),h.name&&(0,s.jsx)("p",{className:"text-red-500 text-xs mt-1",children:h.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"رقم الهاتف *"}),(0,s.jsx)(d.p,{name:"phone",value:r.phone,onChange:w,placeholder:"07xxxxxxxxx",className:h.phone?"border-red-500":""}),h.phone&&(0,s.jsx)("p",{className:"text-red-500 text-xs mt-1",children:h.phone})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اسم المستخدم *"}),(0,s.jsx)(d.p,{name:"username",type:"text",value:r.username,onChange:w,placeholder:"اسم المستخدم (أحرف وأرقام فقط)",className:h.username?"border-red-500":""}),h.username&&(0,s.jsx)("p",{className:"text-red-500 text-xs mt-1",children:h.username})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"كلمة المرور *"}),(0,s.jsx)(d.p,{name:"password",type:"password",value:r.password,onChange:w,placeholder:"كلمة المرور (6 أحرف على الأقل)",className:h.password?"border-red-500":""}),h.password&&(0,s.jsx)("p",{className:"text-red-500 text-xs mt-1",children:h.password})]})]})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"الدور والصلاحيات"}),(0,s.jsx)(n.BT,{children:"تحديد دور الموظف في النظام"})]}),(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"الدور *"}),(0,s.jsxs)("select",{name:"role",value:r.role,onChange:w,className:"w-full p-3 border rounded-md bg-background ".concat(h.role?"border-red-500":"border-border"),children:[(0,s.jsx)("option",{value:"courier",children:"مندوب - استلام وتسليم الطلبات"}),(0,s.jsx)("option",{value:"supervisor",children:"متابع - متابعة الطلبات والإحصائيات"}),(0,s.jsx)("option",{value:"manager",children:"مدير - صلاحيات كاملة"})]}),h.role&&(0,s.jsx)("p",{className:"text-red-500 text-xs mt-1",children:h.role})]}),(0,s.jsxs)("div",{className:"p-4 bg-muted rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"وصف الدور:"}),"manager"===r.role&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"صلاحيات كاملة في النظام - إدارة جميع الأقسام والمستخدمين والطلبات والمحاسبة"}),"supervisor"===r.role&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"متابعة الطلبات والإحصائيات - عرض وتحديث الطلبات، الوصول للأرشيف والإحصائيات"}),"courier"===r.role&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"استلام وتسليم الطلبات - عرض الطلبات المسندة وتحديث حالتها فقط"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,s.jsx)(x(),{href:"/users",children:(0,s.jsx)(i.$,{variant:"outline",disabled:m,children:"إلغاء"})}),(0,s.jsxs)(i.$,{type:"submit",disabled:m,className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),m?"جاري الحفظ...":"حفظ الموظف"]})]})]})]})})}},9434:(e,r,t)=>{"use strict";t.d(r,{Yq:()=>d,cn:()=>n,ps:()=>m,qY:()=>u,r6:()=>l,vv:()=>i,y7:()=>o,zC:()=>c});var s=t(2596),a=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}function i(e){return"".concat(e.toLocaleString("ar-IQ")," د.ع")}function d(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function l(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function o(){let e=Date.now().toString().slice(-6),r=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"".concat("MRS").concat(e).concat(r)}function c(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function u(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function m(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}}},e=>{var r=r=>e(e.s=r);e.O(0,[455,874,441,684,358],()=>r(4475)),_N_E=e.O()}]);