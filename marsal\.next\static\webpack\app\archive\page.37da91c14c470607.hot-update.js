"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/archive/page",{

/***/ "(app-pages-browser)/./src/lib/firestore.ts":
/*!******************************!*\
  !*** ./src/lib/firestore.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ordersService: () => (/* binding */ ordersService),\n/* harmony export */   settlementsService: () => (/* binding */ settlementsService),\n/* harmony export */   usersService: () => (/* binding */ usersService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _mock_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mock-data */ \"(app-pages-browser)/./src/lib/mock-data.ts\");\n// Updated to use Supabase instead of Firebase\n\n\n\n// Orders Service - Updated to use Supabase\nconst ordersService = {\n    // Create new order\n    async create (orderData) {\n        const trackingNumber = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.generateTrackingNumber)();\n        const supabaseOrderData = {\n            tracking_number: trackingNumber,\n            customer_name: orderData.customerName,\n            customer_phone: orderData.customerPhone,\n            address: orderData.address,\n            amount: orderData.amount,\n            status: 'pending',\n            courier_name: orderData.courierName,\n            courier_id: orderData.courierId,\n            notes: orderData.notes\n        };\n        const order = await _supabase__WEBPACK_IMPORTED_MODULE_0__.orderService.createOrder(supabaseOrderData);\n        // Convert back to frontend format\n        return {\n            id: order.id,\n            trackingNumber: order.tracking_number,\n            customerName: order.customer_name,\n            customerPhone: order.customer_phone,\n            address: order.address,\n            amount: order.amount,\n            status: order.status,\n            courierName: order.courier_name,\n            courierId: order.courier_id,\n            notes: order.notes,\n            createdAt: new Date(order.created_at),\n            updatedAt: new Date(order.updated_at),\n            statusHistory: [\n                {\n                    status: 'pending',\n                    timestamp: new Date(order.created_at),\n                    updatedBy: 'system',\n                    notes: 'تم إنشاء الطلب'\n                }\n            ]\n        };\n    },\n    // Get all orders with pagination\n    async getAll () {\n        let pageSize = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, lastDoc = arguments.length > 1 ? arguments[1] : void 0;\n        try {\n            const supabaseOrders = await _supabase__WEBPACK_IMPORTED_MODULE_0__.orderService.getAllOrders();\n            // Convert from Supabase format to frontend format\n            const orders = supabaseOrders.map((order)=>({\n                    id: order.id,\n                    trackingNumber: order.tracking_number,\n                    customerName: order.customer_name,\n                    customerPhone: order.customer_phone,\n                    address: order.address,\n                    amount: order.amount,\n                    status: order.status,\n                    courierName: order.courier_name,\n                    courierId: order.courier_id,\n                    notes: order.notes,\n                    createdAt: new Date(order.created_at),\n                    updatedAt: new Date(order.updated_at),\n                    deliveredAt: order.delivered_at ? new Date(order.delivered_at) : undefined,\n                    statusHistory: [\n                        {\n                            status: order.status,\n                            timestamp: new Date(order.updated_at),\n                            updatedBy: 'system',\n                            notes: order.notes || ''\n                        }\n                    ]\n                }));\n            // Simple pagination simulation\n            const startIndex = lastDoc ? parseInt(lastDoc) : 0;\n            const endIndex = startIndex + pageSize;\n            const paginatedOrders = orders.slice(startIndex, endIndex);\n            return {\n                orders: paginatedOrders,\n                lastDoc: endIndex.toString(),\n                hasMore: endIndex < orders.length\n            };\n        } catch (error) {\n            console.warn('Supabase not available, using mock data:', error);\n            return {\n                orders: _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockOrders,\n                lastDoc: null,\n                hasMore: false\n            };\n        }\n    },\n    // Get order by ID\n    async getById (id) {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('orders').select('*').eq('id', id).single();\n            if (error || !data) {\n                throw new Error('Order not found');\n            }\n            return {\n                id: data.id,\n                trackingNumber: data.tracking_number,\n                customerName: data.customer_name,\n                customerPhone: data.customer_phone,\n                address: data.address,\n                amount: data.amount,\n                status: data.status,\n                courierName: data.courier_name,\n                courierId: data.courier_id,\n                notes: data.notes,\n                createdAt: new Date(data.created_at),\n                updatedAt: new Date(data.updated_at),\n                deliveredAt: data.delivered_at ? new Date(data.delivered_at) : undefined,\n                statusHistory: [\n                    {\n                        status: data.status,\n                        timestamp: new Date(data.updated_at),\n                        updatedBy: 'system',\n                        notes: data.notes || ''\n                    }\n                ]\n            };\n        } catch (error) {\n            console.error('Error getting order by ID:', error);\n            throw new Error('Order not found');\n        }\n    },\n    // Search orders\n    async search (searchTerm) {\n        try {\n            const supabaseOrders = await _supabase__WEBPACK_IMPORTED_MODULE_0__.orderService.searchOrders(searchTerm);\n            return supabaseOrders.map((order)=>({\n                    id: order.id,\n                    trackingNumber: order.tracking_number,\n                    customerName: order.customer_name,\n                    customerPhone: order.customer_phone,\n                    address: order.address,\n                    amount: order.amount,\n                    status: order.status,\n                    courierName: order.courier_name,\n                    courierId: order.courier_id,\n                    notes: order.notes,\n                    createdAt: new Date(order.created_at),\n                    updatedAt: new Date(order.updated_at),\n                    deliveredAt: order.delivered_at ? new Date(order.delivered_at) : undefined,\n                    statusHistory: [\n                        {\n                            status: order.status,\n                            timestamp: new Date(order.updated_at),\n                            updatedBy: 'system',\n                            notes: order.notes || ''\n                        }\n                    ]\n                }));\n        } catch (error) {\n            console.warn('Search failed, using mock data:', error);\n            return _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockOrders.filter((order)=>order.trackingNumber.includes(searchTerm) || order.customerName.includes(searchTerm) || order.customerPhone.includes(searchTerm));\n        }\n        const orders = new Map();\n        results.forEach((snapshot)=>{\n            snapshot.docs.forEach((doc1)=>{\n                if (!orders.has(doc1.id)) {\n                    var _data_createdAt, _data_updatedAt, _data_deliveredAt, _data_returnedAt, _data_statusHistory;\n                    const data = doc1.data();\n                    orders.set(doc1.id, {\n                        id: doc1.id,\n                        ...data,\n                        createdAt: (_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate(),\n                        updatedAt: (_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate(),\n                        deliveredAt: (_data_deliveredAt = data.deliveredAt) === null || _data_deliveredAt === void 0 ? void 0 : _data_deliveredAt.toDate(),\n                        returnedAt: (_data_returnedAt = data.returnedAt) === null || _data_returnedAt === void 0 ? void 0 : _data_returnedAt.toDate(),\n                        statusHistory: (_data_statusHistory = data.statusHistory) === null || _data_statusHistory === void 0 ? void 0 : _data_statusHistory.map((update)=>{\n                            var _update_timestamp;\n                            return {\n                                ...update,\n                                timestamp: (_update_timestamp = update.timestamp) === null || _update_timestamp === void 0 ? void 0 : _update_timestamp.toDate()\n                            };\n                        })\n                    });\n                }\n            });\n        });\n        return Array.from(orders.values());\n    },\n    // Update order status\n    async updateStatus (orderId, status, notes, updatedBy, image) {\n        const orderRef = doc(db, COLLECTIONS.ORDERS, orderId);\n        const now = Timestamp.now();\n        const statusUpdate = {\n            status: status,\n            timestamp: now.toDate(),\n            updatedBy,\n            notes,\n            image\n        };\n        const updateData = {\n            status,\n            updatedAt: now,\n            [\"statusHistory\"]: [\n                ...(await this.getById(orderId)).statusHistory,\n                statusUpdate\n            ]\n        };\n        if (status === 'delivered') {\n            updateData.deliveredAt = now;\n        } else if (status === 'returned') {\n            updateData.returnedAt = now;\n        }\n        await updateDoc(orderRef, updateData);\n        return statusUpdate;\n    },\n    // Get orders by status\n    async getByStatus (status) {\n        const q = query(collection(db, COLLECTIONS.ORDERS), where('status', '==', status), orderBy('createdAt', 'desc'));\n        const snapshot = await getDocs(q);\n        return snapshot.docs.map((doc1)=>{\n            var _doc_data_createdAt, _doc_data_updatedAt, _doc_data_deliveredAt, _doc_data_returnedAt, _doc_data_statusHistory;\n            return {\n                id: doc1.id,\n                ...doc1.data(),\n                createdAt: (_doc_data_createdAt = doc1.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate(),\n                updatedAt: (_doc_data_updatedAt = doc1.data().updatedAt) === null || _doc_data_updatedAt === void 0 ? void 0 : _doc_data_updatedAt.toDate(),\n                deliveredAt: (_doc_data_deliveredAt = doc1.data().deliveredAt) === null || _doc_data_deliveredAt === void 0 ? void 0 : _doc_data_deliveredAt.toDate(),\n                returnedAt: (_doc_data_returnedAt = doc1.data().returnedAt) === null || _doc_data_returnedAt === void 0 ? void 0 : _doc_data_returnedAt.toDate(),\n                statusHistory: (_doc_data_statusHistory = doc1.data().statusHistory) === null || _doc_data_statusHistory === void 0 ? void 0 : _doc_data_statusHistory.map((update)=>{\n                    var _update_timestamp;\n                    return {\n                        ...update,\n                        timestamp: (_update_timestamp = update.timestamp) === null || _update_timestamp === void 0 ? void 0 : _update_timestamp.toDate()\n                    };\n                })\n            };\n        });\n    },\n    // Get orders by courier\n    async getByCourier (courierId) {\n        const q = query(collection(db, COLLECTIONS.ORDERS), where('assignedTo', '==', courierId), orderBy('createdAt', 'desc'));\n        const snapshot = await getDocs(q);\n        return snapshot.docs.map((doc1)=>{\n            var _doc_data_createdAt, _doc_data_updatedAt, _doc_data_deliveredAt, _doc_data_returnedAt, _doc_data_statusHistory;\n            return {\n                id: doc1.id,\n                ...doc1.data(),\n                createdAt: (_doc_data_createdAt = doc1.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate(),\n                updatedAt: (_doc_data_updatedAt = doc1.data().updatedAt) === null || _doc_data_updatedAt === void 0 ? void 0 : _doc_data_updatedAt.toDate(),\n                deliveredAt: (_doc_data_deliveredAt = doc1.data().deliveredAt) === null || _doc_data_deliveredAt === void 0 ? void 0 : _doc_data_deliveredAt.toDate(),\n                returnedAt: (_doc_data_returnedAt = doc1.data().returnedAt) === null || _doc_data_returnedAt === void 0 ? void 0 : _doc_data_returnedAt.toDate(),\n                statusHistory: (_doc_data_statusHistory = doc1.data().statusHistory) === null || _doc_data_statusHistory === void 0 ? void 0 : _doc_data_statusHistory.map((update)=>{\n                    var _update_timestamp;\n                    return {\n                        ...update,\n                        timestamp: (_update_timestamp = update.timestamp) === null || _update_timestamp === void 0 ? void 0 : _update_timestamp.toDate()\n                    };\n                })\n            };\n        });\n    },\n    // Assign orders to courier\n    async assignToCourier (orderIds, courierId, assignedBy) {\n        const batch = writeBatch(db);\n        const now = Timestamp.now();\n        for (const orderId of orderIds){\n            const orderRef = doc(db, COLLECTIONS.ORDERS, orderId);\n            const order = await this.getById(orderId);\n            const statusUpdate = {\n                status: 'assigned',\n                timestamp: now.toDate(),\n                updatedBy: assignedBy,\n                notes: \"تم إسناد الطلب للمندوب\"\n            };\n            batch.update(orderRef, {\n                assignedTo: courierId,\n                status: 'assigned',\n                updatedAt: now,\n                statusHistory: [\n                    ...order.statusHistory,\n                    statusUpdate\n                ]\n            });\n        }\n        await batch.commit();\n    },\n    // Delete order\n    async delete (id) {\n        await deleteDoc(doc(db, COLLECTIONS.ORDERS, id));\n    }\n};\n// Users Service - Updated to use Supabase\nconst usersService = {\n    // Create user\n    async create (userData) {\n        try {\n            const supabaseUserData = {\n                username: userData.username,\n                name: userData.name,\n                phone: userData.phone,\n                role: userData.role,\n                password_hash: '$2b$10$example_hash_for_123456',\n                is_active: true,\n                created_by: userData.createdBy || 'system'\n            };\n            const user = await _supabase__WEBPACK_IMPORTED_MODULE_0__.userService.createUser(supabaseUserData);\n            return {\n                id: user.id,\n                username: user.username,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                isActive: user.is_active,\n                createdAt: new Date(user.created_at),\n                updatedAt: new Date(user.created_at),\n                createdBy: user.created_by\n            };\n        } catch (error) {\n            console.error('Error creating user:', error);\n            throw error;\n        }\n    },\n    // Get all users\n    async getAll () {\n        try {\n            const supabaseUsers = await _supabase__WEBPACK_IMPORTED_MODULE_0__.userService.getAllUsers();\n            return supabaseUsers.map((user)=>({\n                    id: user.id,\n                    username: user.username,\n                    name: user.name,\n                    phone: user.phone,\n                    role: user.role,\n                    isActive: user.is_active,\n                    createdAt: new Date(user.created_at),\n                    updatedAt: new Date(user.created_at),\n                    createdBy: user.created_by\n                }));\n        } catch (error) {\n            console.warn('Error getting users, using mock data:', error);\n            return _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockUsers;\n        }\n    },\n    // Get couriers only\n    async getCouriers () {\n        try {\n            const couriers = await _supabase__WEBPACK_IMPORTED_MODULE_0__.userService.getUsersByRole('courier');\n            return couriers.filter((user)=>user.is_active).map((user)=>({\n                    id: user.id,\n                    username: user.username,\n                    name: user.name,\n                    phone: user.phone,\n                    role: user.role,\n                    isActive: user.is_active,\n                    createdAt: new Date(user.created_at),\n                    updatedAt: new Date(user.created_at),\n                    createdBy: user.created_by\n                }));\n        } catch (error) {\n            console.warn('Error getting couriers, using mock data:', error);\n            return _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockUsers.filter((user)=>user.role === 'courier' && user.isActive);\n        }\n    },\n    // Get user by ID\n    async getById (id) {\n        var _data_createdAt, _data_updatedAt;\n        const docRef = doc(db, COLLECTIONS.USERS, id);\n        const docSnap = await getDoc(docRef);\n        if (!docSnap.exists()) {\n            throw new Error('User not found');\n        }\n        const data = docSnap.data();\n        return {\n            id: docSnap.id,\n            ...data,\n            createdAt: (_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate(),\n            updatedAt: (_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()\n        };\n    },\n    // Update user\n    async update (id, userData) {\n        const userRef = doc(db, COLLECTIONS.USERS, id);\n        await updateDoc(userRef, {\n            ...userData,\n            updatedAt: Timestamp.now()\n        });\n    },\n    // Delete user\n    async delete (id) {\n        await deleteDoc(doc(db, COLLECTIONS.USERS, id));\n    }\n};\n// Settlements Service\nconst settlementsService = {\n    // Create settlement\n    async create (settlementData) {\n        const now = Timestamp.now();\n        const settlement = {\n            ...settlementData,\n            createdAt: now.toDate(),\n            isSettled: false\n        };\n        const docRef = await addDoc(collection(db, COLLECTIONS.SETTLEMENTS), settlement);\n        return {\n            id: docRef.id,\n            ...settlement\n        };\n    },\n    // Get all settlements\n    async getAll () {\n        const q = query(collection(db, COLLECTIONS.SETTLEMENTS), orderBy('createdAt', 'desc'));\n        const snapshot = await getDocs(q);\n        return snapshot.docs.map((doc1)=>{\n            var _doc_data_createdAt, _doc_data_settledAt;\n            return {\n                id: doc1.id,\n                ...doc1.data(),\n                createdAt: (_doc_data_createdAt = doc1.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate(),\n                settledAt: (_doc_data_settledAt = doc1.data().settledAt) === null || _doc_data_settledAt === void 0 ? void 0 : _doc_data_settledAt.toDate()\n            };\n        });\n    },\n    // Mark settlement as settled\n    async markAsSettled (id) {\n        const settlementRef = doc(db, COLLECTIONS.SETTLEMENTS, id);\n        await updateDoc(settlementRef, {\n            isSettled: true,\n            settledAt: Timestamp.now()\n        });\n    },\n    // Real-time subscription to orders\n    subscribeToOrders (callback) {\n        try {\n            const q = query(collection(db, COLLECTIONS.ORDERS), orderBy('createdAt', 'desc'));\n            return onSnapshot(q, (snapshot)=>{\n                const orders = snapshot.docs.map((doc1)=>({\n                        id: doc1.id,\n                        ...doc1.data()\n                    }));\n                callback(orders);\n            });\n        } catch (error) {\n            console.warn('Firebase subscription not available:', error);\n            // Return a dummy unsubscribe function\n            return ()=>{};\n        }\n    },\n    // Real-time subscription to user's orders\n    subscribeToUserOrders (userId, callback) {\n        try {\n            const q = query(collection(db, COLLECTIONS.ORDERS), where('courierId', '==', userId), orderBy('createdAt', 'desc'));\n            return onSnapshot(q, (snapshot)=>{\n                const orders = snapshot.docs.map((doc1)=>({\n                        id: doc1.id,\n                        ...doc1.data()\n                    }));\n                callback(orders);\n            });\n        } catch (error) {\n            console.warn('Firebase subscription not available:', error);\n            return ()=>{};\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvZmlyZXN0b3JlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLDhDQUE4QztBQUMyRTtBQUV4RTtBQUNvQjtBQUVyRSwyQ0FBMkM7QUFDcEMsTUFBTU0sZ0JBQWdCO0lBQzNCLG1CQUFtQjtJQUNuQixNQUFNQyxRQUFPQyxTQUE2RjtRQUN4RyxNQUFNQyxpQkFBaUJOLDhEQUFzQkE7UUFFN0MsTUFBTU8sb0JBQW9CO1lBQ3hCQyxpQkFBaUJGO1lBQ2pCRyxlQUFlSixVQUFVSyxZQUFZO1lBQ3JDQyxnQkFBZ0JOLFVBQVVPLGFBQWE7WUFDdkNDLFNBQVNSLFVBQVVRLE9BQU87WUFDMUJDLFFBQVFULFVBQVVTLE1BQU07WUFDeEJDLFFBQVE7WUFDUkMsY0FBY1gsVUFBVVksV0FBVztZQUNuQ0MsWUFBWWIsVUFBVWMsU0FBUztZQUMvQkMsT0FBT2YsVUFBVWUsS0FBSztRQUN4QjtRQUVBLE1BQU1DLFFBQVEsTUFBTXRCLG1EQUFZQSxDQUFDdUIsV0FBVyxDQUFDZjtRQUU3QyxrQ0FBa0M7UUFDbEMsT0FBTztZQUNMZ0IsSUFBSUYsTUFBTUUsRUFBRTtZQUNaakIsZ0JBQWdCZSxNQUFNYixlQUFlO1lBQ3JDRSxjQUFjVyxNQUFNWixhQUFhO1lBQ2pDRyxlQUFlUyxNQUFNVixjQUFjO1lBQ25DRSxTQUFTUSxNQUFNUixPQUFPO1lBQ3RCQyxRQUFRTyxNQUFNUCxNQUFNO1lBQ3BCQyxRQUFRTSxNQUFNTixNQUFNO1lBQ3BCRSxhQUFhSSxNQUFNTCxZQUFZO1lBQy9CRyxXQUFXRSxNQUFNSCxVQUFVO1lBQzNCRSxPQUFPQyxNQUFNRCxLQUFLO1lBQ2xCSSxXQUFXLElBQUlDLEtBQUtKLE1BQU1LLFVBQVU7WUFDcENDLFdBQVcsSUFBSUYsS0FBS0osTUFBTU8sVUFBVTtZQUNwQ0MsZUFBZTtnQkFBQztvQkFDZGQsUUFBUTtvQkFDUmUsV0FBVyxJQUFJTCxLQUFLSixNQUFNSyxVQUFVO29CQUNwQ0ssV0FBVztvQkFDWFgsT0FBTztnQkFDVDthQUFFO1FBQ0o7SUFDRjtJQUVBLGlDQUFpQztJQUNqQyxNQUFNWTtZQUFPQyxXQUFBQSxpRUFBVyxJQUFJQztRQUMxQixJQUFJO1lBQ0YsTUFBTUMsaUJBQWlCLE1BQU1wQyxtREFBWUEsQ0FBQ3FDLFlBQVk7WUFFdEQsa0RBQWtEO1lBQ2xELE1BQU1DLFNBQVNGLGVBQWVHLEdBQUcsQ0FBQ2pCLENBQUFBLFFBQVU7b0JBQzFDRSxJQUFJRixNQUFNRSxFQUFFO29CQUNaakIsZ0JBQWdCZSxNQUFNYixlQUFlO29CQUNyQ0UsY0FBY1csTUFBTVosYUFBYTtvQkFDakNHLGVBQWVTLE1BQU1WLGNBQWM7b0JBQ25DRSxTQUFTUSxNQUFNUixPQUFPO29CQUN0QkMsUUFBUU8sTUFBTVAsTUFBTTtvQkFDcEJDLFFBQVFNLE1BQU1OLE1BQU07b0JBQ3BCRSxhQUFhSSxNQUFNTCxZQUFZO29CQUMvQkcsV0FBV0UsTUFBTUgsVUFBVTtvQkFDM0JFLE9BQU9DLE1BQU1ELEtBQUs7b0JBQ2xCSSxXQUFXLElBQUlDLEtBQUtKLE1BQU1LLFVBQVU7b0JBQ3BDQyxXQUFXLElBQUlGLEtBQUtKLE1BQU1PLFVBQVU7b0JBQ3BDVyxhQUFhbEIsTUFBTW1CLFlBQVksR0FBRyxJQUFJZixLQUFLSixNQUFNbUIsWUFBWSxJQUFJQztvQkFDakVaLGVBQWU7d0JBQUM7NEJBQ2RkLFFBQVFNLE1BQU1OLE1BQU07NEJBQ3BCZSxXQUFXLElBQUlMLEtBQUtKLE1BQU1PLFVBQVU7NEJBQ3BDRyxXQUFXOzRCQUNYWCxPQUFPQyxNQUFNRCxLQUFLLElBQUk7d0JBQ3hCO3FCQUFFO2dCQUNKO1lBRUEsK0JBQStCO1lBQy9CLE1BQU1zQixhQUFhUixVQUFVUyxTQUFTVCxXQUFXO1lBQ2pELE1BQU1VLFdBQVdGLGFBQWFUO1lBQzlCLE1BQU1ZLGtCQUFrQlIsT0FBT1MsS0FBSyxDQUFDSixZQUFZRTtZQUVqRCxPQUFPO2dCQUNMUCxRQUFRUTtnQkFDUlgsU0FBU1UsU0FBU0csUUFBUTtnQkFDMUJDLFNBQVNKLFdBQVdQLE9BQU9ZLE1BQU07WUFDbkM7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUMsSUFBSSxDQUFDLDRDQUE0Q0Y7WUFDekQsT0FBTztnQkFDTGIsUUFBUXBDLGtEQUFVQTtnQkFDbEJpQyxTQUFTO2dCQUNUYyxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEsa0JBQWtCO0lBQ2xCLE1BQU1LLFNBQVE5QixFQUFVO1FBQ3RCLElBQUk7WUFDRixNQUFNLEVBQUUrQixJQUFJLEVBQUVKLEtBQUssRUFBRSxHQUFHLE1BQU1yRCwrQ0FBUUEsQ0FDbkMwRCxJQUFJLENBQUMsVUFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxNQUFNbEMsSUFDVG1DLE1BQU07WUFFVCxJQUFJUixTQUFTLENBQUNJLE1BQU07Z0JBQ2xCLE1BQU0sSUFBSUssTUFBTTtZQUNsQjtZQUVBLE9BQU87Z0JBQ0xwQyxJQUFJK0IsS0FBSy9CLEVBQUU7Z0JBQ1hqQixnQkFBZ0JnRCxLQUFLOUMsZUFBZTtnQkFDcENFLGNBQWM0QyxLQUFLN0MsYUFBYTtnQkFDaENHLGVBQWUwQyxLQUFLM0MsY0FBYztnQkFDbENFLFNBQVN5QyxLQUFLekMsT0FBTztnQkFDckJDLFFBQVF3QyxLQUFLeEMsTUFBTTtnQkFDbkJDLFFBQVF1QyxLQUFLdkMsTUFBTTtnQkFDbkJFLGFBQWFxQyxLQUFLdEMsWUFBWTtnQkFDOUJHLFdBQVdtQyxLQUFLcEMsVUFBVTtnQkFDMUJFLE9BQU9rQyxLQUFLbEMsS0FBSztnQkFDakJJLFdBQVcsSUFBSUMsS0FBSzZCLEtBQUs1QixVQUFVO2dCQUNuQ0MsV0FBVyxJQUFJRixLQUFLNkIsS0FBSzFCLFVBQVU7Z0JBQ25DVyxhQUFhZSxLQUFLZCxZQUFZLEdBQUcsSUFBSWYsS0FBSzZCLEtBQUtkLFlBQVksSUFBSUM7Z0JBQy9EWixlQUFlO29CQUFDO3dCQUNkZCxRQUFRdUMsS0FBS3ZDLE1BQU07d0JBQ25CZSxXQUFXLElBQUlMLEtBQUs2QixLQUFLMUIsVUFBVTt3QkFDbkNHLFdBQVc7d0JBQ1hYLE9BQU9rQyxLQUFLbEMsS0FBSyxJQUFJO29CQUN2QjtpQkFBRTtZQUNKO1FBQ0YsRUFBRSxPQUFPOEIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsOEJBQThCQTtZQUM1QyxNQUFNLElBQUlTLE1BQU07UUFDbEI7SUFDRjtJQUVBLGdCQUFnQjtJQUNoQixNQUFNQyxRQUFPQyxVQUFrQjtRQUM3QixJQUFJO1lBQ0YsTUFBTTFCLGlCQUFpQixNQUFNcEMsbURBQVlBLENBQUMrRCxZQUFZLENBQUNEO1lBRXZELE9BQU8xQixlQUFlRyxHQUFHLENBQUNqQixDQUFBQSxRQUFVO29CQUNsQ0UsSUFBSUYsTUFBTUUsRUFBRTtvQkFDWmpCLGdCQUFnQmUsTUFBTWIsZUFBZTtvQkFDckNFLGNBQWNXLE1BQU1aLGFBQWE7b0JBQ2pDRyxlQUFlUyxNQUFNVixjQUFjO29CQUNuQ0UsU0FBU1EsTUFBTVIsT0FBTztvQkFDdEJDLFFBQVFPLE1BQU1QLE1BQU07b0JBQ3BCQyxRQUFRTSxNQUFNTixNQUFNO29CQUNwQkUsYUFBYUksTUFBTUwsWUFBWTtvQkFDL0JHLFdBQVdFLE1BQU1ILFVBQVU7b0JBQzNCRSxPQUFPQyxNQUFNRCxLQUFLO29CQUNsQkksV0FBVyxJQUFJQyxLQUFLSixNQUFNSyxVQUFVO29CQUNwQ0MsV0FBVyxJQUFJRixLQUFLSixNQUFNTyxVQUFVO29CQUNwQ1csYUFBYWxCLE1BQU1tQixZQUFZLEdBQUcsSUFBSWYsS0FBS0osTUFBTW1CLFlBQVksSUFBSUM7b0JBQ2pFWixlQUFlO3dCQUFDOzRCQUNkZCxRQUFRTSxNQUFNTixNQUFNOzRCQUNwQmUsV0FBVyxJQUFJTCxLQUFLSixNQUFNTyxVQUFVOzRCQUNwQ0csV0FBVzs0QkFDWFgsT0FBT0MsTUFBTUQsS0FBSyxJQUFJO3dCQUN4QjtxQkFBRTtnQkFDSjtRQUNGLEVBQUUsT0FBTzhCLE9BQU87WUFDZEMsUUFBUUMsSUFBSSxDQUFDLG1DQUFtQ0Y7WUFDaEQsT0FBT2pELGtEQUFVQSxDQUFDOEQsTUFBTSxDQUFDMUMsQ0FBQUEsUUFDdkJBLE1BQU1mLGNBQWMsQ0FBQzBELFFBQVEsQ0FBQ0gsZUFDOUJ4QyxNQUFNWCxZQUFZLENBQUNzRCxRQUFRLENBQUNILGVBQzVCeEMsTUFBTVQsYUFBYSxDQUFDb0QsUUFBUSxDQUFDSDtRQUVqQztRQUNBLE1BQU14QixTQUFTLElBQUk0QjtRQUVuQkMsUUFBUUMsT0FBTyxDQUFDQyxDQUFBQTtZQUNkQSxTQUFTQyxJQUFJLENBQUNGLE9BQU8sQ0FBQ0csQ0FBQUE7Z0JBQ3BCLElBQUksQ0FBQ2pDLE9BQU9rQyxHQUFHLENBQUNELEtBQUkvQyxFQUFFLEdBQUc7d0JBS1YrQixpQkFDQUEsaUJBQ0VBLG1CQUNEQSxrQkFDR0E7b0JBUmpCLE1BQU1BLE9BQU9nQixLQUFJaEIsSUFBSTtvQkFDckJqQixPQUFPbUMsR0FBRyxDQUFDRixLQUFJL0MsRUFBRSxFQUFFO3dCQUNqQkEsSUFBSStDLEtBQUkvQyxFQUFFO3dCQUNWLEdBQUcrQixJQUFJO3dCQUNQOUIsU0FBUyxHQUFFOEIsa0JBQUFBLEtBQUs5QixTQUFTLGNBQWQ4QixzQ0FBQUEsZ0JBQWdCbUIsTUFBTTt3QkFDakM5QyxTQUFTLEdBQUUyQixrQkFBQUEsS0FBSzNCLFNBQVMsY0FBZDJCLHNDQUFBQSxnQkFBZ0JtQixNQUFNO3dCQUNqQ2xDLFdBQVcsR0FBRWUsb0JBQUFBLEtBQUtmLFdBQVcsY0FBaEJlLHdDQUFBQSxrQkFBa0JtQixNQUFNO3dCQUNyQ0MsVUFBVSxHQUFFcEIsbUJBQUFBLEtBQUtvQixVQUFVLGNBQWZwQix1Q0FBQUEsaUJBQWlCbUIsTUFBTTt3QkFDbkM1QyxhQUFhLEdBQUV5QixzQkFBQUEsS0FBS3pCLGFBQWEsY0FBbEJ5QiwwQ0FBQUEsb0JBQW9CaEIsR0FBRyxDQUFDLENBQUNxQztnQ0FFM0JBO21DQUY0QztnQ0FDdkQsR0FBR0EsTUFBTTtnQ0FDVDdDLFNBQVMsR0FBRTZDLG9CQUFBQSxPQUFPN0MsU0FBUyxjQUFoQjZDLHdDQUFBQSxrQkFBa0JGLE1BQU07NEJBQ3JDOztvQkFDRjtnQkFDRjtZQUNGO1FBQ0Y7UUFFQSxPQUFPRyxNQUFNckIsSUFBSSxDQUFDbEIsT0FBT3dDLE1BQU07SUFDakM7SUFFQSxzQkFBc0I7SUFDdEIsTUFBTUMsY0FBYUMsT0FBZSxFQUFFaEUsTUFBYyxFQUFFSyxLQUFhLEVBQUVXLFNBQWlCLEVBQUVpRCxLQUFjO1FBQ2xHLE1BQU1DLFdBQVdYLElBQUlZLElBQUlDLFlBQVlDLE1BQU0sRUFBRUw7UUFDN0MsTUFBTU0sTUFBTUMsVUFBVUQsR0FBRztRQUV6QixNQUFNRSxlQUE2QjtZQUNqQ3hFLFFBQVFBO1lBQ1JlLFdBQVd1RCxJQUFJWixNQUFNO1lBQ3JCMUM7WUFDQVg7WUFDQTREO1FBQ0Y7UUFFQSxNQUFNUSxhQUFrQjtZQUN0QnpFO1lBQ0FZLFdBQVcwRDtZQUNYLENBQUUsZ0JBQWUsRUFBRTttQkFBSSxDQUFDLE1BQU0sSUFBSSxDQUFDaEMsT0FBTyxDQUFDMEIsUUFBTyxFQUFHbEQsYUFBYTtnQkFBRTBEO2FBQWE7UUFDbkY7UUFFQSxJQUFJeEUsV0FBVyxhQUFhO1lBQzFCeUUsV0FBV2pELFdBQVcsR0FBRzhDO1FBQzNCLE9BQU8sSUFBSXRFLFdBQVcsWUFBWTtZQUNoQ3lFLFdBQVdkLFVBQVUsR0FBR1c7UUFDMUI7UUFFQSxNQUFNSSxVQUFVUixVQUFVTztRQUMxQixPQUFPRDtJQUNUO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU1HLGFBQVkzRSxNQUFjO1FBQzlCLE1BQU00RSxJQUFJQyxNQUNSQyxXQUFXWCxJQUFJQyxZQUFZQyxNQUFNLEdBQ2pDVSxNQUFNLFVBQVUsTUFBTS9FLFNBQ3RCZ0YsUUFBUSxhQUFhO1FBR3ZCLE1BQU0zQixXQUFXLE1BQU00QixRQUFRTDtRQUMvQixPQUFPdkIsU0FBU0MsSUFBSSxDQUFDL0IsR0FBRyxDQUFDZ0MsQ0FBQUE7Z0JBR1pBLHFCQUNBQSxxQkFDRUEsdUJBQ0RBLHNCQUNHQTttQkFQZ0I7Z0JBQy9CL0MsSUFBSStDLEtBQUkvQyxFQUFFO2dCQUNWLEdBQUcrQyxLQUFJaEIsSUFBSSxFQUFFO2dCQUNiOUIsU0FBUyxHQUFFOEMsc0JBQUFBLEtBQUloQixJQUFJLEdBQUc5QixTQUFTLGNBQXBCOEMsMENBQUFBLG9CQUFzQkcsTUFBTTtnQkFDdkM5QyxTQUFTLEdBQUUyQyxzQkFBQUEsS0FBSWhCLElBQUksR0FBRzNCLFNBQVMsY0FBcEIyQywwQ0FBQUEsb0JBQXNCRyxNQUFNO2dCQUN2Q2xDLFdBQVcsR0FBRStCLHdCQUFBQSxLQUFJaEIsSUFBSSxHQUFHZixXQUFXLGNBQXRCK0IsNENBQUFBLHNCQUF3QkcsTUFBTTtnQkFDM0NDLFVBQVUsR0FBRUosdUJBQUFBLEtBQUloQixJQUFJLEdBQUdvQixVQUFVLGNBQXJCSiwyQ0FBQUEscUJBQXVCRyxNQUFNO2dCQUN6QzVDLGFBQWEsR0FBRXlDLDBCQUFBQSxLQUFJaEIsSUFBSSxHQUFHekIsYUFBYSxjQUF4QnlDLDhDQUFBQSx3QkFBMEJoQyxHQUFHLENBQUMsQ0FBQ3FDO3dCQUVqQ0E7MkJBRmtEO3dCQUM3RCxHQUFHQSxNQUFNO3dCQUNUN0MsU0FBUyxHQUFFNkMsb0JBQUFBLE9BQU83QyxTQUFTLGNBQWhCNkMsd0NBQUFBLGtCQUFrQkYsTUFBTTtvQkFDckM7O1lBQ0Y7O0lBQ0Y7SUFFQSx3QkFBd0I7SUFDeEIsTUFBTXdCLGNBQWE5RSxTQUFpQjtRQUNsQyxNQUFNd0UsSUFBSUMsTUFDUkMsV0FBV1gsSUFBSUMsWUFBWUMsTUFBTSxHQUNqQ1UsTUFBTSxjQUFjLE1BQU0zRSxZQUMxQjRFLFFBQVEsYUFBYTtRQUd2QixNQUFNM0IsV0FBVyxNQUFNNEIsUUFBUUw7UUFDL0IsT0FBT3ZCLFNBQVNDLElBQUksQ0FBQy9CLEdBQUcsQ0FBQ2dDLENBQUFBO2dCQUdaQSxxQkFDQUEscUJBQ0VBLHVCQUNEQSxzQkFDR0E7bUJBUGdCO2dCQUMvQi9DLElBQUkrQyxLQUFJL0MsRUFBRTtnQkFDVixHQUFHK0MsS0FBSWhCLElBQUksRUFBRTtnQkFDYjlCLFNBQVMsR0FBRThDLHNCQUFBQSxLQUFJaEIsSUFBSSxHQUFHOUIsU0FBUyxjQUFwQjhDLDBDQUFBQSxvQkFBc0JHLE1BQU07Z0JBQ3ZDOUMsU0FBUyxHQUFFMkMsc0JBQUFBLEtBQUloQixJQUFJLEdBQUczQixTQUFTLGNBQXBCMkMsMENBQUFBLG9CQUFzQkcsTUFBTTtnQkFDdkNsQyxXQUFXLEdBQUUrQix3QkFBQUEsS0FBSWhCLElBQUksR0FBR2YsV0FBVyxjQUF0QitCLDRDQUFBQSxzQkFBd0JHLE1BQU07Z0JBQzNDQyxVQUFVLEdBQUVKLHVCQUFBQSxLQUFJaEIsSUFBSSxHQUFHb0IsVUFBVSxjQUFyQkosMkNBQUFBLHFCQUF1QkcsTUFBTTtnQkFDekM1QyxhQUFhLEdBQUV5QywwQkFBQUEsS0FBSWhCLElBQUksR0FBR3pCLGFBQWEsY0FBeEJ5Qyw4Q0FBQUEsd0JBQTBCaEMsR0FBRyxDQUFDLENBQUNxQzt3QkFFakNBOzJCQUZrRDt3QkFDN0QsR0FBR0EsTUFBTTt3QkFDVDdDLFNBQVMsR0FBRTZDLG9CQUFBQSxPQUFPN0MsU0FBUyxjQUFoQjZDLHdDQUFBQSxrQkFBa0JGLE1BQU07b0JBQ3JDOztZQUNGOztJQUNGO0lBRUEsMkJBQTJCO0lBQzNCLE1BQU15QixpQkFBZ0JDLFFBQWtCLEVBQUVoRixTQUFpQixFQUFFaUYsVUFBa0I7UUFDN0UsTUFBTUMsUUFBUUMsV0FBV3BCO1FBQ3pCLE1BQU1HLE1BQU1DLFVBQVVELEdBQUc7UUFFekIsS0FBSyxNQUFNTixXQUFXb0IsU0FBVTtZQUM5QixNQUFNbEIsV0FBV1gsSUFBSVksSUFBSUMsWUFBWUMsTUFBTSxFQUFFTDtZQUM3QyxNQUFNMUQsUUFBUSxNQUFNLElBQUksQ0FBQ2dDLE9BQU8sQ0FBQzBCO1lBRWpDLE1BQU1RLGVBQTZCO2dCQUNqQ3hFLFFBQVE7Z0JBQ1JlLFdBQVd1RCxJQUFJWixNQUFNO2dCQUNyQjFDLFdBQVdxRTtnQkFDWGhGLE9BQVE7WUFDVjtZQUVBaUYsTUFBTTFCLE1BQU0sQ0FBQ00sVUFBVTtnQkFDckJzQixZQUFZcEY7Z0JBQ1pKLFFBQVE7Z0JBQ1JZLFdBQVcwRDtnQkFDWHhELGVBQWU7dUJBQUlSLE1BQU1RLGFBQWE7b0JBQUUwRDtpQkFBYTtZQUN2RDtRQUNGO1FBRUEsTUFBTWMsTUFBTUcsTUFBTTtJQUNwQjtJQUVBLGVBQWU7SUFDZixNQUFNQyxRQUFPbEYsRUFBVTtRQUNyQixNQUFNbUYsVUFBVXBDLElBQUlZLElBQUlDLFlBQVlDLE1BQU0sRUFBRTdEO0lBQzlDO0FBQ0YsRUFBRTtBQUVGLDBDQUEwQztBQUNuQyxNQUFNb0YsZUFBZTtJQUMxQixjQUFjO0lBQ2QsTUFBTXZHLFFBQU93RyxRQUFzRDtRQUNqRSxJQUFJO1lBQ0YsTUFBTUMsbUJBQW1CO2dCQUN2QkMsVUFBVUYsU0FBU0UsUUFBUTtnQkFDM0JDLE1BQU1ILFNBQVNHLElBQUk7Z0JBQ25CQyxPQUFPSixTQUFTSSxLQUFLO2dCQUNyQkMsTUFBTUwsU0FBU0ssSUFBSTtnQkFDbkJDLGVBQWU7Z0JBQ2ZDLFdBQVc7Z0JBQ1hDLFlBQVlSLFNBQVNTLFNBQVMsSUFBSTtZQUNwQztZQUVBLE1BQU1DLE9BQU8sTUFBTXhILGtEQUFXQSxDQUFDeUgsVUFBVSxDQUFDVjtZQUUxQyxPQUFPO2dCQUNMdEYsSUFBSStGLEtBQUsvRixFQUFFO2dCQUNYdUYsVUFBVVEsS0FBS1IsUUFBUTtnQkFDdkJDLE1BQU1PLEtBQUtQLElBQUk7Z0JBQ2ZDLE9BQU9NLEtBQUtOLEtBQUs7Z0JBQ2pCQyxNQUFNSyxLQUFLTCxJQUFJO2dCQUNmTyxVQUFVRixLQUFLSCxTQUFTO2dCQUN4QjNGLFdBQVcsSUFBSUMsS0FBSzZGLEtBQUs1RixVQUFVO2dCQUNuQ0MsV0FBVyxJQUFJRixLQUFLNkYsS0FBSzVGLFVBQVU7Z0JBQ25DMkYsV0FBV0MsS0FBS0YsVUFBVTtZQUM1QjtRQUNGLEVBQUUsT0FBT2xFLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHdCQUF3QkE7WUFDdEMsTUFBTUE7UUFDUjtJQUNGO0lBRUEsZ0JBQWdCO0lBQ2hCLE1BQU1sQjtRQUNKLElBQUk7WUFDRixNQUFNeUYsZ0JBQWdCLE1BQU0zSCxrREFBV0EsQ0FBQzRILFdBQVc7WUFFbkQsT0FBT0QsY0FBY25GLEdBQUcsQ0FBQ2dGLENBQUFBLE9BQVM7b0JBQ2hDL0YsSUFBSStGLEtBQUsvRixFQUFFO29CQUNYdUYsVUFBVVEsS0FBS1IsUUFBUTtvQkFDdkJDLE1BQU1PLEtBQUtQLElBQUk7b0JBQ2ZDLE9BQU9NLEtBQUtOLEtBQUs7b0JBQ2pCQyxNQUFNSyxLQUFLTCxJQUFJO29CQUNmTyxVQUFVRixLQUFLSCxTQUFTO29CQUN4QjNGLFdBQVcsSUFBSUMsS0FBSzZGLEtBQUs1RixVQUFVO29CQUNuQ0MsV0FBVyxJQUFJRixLQUFLNkYsS0FBSzVGLFVBQVU7b0JBQ25DMkYsV0FBV0MsS0FBS0YsVUFBVTtnQkFDNUI7UUFDRixFQUFFLE9BQU9sRSxPQUFPO1lBQ2RDLFFBQVFDLElBQUksQ0FBQyx5Q0FBeUNGO1lBQ3RELE9BQU9oRCxpREFBU0E7UUFDbEI7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNeUg7UUFDSixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNOUgsa0RBQVdBLENBQUMrSCxjQUFjLENBQUM7WUFFbEQsT0FBT0QsU0FBUzdELE1BQU0sQ0FBQ3VELENBQUFBLE9BQVFBLEtBQUtILFNBQVMsRUFBRTdFLEdBQUcsQ0FBQ2dGLENBQUFBLE9BQVM7b0JBQzFEL0YsSUFBSStGLEtBQUsvRixFQUFFO29CQUNYdUYsVUFBVVEsS0FBS1IsUUFBUTtvQkFDdkJDLE1BQU1PLEtBQUtQLElBQUk7b0JBQ2ZDLE9BQU9NLEtBQUtOLEtBQUs7b0JBQ2pCQyxNQUFNSyxLQUFLTCxJQUFJO29CQUNmTyxVQUFVRixLQUFLSCxTQUFTO29CQUN4QjNGLFdBQVcsSUFBSUMsS0FBSzZGLEtBQUs1RixVQUFVO29CQUNuQ0MsV0FBVyxJQUFJRixLQUFLNkYsS0FBSzVGLFVBQVU7b0JBQ25DMkYsV0FBV0MsS0FBS0YsVUFBVTtnQkFDNUI7UUFDRixFQUFFLE9BQU9sRSxPQUFPO1lBQ2RDLFFBQVFDLElBQUksQ0FBQyw0Q0FBNENGO1lBQ3pELE9BQU9oRCxpREFBU0EsQ0FBQzZELE1BQU0sQ0FBQ3VELENBQUFBLE9BQVFBLEtBQUtMLElBQUksS0FBSyxhQUFhSyxLQUFLRSxRQUFRO1FBQzFFO0lBQ0Y7SUFFQSxpQkFBaUI7SUFDakIsTUFBTW5FLFNBQVE5QixFQUFVO1lBWVQrQixpQkFDQUE7UUFaYixNQUFNd0UsU0FBU3hELElBQUlZLElBQUlDLFlBQVk0QyxLQUFLLEVBQUV4RztRQUMxQyxNQUFNeUcsVUFBVSxNQUFNQyxPQUFPSDtRQUU3QixJQUFJLENBQUNFLFFBQVFFLE1BQU0sSUFBSTtZQUNyQixNQUFNLElBQUl2RSxNQUFNO1FBQ2xCO1FBRUEsTUFBTUwsT0FBTzBFLFFBQVExRSxJQUFJO1FBQ3pCLE9BQU87WUFDTC9CLElBQUl5RyxRQUFRekcsRUFBRTtZQUNkLEdBQUcrQixJQUFJO1lBQ1A5QixTQUFTLEdBQUU4QixrQkFBQUEsS0FBSzlCLFNBQVMsY0FBZDhCLHNDQUFBQSxnQkFBZ0JtQixNQUFNO1lBQ2pDOUMsU0FBUyxHQUFFMkIsa0JBQUFBLEtBQUszQixTQUFTLGNBQWQyQixzQ0FBQUEsZ0JBQWdCbUIsTUFBTTtRQUNuQztJQUNGO0lBRUEsY0FBYztJQUNkLE1BQU1FLFFBQU9wRCxFQUFVLEVBQUVxRixRQUF1QjtRQUM5QyxNQUFNdUIsVUFBVTdELElBQUlZLElBQUlDLFlBQVk0QyxLQUFLLEVBQUV4RztRQUMzQyxNQUFNa0UsVUFBVTBDLFNBQVM7WUFDdkIsR0FBR3ZCLFFBQVE7WUFDWGpGLFdBQVcyRCxVQUFVRCxHQUFHO1FBQzFCO0lBQ0Y7SUFFQSxjQUFjO0lBQ2QsTUFBTW9CLFFBQU9sRixFQUFVO1FBQ3JCLE1BQU1tRixVQUFVcEMsSUFBSVksSUFBSUMsWUFBWTRDLEtBQUssRUFBRXhHO0lBQzdDO0FBQ0YsRUFBRTtBQUVGLHNCQUFzQjtBQUNmLE1BQU02RyxxQkFBcUI7SUFDaEMsb0JBQW9CO0lBQ3BCLE1BQU1oSSxRQUFPaUksY0FBb0Q7UUFDL0QsTUFBTWhELE1BQU1DLFVBQVVELEdBQUc7UUFDekIsTUFBTWlELGFBQXFDO1lBQ3pDLEdBQUdELGNBQWM7WUFDakI3RyxXQUFXNkQsSUFBSVosTUFBTTtZQUNyQjhELFdBQVc7UUFDYjtRQUVBLE1BQU1ULFNBQVMsTUFBTVUsT0FBTzNDLFdBQVdYLElBQUlDLFlBQVlzRCxXQUFXLEdBQUdIO1FBQ3JFLE9BQU87WUFBRS9HLElBQUl1RyxPQUFPdkcsRUFBRTtZQUFFLEdBQUcrRyxVQUFVO1FBQUM7SUFDeEM7SUFFQSxzQkFBc0I7SUFDdEIsTUFBTXRHO1FBQ0osTUFBTTJELElBQUlDLE1BQU1DLFdBQVdYLElBQUlDLFlBQVlzRCxXQUFXLEdBQUcxQyxRQUFRLGFBQWE7UUFDOUUsTUFBTTNCLFdBQVcsTUFBTTRCLFFBQVFMO1FBRS9CLE9BQU92QixTQUFTQyxJQUFJLENBQUMvQixHQUFHLENBQUNnQyxDQUFBQTtnQkFHWkEscUJBQ0FBO21CQUpvQjtnQkFDL0IvQyxJQUFJK0MsS0FBSS9DLEVBQUU7Z0JBQ1YsR0FBRytDLEtBQUloQixJQUFJLEVBQUU7Z0JBQ2I5QixTQUFTLEdBQUU4QyxzQkFBQUEsS0FBSWhCLElBQUksR0FBRzlCLFNBQVMsY0FBcEI4QywwQ0FBQUEsb0JBQXNCRyxNQUFNO2dCQUN2Q2lFLFNBQVMsR0FBRXBFLHNCQUFBQSxLQUFJaEIsSUFBSSxHQUFHb0YsU0FBUyxjQUFwQnBFLDBDQUFBQSxvQkFBc0JHLE1BQU07WUFDekM7O0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTWtFLGVBQWNwSCxFQUFVO1FBQzVCLE1BQU1xSCxnQkFBZ0J0RSxJQUFJWSxJQUFJQyxZQUFZc0QsV0FBVyxFQUFFbEg7UUFDdkQsTUFBTWtFLFVBQVVtRCxlQUFlO1lBQzdCTCxXQUFXO1lBQ1hHLFdBQVdwRCxVQUFVRCxHQUFHO1FBQzFCO0lBQ0Y7SUFFQSxtQ0FBbUM7SUFDbkN3RCxtQkFBa0JDLFFBQW1DO1FBQ25ELElBQUk7WUFDRixNQUFNbkQsSUFBSUMsTUFBTUMsV0FBV1gsSUFBSUMsWUFBWUMsTUFBTSxHQUFHVyxRQUFRLGFBQWE7WUFDekUsT0FBT2dELFdBQVdwRCxHQUFHLENBQUN2QjtnQkFDcEIsTUFBTS9CLFNBQVMrQixTQUFTQyxJQUFJLENBQUMvQixHQUFHLENBQUNnQyxDQUFBQSxPQUFRO3dCQUN2Qy9DLElBQUkrQyxLQUFJL0MsRUFBRTt3QkFDVixHQUFHK0MsS0FBSWhCLElBQUksRUFBRTtvQkFDZjtnQkFDQXdGLFNBQVN6RztZQUNYO1FBQ0YsRUFBRSxPQUFPYSxPQUFPO1lBQ2RDLFFBQVFDLElBQUksQ0FBQyx3Q0FBd0NGO1lBQ3JELHNDQUFzQztZQUN0QyxPQUFPLEtBQU87UUFDaEI7SUFDRjtJQUVBLDBDQUEwQztJQUMxQzhGLHVCQUFzQkMsTUFBYyxFQUFFSCxRQUFtQztRQUN2RSxJQUFJO1lBQ0YsTUFBTW5ELElBQUlDLE1BQ1JDLFdBQVdYLElBQUlDLFlBQVlDLE1BQU0sR0FDakNVLE1BQU0sYUFBYSxNQUFNbUQsU0FDekJsRCxRQUFRLGFBQWE7WUFFdkIsT0FBT2dELFdBQVdwRCxHQUFHLENBQUN2QjtnQkFDcEIsTUFBTS9CLFNBQVMrQixTQUFTQyxJQUFJLENBQUMvQixHQUFHLENBQUNnQyxDQUFBQSxPQUFRO3dCQUN2Qy9DLElBQUkrQyxLQUFJL0MsRUFBRTt3QkFDVixHQUFHK0MsS0FBSWhCLElBQUksRUFBRTtvQkFDZjtnQkFDQXdGLFNBQVN6RztZQUNYO1FBQ0YsRUFBRSxPQUFPYSxPQUFPO1lBQ2RDLFFBQVFDLElBQUksQ0FBQyx3Q0FBd0NGO1lBQ3JELE9BQU8sS0FBTztRQUNoQjtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiRTpcXE1hcnNhbFxcbWFyc2FsXFxzcmNcXGxpYlxcZmlyZXN0b3JlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFVwZGF0ZWQgdG8gdXNlIFN1cGFiYXNlIGluc3RlYWQgb2YgRmlyZWJhc2VcbmltcG9ydCB7IHN1cGFiYXNlLCB1c2VyU2VydmljZSwgb3JkZXJTZXJ2aWNlLCB0eXBlIFVzZXIgYXMgU3VwYWJhc2VVc2VyLCB0eXBlIE9yZGVyIGFzIFN1cGFiYXNlT3JkZXIgfSBmcm9tICcuL3N1cGFiYXNlJztcbmltcG9ydCB7IE9yZGVyLCBVc2VyLCBTZXR0bGVtZW50LCBTdGF0dXNVcGRhdGUgfSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCB7IGdlbmVyYXRlVHJhY2tpbmdOdW1iZXIgfSBmcm9tICcuL3V0aWxzJztcbmltcG9ydCB7IG1vY2tPcmRlcnMsIG1vY2tVc2VycywgbW9ja1NldHRsZW1lbnRzIH0gZnJvbSAnLi9tb2NrLWRhdGEnO1xuXG4vLyBPcmRlcnMgU2VydmljZSAtIFVwZGF0ZWQgdG8gdXNlIFN1cGFiYXNlXG5leHBvcnQgY29uc3Qgb3JkZXJzU2VydmljZSA9IHtcbiAgLy8gQ3JlYXRlIG5ldyBvcmRlclxuICBhc3luYyBjcmVhdGUob3JkZXJEYXRhOiBPbWl0PE9yZGVyLCAnaWQnIHwgJ2NyZWF0ZWRBdCcgfCAndXBkYXRlZEF0JyB8ICd0cmFja2luZ051bWJlcicgfCAnc3RhdHVzSGlzdG9yeSc+KSB7XG4gICAgY29uc3QgdHJhY2tpbmdOdW1iZXIgPSBnZW5lcmF0ZVRyYWNraW5nTnVtYmVyKCk7XG5cbiAgICBjb25zdCBzdXBhYmFzZU9yZGVyRGF0YSA9IHtcbiAgICAgIHRyYWNraW5nX251bWJlcjogdHJhY2tpbmdOdW1iZXIsXG4gICAgICBjdXN0b21lcl9uYW1lOiBvcmRlckRhdGEuY3VzdG9tZXJOYW1lLFxuICAgICAgY3VzdG9tZXJfcGhvbmU6IG9yZGVyRGF0YS5jdXN0b21lclBob25lLFxuICAgICAgYWRkcmVzczogb3JkZXJEYXRhLmFkZHJlc3MsXG4gICAgICBhbW91bnQ6IG9yZGVyRGF0YS5hbW91bnQsXG4gICAgICBzdGF0dXM6ICdwZW5kaW5nJyxcbiAgICAgIGNvdXJpZXJfbmFtZTogb3JkZXJEYXRhLmNvdXJpZXJOYW1lLFxuICAgICAgY291cmllcl9pZDogb3JkZXJEYXRhLmNvdXJpZXJJZCxcbiAgICAgIG5vdGVzOiBvcmRlckRhdGEubm90ZXNcbiAgICB9O1xuXG4gICAgY29uc3Qgb3JkZXIgPSBhd2FpdCBvcmRlclNlcnZpY2UuY3JlYXRlT3JkZXIoc3VwYWJhc2VPcmRlckRhdGEpO1xuXG4gICAgLy8gQ29udmVydCBiYWNrIHRvIGZyb250ZW5kIGZvcm1hdFxuICAgIHJldHVybiB7XG4gICAgICBpZDogb3JkZXIuaWQsXG4gICAgICB0cmFja2luZ051bWJlcjogb3JkZXIudHJhY2tpbmdfbnVtYmVyLFxuICAgICAgY3VzdG9tZXJOYW1lOiBvcmRlci5jdXN0b21lcl9uYW1lLFxuICAgICAgY3VzdG9tZXJQaG9uZTogb3JkZXIuY3VzdG9tZXJfcGhvbmUsXG4gICAgICBhZGRyZXNzOiBvcmRlci5hZGRyZXNzLFxuICAgICAgYW1vdW50OiBvcmRlci5hbW91bnQsXG4gICAgICBzdGF0dXM6IG9yZGVyLnN0YXR1cyxcbiAgICAgIGNvdXJpZXJOYW1lOiBvcmRlci5jb3VyaWVyX25hbWUsXG4gICAgICBjb3VyaWVySWQ6IG9yZGVyLmNvdXJpZXJfaWQsXG4gICAgICBub3Rlczogb3JkZXIubm90ZXMsXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKG9yZGVyLmNyZWF0ZWRfYXQpLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZShvcmRlci51cGRhdGVkX2F0KSxcbiAgICAgIHN0YXR1c0hpc3Rvcnk6IFt7XG4gICAgICAgIHN0YXR1czogJ3BlbmRpbmcnLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKG9yZGVyLmNyZWF0ZWRfYXQpLFxuICAgICAgICB1cGRhdGVkQnk6ICdzeXN0ZW0nLFxuICAgICAgICBub3RlczogJ9iq2YUg2KXZhti02KfYoSDYp9mE2LfZhNioJ1xuICAgICAgfV1cbiAgICB9IGFzIE9yZGVyO1xuICB9LFxuXG4gIC8vIEdldCBhbGwgb3JkZXJzIHdpdGggcGFnaW5hdGlvblxuICBhc3luYyBnZXRBbGwocGFnZVNpemUgPSAyMCwgbGFzdERvYz86IGFueSkge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzdXBhYmFzZU9yZGVycyA9IGF3YWl0IG9yZGVyU2VydmljZS5nZXRBbGxPcmRlcnMoKTtcblxuICAgICAgLy8gQ29udmVydCBmcm9tIFN1cGFiYXNlIGZvcm1hdCB0byBmcm9udGVuZCBmb3JtYXRcbiAgICAgIGNvbnN0IG9yZGVycyA9IHN1cGFiYXNlT3JkZXJzLm1hcChvcmRlciA9PiAoe1xuICAgICAgICBpZDogb3JkZXIuaWQsXG4gICAgICAgIHRyYWNraW5nTnVtYmVyOiBvcmRlci50cmFja2luZ19udW1iZXIsXG4gICAgICAgIGN1c3RvbWVyTmFtZTogb3JkZXIuY3VzdG9tZXJfbmFtZSxcbiAgICAgICAgY3VzdG9tZXJQaG9uZTogb3JkZXIuY3VzdG9tZXJfcGhvbmUsXG4gICAgICAgIGFkZHJlc3M6IG9yZGVyLmFkZHJlc3MsXG4gICAgICAgIGFtb3VudDogb3JkZXIuYW1vdW50LFxuICAgICAgICBzdGF0dXM6IG9yZGVyLnN0YXR1cyxcbiAgICAgICAgY291cmllck5hbWU6IG9yZGVyLmNvdXJpZXJfbmFtZSxcbiAgICAgICAgY291cmllcklkOiBvcmRlci5jb3VyaWVyX2lkLFxuICAgICAgICBub3Rlczogb3JkZXIubm90ZXMsXG4gICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUob3JkZXIuY3JlYXRlZF9hdCksXG4gICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUob3JkZXIudXBkYXRlZF9hdCksXG4gICAgICAgIGRlbGl2ZXJlZEF0OiBvcmRlci5kZWxpdmVyZWRfYXQgPyBuZXcgRGF0ZShvcmRlci5kZWxpdmVyZWRfYXQpIDogdW5kZWZpbmVkLFxuICAgICAgICBzdGF0dXNIaXN0b3J5OiBbe1xuICAgICAgICAgIHN0YXR1czogb3JkZXIuc3RhdHVzLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUob3JkZXIudXBkYXRlZF9hdCksXG4gICAgICAgICAgdXBkYXRlZEJ5OiAnc3lzdGVtJyxcbiAgICAgICAgICBub3Rlczogb3JkZXIubm90ZXMgfHwgJydcbiAgICAgICAgfV1cbiAgICAgIH0pKSBhcyBPcmRlcltdO1xuXG4gICAgICAvLyBTaW1wbGUgcGFnaW5hdGlvbiBzaW11bGF0aW9uXG4gICAgICBjb25zdCBzdGFydEluZGV4ID0gbGFzdERvYyA/IHBhcnNlSW50KGxhc3REb2MpIDogMDtcbiAgICAgIGNvbnN0IGVuZEluZGV4ID0gc3RhcnRJbmRleCArIHBhZ2VTaXplO1xuICAgICAgY29uc3QgcGFnaW5hdGVkT3JkZXJzID0gb3JkZXJzLnNsaWNlKHN0YXJ0SW5kZXgsIGVuZEluZGV4KTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgb3JkZXJzOiBwYWdpbmF0ZWRPcmRlcnMsXG4gICAgICAgIGxhc3REb2M6IGVuZEluZGV4LnRvU3RyaW5nKCksXG4gICAgICAgIGhhc01vcmU6IGVuZEluZGV4IDwgb3JkZXJzLmxlbmd0aFxuICAgICAgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKCdTdXBhYmFzZSBub3QgYXZhaWxhYmxlLCB1c2luZyBtb2NrIGRhdGE6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgb3JkZXJzOiBtb2NrT3JkZXJzLFxuICAgICAgICBsYXN0RG9jOiBudWxsLFxuICAgICAgICBoYXNNb3JlOiBmYWxzZVxuICAgICAgfTtcbiAgICB9XG4gIH0sXG5cbiAgLy8gR2V0IG9yZGVyIGJ5IElEXG4gIGFzeW5jIGdldEJ5SWQoaWQ6IHN0cmluZykge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnb3JkZXJzJylcbiAgICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAgIC5lcSgnaWQnLCBpZClcbiAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICBpZiAoZXJyb3IgfHwgIWRhdGEpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdPcmRlciBub3QgZm91bmQnKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgaWQ6IGRhdGEuaWQsXG4gICAgICAgIHRyYWNraW5nTnVtYmVyOiBkYXRhLnRyYWNraW5nX251bWJlcixcbiAgICAgICAgY3VzdG9tZXJOYW1lOiBkYXRhLmN1c3RvbWVyX25hbWUsXG4gICAgICAgIGN1c3RvbWVyUGhvbmU6IGRhdGEuY3VzdG9tZXJfcGhvbmUsXG4gICAgICAgIGFkZHJlc3M6IGRhdGEuYWRkcmVzcyxcbiAgICAgICAgYW1vdW50OiBkYXRhLmFtb3VudCxcbiAgICAgICAgc3RhdHVzOiBkYXRhLnN0YXR1cyxcbiAgICAgICAgY291cmllck5hbWU6IGRhdGEuY291cmllcl9uYW1lLFxuICAgICAgICBjb3VyaWVySWQ6IGRhdGEuY291cmllcl9pZCxcbiAgICAgICAgbm90ZXM6IGRhdGEubm90ZXMsXG4gICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoZGF0YS5jcmVhdGVkX2F0KSxcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZShkYXRhLnVwZGF0ZWRfYXQpLFxuICAgICAgICBkZWxpdmVyZWRBdDogZGF0YS5kZWxpdmVyZWRfYXQgPyBuZXcgRGF0ZShkYXRhLmRlbGl2ZXJlZF9hdCkgOiB1bmRlZmluZWQsXG4gICAgICAgIHN0YXR1c0hpc3Rvcnk6IFt7XG4gICAgICAgICAgc3RhdHVzOiBkYXRhLnN0YXR1cyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKGRhdGEudXBkYXRlZF9hdCksXG4gICAgICAgICAgdXBkYXRlZEJ5OiAnc3lzdGVtJyxcbiAgICAgICAgICBub3RlczogZGF0YS5ub3RlcyB8fCAnJ1xuICAgICAgICB9XVxuICAgICAgfSBhcyBPcmRlcjtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyBvcmRlciBieSBJRDonLCBlcnJvcik7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ09yZGVyIG5vdCBmb3VuZCcpO1xuICAgIH1cbiAgfSxcblxuICAvLyBTZWFyY2ggb3JkZXJzXG4gIGFzeW5jIHNlYXJjaChzZWFyY2hUZXJtOiBzdHJpbmcpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgc3VwYWJhc2VPcmRlcnMgPSBhd2FpdCBvcmRlclNlcnZpY2Uuc2VhcmNoT3JkZXJzKHNlYXJjaFRlcm0pO1xuXG4gICAgICByZXR1cm4gc3VwYWJhc2VPcmRlcnMubWFwKG9yZGVyID0+ICh7XG4gICAgICAgIGlkOiBvcmRlci5pZCxcbiAgICAgICAgdHJhY2tpbmdOdW1iZXI6IG9yZGVyLnRyYWNraW5nX251bWJlcixcbiAgICAgICAgY3VzdG9tZXJOYW1lOiBvcmRlci5jdXN0b21lcl9uYW1lLFxuICAgICAgICBjdXN0b21lclBob25lOiBvcmRlci5jdXN0b21lcl9waG9uZSxcbiAgICAgICAgYWRkcmVzczogb3JkZXIuYWRkcmVzcyxcbiAgICAgICAgYW1vdW50OiBvcmRlci5hbW91bnQsXG4gICAgICAgIHN0YXR1czogb3JkZXIuc3RhdHVzLFxuICAgICAgICBjb3VyaWVyTmFtZTogb3JkZXIuY291cmllcl9uYW1lLFxuICAgICAgICBjb3VyaWVySWQ6IG9yZGVyLmNvdXJpZXJfaWQsXG4gICAgICAgIG5vdGVzOiBvcmRlci5ub3RlcyxcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZShvcmRlci5jcmVhdGVkX2F0KSxcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZShvcmRlci51cGRhdGVkX2F0KSxcbiAgICAgICAgZGVsaXZlcmVkQXQ6IG9yZGVyLmRlbGl2ZXJlZF9hdCA/IG5ldyBEYXRlKG9yZGVyLmRlbGl2ZXJlZF9hdCkgOiB1bmRlZmluZWQsXG4gICAgICAgIHN0YXR1c0hpc3Rvcnk6IFt7XG4gICAgICAgICAgc3RhdHVzOiBvcmRlci5zdGF0dXMsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZShvcmRlci51cGRhdGVkX2F0KSxcbiAgICAgICAgICB1cGRhdGVkQnk6ICdzeXN0ZW0nLFxuICAgICAgICAgIG5vdGVzOiBvcmRlci5ub3RlcyB8fCAnJ1xuICAgICAgICB9XVxuICAgICAgfSkpIGFzIE9yZGVyW107XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybignU2VhcmNoIGZhaWxlZCwgdXNpbmcgbW9jayBkYXRhOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBtb2NrT3JkZXJzLmZpbHRlcihvcmRlciA9PlxuICAgICAgICBvcmRlci50cmFja2luZ051bWJlci5pbmNsdWRlcyhzZWFyY2hUZXJtKSB8fFxuICAgICAgICBvcmRlci5jdXN0b21lck5hbWUuaW5jbHVkZXMoc2VhcmNoVGVybSkgfHxcbiAgICAgICAgb3JkZXIuY3VzdG9tZXJQaG9uZS5pbmNsdWRlcyhzZWFyY2hUZXJtKVxuICAgICAgKTtcbiAgICB9XG4gICAgY29uc3Qgb3JkZXJzID0gbmV3IE1hcCgpO1xuXG4gICAgcmVzdWx0cy5mb3JFYWNoKHNuYXBzaG90ID0+IHtcbiAgICAgIHNuYXBzaG90LmRvY3MuZm9yRWFjaChkb2MgPT4ge1xuICAgICAgICBpZiAoIW9yZGVycy5oYXMoZG9jLmlkKSkge1xuICAgICAgICAgIGNvbnN0IGRhdGEgPSBkb2MuZGF0YSgpO1xuICAgICAgICAgIG9yZGVycy5zZXQoZG9jLmlkLCB7XG4gICAgICAgICAgICBpZDogZG9jLmlkLFxuICAgICAgICAgICAgLi4uZGF0YSxcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogZGF0YS5jcmVhdGVkQXQ/LnRvRGF0ZSgpLFxuICAgICAgICAgICAgdXBkYXRlZEF0OiBkYXRhLnVwZGF0ZWRBdD8udG9EYXRlKCksXG4gICAgICAgICAgICBkZWxpdmVyZWRBdDogZGF0YS5kZWxpdmVyZWRBdD8udG9EYXRlKCksXG4gICAgICAgICAgICByZXR1cm5lZEF0OiBkYXRhLnJldHVybmVkQXQ/LnRvRGF0ZSgpLFxuICAgICAgICAgICAgc3RhdHVzSGlzdG9yeTogZGF0YS5zdGF0dXNIaXN0b3J5Py5tYXAoKHVwZGF0ZTogYW55KSA9PiAoe1xuICAgICAgICAgICAgICAuLi51cGRhdGUsXG4gICAgICAgICAgICAgIHRpbWVzdGFtcDogdXBkYXRlLnRpbWVzdGFtcD8udG9EYXRlKClcbiAgICAgICAgICAgIH0pKVxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9KTtcblxuICAgIHJldHVybiBBcnJheS5mcm9tKG9yZGVycy52YWx1ZXMoKSkgYXMgT3JkZXJbXTtcbiAgfSxcblxuICAvLyBVcGRhdGUgb3JkZXIgc3RhdHVzXG4gIGFzeW5jIHVwZGF0ZVN0YXR1cyhvcmRlcklkOiBzdHJpbmcsIHN0YXR1czogc3RyaW5nLCBub3Rlczogc3RyaW5nLCB1cGRhdGVkQnk6IHN0cmluZywgaW1hZ2U/OiBzdHJpbmcpIHtcbiAgICBjb25zdCBvcmRlclJlZiA9IGRvYyhkYiwgQ09MTEVDVElPTlMuT1JERVJTLCBvcmRlcklkKTtcbiAgICBjb25zdCBub3cgPSBUaW1lc3RhbXAubm93KCk7XG4gICAgXG4gICAgY29uc3Qgc3RhdHVzVXBkYXRlOiBTdGF0dXNVcGRhdGUgPSB7XG4gICAgICBzdGF0dXM6IHN0YXR1cyBhcyBhbnksXG4gICAgICB0aW1lc3RhbXA6IG5vdy50b0RhdGUoKSxcbiAgICAgIHVwZGF0ZWRCeSxcbiAgICAgIG5vdGVzLFxuICAgICAgaW1hZ2VcbiAgICB9O1xuXG4gICAgY29uc3QgdXBkYXRlRGF0YTogYW55ID0ge1xuICAgICAgc3RhdHVzLFxuICAgICAgdXBkYXRlZEF0OiBub3csXG4gICAgICBbYHN0YXR1c0hpc3RvcnlgXTogWy4uLihhd2FpdCB0aGlzLmdldEJ5SWQob3JkZXJJZCkpLnN0YXR1c0hpc3RvcnksIHN0YXR1c1VwZGF0ZV1cbiAgICB9O1xuXG4gICAgaWYgKHN0YXR1cyA9PT0gJ2RlbGl2ZXJlZCcpIHtcbiAgICAgIHVwZGF0ZURhdGEuZGVsaXZlcmVkQXQgPSBub3c7XG4gICAgfSBlbHNlIGlmIChzdGF0dXMgPT09ICdyZXR1cm5lZCcpIHtcbiAgICAgIHVwZGF0ZURhdGEucmV0dXJuZWRBdCA9IG5vdztcbiAgICB9XG5cbiAgICBhd2FpdCB1cGRhdGVEb2Mob3JkZXJSZWYsIHVwZGF0ZURhdGEpO1xuICAgIHJldHVybiBzdGF0dXNVcGRhdGU7XG4gIH0sXG5cbiAgLy8gR2V0IG9yZGVycyBieSBzdGF0dXNcbiAgYXN5bmMgZ2V0QnlTdGF0dXMoc3RhdHVzOiBzdHJpbmcpIHtcbiAgICBjb25zdCBxID0gcXVlcnkoXG4gICAgICBjb2xsZWN0aW9uKGRiLCBDT0xMRUNUSU9OUy5PUkRFUlMpLFxuICAgICAgd2hlcmUoJ3N0YXR1cycsICc9PScsIHN0YXR1cyksXG4gICAgICBvcmRlckJ5KCdjcmVhdGVkQXQnLCAnZGVzYycpXG4gICAgKTtcbiAgICBcbiAgICBjb25zdCBzbmFwc2hvdCA9IGF3YWl0IGdldERvY3MocSk7XG4gICAgcmV0dXJuIHNuYXBzaG90LmRvY3MubWFwKGRvYyA9PiAoe1xuICAgICAgaWQ6IGRvYy5pZCxcbiAgICAgIC4uLmRvYy5kYXRhKCksXG4gICAgICBjcmVhdGVkQXQ6IGRvYy5kYXRhKCkuY3JlYXRlZEF0Py50b0RhdGUoKSxcbiAgICAgIHVwZGF0ZWRBdDogZG9jLmRhdGEoKS51cGRhdGVkQXQ/LnRvRGF0ZSgpLFxuICAgICAgZGVsaXZlcmVkQXQ6IGRvYy5kYXRhKCkuZGVsaXZlcmVkQXQ/LnRvRGF0ZSgpLFxuICAgICAgcmV0dXJuZWRBdDogZG9jLmRhdGEoKS5yZXR1cm5lZEF0Py50b0RhdGUoKSxcbiAgICAgIHN0YXR1c0hpc3Rvcnk6IGRvYy5kYXRhKCkuc3RhdHVzSGlzdG9yeT8ubWFwKCh1cGRhdGU6IGFueSkgPT4gKHtcbiAgICAgICAgLi4udXBkYXRlLFxuICAgICAgICB0aW1lc3RhbXA6IHVwZGF0ZS50aW1lc3RhbXA/LnRvRGF0ZSgpXG4gICAgICB9KSlcbiAgICB9KSkgYXMgT3JkZXJbXTtcbiAgfSxcblxuICAvLyBHZXQgb3JkZXJzIGJ5IGNvdXJpZXJcbiAgYXN5bmMgZ2V0QnlDb3VyaWVyKGNvdXJpZXJJZDogc3RyaW5nKSB7XG4gICAgY29uc3QgcSA9IHF1ZXJ5KFxuICAgICAgY29sbGVjdGlvbihkYiwgQ09MTEVDVElPTlMuT1JERVJTKSxcbiAgICAgIHdoZXJlKCdhc3NpZ25lZFRvJywgJz09JywgY291cmllcklkKSxcbiAgICAgIG9yZGVyQnkoJ2NyZWF0ZWRBdCcsICdkZXNjJylcbiAgICApO1xuICAgIFxuICAgIGNvbnN0IHNuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhxKTtcbiAgICByZXR1cm4gc25hcHNob3QuZG9jcy5tYXAoZG9jID0+ICh7XG4gICAgICBpZDogZG9jLmlkLFxuICAgICAgLi4uZG9jLmRhdGEoKSxcbiAgICAgIGNyZWF0ZWRBdDogZG9jLmRhdGEoKS5jcmVhdGVkQXQ/LnRvRGF0ZSgpLFxuICAgICAgdXBkYXRlZEF0OiBkb2MuZGF0YSgpLnVwZGF0ZWRBdD8udG9EYXRlKCksXG4gICAgICBkZWxpdmVyZWRBdDogZG9jLmRhdGEoKS5kZWxpdmVyZWRBdD8udG9EYXRlKCksXG4gICAgICByZXR1cm5lZEF0OiBkb2MuZGF0YSgpLnJldHVybmVkQXQ/LnRvRGF0ZSgpLFxuICAgICAgc3RhdHVzSGlzdG9yeTogZG9jLmRhdGEoKS5zdGF0dXNIaXN0b3J5Py5tYXAoKHVwZGF0ZTogYW55KSA9PiAoe1xuICAgICAgICAuLi51cGRhdGUsXG4gICAgICAgIHRpbWVzdGFtcDogdXBkYXRlLnRpbWVzdGFtcD8udG9EYXRlKClcbiAgICAgIH0pKVxuICAgIH0pKSBhcyBPcmRlcltdO1xuICB9LFxuXG4gIC8vIEFzc2lnbiBvcmRlcnMgdG8gY291cmllclxuICBhc3luYyBhc3NpZ25Ub0NvdXJpZXIob3JkZXJJZHM6IHN0cmluZ1tdLCBjb3VyaWVySWQ6IHN0cmluZywgYXNzaWduZWRCeTogc3RyaW5nKSB7XG4gICAgY29uc3QgYmF0Y2ggPSB3cml0ZUJhdGNoKGRiKTtcbiAgICBjb25zdCBub3cgPSBUaW1lc3RhbXAubm93KCk7XG5cbiAgICBmb3IgKGNvbnN0IG9yZGVySWQgb2Ygb3JkZXJJZHMpIHtcbiAgICAgIGNvbnN0IG9yZGVyUmVmID0gZG9jKGRiLCBDT0xMRUNUSU9OUy5PUkRFUlMsIG9yZGVySWQpO1xuICAgICAgY29uc3Qgb3JkZXIgPSBhd2FpdCB0aGlzLmdldEJ5SWQob3JkZXJJZCk7XG4gICAgICBcbiAgICAgIGNvbnN0IHN0YXR1c1VwZGF0ZTogU3RhdHVzVXBkYXRlID0ge1xuICAgICAgICBzdGF0dXM6ICdhc3NpZ25lZCcsXG4gICAgICAgIHRpbWVzdGFtcDogbm93LnRvRGF0ZSgpLFxuICAgICAgICB1cGRhdGVkQnk6IGFzc2lnbmVkQnksXG4gICAgICAgIG5vdGVzOiBg2KrZhSDYpdiz2YbYp9ivINin2YTYt9mE2Kgg2YTZhNmF2YbYr9mI2KhgXG4gICAgICB9O1xuXG4gICAgICBiYXRjaC51cGRhdGUob3JkZXJSZWYsIHtcbiAgICAgICAgYXNzaWduZWRUbzogY291cmllcklkLFxuICAgICAgICBzdGF0dXM6ICdhc3NpZ25lZCcsXG4gICAgICAgIHVwZGF0ZWRBdDogbm93LFxuICAgICAgICBzdGF0dXNIaXN0b3J5OiBbLi4ub3JkZXIuc3RhdHVzSGlzdG9yeSwgc3RhdHVzVXBkYXRlXVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgYXdhaXQgYmF0Y2guY29tbWl0KCk7XG4gIH0sXG5cbiAgLy8gRGVsZXRlIG9yZGVyXG4gIGFzeW5jIGRlbGV0ZShpZDogc3RyaW5nKSB7XG4gICAgYXdhaXQgZGVsZXRlRG9jKGRvYyhkYiwgQ09MTEVDVElPTlMuT1JERVJTLCBpZCkpO1xuICB9XG59O1xuXG4vLyBVc2VycyBTZXJ2aWNlIC0gVXBkYXRlZCB0byB1c2UgU3VwYWJhc2VcbmV4cG9ydCBjb25zdCB1c2Vyc1NlcnZpY2UgPSB7XG4gIC8vIENyZWF0ZSB1c2VyXG4gIGFzeW5jIGNyZWF0ZSh1c2VyRGF0YTogT21pdDxVc2VyLCAnaWQnIHwgJ2NyZWF0ZWRBdCcgfCAndXBkYXRlZEF0Jz4pIHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgc3VwYWJhc2VVc2VyRGF0YSA9IHtcbiAgICAgICAgdXNlcm5hbWU6IHVzZXJEYXRhLnVzZXJuYW1lLFxuICAgICAgICBuYW1lOiB1c2VyRGF0YS5uYW1lLFxuICAgICAgICBwaG9uZTogdXNlckRhdGEucGhvbmUsXG4gICAgICAgIHJvbGU6IHVzZXJEYXRhLnJvbGUsXG4gICAgICAgIHBhc3N3b3JkX2hhc2g6ICckMmIkMTAkZXhhbXBsZV9oYXNoX2Zvcl8xMjM0NTYnLCAvLyBJbiBwcm9kdWN0aW9uLCBoYXNoIHRoZSBwYXNzd29yZCBwcm9wZXJseVxuICAgICAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgICAgIGNyZWF0ZWRfYnk6IHVzZXJEYXRhLmNyZWF0ZWRCeSB8fCAnc3lzdGVtJ1xuICAgICAgfTtcblxuICAgICAgY29uc3QgdXNlciA9IGF3YWl0IHVzZXJTZXJ2aWNlLmNyZWF0ZVVzZXIoc3VwYWJhc2VVc2VyRGF0YSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGlkOiB1c2VyLmlkLFxuICAgICAgICB1c2VybmFtZTogdXNlci51c2VybmFtZSxcbiAgICAgICAgbmFtZTogdXNlci5uYW1lLFxuICAgICAgICBwaG9uZTogdXNlci5waG9uZSxcbiAgICAgICAgcm9sZTogdXNlci5yb2xlLFxuICAgICAgICBpc0FjdGl2ZTogdXNlci5pc19hY3RpdmUsXG4gICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUodXNlci5jcmVhdGVkX2F0KSxcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSh1c2VyLmNyZWF0ZWRfYXQpLFxuICAgICAgICBjcmVhdGVkQnk6IHVzZXIuY3JlYXRlZF9ieVxuICAgICAgfSBhcyBVc2VyO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyB1c2VyOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfSxcblxuICAvLyBHZXQgYWxsIHVzZXJzXG4gIGFzeW5jIGdldEFsbCgpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgc3VwYWJhc2VVc2VycyA9IGF3YWl0IHVzZXJTZXJ2aWNlLmdldEFsbFVzZXJzKCk7XG5cbiAgICAgIHJldHVybiBzdXBhYmFzZVVzZXJzLm1hcCh1c2VyID0+ICh7XG4gICAgICAgIGlkOiB1c2VyLmlkLFxuICAgICAgICB1c2VybmFtZTogdXNlci51c2VybmFtZSxcbiAgICAgICAgbmFtZTogdXNlci5uYW1lLFxuICAgICAgICBwaG9uZTogdXNlci5waG9uZSxcbiAgICAgICAgcm9sZTogdXNlci5yb2xlLFxuICAgICAgICBpc0FjdGl2ZTogdXNlci5pc19hY3RpdmUsXG4gICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUodXNlci5jcmVhdGVkX2F0KSxcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSh1c2VyLmNyZWF0ZWRfYXQpLFxuICAgICAgICBjcmVhdGVkQnk6IHVzZXIuY3JlYXRlZF9ieVxuICAgICAgfSkpIGFzIFVzZXJbXTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKCdFcnJvciBnZXR0aW5nIHVzZXJzLCB1c2luZyBtb2NrIGRhdGE6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIG1vY2tVc2VycztcbiAgICB9XG4gIH0sXG5cbiAgLy8gR2V0IGNvdXJpZXJzIG9ubHlcbiAgYXN5bmMgZ2V0Q291cmllcnMoKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNvdXJpZXJzID0gYXdhaXQgdXNlclNlcnZpY2UuZ2V0VXNlcnNCeVJvbGUoJ2NvdXJpZXInKTtcblxuICAgICAgcmV0dXJuIGNvdXJpZXJzLmZpbHRlcih1c2VyID0+IHVzZXIuaXNfYWN0aXZlKS5tYXAodXNlciA9PiAoe1xuICAgICAgICBpZDogdXNlci5pZCxcbiAgICAgICAgdXNlcm5hbWU6IHVzZXIudXNlcm5hbWUsXG4gICAgICAgIG5hbWU6IHVzZXIubmFtZSxcbiAgICAgICAgcGhvbmU6IHVzZXIucGhvbmUsXG4gICAgICAgIHJvbGU6IHVzZXIucm9sZSxcbiAgICAgICAgaXNBY3RpdmU6IHVzZXIuaXNfYWN0aXZlLFxuICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKHVzZXIuY3JlYXRlZF9hdCksXG4gICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUodXNlci5jcmVhdGVkX2F0KSxcbiAgICAgICAgY3JlYXRlZEJ5OiB1c2VyLmNyZWF0ZWRfYnlcbiAgICAgIH0pKSBhcyBVc2VyW107XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybignRXJyb3IgZ2V0dGluZyBjb3VyaWVycywgdXNpbmcgbW9jayBkYXRhOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBtb2NrVXNlcnMuZmlsdGVyKHVzZXIgPT4gdXNlci5yb2xlID09PSAnY291cmllcicgJiYgdXNlci5pc0FjdGl2ZSk7XG4gICAgfVxuICB9LFxuXG4gIC8vIEdldCB1c2VyIGJ5IElEXG4gIGFzeW5jIGdldEJ5SWQoaWQ6IHN0cmluZykge1xuICAgIGNvbnN0IGRvY1JlZiA9IGRvYyhkYiwgQ09MTEVDVElPTlMuVVNFUlMsIGlkKTtcbiAgICBjb25zdCBkb2NTbmFwID0gYXdhaXQgZ2V0RG9jKGRvY1JlZik7XG4gICAgXG4gICAgaWYgKCFkb2NTbmFwLmV4aXN0cygpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGZvdW5kJyk7XG4gICAgfVxuXG4gICAgY29uc3QgZGF0YSA9IGRvY1NuYXAuZGF0YSgpO1xuICAgIHJldHVybiB7XG4gICAgICBpZDogZG9jU25hcC5pZCxcbiAgICAgIC4uLmRhdGEsXG4gICAgICBjcmVhdGVkQXQ6IGRhdGEuY3JlYXRlZEF0Py50b0RhdGUoKSxcbiAgICAgIHVwZGF0ZWRBdDogZGF0YS51cGRhdGVkQXQ/LnRvRGF0ZSgpXG4gICAgfSBhcyBVc2VyO1xuICB9LFxuXG4gIC8vIFVwZGF0ZSB1c2VyXG4gIGFzeW5jIHVwZGF0ZShpZDogc3RyaW5nLCB1c2VyRGF0YTogUGFydGlhbDxVc2VyPikge1xuICAgIGNvbnN0IHVzZXJSZWYgPSBkb2MoZGIsIENPTExFQ1RJT05TLlVTRVJTLCBpZCk7XG4gICAgYXdhaXQgdXBkYXRlRG9jKHVzZXJSZWYsIHtcbiAgICAgIC4uLnVzZXJEYXRhLFxuICAgICAgdXBkYXRlZEF0OiBUaW1lc3RhbXAubm93KClcbiAgICB9KTtcbiAgfSxcblxuICAvLyBEZWxldGUgdXNlclxuICBhc3luYyBkZWxldGUoaWQ6IHN0cmluZykge1xuICAgIGF3YWl0IGRlbGV0ZURvYyhkb2MoZGIsIENPTExFQ1RJT05TLlVTRVJTLCBpZCkpO1xuICB9XG59O1xuXG4vLyBTZXR0bGVtZW50cyBTZXJ2aWNlXG5leHBvcnQgY29uc3Qgc2V0dGxlbWVudHNTZXJ2aWNlID0ge1xuICAvLyBDcmVhdGUgc2V0dGxlbWVudFxuICBhc3luYyBjcmVhdGUoc2V0dGxlbWVudERhdGE6IE9taXQ8U2V0dGxlbWVudCwgJ2lkJyB8ICdjcmVhdGVkQXQnPikge1xuICAgIGNvbnN0IG5vdyA9IFRpbWVzdGFtcC5ub3coKTtcbiAgICBjb25zdCBzZXR0bGVtZW50OiBPbWl0PFNldHRsZW1lbnQsICdpZCc+ID0ge1xuICAgICAgLi4uc2V0dGxlbWVudERhdGEsXG4gICAgICBjcmVhdGVkQXQ6IG5vdy50b0RhdGUoKSxcbiAgICAgIGlzU2V0dGxlZDogZmFsc2VcbiAgICB9O1xuXG4gICAgY29uc3QgZG9jUmVmID0gYXdhaXQgYWRkRG9jKGNvbGxlY3Rpb24oZGIsIENPTExFQ1RJT05TLlNFVFRMRU1FTlRTKSwgc2V0dGxlbWVudCk7XG4gICAgcmV0dXJuIHsgaWQ6IGRvY1JlZi5pZCwgLi4uc2V0dGxlbWVudCB9O1xuICB9LFxuXG4gIC8vIEdldCBhbGwgc2V0dGxlbWVudHNcbiAgYXN5bmMgZ2V0QWxsKCkge1xuICAgIGNvbnN0IHEgPSBxdWVyeShjb2xsZWN0aW9uKGRiLCBDT0xMRUNUSU9OUy5TRVRUTEVNRU5UUyksIG9yZGVyQnkoJ2NyZWF0ZWRBdCcsICdkZXNjJykpO1xuICAgIGNvbnN0IHNuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhxKTtcbiAgICBcbiAgICByZXR1cm4gc25hcHNob3QuZG9jcy5tYXAoZG9jID0+ICh7XG4gICAgICBpZDogZG9jLmlkLFxuICAgICAgLi4uZG9jLmRhdGEoKSxcbiAgICAgIGNyZWF0ZWRBdDogZG9jLmRhdGEoKS5jcmVhdGVkQXQ/LnRvRGF0ZSgpLFxuICAgICAgc2V0dGxlZEF0OiBkb2MuZGF0YSgpLnNldHRsZWRBdD8udG9EYXRlKClcbiAgICB9KSkgYXMgU2V0dGxlbWVudFtdO1xuICB9LFxuXG4gIC8vIE1hcmsgc2V0dGxlbWVudCBhcyBzZXR0bGVkXG4gIGFzeW5jIG1hcmtBc1NldHRsZWQoaWQ6IHN0cmluZykge1xuICAgIGNvbnN0IHNldHRsZW1lbnRSZWYgPSBkb2MoZGIsIENPTExFQ1RJT05TLlNFVFRMRU1FTlRTLCBpZCk7XG4gICAgYXdhaXQgdXBkYXRlRG9jKHNldHRsZW1lbnRSZWYsIHtcbiAgICAgIGlzU2V0dGxlZDogdHJ1ZSxcbiAgICAgIHNldHRsZWRBdDogVGltZXN0YW1wLm5vdygpXG4gICAgfSk7XG4gIH0sXG5cbiAgLy8gUmVhbC10aW1lIHN1YnNjcmlwdGlvbiB0byBvcmRlcnNcbiAgc3Vic2NyaWJlVG9PcmRlcnMoY2FsbGJhY2s6IChvcmRlcnM6IE9yZGVyW10pID0+IHZvaWQpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcSA9IHF1ZXJ5KGNvbGxlY3Rpb24oZGIsIENPTExFQ1RJT05TLk9SREVSUyksIG9yZGVyQnkoJ2NyZWF0ZWRBdCcsICdkZXNjJykpO1xuICAgICAgcmV0dXJuIG9uU25hcHNob3QocSwgKHNuYXBzaG90KSA9PiB7XG4gICAgICAgIGNvbnN0IG9yZGVycyA9IHNuYXBzaG90LmRvY3MubWFwKGRvYyA9PiAoe1xuICAgICAgICAgIGlkOiBkb2MuaWQsXG4gICAgICAgICAgLi4uZG9jLmRhdGEoKVxuICAgICAgICB9IGFzIE9yZGVyKSk7XG4gICAgICAgIGNhbGxiYWNrKG9yZGVycyk7XG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKCdGaXJlYmFzZSBzdWJzY3JpcHRpb24gbm90IGF2YWlsYWJsZTonLCBlcnJvcik7XG4gICAgICAvLyBSZXR1cm4gYSBkdW1teSB1bnN1YnNjcmliZSBmdW5jdGlvblxuICAgICAgcmV0dXJuICgpID0+IHt9O1xuICAgIH1cbiAgfSxcblxuICAvLyBSZWFsLXRpbWUgc3Vic2NyaXB0aW9uIHRvIHVzZXIncyBvcmRlcnNcbiAgc3Vic2NyaWJlVG9Vc2VyT3JkZXJzKHVzZXJJZDogc3RyaW5nLCBjYWxsYmFjazogKG9yZGVyczogT3JkZXJbXSkgPT4gdm9pZCkge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBxID0gcXVlcnkoXG4gICAgICAgIGNvbGxlY3Rpb24oZGIsIENPTExFQ1RJT05TLk9SREVSUyksXG4gICAgICAgIHdoZXJlKCdjb3VyaWVySWQnLCAnPT0nLCB1c2VySWQpLFxuICAgICAgICBvcmRlckJ5KCdjcmVhdGVkQXQnLCAnZGVzYycpXG4gICAgICApO1xuICAgICAgcmV0dXJuIG9uU25hcHNob3QocSwgKHNuYXBzaG90KSA9PiB7XG4gICAgICAgIGNvbnN0IG9yZGVycyA9IHNuYXBzaG90LmRvY3MubWFwKGRvYyA9PiAoe1xuICAgICAgICAgIGlkOiBkb2MuaWQsXG4gICAgICAgICAgLi4uZG9jLmRhdGEoKVxuICAgICAgICB9IGFzIE9yZGVyKSk7XG4gICAgICAgIGNhbGxiYWNrKG9yZGVycyk7XG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKCdGaXJlYmFzZSBzdWJzY3JpcHRpb24gbm90IGF2YWlsYWJsZTonLCBlcnJvcik7XG4gICAgICByZXR1cm4gKCkgPT4ge307XG4gICAgfVxuICB9XG59O1xuIl0sIm5hbWVzIjpbInN1cGFiYXNlIiwidXNlclNlcnZpY2UiLCJvcmRlclNlcnZpY2UiLCJnZW5lcmF0ZVRyYWNraW5nTnVtYmVyIiwibW9ja09yZGVycyIsIm1vY2tVc2VycyIsIm9yZGVyc1NlcnZpY2UiLCJjcmVhdGUiLCJvcmRlckRhdGEiLCJ0cmFja2luZ051bWJlciIsInN1cGFiYXNlT3JkZXJEYXRhIiwidHJhY2tpbmdfbnVtYmVyIiwiY3VzdG9tZXJfbmFtZSIsImN1c3RvbWVyTmFtZSIsImN1c3RvbWVyX3Bob25lIiwiY3VzdG9tZXJQaG9uZSIsImFkZHJlc3MiLCJhbW91bnQiLCJzdGF0dXMiLCJjb3VyaWVyX25hbWUiLCJjb3VyaWVyTmFtZSIsImNvdXJpZXJfaWQiLCJjb3VyaWVySWQiLCJub3RlcyIsIm9yZGVyIiwiY3JlYXRlT3JkZXIiLCJpZCIsImNyZWF0ZWRBdCIsIkRhdGUiLCJjcmVhdGVkX2F0IiwidXBkYXRlZEF0IiwidXBkYXRlZF9hdCIsInN0YXR1c0hpc3RvcnkiLCJ0aW1lc3RhbXAiLCJ1cGRhdGVkQnkiLCJnZXRBbGwiLCJwYWdlU2l6ZSIsImxhc3REb2MiLCJzdXBhYmFzZU9yZGVycyIsImdldEFsbE9yZGVycyIsIm9yZGVycyIsIm1hcCIsImRlbGl2ZXJlZEF0IiwiZGVsaXZlcmVkX2F0IiwidW5kZWZpbmVkIiwic3RhcnRJbmRleCIsInBhcnNlSW50IiwiZW5kSW5kZXgiLCJwYWdpbmF0ZWRPcmRlcnMiLCJzbGljZSIsInRvU3RyaW5nIiwiaGFzTW9yZSIsImxlbmd0aCIsImVycm9yIiwiY29uc29sZSIsIndhcm4iLCJnZXRCeUlkIiwiZGF0YSIsImZyb20iLCJzZWxlY3QiLCJlcSIsInNpbmdsZSIsIkVycm9yIiwic2VhcmNoIiwic2VhcmNoVGVybSIsInNlYXJjaE9yZGVycyIsImZpbHRlciIsImluY2x1ZGVzIiwiTWFwIiwicmVzdWx0cyIsImZvckVhY2giLCJzbmFwc2hvdCIsImRvY3MiLCJkb2MiLCJoYXMiLCJzZXQiLCJ0b0RhdGUiLCJyZXR1cm5lZEF0IiwidXBkYXRlIiwiQXJyYXkiLCJ2YWx1ZXMiLCJ1cGRhdGVTdGF0dXMiLCJvcmRlcklkIiwiaW1hZ2UiLCJvcmRlclJlZiIsImRiIiwiQ09MTEVDVElPTlMiLCJPUkRFUlMiLCJub3ciLCJUaW1lc3RhbXAiLCJzdGF0dXNVcGRhdGUiLCJ1cGRhdGVEYXRhIiwidXBkYXRlRG9jIiwiZ2V0QnlTdGF0dXMiLCJxIiwicXVlcnkiLCJjb2xsZWN0aW9uIiwid2hlcmUiLCJvcmRlckJ5IiwiZ2V0RG9jcyIsImdldEJ5Q291cmllciIsImFzc2lnblRvQ291cmllciIsIm9yZGVySWRzIiwiYXNzaWduZWRCeSIsImJhdGNoIiwid3JpdGVCYXRjaCIsImFzc2lnbmVkVG8iLCJjb21taXQiLCJkZWxldGUiLCJkZWxldGVEb2MiLCJ1c2Vyc1NlcnZpY2UiLCJ1c2VyRGF0YSIsInN1cGFiYXNlVXNlckRhdGEiLCJ1c2VybmFtZSIsIm5hbWUiLCJwaG9uZSIsInJvbGUiLCJwYXNzd29yZF9oYXNoIiwiaXNfYWN0aXZlIiwiY3JlYXRlZF9ieSIsImNyZWF0ZWRCeSIsInVzZXIiLCJjcmVhdGVVc2VyIiwiaXNBY3RpdmUiLCJzdXBhYmFzZVVzZXJzIiwiZ2V0QWxsVXNlcnMiLCJnZXRDb3VyaWVycyIsImNvdXJpZXJzIiwiZ2V0VXNlcnNCeVJvbGUiLCJkb2NSZWYiLCJVU0VSUyIsImRvY1NuYXAiLCJnZXREb2MiLCJleGlzdHMiLCJ1c2VyUmVmIiwic2V0dGxlbWVudHNTZXJ2aWNlIiwic2V0dGxlbWVudERhdGEiLCJzZXR0bGVtZW50IiwiaXNTZXR0bGVkIiwiYWRkRG9jIiwiU0VUVExFTUVOVFMiLCJzZXR0bGVkQXQiLCJtYXJrQXNTZXR0bGVkIiwic2V0dGxlbWVudFJlZiIsInN1YnNjcmliZVRvT3JkZXJzIiwiY2FsbGJhY2siLCJvblNuYXBzaG90Iiwic3Vic2NyaWJlVG9Vc2VyT3JkZXJzIiwidXNlcklkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/firestore.ts\n"));

/***/ })

});