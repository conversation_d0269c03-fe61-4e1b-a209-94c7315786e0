"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   STATUS_COLORS: () => (/* binding */ STATUS_COLORS),\n/* harmony export */   STATUS_LABELS: () => (/* binding */ STATUS_LABELS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Application configuration\nconst APP_CONFIG = {\n    name: \"مرسال\",\n    description: \"نظام إدارة عمليات التوصيل السريع\",\n    version: \"1.1.0\",\n    // Colors\n    colors: {\n        primary: \"#41a7ff\",\n        background: \"#f0f3f5\",\n        accent: \"#b19cd9\"\n    },\n    // Business rules\n    business: {\n        commissionPerOrder: 1000,\n        currency: \"د.ع\",\n        defaultOrderStatuses: [\n            \"pending\",\n            \"assigned\",\n            \"picked_up\",\n            \"in_transit\",\n            \"delivered\",\n            \"returned\",\n            \"cancelled\",\n            \"postponed\"\n        ]\n    },\n    // API endpoints (when backend is ready)\n    api: {\n        baseUrl: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\",\n        endpoints: {\n            orders: \"/api/orders\",\n            users: \"/api/users\",\n            couriers: \"/api/couriers\",\n            settlements: \"/api/settlements\"\n        }\n    },\n    // Features flags\n    features: {\n        enableNotifications: true,\n        enableReports: true,\n        enableBulkOperations: true,\n        enableImageUpload: true\n    },\n    // Production mode settings - إعدادات الإنتاج (Supabase فقط)\n    demo: {\n        enabled: false,\n        autoLogin: false,\n        defaultUser: 'manager',\n        showDemoNotice: false,\n        cloudOnly: true // استخدام قاعدة البيانات السحابية فقط (Supabase)\n    },\n    // Company info\n    company: {\n        name: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        phone: '+*********** 4567',\n        address: 'بغداد، العراق'\n    },\n    // Receipt settings - إعدادات الوصل الجديد المحدث\n    receipt: {\n        // الأبعاد المطلوبة (110×130 ملم)\n        dimensions: {\n            width: '110mm',\n            height: '130mm'\n        },\n        // معلومات الشركة الجديدة\n        company: {\n            name: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n            subtitle: 'خدمة توصيل سريعة وموثوقة',\n            phone: '+*********** 4567',\n            address: 'بغداد، العراق'\n        },\n        // تصميم الوصل المحسن\n        layout: {\n            header: {\n                fontSize: '14px',\n                fontWeight: 'bold',\n                textAlign: 'center',\n                marginBottom: '8px',\n                borderBottom: '2px solid #000',\n                padding: '5px'\n            },\n            trackingSection: {\n                fontSize: '16px',\n                fontWeight: 'bold',\n                backgroundColor: '#f8f9fa',\n                border: '2px solid #000',\n                borderRadius: '3px',\n                padding: '5px',\n                textAlign: 'center'\n            },\n            orderInfo: {\n                backgroundColor: '#fafafa',\n                border: '1px solid #ddd',\n                borderRadius: '3px'\n            },\n            pricing: {\n                fontSize: '20px',\n                fontWeight: 'bold',\n                backgroundColor: '#fff3cd',\n                border: '3px solid #000',\n                borderRadius: '4px',\n                textAlign: 'center'\n            },\n            barcode: {\n                height: '25px',\n                backgroundColor: '#f8f9fa',\n                border: '2px solid #000',\n                borderRadius: '3px',\n                fontSize: '9px'\n            }\n        },\n        // الإعدادات الأساسية\n        companyName: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        showBarcode: true,\n        showDate: true,\n        showTime: true,\n        priceFormat: 'en-US',\n        currency: 'IQD',\n        // الحقول المطلوبة\n        fields: {\n            trackingNumber: true,\n            customerPhone: true,\n            status: true,\n            courierName: true,\n            amount: true,\n            barcode: true,\n            date: true,\n            time: true // الوقت\n        }\n    }\n};\n// Status labels in Arabic\nconst STATUS_LABELS = {\n    pending: \"في الانتظار\",\n    assigned: \"مسند\",\n    picked_up: \"تم الاستلام\",\n    in_transit: \"في الطريق\",\n    delivered: \"تم التسليم\",\n    returned: \"راجع\",\n    cancelled: \"ملغي\",\n    postponed: \"مؤجل\"\n};\n// Status colors for UI\nconst STATUS_COLORS = {\n    pending: \"text-yellow-600 bg-yellow-100\",\n    assigned: \"text-blue-600 bg-blue-100\",\n    picked_up: \"text-purple-600 bg-purple-100\",\n    in_transit: \"text-orange-600 bg-orange-100\",\n    delivered: \"text-green-600 bg-green-100\",\n    returned: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    postponed: \"text-gray-600 bg-gray-100\"\n};\n// User roles\nconst USER_ROLES = {\n    admin: \"مدير\",\n    manager: \"مدير فرع\",\n    dispatcher: \"موزع\",\n    courier: \"مندوب\",\n    accountant: \"محاسب\",\n    viewer: \"مشاهد\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/statistics.ts":
/*!*******************************!*\
  !*** ./src/lib/statistics.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   statisticsService: () => (/* binding */ statisticsService)\n/* harmony export */ });\n/* harmony import */ var _mock_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mock-data */ \"(app-pages-browser)/./src/lib/mock-data.ts\");\n// Statistics service - Updated to use Supabase only\n\nconst statisticsService = {\n    async getOverallStats () {\n        try {\n            const ordersRef = collection(db, 'orders');\n            // Get all orders\n            const allOrdersSnapshot = await getDocs(ordersRef);\n            const totalOrders = allOrdersSnapshot.size;\n            // Get delivered orders\n            const deliveredQuery = query(ordersRef, where('status', '==', 'delivered'));\n            const deliveredSnapshot = await getDocs(deliveredQuery);\n            const deliveredOrders = deliveredSnapshot.size;\n            // Get returned orders\n            const returnedQuery = query(ordersRef, where('status', '==', 'returned'));\n            const returnedSnapshot = await getDocs(returnedQuery);\n            const returnedOrders = returnedSnapshot.size;\n            // Get pending orders\n            const pendingQuery = query(ordersRef, where('status', '==', 'pending'));\n            const pendingSnapshot = await getDocs(pendingQuery);\n            const pendingOrders = pendingSnapshot.size;\n            // Calculate total amount from delivered orders\n            let totalAmount = 0;\n            deliveredSnapshot.docs.forEach((doc)=>{\n                const data = doc.data();\n                totalAmount += data.amount || 0;\n            });\n            // Calculate commission (1000 IQD per delivered order)\n            const totalCommission = deliveredOrders * 1000;\n            return {\n                totalOrders,\n                deliveredOrders,\n                returnedOrders,\n                pendingOrders,\n                totalAmount,\n                totalCommission\n            };\n        } catch (error) {\n            console.warn('Firebase not available, using mock data:', error);\n            // Fallback to mock data\n            const totalOrders = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.length;\n            const deliveredOrders = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.filter((o)=>o.status === 'delivered').length;\n            const returnedOrders = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.filter((o)=>o.status === 'returned').length;\n            const pendingOrders = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.filter((o)=>o.status === 'pending').length;\n            const totalAmount = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.filter((o)=>o.status === 'delivered').reduce((sum, o)=>sum + o.amount, 0);\n            const totalCommission = deliveredOrders * 1000;\n            return {\n                totalOrders,\n                deliveredOrders,\n                returnedOrders,\n                pendingOrders,\n                totalAmount,\n                totalCommission\n            };\n        }\n    },\n    async getTodayStats () {\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const todayTimestamp = Timestamp.fromDate(today);\n        const ordersRef = collection(db, 'orders');\n        const todayQuery = query(ordersRef, where('createdAt', '>=', todayTimestamp));\n        const snapshot = await getDocs(todayQuery);\n        return snapshot.size;\n    },\n    async getCourierStats (courierId) {\n        const ordersRef = collection(db, 'orders');\n        const courierQuery = query(ordersRef, where('assignedTo', '==', courierId));\n        const snapshot = await getDocs(courierQuery);\n        let delivered = 0;\n        let returned = 0;\n        let pending = 0;\n        let totalAmount = 0;\n        snapshot.docs.forEach((doc)=>{\n            const data = doc.data();\n            switch(data.status){\n                case 'delivered':\n                    delivered++;\n                    totalAmount += data.amount || 0;\n                    break;\n                case 'returned':\n                    returned++;\n                    break;\n                case 'pending':\n                case 'assigned':\n                case 'picked_up':\n                case 'in_transit':\n                    pending++;\n                    break;\n            }\n        });\n        return {\n            totalOrders: snapshot.size,\n            deliveredOrders: delivered,\n            returnedOrders: returned,\n            pendingOrders: pending,\n            totalAmount,\n            totalCommission: delivered * 1000\n        };\n    },\n    async getMonthlyStats (year, month) {\n        const startDate = new Date(year, month - 1, 1);\n        const endDate = new Date(year, month, 0, 23, 59, 59);\n        const ordersRef = collection(db, 'orders');\n        const monthQuery = query(ordersRef, where('createdAt', '>=', Timestamp.fromDate(startDate)), where('createdAt', '<=', Timestamp.fromDate(endDate)));\n        const snapshot = await getDocs(monthQuery);\n        let delivered = 0;\n        let returned = 0;\n        let totalAmount = 0;\n        snapshot.docs.forEach((doc)=>{\n            const data = doc.data();\n            if (data.status === 'delivered') {\n                delivered++;\n                totalAmount += data.amount || 0;\n            } else if (data.status === 'returned') {\n                returned++;\n            }\n        });\n        return {\n            totalOrders: snapshot.size,\n            deliveredOrders: delivered,\n            returnedOrders: returned,\n            pendingOrders: snapshot.size - delivered - returned,\n            totalAmount,\n            totalCommission: delivered * 1000\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/statistics.ts\n"));

/***/ })

});