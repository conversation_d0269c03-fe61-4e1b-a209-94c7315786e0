(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4662],{22446:(e,s,r)=>{Promise.resolve().then(r.bind(r,72608))},30285:(e,s,r)=>{"use strict";r.d(s,{$:()=>l});var t=r(95155);r(12115);var a=r(99708),n=r(74466),i=r(59434);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:s,variant:r,size:n,asChild:l=!1,...o}=e,d=l?a.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,i.cn)(c({variant:r,size:n,className:s})),...o})}},42099:(e,s,r)=>{"use strict";r.d(s,{Dv:()=>m,LC:()=>l});var t=r(13798),a=r(49509);let n=a.env.NEXT_PUBLIC_SUPABASE_URL||"https://your-project.supabase.co",i=a.env.NEXT_PUBLIC_SUPABASE_ANON_KEY||"your-anon-key",c=(0,t.UU)(n,i),l=async()=>{try{let{data:e,error:s}=await c.from("users").select("count").limit(1);if(s)return console.error("Supabase connection test failed:",s),{success:!1,message:"فشل في الاتصال بقاعدة البيانات السحابية: ".concat(s.message)};return{success:!0,message:"تم الاتصال بقاعدة البيانات السحابية بنجاح ✅"}}catch(s){console.error("Supabase connection test failed:",s);let e="فشل في الاتصال بقاعدة البيانات السحابية";return s instanceof Error&&(s.message.includes("network")?e+=" - تحقق من الاتصال بالإنترنت":s.message.includes("permission")?e+=" - مشكلة في الصلاحيات":e+=": "+s.message),{success:!1,message:e}}};class o{async createUser(e){let{data:s,error:r}=await c.from("users").insert([e]).select().single();if(r)throw Error("Failed to create user: ".concat(r.message));return s}async getUserByUsername(e){let{data:s,error:r}=await c.from("users").select("*").eq("username",e).single();if(r&&"PGRST116"!==r.code)throw Error("Failed to get user: ".concat(r.message));return s||null}async getAllUsers(){let{data:e,error:s}=await c.from("users").select("*").order("created_at",{ascending:!1});if(s)throw Error("Failed to get users: ".concat(s.message));return e||[]}async updateUser(e,s){let{error:r}=await c.from("users").update(s).eq("id",e);if(r)throw Error("Failed to update user: ".concat(r.message))}async deleteUser(e){let{error:s}=await c.from("users").delete().eq("id",e);if(s)throw Error("Failed to delete user: ".concat(s.message))}async getUsersByRole(e){let{data:s,error:r}=await c.from("users").select("*").eq("role",e).order("created_at",{ascending:!1});if(r)throw Error("Failed to get users by role: ".concat(r.message));return s||[]}}class d{async createOrder(e){let{data:s,error:r}=await c.from("orders").insert([{...e,updated_at:new Date().toISOString()}]).select().single();if(r)throw Error("Failed to create order: ".concat(r.message));return s}async getOrderByTrackingNumber(e){let{data:s,error:r}=await c.from("orders").select("*").eq("tracking_number",e).single();if(r&&"PGRST116"!==r.code)throw Error("Failed to get order: ".concat(r.message));return s||null}async getAllOrders(){let{data:e,error:s}=await c.from("orders").select("*").order("created_at",{ascending:!1});if(s)throw Error("Failed to get orders: ".concat(s.message));return e||[]}async getOrdersByStatus(e){let{data:s,error:r}=await c.from("orders").select("*").eq("status",e).order("created_at",{ascending:!1});if(r)throw Error("Failed to get orders by status: ".concat(r.message));return s||[]}async getOrdersByCourier(e){let{data:s,error:r}=await c.from("orders").select("*").eq("courier_id",e).order("created_at",{ascending:!1});if(r)throw Error("Failed to get orders by courier: ".concat(r.message));return s||[]}async updateOrder(e,s){let{error:r}=await c.from("orders").update({...s,updated_at:new Date().toISOString()}).eq("id",e);if(r)throw Error("Failed to update order: ".concat(r.message))}async deleteOrder(e){let{error:s}=await c.from("orders").delete().eq("id",e);if(s)throw Error("Failed to delete order: ".concat(s.message))}async searchOrders(e){let{data:s,error:r}=await c.from("orders").select("*").or("tracking_number.ilike.%".concat(e,"%,customer_name.ilike.%").concat(e,"%,customer_phone.ilike.%").concat(e,"%,address.ilike.%").concat(e,"%")).order("created_at",{ascending:!1});if(r)throw Error("Failed to search orders: ".concat(r.message));return s||[]}}let m=new o;new d},59434:(e,s,r)=>{"use strict";r.d(s,{Yq:()=>c,cn:()=>n,ps:()=>u,qY:()=>m,r6:()=>l,vv:()=>i,y7:()=>o,zC:()=>d});var t=r(52596),a=r(39688);function n(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}function i(e){return"".concat(e.toLocaleString("ar-IQ")," د.ع")}function c(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function l(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function o(){let e=Date.now().toString().slice(-6),s=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"".concat("MRS").concat(e).concat(s)}function d(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function m(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function u(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}},62523:(e,s,r)=>{"use strict";r.d(s,{p:()=>n});var t=r(95155);r(12115);var a=r(59434);function n(e){let{className:s,type:r,...n}=e;return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...n})}},66695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>l,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>i});var t=r(95155);r(12115);var a=r(59434);function n(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...r})}function i(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...r})}function c(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...r})}function l(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...r})}function o(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...r})}},72608:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>C});var t=r(95155),a=r(12115),n=r(66695),i=r(30285),c=r(62523),l=r(57340),o=r(381),d=r(71007),m=r(23861),u=r(33127),x=r(54213),g=r(75021),h=r(75525),p=r(4229),f=r(53904),j=r(76517),b=r(94449),v=r(78749),w=r(92657),y=r(40646),N=r(91788),k=r(42099),A=r(6874),S=r.n(A);function C(){let[e,s]=(0,a.useState)("profile"),[r,A]=(0,a.useState)(!1),[C,_]=(0,a.useState)(!1),[P,B]=(0,a.useState)(!1),[E,O]=(0,a.useState)(null),[U,Z]=(0,a.useState)({name:"أحمد محمد",email:"<EMAIL>",phone:"07901234567",role:"مدير"}),[R,F]=(0,a.useState)({notifications:{email:!0,sms:!0,push:!1},theme:"light",language:"ar",commissionPerOrder:1e3,autoAssignment:!1}),[T,$]=(0,a.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[L,q]=(0,a.useState)("idle"),[D,I]=(0,a.useState)(null),[W,z]=(0,a.useState)(!1),M=async()=>{A(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("تم تحديث الملف الشخصي بنجاح")}catch(e){alert("حدث خطأ أثناء التحديث")}finally{A(!1)}},Q=async()=>{if(T.newPassword!==T.confirmPassword)return void alert("كلمة المرور الجديدة غير متطابقة");if(T.newPassword.length<6)return void alert("كلمة المرور يجب أن تكون 6 أحرف على الأقل");A(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("تم تغيير كلمة المرور بنجاح"),$({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){alert("حدث خطأ أثناء تغيير كلمة المرور")}finally{A(!1)}},X=async()=>{A(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("تم حفظ الإعدادات بنجاح")}catch(e){alert("حدث خطأ أثناء حفظ الإعدادات")}finally{A(!1)}},Y=async()=>{B(!0),O(null);try{let e=await (0,k.LC)();O(e)}catch(e){O({success:!1,message:"حدث خطأ أثناء اختبار الاتصال"})}finally{B(!1)}},G=async()=>{q("testing");try{await new Promise(e=>setTimeout(e,2e3)),I({connection:"متصل",collections:["orders","users","couriers","settings"],totalOrders:1250,totalUsers:45,lastBackup:"2024-01-15 14:30:00",version:"Firebase v9.15.0"}),q("success")}catch(e){q("error")}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 animated-bg p-6",dir:"rtl",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto space-y-8",children:[(0,t.jsx)("div",{className:"flex items-center gap-4 mb-6",children:(0,t.jsx)(S(),{href:"/",children:(0,t.jsxs)(i.$,{variant:"outline",size:"sm",className:"flex items-center gap-2 glass",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),"العودة للرئيسية"]})})}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-gray-500 to-gray-700 rounded-3xl shadow-2xl mb-4",children:(0,t.jsx)(o.A,{className:"h-8 w-8 text-white"})}),(0,t.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent mb-2",children:"الإعدادات"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-lg",children:"تخصيص التطبيق وإدارة الحساب"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-4",children:(0,t.jsxs)("nav",{className:"space-y-2",children:[(0,t.jsxs)("button",{onClick:()=>s("profile"),className:"w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ".concat("profile"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),"الملف الشخصي"]}),(0,t.jsxs)("button",{onClick:()=>s("notifications"),className:"w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ".concat("notifications"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(m.A,{className:"h-4 w-4"}),"الإشعارات"]}),(0,t.jsxs)("button",{onClick:()=>s("appearance"),className:"w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ".concat("appearance"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),"المظهر"]}),(0,t.jsxs)("button",{onClick:()=>s("system"),className:"w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ".concat("system"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),"النظام"]}),(0,t.jsxs)("button",{onClick:()=>s("database"),className:"w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ".concat("database"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),"قاعدة البيانات"]}),(0,t.jsxs)("button",{onClick:()=>s("security"),className:"w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ".concat("security"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),"الأمان"]})]})})})}),(0,t.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:["profile"===e&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-5 w-5"}),"الملف الشخصي"]}),(0,t.jsx)(n.BT,{children:"تحديث معلوماتك الشخصية"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"الاسم الكامل"}),(0,t.jsx)(c.p,{value:U.name,onChange:e=>Z(s=>({...s,name:e.target.value})),placeholder:"أدخل الاسم الكامل"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"رقم الهاتف"}),(0,t.jsx)(c.p,{value:U.phone,onChange:e=>Z(s=>({...s,phone:e.target.value})),placeholder:"07xxxxxxxxx"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"البريد الإلكتروني"}),(0,t.jsx)(c.p,{type:"email",value:U.email,onChange:e=>Z(s=>({...s,email:e.target.value})),placeholder:"<EMAIL>"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"الدور"}),(0,t.jsx)(c.p,{value:U.role,disabled:!0,className:"bg-muted"})]}),(0,t.jsxs)(i.$,{onClick:M,disabled:r,className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),r?"جاري الحفظ...":"حفظ التغييرات"]})]})]}),"notifications"===e&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"h-5 w-5"}),"إعدادات الإشعارات"]}),(0,t.jsx)(n.BT,{children:"تخصيص طريقة استلام الإشعارات"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"إشعارات البريد الإلكتروني"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"استلام الإشعارات عبر البريد الإلكتروني"})]}),(0,t.jsx)("input",{type:"checkbox",checked:R.notifications.email,onChange:e=>F(s=>({...s,notifications:{...s.notifications,email:e.target.checked}})),className:"w-4 h-4"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"إشعارات SMS"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"استلام الإشعارات عبر الرسائل النصية"})]}),(0,t.jsx)("input",{type:"checkbox",checked:R.notifications.sms,onChange:e=>F(s=>({...s,notifications:{...s.notifications,sms:e.target.checked}})),className:"w-4 h-4"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"الإشعارات الفورية"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"إشعارات فورية في المتصفح"})]}),(0,t.jsx)("input",{type:"checkbox",checked:R.notifications.push,onChange:e=>F(s=>({...s,notifications:{...s.notifications,push:e.target.checked}})),className:"w-4 h-4"})]})]}),(0,t.jsxs)(i.$,{onClick:X,disabled:r,className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),r?"جاري الحفظ...":"حفظ الإعدادات"]})]})]}),"appearance"===e&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(u.A,{className:"h-5 w-5"}),"المظهر"]}),(0,t.jsx)(n.BT,{children:"تخصيص مظهر التطبيق"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"المظهر"}),(0,t.jsxs)("select",{value:R.theme,onChange:e=>F(s=>({...s,theme:e.target.value})),className:"w-full p-3 border border-border rounded-md bg-background",children:[(0,t.jsx)("option",{value:"light",children:"فاتح"}),(0,t.jsx)("option",{value:"dark",children:"داكن"}),(0,t.jsx)("option",{value:"auto",children:"تلقائي"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اللغة"}),(0,t.jsxs)("select",{value:R.language,onChange:e=>F(s=>({...s,language:e.target.value})),className:"w-full p-3 border border-border rounded-md bg-background",children:[(0,t.jsx)("option",{value:"ar",children:"العربية"}),(0,t.jsx)("option",{value:"en",children:"English"})]})]}),(0,t.jsxs)(i.$,{onClick:X,disabled:r,className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),r?"جاري الحفظ...":"حفظ الإعدادات"]})]})]}),"system"===e&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-5 w-5"}),"إعدادات النظام"]}),(0,t.jsx)(n.BT,{children:"إعدادات عامة للنظام"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"العمولة لكل طلب (دينار عراقي)"}),(0,t.jsx)(c.p,{type:"number",value:R.commissionPerOrder,onChange:e=>F(s=>({...s,commissionPerOrder:parseInt(e.target.value)||0})),placeholder:"1000"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"الإسناد التلقائي"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"إسناد الطلبات تلقائياً للمندوبين"})]}),(0,t.jsx)("input",{type:"checkbox",checked:R.autoAssignment,onChange:e=>F(s=>({...s,autoAssignment:e.target.checked})),className:"w-4 h-4"})]}),(0,t.jsxs)("div",{className:"border-t pt-6",children:[(0,t.jsx)("h3",{className:"font-medium mb-4",children:"اختبار الاتصال بقاعدة البيانات"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(i.$,{onClick:Y,disabled:P,variant:"outline",className:"flex items-center gap-2",children:[P?(0,t.jsx)(f.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(x.A,{className:"h-4 w-4"}),P?"جاري الاختبار...":"اختبار الاتصال"]}),E&&(0,t.jsxs)("div",{className:"p-4 rounded-lg border ".concat(E.success?"bg-green-50 border-green-200 text-green-800":"bg-red-50 border-red-200 text-red-800"),children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[E.success?(0,t.jsx)(j.A,{className:"h-4 w-4"}):(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:E.success?"نجح الاتصال":"فشل الاتصال"})]}),(0,t.jsx)("p",{className:"mt-1 text-sm",children:E.message})]})]})]}),(0,t.jsxs)(i.$,{onClick:X,disabled:r,className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),r?"جاري الحفظ...":"حفظ الإعدادات"]})]})]}),"security"===e&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5"}),"الأمان"]}),(0,t.jsx)(n.BT,{children:"تغيير كلمة المرور وإعدادات الأمان"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"كلمة المرور الحالية"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.p,{type:C?"text":"password",value:T.currentPassword,onChange:e=>$(s=>({...s,currentPassword:e.target.value})),placeholder:"أدخل كلمة المرور الحالية"}),(0,t.jsx)("button",{type:"button",onClick:()=>_(!C),className:"absolute left-3 top-1/2 transform -translate-y-1/2",children:C?(0,t.jsx)(v.A,{className:"h-4 w-4"}):(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"كلمة المرور الجديدة"}),(0,t.jsx)(c.p,{type:"password",value:T.newPassword,onChange:e=>$(s=>({...s,newPassword:e.target.value})),placeholder:"أدخل كلمة المرور الجديدة"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"تأكيد كلمة المرور الجديدة"}),(0,t.jsx)(c.p,{type:"password",value:T.confirmPassword,onChange:e=>$(s=>({...s,confirmPassword:e.target.value})),placeholder:"أعد إدخال كلمة المرور الجديدة"})]}),(0,t.jsxs)(i.$,{onClick:Q,disabled:r||!T.currentPassword||!T.newPassword||!T.confirmPassword,className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),r?"جاري التغيير...":"تغيير كلمة المرور"]})]})]}),"database"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(g.A,{className:"h-5 w-5"}),"اختبار قاعدة البيانات"]}),(0,t.jsx)(n.BT,{children:"اختبار الاتصال بقاعدة البيانات والتحقق من حالتها"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"حالة الاتصال:"}),(0,t.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("success"===L?"bg-green-100 text-green-800":"error"===L?"bg-red-100 text-red-800":"testing"===L?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:"success"===L?"متصل":"error"===L?"خطأ":"testing"===L?"جاري الاختبار...":"غير مختبر"})]}),(0,t.jsx)(i.$,{onClick:G,disabled:"testing"===L,className:"w-full",children:"testing"===L?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 animate-spin"}),"جاري الاختبار..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.A,{className:"h-4 w-4 ml-2"}),"اختبار قاعدة البيانات"]})}),D&&(0,t.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsx)("span",{className:"font-medium text-green-800",children:"قاعدة البيانات متصلة"})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"الحالة:"}),(0,t.jsx)("span",{className:"font-medium",children:D.connection})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"إجمالي الطلبات:"}),(0,t.jsx)("span",{className:"font-medium",children:D.totalOrders})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"المستخدمين:"}),(0,t.jsx)("span",{className:"font-medium",children:D.totalUsers})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"آخر نسخة احتياطية:"}),(0,t.jsx)("span",{className:"font-medium",children:D.lastBackup})]})]})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-5 w-5"}),"إدارة قاعدة البيانات"]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)(i.$,{onClick:()=>z(!W),variant:"outline",className:"w-full",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 ml-2"}),W?"إخفاء":"عرض"," قاعدة البيانات"]}),(0,t.jsxs)(i.$,{onClick:()=>{let e=new Blob([JSON.stringify({orders:[],users:[],settings:{},exportDate:new Date().toISOString()},null,2)],{type:"application/json"}),s=URL.createObjectURL(e),r=document.createElement("a");r.href=s,r.download="marsal_backup_".concat(new Date().toISOString().split("T")[0],".json"),r.click(),URL.revokeObjectURL(s)},variant:"outline",className:"w-full",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 ml-2"}),"تصدير نسخة احتياطية"]})]})]}),W&&D&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-5 w-5"}),"بيانات قاعدة البيانات"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-auto",children:(0,t.jsx)("pre",{children:JSON.stringify(D,null,2)})})})]})]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8779,622,2432,7979,1899,9744,4495,5138,6321,2652,5030,3719,9473,558,9401,6325,6077,4409,1124,9385,4927,37,2041,4979,8321,2900,3610,9988,2158,7358],()=>s(22446)),_N_E=e.O()}]);