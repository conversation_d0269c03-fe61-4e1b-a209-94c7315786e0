(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[287],{30189:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>_});var t=a(95155),s=a(12115),n=a(35695),l=a(66695),i=a(30285),d=a(62523),c=a(88539),o=a(40646),u=a(54861),x=a(14186),p=a(37108),g=a(1243),m=a(40133),h=a(92138),b=a(29869),v=a(84355),f=a(4229),j=a(6874),N=a.n(j);let y=[{value:"no_answer",label:"لا يرد"},{value:"closed",label:"مغلق"},{value:"out_of_service",label:"غير داخل بالخدمة"},{value:"unreachable",label:"لا يمكن الاتصال به"},{value:"refused",label:"رفض الطلب"},{value:"price_difference",label:"اختلاف سعر"},{value:"not_ordered",label:"لم يطلب"},{value:"traveling",label:"مسافر"},{value:"other",label:"أخرى"}],w=[{value:"tonight",label:"مؤجل ليلاً"},{value:"tomorrow",label:"مؤجل غداً"}],k={trackingNumber:"MRS001",recipientName:"فاطمة علي",recipientPhone:"07801234567",amount:5e4};function _(){let e=(0,n.useParams)(),r=(0,n.useRouter)(),a=e.id,j=(0,s.useRef)(null),[_,S]=(0,s.useState)(""),[Z,B]=(0,s.useState)(""),[R,A]=(0,s.useState)(null),[C,T]=(0,s.useState)(""),[W,D]=(0,s.useState)(!1),[$,P]=(0,s.useState)(""),[q,z]=(0,s.useState)(""),[L,Q]=(0,s.useState)(""),[E,I]=(0,s.useState)(""),M=()=>_?["returned","postponed"].includes(_)&&!R?(alert("يجب إضافة صورة لهذه الحالة"),!1):"returned"!==_||$?"postponed"!==_||q?"partial_delivery"!==_||L&&E?"price_changed"!==_||!!E||(alert("يرجى إدخال السعر الجديد"),!1):(alert("يرجى إدخال عدد القطع الراجعة والسعر الجديد"),!1):(alert("يرجى اختيار نوع التأجيل"),!1):(alert("يرجى اختيار سبب الإرجاع"),!1):(alert("يرجى اختيار حالة الطلب"),!1),F=async e=>{if(e.preventDefault(),M()){D(!0);try{let e=new FormData;e.append("orderId",a),e.append("status",_),e.append("notes",Z),R&&e.append("image",R),$&&e.append("returnReason",$),q&&e.append("postponeType",q),L&&e.append("partialQuantity",L),E&&e.append("newPrice",E),await new Promise(e=>setTimeout(e,1500)),alert("تم تحديث حالة الطلب بنجاح!"),r.push("/orders")}catch(e){console.error("Error updating order status:",e),alert("حدث خطأ أثناء تحديث حالة الطلب")}finally{D(!1)}}},O=e=>{switch(e){case"delivered":return(0,t.jsx)(o.A,{className:"h-5 w-5 text-green-600"});case"returned":return(0,t.jsx)(u.A,{className:"h-5 w-5 text-red-600"});case"postponed":return(0,t.jsx)(x.A,{className:"h-5 w-5 text-orange-600"});case"partial_delivery":return(0,t.jsx)(p.A,{className:"h-5 w-5 text-blue-600"});case"price_changed":return(0,t.jsx)(g.A,{className:"h-5 w-5 text-purple-600"});default:return(0,t.jsx)(m.A,{className:"h-5 w-5 text-gray-600"})}};return(0,t.jsx)("div",{className:"min-h-screen bg-background p-4 md:p-6 lg:p-8",dir:"rtl",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-primary flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-8 w-8"}),"تحديث حالة الطلب ",k.trackingNumber]}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2",children:"تحديث حالة الطلب وإضافة ملاحظات"})]}),(0,t.jsx)(N(),{href:"/orders/".concat(a),children:(0,t.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),"العودة للطلب"]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsx)(l.ZB,{children:"معلومات الطلب"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"رقم الوصل"}),(0,t.jsx)("p",{className:"font-semibold",children:k.trackingNumber})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"المستلم"}),(0,t.jsx)("p",{className:"font-medium",children:k.recipientName}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:k.recipientPhone})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"المبلغ"}),(0,t.jsxs)("p",{className:"font-semibold",children:[k.amount.toLocaleString()," د.ع"]})]})]})})]}),(0,t.jsxs)("form",{onSubmit:F,className:"space-y-6",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"اختيار الحالة الجديدة"}),(0,t.jsx)(l.BT,{children:"اختر الحالة المناسبة للطلب"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[{value:"delivered",label:"تم التسليم",color:"border-green-200 bg-green-50"},{value:"returned",label:"راجع عند المندوب",color:"border-red-200 bg-red-50"},{value:"postponed",label:"مؤجل",color:"border-orange-200 bg-orange-50"},{value:"partial_delivery",label:"تسليم جزئي",color:"border-blue-200 bg-blue-50"},{value:"price_changed",label:"تغيير سعر",color:"border-purple-200 bg-purple-50"}].map(e=>(0,t.jsx)("div",{className:"p-4 border-2 rounded-lg cursor-pointer transition-all ".concat(_===e.value?"".concat(e.color," border-opacity-100"):"border-border hover:border-primary/50"),onClick:()=>S(e.value),children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[O(e.value),(0,t.jsx)("span",{className:"font-medium",children:e.label})]})},e.value))})})]}),"returned"===_&&(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"سبب الإرجاع"}),(0,t.jsx)(l.BT,{children:"اختر سبب إرجاع الطلب"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("select",{value:$,onChange:e=>P(e.target.value),className:"w-full p-3 border border-border rounded-md bg-background",required:!0,children:[(0,t.jsx)("option",{value:"",children:"اختر سبب الإرجاع"}),y.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))]})})]}),"postponed"===_&&(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"نوع التأجيل"}),(0,t.jsx)(l.BT,{children:"اختر نوع التأجيل"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:w.map(e=>(0,t.jsx)("div",{className:"p-4 border-2 rounded-lg cursor-pointer transition-all ".concat(q===e.value?"border-primary bg-primary/5":"border-border hover:border-primary/50"),onClick:()=>z(e.value),children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-orange-600"}),(0,t.jsx)("span",{className:"font-medium",children:e.label})]})},e.value))})})]}),"partial_delivery"===_&&(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"تفاصيل التسليم الجزئي"}),(0,t.jsx)(l.BT,{children:"أدخل عدد القطع الراجعة والسعر الجديد"})]}),(0,t.jsx)(l.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"عدد القطع الراجعة"}),(0,t.jsx)(d.p,{type:"number",value:L,onChange:e=>Q(e.target.value),placeholder:"أدخل عدد القطع الراجعة",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"السعر الجديد (د.ع)"}),(0,t.jsx)(d.p,{type:"number",value:E,onChange:e=>I(e.target.value),placeholder:"أدخل السعر الجديد",required:!0})]})]})})]}),"price_changed"===_&&(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"السعر الجديد"}),(0,t.jsx)(l.BT,{children:"أدخل السعر الجديد للطلب"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"السعر الجديد (د.ع)"}),(0,t.jsx)(d.p,{type:"number",value:E,onChange:e=>I(e.target.value),placeholder:"أدخل السعر الجديد",required:!0})]})})]}),["returned","postponed"].includes(_)&&(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"إضافة صورة *"}),(0,t.jsx)(l.BT,{children:"يجب إضافة صورة لتأكيد هذه الحالة"})]}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{var e;return null==(e=j.current)?void 0:e.click()},className:"flex items-center gap-2",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),"اختيار صورة"]}),(0,t.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{j.current&&(j.current.setAttribute("capture","environment"),j.current.click())},className:"flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),"التقاط صورة"]})]}),(0,t.jsx)("input",{ref:j,type:"file",accept:"image/*",onChange:e=>{var r;let a=null==(r=e.target.files)?void 0:r[0];if(a){A(a);let e=new FileReader;e.onload=e=>{var r;T(null==(r=e.target)?void 0:r.result)},e.readAsDataURL(a)}},className:"hidden"}),C&&(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)("img",{src:C,alt:"معاينة الصورة",className:"max-w-xs h-auto rounded-lg border"})})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"ملاحظات"}),(0,t.jsx)(l.BT,{children:"أضف أي ملاحظات إضافية"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)(c.T,{value:Z,onChange:e=>B(e.target.value),placeholder:"أدخل ملاحظات حول تحديث الحالة...",rows:4})})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,t.jsx)(N(),{href:"/orders/".concat(a),children:(0,t.jsx)(i.$,{variant:"outline",disabled:W,children:"إلغاء"})}),(0,t.jsxs)(i.$,{type:"submit",disabled:W||!_,className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),W?"جاري التحديث...":"تحديث الحالة"]})]})]})]})})}},30285:(e,r,a)=>{"use strict";a.d(r,{$:()=>d});var t=a(95155);a(12115);var s=a(99708),n=a(74466),l=a(59434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:a,size:n,asChild:d=!1,...c}=e,o=d?s.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,l.cn)(i({variant:a,size:n,className:r})),...c})}},59434:(e,r,a)=>{"use strict";a.d(r,{Yq:()=>i,cn:()=>n,ps:()=>x,qY:()=>u,r6:()=>d,vv:()=>l,y7:()=>c,zC:()=>o});var t=a(52596),s=a(39688);function n(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,s.QP)((0,t.$)(r))}function l(e){return"".concat(e.toLocaleString("ar-IQ")," د.ع")}function i(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function d(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function c(){let e=Date.now().toString().slice(-6),r=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"".concat("MRS").concat(e).concat(r)}function o(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function u(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function x(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}},62523:(e,r,a)=>{"use strict";a.d(r,{p:()=>n});var t=a(95155);a(12115);var s=a(59434);function n(e){let{className:r,type:a,...n}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...n})}},66695:(e,r,a)=>{"use strict";a.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>l});var t=a(95155);a(12115);var s=a(59434);function n(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...a})}function l(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...a})}function i(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",r),...a})}function d(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",r),...a})}function c(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",r),...a})}},85429:(e,r,a)=>{Promise.resolve().then(a.bind(a,30189))},88539:(e,r,a)=>{"use strict";a.d(r,{T:()=>l});var t=a(95155),s=a(12115),n=a(59434);let l=s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:r,...s})});l.displayName="Textarea"}},e=>{var r=r=>e(e.s=r);e.O(0,[8779,622,2432,7979,1899,9744,4495,5138,6321,2652,5030,3719,9473,558,9401,6325,6077,4409,1124,9385,4927,37,2041,4979,8321,2900,3610,9988,2158,7358],()=>r(85429)),_N_E=e.O()}]);