"use client";

import { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { authService, AuthUser } from '@/lib/auth';
import { APP_CONFIG } from '@/lib/config';
import { testFirebaseConnection } from '@/lib/firebase';
import { firebaseAuthService } from '@/lib/firebase-auth';

interface AuthContextType {
  user: AuthUser | null;
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in on app start
    const checkAuth = async () => {
      try {
        // Demo mode auto-login - تسجيل دخول تلقائي في الوضع التجريبي
        if (APP_CONFIG.demo.enabled && APP_CONFIG.demo.autoLogin) {
          try {
            console.log('Starting demo auto-login...');

            // استخدام timeout لتجنب التعليق
            const loginPromise = authService.login({
              username: APP_CONFIG.demo.defaultUser,
              password: '123456'
            });

            const timeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Login timeout')), 3000)
            );

            const demoUser = await Promise.race([loginPromise, timeoutPromise]) as AuthUser;

            console.log('Demo user logged in:', demoUser);
            setUser(demoUser);
            setIsAuthenticated(true);

            if (typeof window !== 'undefined') {
              localStorage.setItem('user', JSON.stringify(demoUser));
              localStorage.setItem('isAuthenticated', 'true');
              document.cookie = 'isAuthenticated=true; path=/; max-age=86400';
            }

            setLoading(false);
            return;
          } catch (error) {
            console.error('Demo auto-login failed:', error);
            // في حالة فشل التسجيل التلقائي، استخدم النظام الاحتياطي
            console.log('Using fallback authentication...');

            // إنشاء مستخدم تجريبي مباشرة
            const fallbackUser: AuthUser = {
              id: 'manager_fallback',
              username: 'manager',
              name: 'مدير النظام',
              phone: '07901234567',
              role: 'manager' as any,
              permissions: [],
              locationId: 'main_center',
              location: {
                id: 'main_center',
                name: 'المركز العام',
                type: 'company'
              },
              createdBy: 'system',
              createdAt: new Date(),
              isActive: true,
              accessToken: 'fallback_token',
              refreshToken: 'fallback_refresh'
            };

            setUser(fallbackUser);
            setIsAuthenticated(true);

            if (typeof window !== 'undefined') {
              localStorage.setItem('user', JSON.stringify(fallbackUser));
              localStorage.setItem('isAuthenticated', 'true');
              document.cookie = 'isAuthenticated=true; path=/; max-age=86400';
            }

            setLoading(false);
            return;
          }
        }

        if (typeof window !== 'undefined') {
          // Check both localStorage keys for compatibility
          const storedUser = localStorage.getItem('user') || localStorage.getItem('auth_user');
          const storedAuth = localStorage.getItem('isAuthenticated');

          if (storedAuth === 'true' && storedUser) {
            const userData = JSON.parse(storedUser);
            setUser(userData);
            setIsAuthenticated(true);

            // Set cookie for middleware
            document.cookie = 'isAuthenticated=true; path=/; max-age=86400'; // 24 hours
          } else {
            // Try to get user from authService
            const currentUser = authService.getCurrentUser();
            if (currentUser) {
              setUser(currentUser);
              setIsAuthenticated(true);

              // Update localStorage
              localStorage.setItem('user', JSON.stringify(currentUser));
              localStorage.setItem('isAuthenticated', 'true');
              document.cookie = 'isAuthenticated=true; path=/; max-age=86400';
            }
          }
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        // Clear invalid data
        if (typeof window !== 'undefined') {
          localStorage.removeItem('user');
          localStorage.removeItem('auth_user');
          localStorage.removeItem('isAuthenticated');
          document.cookie = 'isAuthenticated=; path=/; max-age=0';
        }
      } finally {
        // Always set loading to false after a short delay to ensure UI updates
        setTimeout(() => {
          setLoading(false);
        }, 300);
      }
    };

    checkAuth();
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      console.log('🔐 AuthProvider: محاولة تسجيل الدخول مع Firebase...');

      // استخدام Firebase Auth Service مباشرة
      const result = await firebaseAuthService.login(username, password);

      if (!result.success || !result.user) {
        console.error('❌ فشل تسجيل الدخول:', result.error);
        return false;
      }

      // تحويل بيانات المستخدم إلى AuthUser
      const userData: AuthUser = {
        ...result.user,
        accessToken: 'firebase_token',
        refreshToken: 'firebase_refresh'
      };

      // Update state
      setUser(userData);
      setIsAuthenticated(true);

      // Store in localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('isAuthenticated', 'true');
        localStorage.setItem('currentUser', JSON.stringify(result.user));

        // Set cookie for middleware with proper expiration
        document.cookie = 'isAuthenticated=true; path=/; max-age=86400; SameSite=Lax';
      }

      console.log('✅ تم تسجيل الدخول بنجاح:', result.user.name);
      return true;
    } catch (error) {
      console.error('❌ خطأ في تسجيل الدخول:', error);
      return false;
    }
  };

  const logout = async () => {
    try {
      console.log('🚪 AuthProvider: تسجيل الخروج من Firebase...');

      // Use Firebase Auth Service logout
      await firebaseAuthService.logout();

      // Update state
      setUser(null);
      setIsAuthenticated(false);

      // Clear localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('user');
        localStorage.removeItem('currentUser');
        localStorage.removeItem('isAuthenticated');

        // Clear cookie
        document.cookie = 'isAuthenticated=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=Lax';
      }

      // Redirect to login
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Force redirect even if there's an error
      router.push('/login');
    }
  };

  const value = {
    user,
    isAuthenticated,
    login,
    logout,
    loading
  };

  // Show loading screen while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse">
            <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">مرسال</h2>
          <p className="text-gray-500">جاري التحقق من بيانات الدخول...</p>
        </div>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
