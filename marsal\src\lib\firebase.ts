import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { getStorage } from 'firebase/storage';
import { APP_CONFIG } from './config';

// Firebase configuration - إعدادات قاعدة البيانات السحابية
const firebaseConfig = {
  apiKey: APP_CONFIG.firebase.apiKey || "AIzaSyDemoKeyForMarsalDeliveryApp123456789",
  authDomain: APP_CONFIG.firebase.authDomain || "marsal-delivery.firebaseapp.com",
  projectId: APP_CONFIG.firebase.projectId || "marsal-delivery-system",
  storageBucket: APP_CONFIG.firebase.storageBucket || "marsal-delivery.appspot.com",
  messagingSenderId: APP_CONFIG.firebase.messagingSenderId || "987654321",
  appId: APP_CONFIG.firebase.appId || "1:987654321:web:abc123def456ghi789"
};

// Initialize Firebase only if not in demo mode or if Firebase is not skipped
let app: any = null;
let db: any = null;
let auth: any = null;
let storage: any = null;

if (!APP_CONFIG.demo.skipFirebase) {
  try {
    app = initializeApp(firebaseConfig);
    db = getFirestore(app);
    auth = getAuth(app);
    storage = getStorage(app);
  } catch (error) {
    console.warn('Firebase initialization failed, running in demo mode:', error);
  }
}

export { db, auth, storage };

export default app;

// Test connection function - اختبار الاتصال بقاعدة البيانات السحابية
export const testFirebaseConnection = async (): Promise<{ success: boolean; message: string }> => {
  // في الوضع التجريبي، نعيد نجاح وهمي
  if (APP_CONFIG.demo.skipFirebase || !db) {
    return {
      success: true,
      message: 'تم الاتصال بقاعدة البيانات التجريبية بنجاح ✅ (وضع تجريبي)'
    };
  }

  try {
    // Test Firestore connection with timeout
    const { doc, getDoc, setDoc } = await import('firebase/firestore');

    // Create a test document to verify write access
    const testDocRef = doc(db, 'system', 'connection_test');
    await setDoc(testDocRef, {
      timestamp: new Date(),
      status: 'connected',
      app: 'marsal-delivery'
    });

    // Try to read the document back
    const testDoc = await getDoc(testDocRef);

    if (testDoc.exists()) {
      return {
        success: true,
        message: 'تم الاتصال بقاعدة البيانات السحابية بنجاح ✅'
      };
    } else {
      return {
        success: false,
        message: 'تم الاتصال ولكن فشل في قراءة البيانات'
      };
    }
  } catch (error) {
    console.error('Firebase connection test failed:', error);

    // Return a more user-friendly error message
    let errorMessage = 'فشل في الاتصال بقاعدة البيانات السحابية';

    if (error instanceof Error) {
      if (error.message.includes('network')) {
        errorMessage += ' - تحقق من الاتصال بالإنترنت';
      } else if (error.message.includes('permission')) {
        errorMessage += ' - مشكلة في الصلاحيات';
      } else {
        errorMessage += ': ' + error.message;
      }
    }

    return {
      success: false,
      message: errorMessage
    };
  }
};
