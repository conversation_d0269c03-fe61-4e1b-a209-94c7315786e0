// Firebase Authentication service for Marsal Delivery App
import { 
  signInWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged,
  User as FirebaseUser,
  createUserWithEmailAndPassword
} from 'firebase/auth';
import { auth, db } from './firebase';
import { doc, getDoc, setDoc, serverTimestamp, collection, query, where, getDocs } from 'firebase/firestore';
import { User } from '@/types';

// Default users for initial setup
const defaultUsers = [
  {
    username: 'manager',
    email: '<EMAIL>',
    name: 'مدير النظام',
    phone: '07801234567',
    role: 'manager',
    password: '123456'
  },
  {
    username: 'supervisor',
    email: '<EMAIL>',
    name: 'المشرف العام',
    phone: '07801234568',
    role: 'supervisor',
    password: '123456'
  },
  {
    username: 'courier',
    email: '<EMAIL>',
    name: 'مندوب التوصيل',
    phone: '07801234569',
    role: 'courier',
    password: '123456'
  }
];

export interface LoginResult {
  success: boolean;
  user?: User;
  error?: string;
}

export const firebaseAuthService = {
  // Initialize default users (run once)
  async initializeDefaultUsers(): Promise<void> {
    try {
      console.log('🔧 إعداد المستخدمين الافتراضيين...');
      
      for (const userData of defaultUsers) {
        // Check if user already exists
        const existingUser = await this.getUserByUsername(userData.username);
        if (existingUser) {
          console.log(`✅ المستخدم ${userData.username} موجود مسبقاً`);
          continue;
        }

        // Create Firebase Auth user
        try {
          const userCredential = await createUserWithEmailAndPassword(
            auth, 
            userData.email, 
            userData.password
          );

          // Create user document in Firestore
          await setDoc(doc(db, 'users', userCredential.user.uid), {
            username: userData.username,
            email: userData.email,
            name: userData.name,
            phone: userData.phone,
            role: userData.role,
            isActive: true,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            createdBy: 'system'
          });

          console.log(`✅ تم إنشاء المستخدم: ${userData.username}`);
        } catch (error: any) {
          if (error.code === 'auth/email-already-in-use') {
            console.log(`⚠️ البريد الإلكتروني ${userData.email} مستخدم مسبقاً`);
          } else {
            console.error(`❌ خطأ في إنشاء المستخدم ${userData.username}:`, error);
          }
        }
      }
    } catch (error) {
      console.error('❌ خطأ في إعداد المستخدمين الافتراضيين:', error);
    }
  },

  // Login with username/password
  async login(username: string, password: string): Promise<LoginResult> {
    try {
      console.log('🔐 محاولة تسجيل الدخول للمستخدم:', username);

      // Get user by username to find email
      const user = await this.getUserByUsername(username);
      if (!user) {
        return {
          success: false,
          error: 'اسم المستخدم غير موجود'
        };
      }

      if (!user.isActive) {
        return {
          success: false,
          error: 'الحساب غير مفعل'
        };
      }

      // Sign in with email and password
      const userCredential = await signInWithEmailAndPassword(auth, user.email!, password);
      
      // Update last login
      await setDoc(doc(db, 'users', userCredential.user.uid), {
        lastLogin: serverTimestamp()
      }, { merge: true });

      console.log('✅ تم تسجيل الدخول بنجاح');
      
      return {
        success: true,
        user: {
          ...user,
          id: userCredential.user.uid
        }
      };

    } catch (error: any) {
      console.error('❌ خطأ في تسجيل الدخول:', error);
      
      let errorMessage = 'خطأ في تسجيل الدخول';
      
      switch (error.code) {
        case 'auth/user-not-found':
          errorMessage = 'المستخدم غير موجود';
          break;
        case 'auth/wrong-password':
          errorMessage = 'كلمة المرور غير صحيحة';
          break;
        case 'auth/invalid-email':
          errorMessage = 'البريد الإلكتروني غير صحيح';
          break;
        case 'auth/user-disabled':
          errorMessage = 'الحساب معطل';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'محاولات كثيرة، يرجى المحاولة لاحقاً';
          break;
        default:
          errorMessage = error.message || 'خطأ غير معروف';
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Get user by username
  async getUserByUsername(username: string): Promise<User | null> {
    try {
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('username', '==', username));
      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        return null;
      }

      const doc = snapshot.docs[0];
      const data = doc.data();
      
      return {
        id: doc.id,
        username: data.username,
        email: data.email,
        name: data.name,
        phone: data.phone,
        role: data.role,
        isActive: data.isActive,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        createdBy: data.createdBy,
        lastLogin: data.lastLogin?.toDate()
      };
    } catch (error) {
      console.error('Error getting user by username:', error);
      return null;
    }
  },

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) {
        return null;
      }

      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      if (!userDoc.exists()) {
        return null;
      }

      const data = userDoc.data();
      return {
        id: userDoc.id,
        username: data.username,
        email: data.email,
        name: data.name,
        phone: data.phone,
        role: data.role,
        isActive: data.isActive,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        createdBy: data.createdBy,
        lastLogin: data.lastLogin?.toDate()
      };
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },

  // Logout
  async logout(): Promise<void> {
    try {
      await signOut(auth);
      console.log('✅ تم تسجيل الخروج بنجاح');
    } catch (error) {
      console.error('❌ خطأ في تسجيل الخروج:', error);
      throw error;
    }
  },

  // Listen to auth state changes
  onAuthStateChanged(callback: (user: User | null) => void): () => void {
    return onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        const user = await this.getCurrentUser();
        callback(user);
      } else {
        callback(null);
      }
    });
  },

  // Check if Firebase is connected
  async checkConnection(): Promise<{ connected: boolean; message: string }> {
    try {
      // Try to get current user
      const user = auth.currentUser;
      console.log('✅ Firebase Auth متصل');
      return {
        connected: true,
        message: 'Firebase Auth متصل بنجاح'
      };
    } catch (error: any) {
      console.error('❌ Firebase Auth غير متصل:', error);
      return {
        connected: false,
        message: `خطأ في الاتصال: ${error.message}`
      };
    }
  }
};

// Auto-initialize default users when the module loads
firebaseAuthService.initializeDefaultUsers().catch(console.error);
