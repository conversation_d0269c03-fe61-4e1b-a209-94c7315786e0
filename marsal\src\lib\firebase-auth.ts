// Firebase Authentication service for Marsal Delivery App
import { 
  signInWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged,
  User as FirebaseUser,
  createUserWithEmailAndPassword
} from 'firebase/auth';
import { auth, db } from './firebase';
import { doc, getDoc, setDoc, serverTimestamp, collection, query, where, getDocs, deleteDoc } from 'firebase/firestore';
import { User } from '@/types';

// Default users for initial setup
const defaultUsers = [
  {
    username: 'azad95',
    email: '<EMAIL>',
    name: 'أزاد - مدير النظام الرئيسي',
    phone: '07801234567',
    role: 'manager',
    password: 'A<PERSON>@1995'
  },
  {
    username: 'manager',
    email: '<EMAIL>',
    name: 'مدير النظام',
    phone: '07801234568',
    role: 'manager',
    password: '123456'
  },
  {
    username: 'supervisor',
    email: '<EMAIL>',
    name: 'المشرف العام',
    phone: '07801234569',
    role: 'supervisor',
    password: '123456'
  },
  {
    username: 'courier',
    email: '<EMAIL>',
    name: 'مندوب التوصيل',
    phone: '07801234570',
    role: 'courier',
    password: '123456'
  }
];

export interface LoginResult {
  success: boolean;
  user?: User;
  error?: string;
}

export const firebaseAuthService = {
  // Initialize default users (run once) - Local storage fallback
  async initializeDefaultUsers(): Promise<void> {
    try {
      console.log('🔧 إعداد المستخدمين الافتراضيين...');

      // Check if Firebase is available
      if (!auth || !db) {
        console.log('⚠️ Firebase غير متاح، استخدام التخزين المحلي');
        this.initializeLocalUsers();
        return;
      }

      for (const userData of defaultUsers) {
        // Check if user already exists
        const existingUser = await this.getUserByUsername(userData.username);
        if (existingUser) {
          console.log(`✅ المستخدم ${userData.username} موجود مسبقاً`);
          continue;
        }

        // Create Firebase Auth user
        try {
          const userCredential = await createUserWithEmailAndPassword(
            auth,
            userData.email,
            userData.password
          );

          // Create user document in Firestore
          await setDoc(doc(db, 'users', userCredential.user.uid), {
            username: userData.username,
            email: userData.email,
            name: userData.name,
            phone: userData.phone,
            role: userData.role,
            isActive: true,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            createdBy: 'system'
          });

          console.log(`✅ تم إنشاء المستخدم: ${userData.username}`);
        } catch (error: any) {
          if (error.code === 'auth/email-already-in-use') {
            console.log(`⚠️ البريد الإلكتروني ${userData.email} مستخدم مسبقاً`);
          } else {
            console.error(`❌ خطأ في إنشاء المستخدم ${userData.username}:`, error);
            // Fallback to local storage
            this.initializeLocalUsers();
            return;
          }
        }
      }
    } catch (error) {
      console.error('❌ خطأ في إعداد المستخدمين الافتراضيين:', error);
      // Fallback to local storage
      this.initializeLocalUsers();
    }
  },

  // Initialize users in local storage as fallback
  initializeLocalUsers(): void {
    try {
      const existingUsers = localStorage.getItem('marsal_users');
      if (existingUsers) {
        console.log('✅ المستخدمين موجودين في التخزين المحلي');
        return;
      }

      const localUsers = defaultUsers.map((userData, index) => ({
        id: `local_${index + 1}`,
        username: userData.username,
        email: userData.email,
        name: userData.name,
        phone: userData.phone,
        role: userData.role,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'system',
        password: userData.password // Store password for local auth
      }));

      localStorage.setItem('marsal_users', JSON.stringify(localUsers));
      console.log('✅ تم إنشاء المستخدمين في التخزين المحلي');
    } catch (error) {
      console.error('❌ خطأ في إنشاء المستخدمين المحليين:', error);
    }
  },

  // Login with username/password - Firebase or Local
  async login(username: string, password: string): Promise<LoginResult> {
    try {
      console.log('🔐 محاولة تسجيل الدخول للمستخدم:', username);

      // Try Firebase first
      if (auth && db) {
        try {
          return await this.loginWithFirebase(username, password);
        } catch (error) {
          console.warn('⚠️ فشل تسجيل الدخول مع Firebase، محاولة التخزين المحلي');
        }
      }

      // Fallback to local storage
      return await this.loginWithLocalStorage(username, password);

    } catch (error: any) {
      console.error('❌ خطأ في تسجيل الدخول:', error);
      return {
        success: false,
        error: error.message || 'خطأ في تسجيل الدخول'
      };
    }
  },

  // Login with Firebase
  async loginWithFirebase(username: string, password: string): Promise<LoginResult> {
    // Get user by username to find email
    const user = await this.getUserByUsername(username);
    if (!user) {
      throw new Error('اسم المستخدم غير موجود');
    }

    if (!user.isActive) {
      throw new Error('الحساب غير مفعل');
    }

    // Sign in with email and password
    const userCredential = await signInWithEmailAndPassword(auth, user.email!, password);

    // Update last login
    await setDoc(doc(db, 'users', userCredential.user.uid), {
      lastLogin: serverTimestamp()
    }, { merge: true });

    console.log('✅ تم تسجيل الدخول مع Firebase بنجاح');

    return {
      success: true,
      user: {
        ...user,
        id: userCredential.user.uid
      }
    };
  },

  // Login with local storage
  async loginWithLocalStorage(username: string, password: string): Promise<LoginResult> {
    const usersData = localStorage.getItem('marsal_users');
    if (!usersData) {
      throw new Error('لا يوجد مستخدمين مسجلين');
    }

    const users = JSON.parse(usersData);
    const user = users.find((u: any) => u.username === username);

    if (!user) {
      throw new Error('اسم المستخدم غير موجود');
    }

    if (!user.isActive) {
      throw new Error('الحساب غير مفعل');
    }

    if (user.password !== password) {
      throw new Error('كلمة المرور غير صحيحة');
    }

    // Update last login
    user.lastLogin = new Date();
    const updatedUsers = users.map((u: any) => u.id === user.id ? user : u);
    localStorage.setItem('marsal_users', JSON.stringify(updatedUsers));

    console.log('✅ تم تسجيل الدخول مع التخزين المحلي بنجاح');

    return {
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        phone: user.phone,
        role: user.role,
        isActive: user.isActive,
        createdAt: new Date(user.createdAt),
        updatedAt: new Date(user.updatedAt),
        createdBy: user.createdBy,
        lastLogin: user.lastLogin ? new Date(user.lastLogin) : undefined
      }
    };
  },

  // Get user by username - Firebase or Local
  async getUserByUsername(username: string): Promise<User | null> {
    try {
      // Try Firebase first
      if (db) {
        try {
          const usersRef = collection(db, 'users');
          const q = query(usersRef, where('username', '==', username));
          const snapshot = await getDocs(q);

          if (!snapshot.empty) {
            const doc = snapshot.docs[0];
            const data = doc.data();

            return {
              id: doc.id,
              username: data.username,
              email: data.email,
              name: data.name,
              phone: data.phone,
              role: data.role,
              isActive: data.isActive,
              createdAt: data.createdAt?.toDate() || new Date(),
              updatedAt: data.updatedAt?.toDate() || new Date(),
              createdBy: data.createdBy,
              lastLogin: data.lastLogin?.toDate()
            };
          }
        } catch (error) {
          console.warn('⚠️ فشل البحث في Firebase، محاولة التخزين المحلي');
        }
      }

      // Fallback to local storage
      const usersData = localStorage.getItem('marsal_users');
      if (!usersData) {
        return null;
      }

      const users = JSON.parse(usersData);
      const user = users.find((u: any) => u.username === username);

      if (!user) {
        return null;
      }

      return {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        phone: user.phone,
        role: user.role,
        isActive: user.isActive,
        createdAt: new Date(user.createdAt),
        updatedAt: new Date(user.updatedAt),
        createdBy: user.createdBy,
        lastLogin: user.lastLogin ? new Date(user.lastLogin) : undefined
      };
    } catch (error) {
      console.error('Error getting user by username:', error);
      return null;
    }
  },

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) {
        return null;
      }

      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      if (!userDoc.exists()) {
        return null;
      }

      const data = userDoc.data();
      return {
        id: userDoc.id,
        username: data.username,
        email: data.email,
        name: data.name,
        phone: data.phone,
        role: data.role,
        isActive: data.isActive,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        createdBy: data.createdBy,
        lastLogin: data.lastLogin?.toDate()
      };
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },

  // Logout
  async logout(): Promise<void> {
    try {
      await signOut(auth);
      console.log('✅ تم تسجيل الخروج بنجاح');
    } catch (error) {
      console.error('❌ خطأ في تسجيل الخروج:', error);
      throw error;
    }
  },

  // Listen to auth state changes
  onAuthStateChanged(callback: (user: User | null) => void): () => void {
    return onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        const user = await this.getCurrentUser();
        callback(user);
      } else {
        callback(null);
      }
    });
  },

  // Create new user (only for managers)
  async createUser(userData: {
    username: string;
    email: string;
    name: string;
    phone: string;
    role: string;
    password: string;
    createdBy: string;
  }): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      console.log('👤 إنشاء مستخدم جديد:', userData.username);

      // Create Firebase Auth user
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        userData.email,
        userData.password
      );

      // Create user document in Firestore
      await setDoc(doc(db, 'users', userCredential.user.uid), {
        username: userData.username,
        email: userData.email,
        name: userData.name,
        phone: userData.phone,
        role: userData.role,
        isActive: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        createdBy: userData.createdBy
      });

      console.log('✅ تم إنشاء المستخدم بنجاح:', userData.username);

      const newUser: User = {
        id: userCredential.user.uid,
        username: userData.username,
        email: userData.email,
        name: userData.name,
        phone: userData.phone,
        role: userData.role as any,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: userData.createdBy
      };

      return {
        success: true,
        user: newUser
      };

    } catch (error: any) {
      console.error('❌ خطأ في إنشاء المستخدم:', error);

      let errorMessage = 'خطأ في إنشاء المستخدم';

      switch (error.code) {
        case 'auth/email-already-in-use':
          errorMessage = 'البريد الإلكتروني مستخدم مسبقاً';
          break;
        case 'auth/weak-password':
          errorMessage = 'كلمة المرور ضعيفة';
          break;
        case 'auth/invalid-email':
          errorMessage = 'البريد الإلكتروني غير صحيح';
          break;
        default:
          errorMessage = error.message || 'خطأ غير معروف';
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Get all users (for managers)
  async getAllUsers(): Promise<User[]> {
    try {
      const usersRef = collection(db, 'users');
      const snapshot = await getDocs(usersRef);

      return snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          username: data.username,
          email: data.email,
          name: data.name,
          phone: data.phone,
          role: data.role,
          isActive: data.isActive,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          createdBy: data.createdBy,
          lastLogin: data.lastLogin?.toDate()
        };
      });
    } catch (error) {
      console.error('Error getting all users:', error);
      return [];
    }
  },

  // Update user (for managers)
  async updateUser(userId: string, updates: Partial<User>): Promise<{ success: boolean; error?: string }> {
    try {
      await setDoc(doc(db, 'users', userId), {
        ...updates,
        updatedAt: serverTimestamp()
      }, { merge: true });

      console.log('✅ تم تحديث المستخدم:', userId);
      return { success: true };
    } catch (error: any) {
      console.error('❌ خطأ في تحديث المستخدم:', error);
      return {
        success: false,
        error: error.message || 'خطأ في تحديث المستخدم'
      };
    }
  },

  // Delete user (for managers)
  async deleteUser(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Delete from Firestore
      await deleteDoc(doc(db, 'users', userId));

      // Note: Deleting from Firebase Auth requires Admin SDK
      // For now, we just deactivate the user
      await this.updateUser(userId, { isActive: false });

      console.log('✅ تم حذف المستخدم:', userId);
      return { success: true };
    } catch (error: any) {
      console.error('❌ خطأ في حذف المستخدم:', error);
      return {
        success: false,
        error: error.message || 'خطأ في حذف المستخدم'
      };
    }
  },

  // Check if Firebase is connected
  async checkConnection(): Promise<{ connected: boolean; message: string }> {
    try {
      // Try to get current user
      const user = auth.currentUser;
      console.log('✅ Firebase Auth متصل');
      return {
        connected: true,
        message: 'Firebase Auth متصل بنجاح'
      };
    } catch (error: any) {
      console.error('❌ Firebase Auth غير متصل:', error);
      return {
        connected: false,
        message: `خطأ في الاتصال: ${error.message}`
      };
    }
  }
};

// Auto-initialize default users when the module loads
firebaseAuthService.initializeDefaultUsers().catch(console.error);
