import { collection, query, where, getDocs, Timestamp } from 'firebase/firestore';
import { db } from './firebase';
import { Statistics } from '@/types';
import { mockOrders } from './mock-data';

export const statisticsService = {
  async getOverallStats(): Promise<Statistics> {
    try {
      const ordersRef = collection(db, 'orders');

      // Get all orders
      const allOrdersSnapshot = await getDocs(ordersRef);
      const totalOrders = allOrdersSnapshot.size;

      // Get delivered orders
      const deliveredQuery = query(ordersRef, where('status', '==', 'delivered'));
      const deliveredSnapshot = await getDocs(deliveredQuery);
      const deliveredOrders = deliveredSnapshot.size;

      // Get returned orders
      const returnedQuery = query(ordersRef, where('status', '==', 'returned'));
      const returnedSnapshot = await getDocs(returnedQuery);
      const returnedOrders = returnedSnapshot.size;

      // Get pending orders
      const pendingQuery = query(ordersRef, where('status', '==', 'pending'));
      const pendingSnapshot = await getDocs(pendingQuery);
      const pendingOrders = pendingSnapshot.size;

      // Calculate total amount from delivered orders
      let totalAmount = 0;
      deliveredSnapshot.docs.forEach(doc => {
        const data = doc.data();
        totalAmount += data.amount || 0;
      });

      // Calculate commission (1000 IQD per delivered order)
      const totalCommission = deliveredOrders * 1000;

      return {
        totalOrders,
        deliveredOrders,
        returnedOrders,
        pendingOrders,
        totalAmount,
        totalCommission
      };
    } catch (error) {
      console.warn('Firebase not available, using mock data:', error);
      // Fallback to mock data
      const totalOrders = mockOrders.length;
      const deliveredOrders = mockOrders.filter(o => o.status === 'delivered').length;
      const returnedOrders = mockOrders.filter(o => o.status === 'returned').length;
      const pendingOrders = mockOrders.filter(o => o.status === 'pending').length;
      const totalAmount = mockOrders
        .filter(o => o.status === 'delivered')
        .reduce((sum, o) => sum + o.amount, 0);
      const totalCommission = deliveredOrders * 1000;

      return {
        totalOrders,
        deliveredOrders,
        returnedOrders,
        pendingOrders,
        totalAmount,
        totalCommission
      };
    }
  },

  async getTodayStats() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayTimestamp = Timestamp.fromDate(today);
    
    const ordersRef = collection(db, 'orders');
    const todayQuery = query(
      ordersRef, 
      where('createdAt', '>=', todayTimestamp)
    );
    
    const snapshot = await getDocs(todayQuery);
    return snapshot.size;
  },

  async getCourierStats(courierId: string) {
    const ordersRef = collection(db, 'orders');
    const courierQuery = query(ordersRef, where('assignedTo', '==', courierId));
    const snapshot = await getDocs(courierQuery);
    
    let delivered = 0;
    let returned = 0;
    let pending = 0;
    let totalAmount = 0;
    
    snapshot.docs.forEach(doc => {
      const data = doc.data();
      switch (data.status) {
        case 'delivered':
          delivered++;
          totalAmount += data.amount || 0;
          break;
        case 'returned':
          returned++;
          break;
        case 'pending':
        case 'assigned':
        case 'picked_up':
        case 'in_transit':
          pending++;
          break;
      }
    });
    
    return {
      totalOrders: snapshot.size,
      deliveredOrders: delivered,
      returnedOrders: returned,
      pendingOrders: pending,
      totalAmount,
      totalCommission: delivered * 1000
    };
  },

  async getMonthlyStats(year: number, month: number) {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0, 23, 59, 59);
    
    const ordersRef = collection(db, 'orders');
    const monthQuery = query(
      ordersRef,
      where('createdAt', '>=', Timestamp.fromDate(startDate)),
      where('createdAt', '<=', Timestamp.fromDate(endDate))
    );
    
    const snapshot = await getDocs(monthQuery);
    
    let delivered = 0;
    let returned = 0;
    let totalAmount = 0;
    
    snapshot.docs.forEach(doc => {
      const data = doc.data();
      if (data.status === 'delivered') {
        delivered++;
        totalAmount += data.amount || 0;
      } else if (data.status === 'returned') {
        returned++;
      }
    });
    
    return {
      totalOrders: snapshot.size,
      deliveredOrders: delivered,
      returnedOrders: returned,
      pendingOrders: snapshot.size - delivered - returned,
      totalAmount,
      totalCommission: delivered * 1000
    };
  }
};
