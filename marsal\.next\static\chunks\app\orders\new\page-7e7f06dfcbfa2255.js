(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3171],{29353:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>m});var s=n(95155),t=n(12115),a=n(66695),i=n(30285),d=n(62523),l=n(37108),o=n(92138),c=n(4229),u=n(6874),x=n.n(u);function m(){let[e,r]=(0,t.useState)({senderName:"",senderPhone:"",senderAddress:"",recipientName:"",recipientPhone:"",recipientAddress:"",amount:"",notes:""}),n=e=>{let{name:n,value:s}=e.target;r(e=>({...e,[n]:s}))};return(0,s.jsx)("div",{className:"min-h-screen bg-background p-4 md:p-6 lg:p-8",dir:"rtl",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-primary flex items-center gap-2",children:[(0,s.jsx)(l.A,{className:"h-8 w-8"}),"طلب جديد"]}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:"إضافة طلب توصيل جديد إلى النظام"})]}),(0,s.jsx)(x(),{href:"/orders",children:(0,s.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),"العودة للطلبات"]})})]}),(0,s.jsxs)("form",{onSubmit:r=>{r.preventDefault(),console.log("Order data:",e),alert("تم إضافة الطلب بنجاح!")},className:"space-y-6",children:[(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"بيانات المرسل"}),(0,s.jsx)(a.BT,{children:"معلومات الشخص أو الشركة المرسلة"})]}),(0,s.jsxs)(a.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اسم المرسل *"}),(0,s.jsx)(d.p,{name:"senderName",value:e.senderName,onChange:n,placeholder:"أدخل اسم المرسل",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"هاتف المرسل *"}),(0,s.jsx)(d.p,{name:"senderPhone",value:e.senderPhone,onChange:n,placeholder:"07xxxxxxxxx",required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"عنوان المرسل *"}),(0,s.jsx)(d.p,{name:"senderAddress",value:e.senderAddress,onChange:n,placeholder:"العنوان الكامل للمرسل",required:!0})]})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"بيانات المستلم"}),(0,s.jsx)(a.BT,{children:"معلومات الشخص المستلم للطلب"})]}),(0,s.jsxs)(a.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اسم المستلم *"}),(0,s.jsx)(d.p,{name:"recipientName",value:e.recipientName,onChange:n,placeholder:"أدخل اسم المستلم",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"هاتف المستلم *"}),(0,s.jsx)(d.p,{name:"recipientPhone",value:e.recipientPhone,onChange:n,placeholder:"07xxxxxxxxx",required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"عنوان المستلم *"}),(0,s.jsx)(d.p,{name:"recipientAddress",value:e.recipientAddress,onChange:n,placeholder:"العنوان الكامل للمستلم",required:!0})]})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"تفاصيل الطلب"}),(0,s.jsx)(a.BT,{children:"المبلغ والملاحظات الإضافية"})]}),(0,s.jsxs)(a.Wu,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"المبلغ (دينار عراقي) *"}),(0,s.jsx)(d.p,{name:"amount",type:"number",value:e.amount,onChange:n,placeholder:"0",required:!0})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"ملاحظات"}),(0,s.jsx)("textarea",{name:"notes",value:e.notes,onChange:n,placeholder:"أي ملاحظات إضافية...",className:"w-full p-3 border border-border rounded-md bg-background resize-none",rows:3})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,s.jsx)(x(),{href:"/orders",children:(0,s.jsx)(i.$,{variant:"outline",children:"إلغاء"})}),(0,s.jsxs)(i.$,{type:"submit",className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),"حفظ الطلب"]})]})]})]})})}},30285:(e,r,n)=>{"use strict";n.d(r,{$:()=>l});var s=n(95155);n(12115);var t=n(99708),a=n(74466),i=n(59434);let d=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:n,size:a,asChild:l=!1,...o}=e,c=l?t.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:n,size:a,className:r})),...o})}},59434:(e,r,n)=>{"use strict";n.d(r,{Yq:()=>d,cn:()=>a,ps:()=>x,qY:()=>u,r6:()=>l,vv:()=>i,y7:()=>o,zC:()=>c});var s=n(52596),t=n(39688);function a(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return(0,t.QP)((0,s.$)(r))}function i(e){return"".concat(e.toLocaleString("ar-IQ")," د.ع")}function d(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function l(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function o(){let e=Date.now().toString().slice(-6),r=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"".concat("MRS").concat(e).concat(r)}function c(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function u(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function x(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}},62523:(e,r,n)=>{"use strict";n.d(r,{p:()=>a});var s=n(95155);n(12115);var t=n(59434);function a(e){let{className:r,type:n,...a}=e;return(0,s.jsx)("input",{type:n,"data-slot":"input",className:(0,t.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...a})}},66695:(e,r,n)=>{"use strict";n.d(r,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>a,aR:()=>i});var s=n(95155);n(12115);var t=n(59434);function a(e){let{className:r,...n}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,t.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...n})}function i(e){let{className:r,...n}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,t.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...n})}function d(e){let{className:r,...n}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,t.cn)("leading-none font-semibold",r),...n})}function l(e){let{className:r,...n}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,t.cn)("text-muted-foreground text-sm",r),...n})}function o(e){let{className:r,...n}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,t.cn)("px-6",r),...n})}},84116:(e,r,n)=>{Promise.resolve().then(n.bind(n,29353))}},e=>{var r=r=>e(e.s=r);e.O(0,[8779,622,2432,7979,1899,9744,4495,5138,6321,2652,5030,3719,9473,558,9401,6325,6077,4409,1124,9385,4927,37,2041,4979,8321,2900,3610,9988,2158,7358],()=>r(84116)),_N_E=e.O()}]);