'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Database, Users, Package, Bell, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { checkFirebaseSetup, getDatabaseStats } from '@/lib/firebase-setup';

export default function FirebaseStatus() {
  const [status, setStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [stats, setStats] = useState({ users: 0, orders: 0, notifications: 0, total: 0 });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkStatus();
  }, []);

  const checkStatus = async () => {
    try {
      setLoading(true);
      
      // Check Firebase connection
      const result = await checkFirebaseSetup();
      
      if (result.success) {
        setStatus('connected');
        
        // Get database statistics
        const dbStats = await getDatabaseStats();
        setStats(dbStats);
      } else {
        setStatus('disconnected');
      }
    } catch (error) {
      setStatus('disconnected');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = () => {
    if (loading) return <Loader2 className="h-4 w-4 animate-spin text-yellow-600" />;
    
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'disconnected':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Loader2 className="h-4 w-4 animate-spin text-yellow-600" />;
    }
  };

  const getStatusText = () => {
    if (loading) return 'جاري الفحص...';
    
    switch (status) {
      case 'connected':
        return 'متصل بـ Firebase';
      case 'disconnected':
        return 'غير متصل بـ Firebase';
      default:
        return 'جاري الفحص...';
    }
  };

  const getStatusColor = () => {
    if (loading) return 'border-yellow-200 bg-yellow-50';
    
    switch (status) {
      case 'connected':
        return 'border-green-200 bg-green-50';
      case 'disconnected':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-yellow-200 bg-yellow-50';
    }
  };

  return (
    <Card className={`${getStatusColor()}`}>
      <CardContent className="pt-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span className="font-medium">{getStatusText()}</span>
          </div>
          
          {status === 'connected' && (
            <Badge variant="default" className="bg-green-100 text-green-800">
              نشط
            </Badge>
          )}
          
          {status === 'disconnected' && (
            <Badge variant="destructive">
              غير متصل
            </Badge>
          )}
        </div>

        {status === 'connected' && stats.total > 0 && (
          <div className="grid grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <Users className="h-6 w-6 text-blue-600 mx-auto mb-1" />
              <p className="text-lg font-bold">{stats.users}</p>
              <p className="text-xs text-gray-600">مستخدمين</p>
            </div>
            
            <div className="text-center">
              <Package className="h-6 w-6 text-green-600 mx-auto mb-1" />
              <p className="text-lg font-bold">{stats.orders}</p>
              <p className="text-xs text-gray-600">طلبات</p>
            </div>
            
            <div className="text-center">
              <Bell className="h-6 w-6 text-yellow-600 mx-auto mb-1" />
              <p className="text-lg font-bold">{stats.notifications}</p>
              <p className="text-xs text-gray-600">إشعارات</p>
            </div>
            
            <div className="text-center">
              <Database className="h-6 w-6 text-purple-600 mx-auto mb-1" />
              <p className="text-lg font-bold">{stats.total}</p>
              <p className="text-xs text-gray-600">إجمالي</p>
            </div>
          </div>
        )}

        <div className="flex gap-2">
          {status === 'disconnected' && (
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => window.open('/firebase-setup', '_blank')}
              className="flex-1"
            >
              <Database className="h-4 w-4 mr-1" />
              إعداد Firebase
            </Button>
          )}
          
          {status === 'connected' && stats.total === 0 && (
            <Button 
              size="sm" 
              onClick={() => window.open('/firebase-setup', '_blank')}
              className="flex-1"
            >
              <Database className="h-4 w-4 mr-1" />
              إعداد البيانات
            </Button>
          )}
          
          <Button 
            size="sm" 
            variant="outline"
            onClick={checkStatus}
            disabled={loading}
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              'تحديث'
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
