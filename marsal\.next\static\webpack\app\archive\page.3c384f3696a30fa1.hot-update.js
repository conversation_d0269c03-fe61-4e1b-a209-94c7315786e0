"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/archive/page",{

/***/ "(app-pages-browser)/./src/components/receipt-template.tsx":
/*!*********************************************!*\
  !*** ./src/components/receipt-template.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ReceiptTemplate = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = (param, ref)=>{\n    let { order } = param;\n    // Generate barcode data (simple implementation)\n    const generateBarcode = (trackingNumber)=>{\n        // This is a simple barcode representation based on tracking number\n        // In a real app, you'd use a proper barcode library\n        const barcodePattern = trackingNumber.split('').map(()=>'|||').join(' ');\n        return \"||||| \".concat(barcodePattern, \" |||||\");\n    };\n    const getStatusText = (status)=>{\n        const statusMap = {\n            pending: \"في الانتظار\",\n            assigned: \"مسند للمندوب\",\n            \"out-for-delivery\": \"خارج للتوصيل\",\n            delivered: \"تم التسليم\",\n            returned: \"راجع للمرسل\",\n            cancelled: \"ملغي\",\n            postponed: \"مؤجل\"\n        };\n        return statusMap[status] || status;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: \"receipt-template\",\n        style: {\n            width: \"110mm\",\n            height: \"130mm\",\n            padding: \"5mm\",\n            fontFamily: \"'Arial', 'Tahoma', sans-serif\",\n            fontSize: \"11px\",\n            lineHeight: \"1.3\",\n            color: \"#000\",\n            backgroundColor: \"#fff\",\n            border: \"2px solid #000\",\n            boxSizing: \"border-box\",\n            direction: \"rtl\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"10px\",\n                    borderBottom: \"2px solid #000\",\n                    paddingBottom: \"6px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: \"13px\",\n                            fontWeight: \"bold\",\n                            margin: \"0 0 2px 0\",\n                            color: \"#000\",\n                            letterSpacing: \"0.3px\"\n                        },\n                        children: \"مكتب علي الشيباني للتوصيل السريع فرع الحي\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            margin: \"0\",\n                            fontSize: \"8px\",\n                            color: \"#666\"\n                        },\n                        children: \"خدمة توصيل سريعة وموثوقة\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"10px\",\n                    border: \"2px solid #000\",\n                    padding: \"6px\",\n                    backgroundColor: \"#f0f8ff\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"9px\",\n                            color: \"#666\",\n                            marginBottom: \"2px\"\n                        },\n                        children: \"رقم الوصل\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"18px\",\n                            fontWeight: \"bold\",\n                            color: \"#000\",\n                            letterSpacing: \"1px\"\n                        },\n                        children: order.trackingNumber\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"6px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"رقم هاتف الزبون:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"12px\",\n                            color: \"#0066cc\",\n                            direction: \"ltr\"\n                        },\n                        children: order.recipientPhone\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"6px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"الحالة:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"11px\",\n                            color: order.status === \"delivered\" ? \"#28a745\" : \"#ffc107\",\n                            padding: \"2px 6px\",\n                            borderRadius: \"3px\",\n                            backgroundColor: order.status === \"delivered\" ? \"#d4edda\" : \"#fff3cd\"\n                        },\n                        children: getStatusText(order.status)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"8px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"اسم المندوب:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"11px\",\n                            color: \"#333\"\n                        },\n                        children: order.assignedTo || \"غير محدد\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"10px\",\n                    border: \"2px solid #000\",\n                    padding: \"8px\",\n                    backgroundColor: \"#fff9e6\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"9px\",\n                            color: \"#666\",\n                            marginBottom: \"2px\"\n                        },\n                        children: \"المبلغ المطلوب\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"16px\",\n                            fontWeight: \"bold\",\n                            color: \"#000\",\n                            letterSpacing: \"1px\",\n                            direction: \"ltr\"\n                        },\n                        children: [\n                            order.amount.toLocaleString('en-US'),\n                            \" IQD\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 160,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"8px\",\n                    border: \"1px solid #000\",\n                    padding: \"6px\",\n                    backgroundColor: \"#fff\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"8px\",\n                            color: \"#666\",\n                            marginBottom: \"3px\"\n                        },\n                        children: \"الباركود\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontFamily: \"monospace\",\n                            fontSize: \"10px\",\n                            letterSpacing: \"1px\",\n                            color: \"#000\",\n                            backgroundColor: \"#fff\",\n                            padding: \"3px\"\n                        },\n                        children: generateBarcode(order.trackingNumber)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"8px\",\n                            color: \"#666\",\n                            marginTop: \"2px\"\n                        },\n                        children: order.trackingNumber\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 180,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    fontSize: \"8px\",\n                    color: \"#666\",\n                    borderTop: \"1px solid #ccc\",\n                    paddingTop: \"4px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"شكراً لاختياركم خدماتنا\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"2px\"\n                        },\n                        children: [\n                            \"التاريخ: \",\n                            new Date().toLocaleDateString('ar-IQ')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n        lineNumber: 34,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = ReceiptTemplate;\nReceiptTemplate.displayName = \"ReceiptTemplate\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReceiptTemplate);\nvar _c, _c1;\n$RefreshReg$(_c, \"ReceiptTemplate$forwardRef\");\n$RefreshReg$(_c1, \"ReceiptTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/receipt-template.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   STATUS_COLORS: () => (/* binding */ STATUS_COLORS),\n/* harmony export */   STATUS_LABELS: () => (/* binding */ STATUS_LABELS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Application configuration\nconst APP_CONFIG = {\n    name: \"مرسال\",\n    description: \"نظام إدارة عمليات التوصيل السريع\",\n    version: \"1.1.0\",\n    // Colors\n    colors: {\n        primary: \"#41a7ff\",\n        background: \"#f0f3f5\",\n        accent: \"#b19cd9\"\n    },\n    // Business rules\n    business: {\n        commissionPerOrder: 1000,\n        currency: \"د.ع\",\n        defaultOrderStatuses: [\n            \"pending\",\n            \"assigned\",\n            \"picked_up\",\n            \"in_transit\",\n            \"delivered\",\n            \"returned\",\n            \"cancelled\",\n            \"postponed\"\n        ]\n    },\n    // API endpoints (when backend is ready)\n    api: {\n        baseUrl: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\",\n        endpoints: {\n            orders: \"/api/orders\",\n            users: \"/api/users\",\n            couriers: \"/api/couriers\",\n            settlements: \"/api/settlements\"\n        }\n    },\n    // Firebase config keys (to be replaced with actual values)\n    firebase: {\n        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\n    },\n    // Features flags\n    features: {\n        enableNotifications: true,\n        enableReports: true,\n        enableBulkOperations: true,\n        enableImageUpload: true\n    },\n    // Demo mode settings - إعدادات الوضع التجريبي\n    demo: {\n        enabled: true,\n        autoLogin: true,\n        defaultUser: 'manager',\n        skipFirebase: true,\n        showDemoNotice: true // إظهار تنبيه الوضع التجريبي\n    },\n    // Company info\n    company: {\n        name: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        phone: '+*********** 4567',\n        address: 'بغداد، العراق'\n    },\n    // Receipt settings - إعدادات الوصل\n    receipt: {\n        dimensions: {\n            width: '110mm',\n            height: '130mm'\n        },\n        companyName: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        showBarcode: true,\n        showDate: true,\n        priceFormat: 'en-US',\n        currency: 'IQD',\n        fields: {\n            trackingNumber: true,\n            customerPhone: true,\n            status: true,\n            courierName: true,\n            amount: true,\n            barcode: true,\n            date: true\n        }\n    }\n};\n// Status labels in Arabic\nconst STATUS_LABELS = {\n    pending: \"في الانتظار\",\n    assigned: \"مسند\",\n    picked_up: \"تم الاستلام\",\n    in_transit: \"في الطريق\",\n    delivered: \"تم التسليم\",\n    returned: \"راجع\",\n    cancelled: \"ملغي\",\n    postponed: \"مؤجل\"\n};\n// Status colors for UI\nconst STATUS_COLORS = {\n    pending: \"text-yellow-600 bg-yellow-100\",\n    assigned: \"text-blue-600 bg-blue-100\",\n    picked_up: \"text-purple-600 bg-purple-100\",\n    in_transit: \"text-orange-600 bg-orange-100\",\n    delivered: \"text-green-600 bg-green-100\",\n    returned: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    postponed: \"text-gray-600 bg-gray-100\"\n};\n// User roles\nconst USER_ROLES = {\n    admin: \"مدير\",\n    manager: \"مدير فرع\",\n    dispatcher: \"موزع\",\n    courier: \"مندوب\",\n    accountant: \"محاسب\",\n    viewer: \"مشاهد\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvY29uZmlnLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUEsNEJBQTRCO0FBQ3JCLE1BQU1BLGFBQWE7SUFDeEJDLE1BQU07SUFDTkMsYUFBYTtJQUNiQyxTQUFTO0lBRVQsU0FBUztJQUNUQyxRQUFRO1FBQ05DLFNBQVM7UUFDVEMsWUFBWTtRQUNaQyxRQUFRO0lBQ1Y7SUFFQSxpQkFBaUI7SUFDakJDLFVBQVU7UUFDUkMsb0JBQW9CO1FBQ3BCQyxVQUFVO1FBQ1ZDLHNCQUFzQjtZQUNwQjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7SUFDSDtJQUVBLHdDQUF3QztJQUN4Q0MsS0FBSztRQUNIQyxTQUFTQyxPQUFPQSxDQUFDQyxHQUFHLENBQUNDLG1CQUFtQixJQUFJO1FBQzVDQyxXQUFXO1lBQ1RDLFFBQVE7WUFDUkMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLGFBQWE7UUFDZjtJQUNGO0lBRUEsMkRBQTJEO0lBQzNEQyxVQUFVO1FBQ1JDLFFBQVFULE9BQU9BLENBQUNDLEdBQUcsQ0FBQ1MsNEJBQTRCO1FBQ2hEQyxZQUFZWCxPQUFPQSxDQUFDQyxHQUFHLENBQUNXLGdDQUFnQztRQUN4REMsV0FBV2IsT0FBT0EsQ0FBQ0MsR0FBRyxDQUFDYSwrQkFBK0I7UUFDdERDLGVBQWVmLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ2UsbUNBQW1DO1FBQzlEQyxtQkFBbUJqQixPQUFPQSxDQUFDQyxHQUFHLENBQUNpQix3Q0FBd0M7UUFDdkVDLE9BQU9uQixPQUFPQSxDQUFDQyxHQUFHLENBQUNtQiwyQkFBMkI7SUFDaEQ7SUFFQSxpQkFBaUI7SUFDakJDLFVBQVU7UUFDUkMscUJBQXFCO1FBQ3JCQyxlQUFlO1FBQ2ZDLHNCQUFzQjtRQUN0QkMsbUJBQW1CO0lBQ3JCO0lBRUEsOENBQThDO0lBQzlDQyxNQUFNO1FBQ0pDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxhQUFhO1FBQ2JDLGNBQWM7UUFDZEMsZ0JBQWdCLEtBQUssNkJBQTZCO0lBQ3BEO0lBRUEsZUFBZTtJQUNmQyxTQUFTO1FBQ1A3QyxNQUFNO1FBQ044QyxPQUFPO1FBQ1BDLFNBQVM7SUFDWDtJQUVBLG1DQUFtQztJQUNuQ0MsU0FBUztRQUNQQyxZQUFZO1lBQ1ZDLE9BQU87WUFDUEMsUUFBUTtRQUNWO1FBQ0FDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYjlDLFVBQVU7UUFDVitDLFFBQVE7WUFDTkMsZ0JBQWdCO1lBQ2hCQyxlQUFlO1lBQ2ZDLFFBQVE7WUFDUkMsYUFBYTtZQUNiQyxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsTUFBTTtRQUNSO0lBQ0Y7QUFDRixFQUFFO0FBRUYsMEJBQTBCO0FBQ25CLE1BQU1DLGdCQUFnQjtJQUMzQkMsU0FBUztJQUNUQyxVQUFVO0lBQ1ZDLFdBQVc7SUFDWEMsWUFBWTtJQUNaQyxXQUFXO0lBQ1hDLFVBQVU7SUFDVkMsV0FBVztJQUNYQyxXQUFXO0FBQ2IsRUFBVztBQUVYLHVCQUF1QjtBQUNoQixNQUFNQyxnQkFBZ0I7SUFDM0JSLFNBQVM7SUFDVEMsVUFBVTtJQUNWQyxXQUFXO0lBQ1hDLFlBQVk7SUFDWkMsV0FBVztJQUNYQyxVQUFVO0lBQ1ZDLFdBQVc7SUFDWEMsV0FBVztBQUNiLEVBQVc7QUFFWCxhQUFhO0FBQ04sTUFBTUUsYUFBYTtJQUN4QkMsT0FBTztJQUNQQyxTQUFTO0lBQ1RDLFlBQVk7SUFDWkMsU0FBUztJQUNUQyxZQUFZO0lBQ1pDLFFBQVE7QUFDVixFQUFXIiwic291cmNlcyI6WyJFOlxcTWFyc2FsXFxtYXJzYWxcXHNyY1xcbGliXFxjb25maWcudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQXBwbGljYXRpb24gY29uZmlndXJhdGlvblxuZXhwb3J0IGNvbnN0IEFQUF9DT05GSUcgPSB7XG4gIG5hbWU6IFwi2YXYsdiz2KfZhFwiLFxuICBkZXNjcmlwdGlvbjogXCLZhti42KfZhSDYpdiv2KfYsdipINi52YXZhNmK2KfYqiDYp9mE2KrZiNi12YrZhCDYp9mE2LPYsdmK2LlcIixcbiAgdmVyc2lvbjogXCIxLjEuMFwiLFxuICBcbiAgLy8gQ29sb3JzXG4gIGNvbG9yczoge1xuICAgIHByaW1hcnk6IFwiIzQxYTdmZlwiLFxuICAgIGJhY2tncm91bmQ6IFwiI2YwZjNmNVwiLCBcbiAgICBhY2NlbnQ6IFwiI2IxOWNkOVwiXG4gIH0sXG4gIFxuICAvLyBCdXNpbmVzcyBydWxlc1xuICBidXNpbmVzczoge1xuICAgIGNvbW1pc3Npb25QZXJPcmRlcjogMTAwMCwgLy8gMTAwMCBJUUQgcGVyIG9yZGVyXG4gICAgY3VycmVuY3k6IFwi2K8u2LlcIixcbiAgICBkZWZhdWx0T3JkZXJTdGF0dXNlczogW1xuICAgICAgXCJwZW5kaW5nXCIsXG4gICAgICBcImFzc2lnbmVkXCIsIFxuICAgICAgXCJwaWNrZWRfdXBcIixcbiAgICAgIFwiaW5fdHJhbnNpdFwiLFxuICAgICAgXCJkZWxpdmVyZWRcIixcbiAgICAgIFwicmV0dXJuZWRcIixcbiAgICAgIFwiY2FuY2VsbGVkXCIsXG4gICAgICBcInBvc3Rwb25lZFwiXG4gICAgXVxuICB9LFxuICBcbiAgLy8gQVBJIGVuZHBvaW50cyAod2hlbiBiYWNrZW5kIGlzIHJlYWR5KVxuICBhcGk6IHtcbiAgICBiYXNlVXJsOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8IFwiaHR0cDovL2xvY2FsaG9zdDozMDAxXCIsXG4gICAgZW5kcG9pbnRzOiB7XG4gICAgICBvcmRlcnM6IFwiL2FwaS9vcmRlcnNcIixcbiAgICAgIHVzZXJzOiBcIi9hcGkvdXNlcnNcIixcbiAgICAgIGNvdXJpZXJzOiBcIi9hcGkvY291cmllcnNcIixcbiAgICAgIHNldHRsZW1lbnRzOiBcIi9hcGkvc2V0dGxlbWVudHNcIlxuICAgIH1cbiAgfSxcbiAgXG4gIC8vIEZpcmViYXNlIGNvbmZpZyBrZXlzICh0byBiZSByZXBsYWNlZCB3aXRoIGFjdHVhbCB2YWx1ZXMpXG4gIGZpcmViYXNlOiB7XG4gICAgYXBpS2V5OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9BUElfS0VZLFxuICAgIGF1dGhEb21haW46IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0ZJUkVCQVNFX0FVVEhfRE9NQUlOLFxuICAgIHByb2plY3RJZDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfUFJPSkVDVF9JRCxcbiAgICBzdG9yYWdlQnVja2V0OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9TVE9SQUdFX0JVQ0tFVCxcbiAgICBtZXNzYWdpbmdTZW5kZXJJZDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfTUVTU0FHSU5HX1NFTkRFUl9JRCxcbiAgICBhcHBJZDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfQVBQX0lEXG4gIH0sXG4gIFxuICAvLyBGZWF0dXJlcyBmbGFnc1xuICBmZWF0dXJlczoge1xuICAgIGVuYWJsZU5vdGlmaWNhdGlvbnM6IHRydWUsXG4gICAgZW5hYmxlUmVwb3J0czogdHJ1ZSxcbiAgICBlbmFibGVCdWxrT3BlcmF0aW9uczogdHJ1ZSxcbiAgICBlbmFibGVJbWFnZVVwbG9hZDogdHJ1ZVxuICB9LFxuXG4gIC8vIERlbW8gbW9kZSBzZXR0aW5ncyAtINil2LnYr9in2K/Yp9iqINin2YTZiNi22Lkg2KfZhNiq2KzYsdmK2KjZilxuICBkZW1vOiB7XG4gICAgZW5hYmxlZDogdHJ1ZSwgLy8g2KrZgdi52YrZhCDYp9mE2YjYtti5INin2YTYqtis2LHZitio2YpcbiAgICBhdXRvTG9naW46IHRydWUsIC8vINiq2LPYrNmK2YQg2K/YrtmI2YQg2KrZhNmC2KfYptmKXG4gICAgZGVmYXVsdFVzZXI6ICdtYW5hZ2VyJywgLy8g2KfZhNmF2LPYqtiu2K/ZhSDYp9mE2KfZgdiq2LHYp9i22YpcbiAgICBza2lwRmlyZWJhc2U6IHRydWUsIC8vINiq2K7Yt9mKIEZpcmViYXNlINmB2Yog2KfZhNmI2LbYuSDYp9mE2KrYrNix2YrYqNmKXG4gICAgc2hvd0RlbW9Ob3RpY2U6IHRydWUgLy8g2KXYuNmH2KfYsSDYqtmG2KjZitmHINin2YTZiNi22Lkg2KfZhNiq2KzYsdmK2KjZilxuICB9LFxuXG4gIC8vIENvbXBhbnkgaW5mb1xuICBjb21wYW55OiB7XG4gICAgbmFtZTogJ9mF2YPYqtioINi52YTZiiDYp9mE2LTZitio2KfZhtmKINmE2YTYqtmI2LXZitmEINin2YTYs9ix2YrYuSDZgdix2Lkg2KfZhNit2YonLFxuICAgIHBob25lOiAnKzk2NCA3NzAgMTIzIDQ1NjcnLFxuICAgIGFkZHJlc3M6ICfYqNi62K/Yp9iv2Iwg2KfZhNi52LHYp9mCJ1xuICB9LFxuXG4gIC8vIFJlY2VpcHQgc2V0dGluZ3MgLSDYpdi52K/Yp9iv2KfYqiDYp9mE2YjYtdmEXG4gIHJlY2VpcHQ6IHtcbiAgICBkaW1lbnNpb25zOiB7XG4gICAgICB3aWR0aDogJzExMG1tJyxcbiAgICAgIGhlaWdodDogJzEzMG1tJ1xuICAgIH0sXG4gICAgY29tcGFueU5hbWU6ICfZhdmD2KrYqCDYudmE2Yog2KfZhNi02YrYqNin2YbZiiDZhNmE2KrZiNi12YrZhCDYp9mE2LPYsdmK2Lkg2YHYsdi5INin2YTYrdmKJyxcbiAgICBzaG93QmFyY29kZTogdHJ1ZSxcbiAgICBzaG93RGF0ZTogdHJ1ZSxcbiAgICBwcmljZUZvcm1hdDogJ2VuLVVTJywgLy8gRW5nbGlzaCBmb3JtYXQgZm9yIHByaWNlXG4gICAgY3VycmVuY3k6ICdJUUQnLFxuICAgIGZpZWxkczoge1xuICAgICAgdHJhY2tpbmdOdW1iZXI6IHRydWUsXG4gICAgICBjdXN0b21lclBob25lOiB0cnVlLFxuICAgICAgc3RhdHVzOiB0cnVlLFxuICAgICAgY291cmllck5hbWU6IHRydWUsXG4gICAgICBhbW91bnQ6IHRydWUsXG4gICAgICBiYXJjb2RlOiB0cnVlLFxuICAgICAgZGF0ZTogdHJ1ZVxuICAgIH1cbiAgfVxufTtcblxuLy8gU3RhdHVzIGxhYmVscyBpbiBBcmFiaWNcbmV4cG9ydCBjb25zdCBTVEFUVVNfTEFCRUxTID0ge1xuICBwZW5kaW5nOiBcItmB2Yog2KfZhNin2YbYqti42KfYsVwiLFxuICBhc3NpZ25lZDogXCLZhdiz2YbYr1wiLCBcbiAgcGlja2VkX3VwOiBcItiq2YUg2KfZhNin2LPYqtmE2KfZhVwiLFxuICBpbl90cmFuc2l0OiBcItmB2Yog2KfZhNi32LHZitmCXCIsXG4gIGRlbGl2ZXJlZDogXCLYqtmFINin2YTYqtiz2YTZitmFXCIsXG4gIHJldHVybmVkOiBcItix2KfYrNi5XCIsXG4gIGNhbmNlbGxlZDogXCLZhdmE2LrZilwiLFxuICBwb3N0cG9uZWQ6IFwi2YXYpNis2YRcIlxufSBhcyBjb25zdDtcblxuLy8gU3RhdHVzIGNvbG9ycyBmb3IgVUlcbmV4cG9ydCBjb25zdCBTVEFUVVNfQ09MT1JTID0ge1xuICBwZW5kaW5nOiBcInRleHQteWVsbG93LTYwMCBiZy15ZWxsb3ctMTAwXCIsXG4gIGFzc2lnbmVkOiBcInRleHQtYmx1ZS02MDAgYmctYmx1ZS0xMDBcIixcbiAgcGlja2VkX3VwOiBcInRleHQtcHVycGxlLTYwMCBiZy1wdXJwbGUtMTAwXCIsIFxuICBpbl90cmFuc2l0OiBcInRleHQtb3JhbmdlLTYwMCBiZy1vcmFuZ2UtMTAwXCIsXG4gIGRlbGl2ZXJlZDogXCJ0ZXh0LWdyZWVuLTYwMCBiZy1ncmVlbi0xMDBcIixcbiAgcmV0dXJuZWQ6IFwidGV4dC1yZWQtNjAwIGJnLXJlZC0xMDBcIixcbiAgY2FuY2VsbGVkOiBcInRleHQtZ3JheS02MDAgYmctZ3JheS0xMDBcIixcbiAgcG9zdHBvbmVkOiBcInRleHQtZ3JheS02MDAgYmctZ3JheS0xMDBcIlxufSBhcyBjb25zdDtcblxuLy8gVXNlciByb2xlc1xuZXhwb3J0IGNvbnN0IFVTRVJfUk9MRVMgPSB7XG4gIGFkbWluOiBcItmF2K/ZitixXCIsXG4gIG1hbmFnZXI6IFwi2YXYr9mK2LEg2YHYsdi5XCIsIFxuICBkaXNwYXRjaGVyOiBcItmF2YjYsti5XCIsXG4gIGNvdXJpZXI6IFwi2YXZhtiv2YjYqFwiLFxuICBhY2NvdW50YW50OiBcItmF2K3Yp9iz2KhcIixcbiAgdmlld2VyOiBcItmF2LTYp9mH2K9cIlxufSBhcyBjb25zdDtcbiJdLCJuYW1lcyI6WyJBUFBfQ09ORklHIiwibmFtZSIsImRlc2NyaXB0aW9uIiwidmVyc2lvbiIsImNvbG9ycyIsInByaW1hcnkiLCJiYWNrZ3JvdW5kIiwiYWNjZW50IiwiYnVzaW5lc3MiLCJjb21taXNzaW9uUGVyT3JkZXIiLCJjdXJyZW5jeSIsImRlZmF1bHRPcmRlclN0YXR1c2VzIiwiYXBpIiwiYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwiZW5kcG9pbnRzIiwib3JkZXJzIiwidXNlcnMiLCJjb3VyaWVycyIsInNldHRsZW1lbnRzIiwiZmlyZWJhc2UiLCJhcGlLZXkiLCJORVhUX1BVQkxJQ19GSVJFQkFTRV9BUElfS0VZIiwiYXV0aERvbWFpbiIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX0FVVEhfRE9NQUlOIiwicHJvamVjdElkIiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfUFJPSkVDVF9JRCIsInN0b3JhZ2VCdWNrZXQiLCJORVhUX1BVQkxJQ19GSVJFQkFTRV9TVE9SQUdFX0JVQ0tFVCIsIm1lc3NhZ2luZ1NlbmRlcklkIiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfTUVTU0FHSU5HX1NFTkRFUl9JRCIsImFwcElkIiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfQVBQX0lEIiwiZmVhdHVyZXMiLCJlbmFibGVOb3RpZmljYXRpb25zIiwiZW5hYmxlUmVwb3J0cyIsImVuYWJsZUJ1bGtPcGVyYXRpb25zIiwiZW5hYmxlSW1hZ2VVcGxvYWQiLCJkZW1vIiwiZW5hYmxlZCIsImF1dG9Mb2dpbiIsImRlZmF1bHRVc2VyIiwic2tpcEZpcmViYXNlIiwic2hvd0RlbW9Ob3RpY2UiLCJjb21wYW55IiwicGhvbmUiLCJhZGRyZXNzIiwicmVjZWlwdCIsImRpbWVuc2lvbnMiLCJ3aWR0aCIsImhlaWdodCIsImNvbXBhbnlOYW1lIiwic2hvd0JhcmNvZGUiLCJzaG93RGF0ZSIsInByaWNlRm9ybWF0IiwiZmllbGRzIiwidHJhY2tpbmdOdW1iZXIiLCJjdXN0b21lclBob25lIiwic3RhdHVzIiwiY291cmllck5hbWUiLCJhbW91bnQiLCJiYXJjb2RlIiwiZGF0ZSIsIlNUQVRVU19MQUJFTFMiLCJwZW5kaW5nIiwiYXNzaWduZWQiLCJwaWNrZWRfdXAiLCJpbl90cmFuc2l0IiwiZGVsaXZlcmVkIiwicmV0dXJuZWQiLCJjYW5jZWxsZWQiLCJwb3N0cG9uZWQiLCJTVEFUVVNfQ09MT1JTIiwiVVNFUl9ST0xFUyIsImFkbWluIiwibWFuYWdlciIsImRpc3BhdGNoZXIiLCJjb3VyaWVyIiwiYWNjb3VudGFudCIsInZpZXdlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});