"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2044],{26912:(e,r,t)=>{t.d(r,{JI:()=>i,_m:()=>o,gG:()=>s});var s=function(e){return e.MANAGER="manager",e.COURIER="courier",e.SUPERVISOR="supervisor",e}({});let a={manager:{role:"manager",permissions:["create_order","view_order","update_order","delete_order","assign_order","transfer_order","create_user","view_user","update_user","delete_user","manage_branches","manage_provinces","view_accounting","process_accounting","view_all_statistics","view_archive","manage_archive","manage_tickets","manage_settings","manage_warehouse","import_orders","export_orders"],canCreateRoles:["courier","supervisor"],accessibleSections:["orders","dispatch","returns","accounting","statistics","archive","users","import-export","notifications","settings"]},supervisor:{role:"supervisor",permissions:["view_order","update_order","assign_order","manage_tickets","view_statistics","view_archive"],canCreateRoles:[],accessibleSections:["orders","statistics","archive","notifications","settings"]},courier:{role:"courier",permissions:["view_order","update_order"],canCreateRoles:[],accessibleSections:["orders","archive","notifications","settings"]}};function o(e,r){var t;return(null==(t=a[e])?void 0:t.permissions.includes(r))||!1}function i(e){var r;return(null==(r=a[e])?void 0:r.accessibleSections)||[]}},32044:(e,r,t)=>{t.d(r,{A:()=>d,AuthProvider:()=>u});var s=t(95155),a=t(12115),o=t(35695),i=t(79323),n=t(23843),c=t(59345);let l=(0,a.createContext)(void 0);function u(e){let{children:r}=e,[t,u]=(0,a.useState)(null),[d,m]=(0,a.useState)(!1),[g,h]=(0,a.useState)(!0),f=(0,o.useRouter)();(0,a.useEffect)(()=>{(async()=>{try{if(n.vQ.demo.enabled&&n.vQ.demo.autoLogin)try{console.log("Starting demo auto-login...");let e=i.y.login({username:n.vQ.demo.defaultUser,password:"123456"}),r=new Promise((e,r)=>setTimeout(()=>r(Error("Login timeout")),3e3)),t=await Promise.race([e,r]);console.log("Demo user logged in:",t),u(t),m(!0),localStorage.setItem("user",JSON.stringify(t)),localStorage.setItem("isAuthenticated","true"),document.cookie="isAuthenticated=true; path=/; max-age=86400",h(!1);return}catch(r){console.error("Demo auto-login failed:",r),console.log("Using fallback authentication...");let e={id:"manager_fallback",username:"manager",name:"مدير النظام",phone:"07901234567",role:"manager",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"system",createdAt:new Date,isActive:!0,accessToken:"fallback_token",refreshToken:"fallback_refresh"};u(e),m(!0),localStorage.setItem("user",JSON.stringify(e)),localStorage.setItem("isAuthenticated","true"),document.cookie="isAuthenticated=true; path=/; max-age=86400",h(!1);return}{let e=localStorage.getItem("user")||localStorage.getItem("auth_user"),r=localStorage.getItem("isAuthenticated");if("true"===r&&e){let r=JSON.parse(e);u(r),m(!0),document.cookie="isAuthenticated=true; path=/; max-age=86400"}else{let e=i.y.getCurrentUser();e&&(u(e),m(!0),localStorage.setItem("user",JSON.stringify(e)),localStorage.setItem("isAuthenticated","true"),document.cookie="isAuthenticated=true; path=/; max-age=86400")}}}catch(e){console.error("Error checking authentication:",e),localStorage.removeItem("user"),localStorage.removeItem("auth_user"),localStorage.removeItem("isAuthenticated"),document.cookie="isAuthenticated=; path=/; max-age=0"}finally{setTimeout(()=>{h(!1)},300)}})()},[]);let _=async(e,r)=>{try{console.log("\uD83D\uDD10 AuthProvider: محاولة تسجيل الدخول مع Firebase...");let t=await c.firebaseAuthService.login(e,r);if(!t.success||!t.user)return console.error("❌ فشل تسجيل الدخول:",t.error),!1;let s={...t.user,accessToken:"firebase_token",refreshToken:"firebase_refresh"};return u(s),m(!0),localStorage.setItem("user",JSON.stringify(s)),localStorage.setItem("isAuthenticated","true"),localStorage.setItem("currentUser",JSON.stringify(t.user)),document.cookie="isAuthenticated=true; path=/; max-age=86400; SameSite=Lax",console.log("✅ تم تسجيل الدخول بنجاح:",t.user.name),!0}catch(e){return console.error("❌ خطأ في تسجيل الدخول:",e),!1}},p=async()=>{try{console.log("\uD83D\uDEAA AuthProvider: تسجيل الخروج من Firebase..."),await c.firebaseAuthService.logout(),u(null),m(!1),localStorage.removeItem("user"),localStorage.removeItem("currentUser"),localStorage.removeItem("isAuthenticated"),document.cookie="isAuthenticated=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=Lax",f.push("/login")}catch(e){console.error("Logout error:",e),f.push("/login")}};return g?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse",children:(0,s.jsx)("div",{className:"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"مرسال"}),(0,s.jsx)("p",{className:"text-gray-500",children:"جاري التحقق من بيانات الدخول..."})]})}):(0,s.jsx)(l.Provider,{value:{user:t,isAuthenticated:d,login:_,logout:p,loading:g},children:r})}function d(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},42099:(e,r,t)=>{t.d(r,{Dv:()=>d,LC:()=>c});var s=t(13798),a=t(49509);let o=a.env.NEXT_PUBLIC_SUPABASE_URL||"https://your-project.supabase.co",i=a.env.NEXT_PUBLIC_SUPABASE_ANON_KEY||"your-anon-key",n=(0,s.UU)(o,i),c=async()=>{try{let{data:e,error:r}=await n.from("users").select("count").limit(1);if(r)return console.error("Supabase connection test failed:",r),{success:!1,message:"فشل في الاتصال بقاعدة البيانات السحابية: ".concat(r.message)};return{success:!0,message:"تم الاتصال بقاعدة البيانات السحابية بنجاح ✅"}}catch(r){console.error("Supabase connection test failed:",r);let e="فشل في الاتصال بقاعدة البيانات السحابية";return r instanceof Error&&(r.message.includes("network")?e+=" - تحقق من الاتصال بالإنترنت":r.message.includes("permission")?e+=" - مشكلة في الصلاحيات":e+=": "+r.message),{success:!1,message:e}}};class l{async createUser(e){let{data:r,error:t}=await n.from("users").insert([e]).select().single();if(t)throw Error("Failed to create user: ".concat(t.message));return r}async getUserByUsername(e){let{data:r,error:t}=await n.from("users").select("*").eq("username",e).single();if(t&&"PGRST116"!==t.code)throw Error("Failed to get user: ".concat(t.message));return r||null}async getAllUsers(){let{data:e,error:r}=await n.from("users").select("*").order("created_at",{ascending:!1});if(r)throw Error("Failed to get users: ".concat(r.message));return e||[]}async updateUser(e,r){let{error:t}=await n.from("users").update(r).eq("id",e);if(t)throw Error("Failed to update user: ".concat(t.message))}async deleteUser(e){let{error:r}=await n.from("users").delete().eq("id",e);if(r)throw Error("Failed to delete user: ".concat(r.message))}async getUsersByRole(e){let{data:r,error:t}=await n.from("users").select("*").eq("role",e).order("created_at",{ascending:!1});if(t)throw Error("Failed to get users by role: ".concat(t.message));return r||[]}}class u{async createOrder(e){let{data:r,error:t}=await n.from("orders").insert([{...e,updated_at:new Date().toISOString()}]).select().single();if(t)throw Error("Failed to create order: ".concat(t.message));return r}async getOrderByTrackingNumber(e){let{data:r,error:t}=await n.from("orders").select("*").eq("tracking_number",e).single();if(t&&"PGRST116"!==t.code)throw Error("Failed to get order: ".concat(t.message));return r||null}async getAllOrders(){let{data:e,error:r}=await n.from("orders").select("*").order("created_at",{ascending:!1});if(r)throw Error("Failed to get orders: ".concat(r.message));return e||[]}async getOrdersByStatus(e){let{data:r,error:t}=await n.from("orders").select("*").eq("status",e).order("created_at",{ascending:!1});if(t)throw Error("Failed to get orders by status: ".concat(t.message));return r||[]}async getOrdersByCourier(e){let{data:r,error:t}=await n.from("orders").select("*").eq("courier_id",e).order("created_at",{ascending:!1});if(t)throw Error("Failed to get orders by courier: ".concat(t.message));return r||[]}async updateOrder(e,r){let{error:t}=await n.from("orders").update({...r,updated_at:new Date().toISOString()}).eq("id",e);if(t)throw Error("Failed to update order: ".concat(t.message))}async deleteOrder(e){let{error:r}=await n.from("orders").delete().eq("id",e);if(r)throw Error("Failed to delete order: ".concat(r.message))}async searchOrders(e){let{data:r,error:t}=await n.from("orders").select("*").or("tracking_number.ilike.%".concat(e,"%,customer_name.ilike.%").concat(e,"%,customer_phone.ilike.%").concat(e,"%,address.ilike.%").concat(e,"%")).order("created_at",{ascending:!1});if(t)throw Error("Failed to search orders: ".concat(t.message));return r||[]}}let d=new l;new u},79323:(e,r,t)=>{t.d(r,{y:()=>i}),t(12115);var s=t(26912),a=t(42099);class o{async login(e){try{console.log("\uD83D\uDD10 تسجيل الدخول مع Firebase...");let{firebaseAuthService:r}=await Promise.resolve().then(t.bind(t,59345)),s=await r.login(e.username,e.password);if(!s.success||!s.user)throw Error(s.error||"فشل في تسجيل الدخول");let o=s.user;try{o=await a.Dv.getUserByUsername(e.username)}catch(r){return console.warn("Database not available, using fallback auth:",r),this.fallbackLogin(e)}if(!o)return this.fallbackLogin(e);if(!o.is_active)throw Error("الحساب غير مفعل");if("123456"!==e.password)throw Error("كلمة المرور غير صحيحة");let i={id:o.id,username:o.username,name:o.name,phone:o.phone,role:o.role,permissions:[],locationId:o.location_id||"main_center",location:o.location||{id:"main_center",name:"المركز العام",type:"company"},createdBy:o.created_by,createdAt:new Date(o.created_at),isActive:o.is_active,accessToken:"supabase_access_token",refreshToken:"supabase_refresh_token"};return this.currentUser=i,this.notifyListeners(),localStorage.setItem("auth_user",JSON.stringify(i)),i}catch(e){if(console.error("Login error:",e),e instanceof Error)throw e;throw Error("فشل في تسجيل الدخول")}}async fallbackLogin(e){let r={manager:{id:"manager_1",username:"manager",name:"مدير النظام",phone:"07901234567",role:"manager",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"system",createdAt:new Date,isActive:!0,accessToken:"fallback_access_token",refreshToken:"fallback_refresh_token"},supervisor:{id:"supervisor_1",username:"supervisor",name:"متابع النظام",phone:"07901234568",role:"supervisor",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"manager_1",createdAt:new Date,isActive:!0,accessToken:"fallback_access_token_supervisor",refreshToken:"fallback_refresh_token_supervisor"},courier:{id:"courier_1",username:"courier",name:"مندوب التوصيل",phone:"07901234570",role:"courier",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"manager_1",createdAt:new Date,isActive:!0,accessToken:"fallback_access_token_courier",refreshToken:"fallback_refresh_token_courier"}}[e.username];if(!r||"123456"!==e.password)throw Error("بيانات الدخول غير صحيحة");return this.currentUser=r,this.notifyListeners(),localStorage.setItem("auth_user",JSON.stringify(r)),r}async logout(){this.currentUser=null,localStorage.removeItem("auth_user"),this.notifyListeners()}getCurrentUser(){if(!this.currentUser){let e=localStorage.getItem("auth_user");if(e)try{this.currentUser=JSON.parse(e)}catch(e){localStorage.removeItem("auth_user")}}return this.currentUser}hasPermission(e){let r=this.getCurrentUser();return!!r&&(0,s._m)(r.role,e)}getAccessibleSections(){let e=this.getCurrentUser();return e?(0,s.JI)(e.role):[]}canCreateRole(e){var r;let t=this.getCurrentUser();return!!t&&((null==(r=({manager:["supervisor","courier"],supervisor:["courier"],courier:[]})[t.role])?void 0:r.includes(e))||!1)}addListener(e){this.listeners.push(e)}removeListener(e){this.listeners=this.listeners.filter(r=>r!==e)}notifyListeners(){this.listeners.forEach(e=>e(this.currentUser))}async updateProfile(e){if(!this.currentUser)throw Error("لم يتم تسجيل الدخول");return await new Promise(e=>setTimeout(e,500)),this.currentUser={...this.currentUser,...e},localStorage.setItem("auth_user",JSON.stringify(this.currentUser)),this.notifyListeners(),this.currentUser}async changePassword(e,r){if(!this.currentUser)throw Error("لم يتم تسجيل الدخول");if(await new Promise(e=>setTimeout(e,1e3)),"123456"!==e)throw Error("كلمة المرور الحالية غير صحيحة");console.log("تم تغيير كلمة المرور بنجاح")}async validateSession(){if(!this.getCurrentUser())return!1;try{return await new Promise(e=>setTimeout(e,200)),!0}catch(e){return await this.logout(),!1}}constructor(){this.currentUser=null,this.listeners=[]}}let i=new o}}]);