(()=>{var e={};e.id=872,e.ids=[872],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11309:(e,r,s)=>{Promise.resolve().then(s.bind(s,25478))},11997:e=>{"use strict";e.exports=require("punycode")},17676:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>h});var t=s(60687),a=s(43210),n=s(44493),i=s(29523),o=s(89667),d=s(23026),l=s(70334),c=s(8819),u=s(4780),p=s(85814),m=s.n(p),x=s(16189);function h(){let e=(0,x.useRouter)(),[r,s]=(0,a.useState)({name:"",username:"",password:"",phone:"",role:"courier"}),[p,h]=(0,a.useState)(!1),[g,v]=(0,a.useState)({}),[f,b]=(0,a.useState)(null),[j,y]=(0,a.useState)("idle"),w=()=>{(r.name.trim()||r.username.trim()||r.phone.trim())&&(y("saving"),localStorage.setItem("newUserDraft",JSON.stringify(r)),setTimeout(()=>{y("saved"),setTimeout(()=>y("idle"),2e3)},500))},N=e=>{let{name:r,value:t}=e.target;s(e=>({...e,[r]:t})),g[r]&&v(e=>({...e,[r]:""})),f&&clearTimeout(f),b(setTimeout(()=>{w()},2e3))},k=()=>{let e={};return r.name.trim()||(e.name="الاسم مطلوب"),r.username.trim()?r.username.length<3?e.username="اسم المستخدم يجب أن يكون 3 أحرف على الأقل":/^[a-zA-Z0-9_]+$/.test(r.username)||(e.username="اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط"):e.username="اسم المستخدم مطلوب",r.password.trim()?r.password.length<6&&(e.password="كلمة المرور يجب أن تكون 6 أحرف على الأقل"):e.password="كلمة المرور مطلوبة",r.phone.trim()?(0,u.zC)(r.phone)||(e.phone="رقم الهاتف غير صحيح (يجب أن يبدأ بـ 07)"):e.phone="رقم الهاتف مطلوب",r.role||(e.role="الدور مطلوب"),v(e),0===Object.keys(e).length},q=async s=>{if(s.preventDefault(),k()){h(!0);try{let s={id:Date.now().toString(),name:r.name.trim(),username:r.username.trim(),password:r.password.trim(),phone:r.phone.trim(),role:r.role,isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},t=JSON.parse(localStorage.getItem("users")||"[]");if(t.some(e=>e.username===s.username)){alert("اسم المستخدم موجود بالفعل! يرجى اختيار اسم مستخدم آخر."),h(!1);return}t.push(s),localStorage.setItem("users",JSON.stringify(t)),localStorage.removeItem("newUserDraft"),alert("تم إضافة الموظف بنجاح!"),e.push("/users")}catch(e){console.error("Error creating user:",e),alert("حدث خطأ أثناء إضافة الموظف. يرجى المحاولة مرة أخرى.")}finally{h(!1)}}};return(0,t.jsx)("div",{className:"min-h-screen bg-background p-4 md:p-6 lg:p-8",dir:"rtl",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-primary flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-8 w-8"}),"موظف جديد","saving"===j&&(0,t.jsx)("span",{className:"text-sm text-yellow-600 bg-yellow-100 px-2 py-1 rounded-full",children:"جاري الحفظ..."}),"saved"===j&&(0,t.jsx)("span",{className:"text-sm text-green-600 bg-green-100 px-2 py-1 rounded-full",children:"تم الحفظ تلقائياً"})]}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2",children:"إضافة موظف جديد إلى النظام - يتم الحفظ التلقائي للمسودة"})]}),(0,t.jsx)(m(),{href:"/users",children:(0,t.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),"العودة للموظفين"]})})]}),(0,t.jsxs)("form",{onSubmit:q,className:"space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{children:"المعلومات الشخصية"}),(0,t.jsx)(n.BT,{children:"البيانات الأساسية للموظف"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"الاسم الكامل *"}),(0,t.jsx)(o.p,{name:"name",value:r.name,onChange:N,placeholder:"أدخل الاسم الكامل",className:g.name?"border-red-500":""}),g.name&&(0,t.jsx)("p",{className:"text-red-500 text-xs mt-1",children:g.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"رقم الهاتف *"}),(0,t.jsx)(o.p,{name:"phone",value:r.phone,onChange:N,placeholder:"07xxxxxxxxx",className:g.phone?"border-red-500":""}),g.phone&&(0,t.jsx)("p",{className:"text-red-500 text-xs mt-1",children:g.phone})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اسم المستخدم *"}),(0,t.jsx)(o.p,{name:"username",type:"text",value:r.username,onChange:N,placeholder:"اسم المستخدم (أحرف وأرقام فقط)",className:g.username?"border-red-500":""}),g.username&&(0,t.jsx)("p",{className:"text-red-500 text-xs mt-1",children:g.username})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"كلمة المرور *"}),(0,t.jsx)(o.p,{name:"password",type:"password",value:r.password,onChange:N,placeholder:"كلمة المرور (6 أحرف على الأقل)",className:g.password?"border-red-500":""}),g.password&&(0,t.jsx)("p",{className:"text-red-500 text-xs mt-1",children:g.password})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{children:"الدور والصلاحيات"}),(0,t.jsx)(n.BT,{children:"تحديد دور الموظف في النظام"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"الدور *"}),(0,t.jsxs)("select",{name:"role",value:r.role,onChange:N,className:`w-full p-3 border rounded-md bg-background ${g.role?"border-red-500":"border-border"}`,children:[(0,t.jsx)("option",{value:"courier",children:"مندوب - استلام وتسليم الطلبات"}),(0,t.jsx)("option",{value:"supervisor",children:"متابع - متابعة الطلبات والإحصائيات"}),(0,t.jsx)("option",{value:"manager",children:"مدير - صلاحيات كاملة"})]}),g.role&&(0,t.jsx)("p",{className:"text-red-500 text-xs mt-1",children:g.role})]}),(0,t.jsxs)("div",{className:"p-4 bg-muted rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"وصف الدور:"}),"manager"===r.role&&(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"صلاحيات كاملة في النظام - إدارة جميع الأقسام والمستخدمين والطلبات والمحاسبة"}),"supervisor"===r.role&&(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"متابعة الطلبات والإحصائيات - عرض وتحديث الطلبات، الوصول للأرشيف والإحصائيات"}),"courier"===r.role&&(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"استلام وتسليم الطلبات - عرض الطلبات المسندة وتحديث حالتها فقط"})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,t.jsx)(m(),{href:"/users",children:(0,t.jsx)(i.$,{variant:"outline",disabled:p,children:"إلغاء"})}),(0,t.jsxs)(i.$,{type:"submit",disabled:p,className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4"}),p?"جاري الحفظ...":"حفظ الموظف"]})]})]})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23026:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},25478:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\users\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\users\\new\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,s)=>{"use strict";s.d(r,{$:()=>d});var t=s(60687);s(43210);var a=s(8730),n=s(24224),i=s(4780);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:s,asChild:n=!1,...d}){let l=n?a.DX:"button";return(0,t.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:s,className:e})),...d})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35277:(e,r,s)=>{Promise.resolve().then(s.bind(s,17676))},37366:e=>{"use strict";e.exports=require("dns")},44493:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i});var t=s(60687);s(43210);var a=s(4780);function n({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function i({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70334:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},77323:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var t=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(r,d);let l={children:["",{children:["users",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,25478)),"E:\\Marsal\\marsal\\src\\app\\users\\new\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\Marsal\\marsal\\src\\app\\users\\new\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/users/new/page",pathname:"/users/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},77972:e=>{"use strict";e.exports=require("https")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89667:(e,r,s)=>{"use strict";s.d(r,{p:()=>n});var t=s(60687);s(43210);var a=s(4780);function n({className:e,type:r,...s}){return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,437,839,814,690],()=>s(77323));module.exports=t})();