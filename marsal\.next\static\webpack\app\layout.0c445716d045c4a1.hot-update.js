"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2b6226e20205\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJFOlxcTWFyc2FsXFxtYXJzYWxcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjJiNjIyNmUyMDIwNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types_roles__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/roles */ \"(app-pages-browser)/./src/types/roles.ts\");\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n// نظام المصادقة والصلاحيات\n\n\n\nclass AuthService {\n    // تسجيل الدخول\n    async login(credentials) {\n        try {\n            // محاولة البحث عن المستخدم في قاعدة البيانات\n            let user = null;\n            try {\n                user = await _supabase__WEBPACK_IMPORTED_MODULE_2__.userService.getUserByUsername(credentials.username);\n            } catch (dbError) {\n                console.warn('Database not available, using fallback auth:', dbError);\n                // في حالة عدم توفر قاعدة البيانات، استخدم المستخدمين التجريبيين\n                return this.fallbackLogin(credentials);\n            }\n            if (!user) {\n                // إذا لم يوجد المستخدم في قاعدة البيانات، جرب المستخدمين التجريبيين\n                return this.fallbackLogin(credentials);\n            }\n            if (!user.is_active) {\n                throw new Error('الحساب غير مفعل');\n            }\n            // في الوضع التجريبي، نقبل كلمة المرور 123456 لجميع المستخدمين\n            if (credentials.password !== '123456') {\n                throw new Error('كلمة المرور غير صحيحة');\n            }\n            // تحويل بيانات المستخدم من Supabase إلى AuthUser\n            const authUser = {\n                id: user.id,\n                username: user.username,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                permissions: [],\n                locationId: user.location_id || 'main_center',\n                location: user.location || {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: user.created_by,\n                createdAt: new Date(user.created_at),\n                isActive: user.is_active,\n                accessToken: 'supabase_access_token',\n                refreshToken: 'supabase_refresh_token'\n            };\n            this.currentUser = authUser;\n            this.notifyListeners();\n            // حفظ في localStorage\n            if (true) {\n                localStorage.setItem('auth_user', JSON.stringify(authUser));\n            }\n            return authUser;\n        } catch (error) {\n            console.error('Login error:', error);\n            if (error instanceof Error) {\n                throw error;\n            }\n            throw new Error('فشل في تسجيل الدخول');\n        }\n    }\n    // تسجيل الدخول الاحتياطي (في حالة عدم توفر قاعدة البيانات)\n    async fallbackLogin(credentials) {\n        // بيانات تجريبية للمستخدمين\n        const mockUsers = {\n            'manager': {\n                id: 'manager_1',\n                username: 'manager',\n                name: 'مدير النظام',\n                phone: '07901234567',\n                role: 'manager',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'system',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token',\n                refreshToken: 'fallback_refresh_token'\n            },\n            'supervisor': {\n                id: 'supervisor_1',\n                username: 'supervisor',\n                name: 'متابع النظام',\n                phone: '07901234568',\n                role: 'supervisor',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'manager_1',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token_supervisor',\n                refreshToken: 'fallback_refresh_token_supervisor'\n            },\n            'courier': {\n                id: 'courier_1',\n                username: 'courier',\n                name: 'مندوب التوصيل',\n                phone: '07901234570',\n                role: 'courier',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'manager_1',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token_courier',\n                refreshToken: 'fallback_refresh_token_courier'\n            }\n        };\n        const user = mockUsers[credentials.username];\n        if (!user || credentials.password !== '123456') {\n            throw new Error('بيانات الدخول غير صحيحة');\n        }\n        this.currentUser = user;\n        this.notifyListeners();\n        // حفظ في localStorage\n        if (true) {\n            localStorage.setItem('auth_user', JSON.stringify(user));\n        }\n        return user;\n    }\n    // تسجيل الخروج\n    async logout() {\n        this.currentUser = null;\n        if (true) {\n            localStorage.removeItem('auth_user');\n        }\n        this.notifyListeners();\n    }\n    // الحصول على المستخدم الحالي\n    getCurrentUser() {\n        if (!this.currentUser && \"object\" !== 'undefined') {\n            const stored = localStorage.getItem('auth_user');\n            if (stored) {\n                try {\n                    this.currentUser = JSON.parse(stored);\n                } catch (error) {\n                    localStorage.removeItem('auth_user');\n                }\n            }\n        }\n        return this.currentUser;\n    }\n    // التحقق من الصلاحية\n    hasPermission(permission) {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        return (0,_types_roles__WEBPACK_IMPORTED_MODULE_1__.hasPermission)(user.role, permission);\n    }\n    // الحصول على الأقسام المتاحة\n    getAccessibleSections() {\n        const user = this.getCurrentUser();\n        if (!user) return [];\n        return (0,_types_roles__WEBPACK_IMPORTED_MODULE_1__.getAccessibleSections)(user.role);\n    }\n    // التحقق من إمكانية إنشاء دور معين\n    canCreateRole(targetRole) {\n        var _rolePermissions_user_role;\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        const rolePermissions = {\n            'manager': [\n                'supervisor',\n                'courier'\n            ],\n            'supervisor': [\n                'courier'\n            ],\n            'courier': []\n        };\n        return ((_rolePermissions_user_role = rolePermissions[user.role]) === null || _rolePermissions_user_role === void 0 ? void 0 : _rolePermissions_user_role.includes(targetRole)) || false;\n    }\n    // إضافة مستمع للتغييرات\n    addListener(listener) {\n        this.listeners.push(listener);\n    }\n    // إزالة مستمع\n    removeListener(listener) {\n        this.listeners = this.listeners.filter((l)=>l !== listener);\n    }\n    notifyListeners() {\n        this.listeners.forEach((listener)=>listener(this.currentUser));\n    }\n    // تحديث بيانات المستخدم\n    async updateProfile(data) {\n        if (!this.currentUser) {\n            throw new Error('لم يتم تسجيل الدخول');\n        }\n        // محاكاة API call\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        this.currentUser = {\n            ...this.currentUser,\n            ...data\n        };\n        if (true) {\n            localStorage.setItem('auth_user', JSON.stringify(this.currentUser));\n        }\n        this.notifyListeners();\n        return this.currentUser;\n    }\n    // تغيير كلمة المرور\n    async changePassword(currentPassword, newPassword) {\n        if (!this.currentUser) {\n            throw new Error('لم يتم تسجيل الدخول');\n        }\n        // محاكاة API call\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // في التطبيق الحقيقي، سيتم التحقق من كلمة المرور الحالية\n        if (currentPassword !== '123456') {\n            throw new Error('كلمة المرور الحالية غير صحيحة');\n        }\n        // تحديث كلمة المرور (في التطبيق الحقيقي)\n        console.log('تم تغيير كلمة المرور بنجاح');\n    }\n    // التحقق من صحة الجلسة\n    async validateSession() {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        try {\n            // محاكاة التحقق من صحة الجلسة\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            return true;\n        } catch (error) {\n            await this.logout();\n            return false;\n        }\n    }\n    constructor(){\n        this.currentUser = null;\n        this.listeners = [];\n    }\n}\nconst authService = new AuthService();\n// Hook للاستخدام في React\nfunction useAuth() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(authService.getCurrentUser());\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const listener = {\n                \"useAuth.useEffect.listener\": (newUser)=>setUser(newUser)\n            }[\"useAuth.useEffect.listener\"];\n            authService.addListener(listener);\n            return ({\n                \"useAuth.useEffect\": ()=>authService.removeListener(listener)\n            })[\"useAuth.useEffect\"];\n        }\n    }[\"useAuth.useEffect\"], []);\n    return {\n        user,\n        login: authService.login.bind(authService),\n        logout: authService.logout.bind(authService),\n        hasPermission: authService.hasPermission.bind(authService),\n        getAccessibleSections: authService.getAccessibleSections.bind(authService),\n        canCreateRole: authService.canCreateRole.bind(authService),\n        updateProfile: authService.updateProfile.bind(authService),\n        changePassword: authService.changePassword.bind(authService),\n        validateSession: authService.validateSession.bind(authService)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ })

});