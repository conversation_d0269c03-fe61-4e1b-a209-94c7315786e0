SKIP_TYPE_CHECK=true
TSC_COMPILE_ON_ERROR=true
NEXT_TELEMETRY_DISABLED=1
DISABLE_ESLINT_PLUGIN=true
NEXT_IGNORE_TYPE_ERRORS=true

# Firebase Configuration for Marsal Delivery App
# يجب تحديث هذه القيم من Firebase Console

# Firebase Project Configuration - مشروع حقيقي
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDUcdNPsyxpWHOw-vxUaw2kmnmv5SXWflY
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=marsal-delivery-app.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=marsal-delivery-app
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=marsal-delivery-app.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=403753087327
NEXT_PUBLIC_FIREBASE_APP_ID=1:403753087327:web:1ed573c427309db39686ea
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-PMLRQKR9NB

# Firebase Emulator Settings (for development)
NEXT_PUBLIC_USE_FIREBASE_EMULATOR=false
NEXT_PUBLIC_FIREBASE_EMULATOR_HOST=localhost
NEXT_PUBLIC_FIRESTORE_EMULATOR_PORT=8080
NEXT_PUBLIC_AUTH_EMULATOR_PORT=9099
NEXT_PUBLIC_STORAGE_EMULATOR_PORT=9199

# App Configuration
NEXT_PUBLIC_APP_NAME=Marsal Delivery App
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_ENVIRONMENT=production

# Database Configuration
NEXT_PUBLIC_DATABASE_TYPE=firebase
NEXT_PUBLIC_ENABLE_OFFLINE_MODE=true
NEXT_PUBLIC_ENABLE_REALTIME_UPDATES=true

# Security Configuration
NEXT_PUBLIC_ENABLE_AUTH_PERSISTENCE=true
NEXT_PUBLIC_SESSION_TIMEOUT=86400000
NEXT_PUBLIC_MAX_LOGIN_ATTEMPTS=5

# Features Configuration
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_FILE_UPLOAD=true
NEXT_PUBLIC_MAX_FILE_SIZE=5242880
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Logging Configuration
NEXT_PUBLIC_LOG_LEVEL=info
NEXT_PUBLIC_ENABLE_CONSOLE_LOGS=true
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true

# Demo Mode Settings
NEXT_PUBLIC_DEMO_MODE=true

# Firebase Configuration for Marsal Delivery App
# يجب تحديث هذه القيم من Firebase Console

# Firebase Project Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDemoKeyForMarsalDeliveryApp123456789
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=marsal-delivery-app.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=marsal-delivery-app
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=marsal-delivery-app.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789012
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789012:web:abcdef123456789012345
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-XXXXXXXXXX

# Firebase Emulator Settings (for development)
NEXT_PUBLIC_USE_FIREBASE_EMULATOR=false
NEXT_PUBLIC_FIREBASE_EMULATOR_HOST=localhost
NEXT_PUBLIC_FIRESTORE_EMULATOR_PORT=8080
NEXT_PUBLIC_AUTH_EMULATOR_PORT=9099
NEXT_PUBLIC_STORAGE_EMULATOR_PORT=9199

# App Configuration
NEXT_PUBLIC_APP_NAME=Marsal Delivery App
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_ENVIRONMENT=production

# Database Configuration
NEXT_PUBLIC_DATABASE_TYPE=firebase
NEXT_PUBLIC_ENABLE_OFFLINE_MODE=true
NEXT_PUBLIC_ENABLE_REALTIME_UPDATES=true

# Security Configuration
NEXT_PUBLIC_ENABLE_AUTH_PERSISTENCE=true
NEXT_PUBLIC_SESSION_TIMEOUT=86400000
NEXT_PUBLIC_MAX_LOGIN_ATTEMPTS=5

# Features Configuration
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_FILE_UPLOAD=true
NEXT_PUBLIC_MAX_FILE_SIZE=5242880
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Logging Configuration
NEXT_PUBLIC_LOG_LEVEL=info
NEXT_PUBLIC_ENABLE_CONSOLE_LOGS=true
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true