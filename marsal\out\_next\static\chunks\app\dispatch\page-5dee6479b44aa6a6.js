(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[137],{735:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>y});var t=r(5155),i=r(2115),a=r(6695),l=r(285),n=r(2523),c=r(7192),d=r(9799),o=r(7924),m=r(7108),x=r(4416),h=r(7580),u=r(1007),p=r(646),g=r(2138),j=r(9086),f=r(9434),N=r(4044);let b=[{id:"1",name:"علي حسين",phone:"07901234567",activeOrders:3},{id:"2",name:"حسام محمد",phone:"07801234567",activeOrders:5},{id:"3",name:"أحمد علي",phone:"07701234567",activeOrders:2}],v=[{id:"1",trackingNumber:"MRS004",recipientName:"سارة أحمد",recipientPhone:"07901234567",recipientAddress:"بغداد - الكرادة",amount:85e3,createdAt:"2024-01-15"},{id:"2",trackingNumber:"MRS005",recipientName:"محمد حسن",recipientPhone:"07801234567",recipientAddress:"بغداد - الجادرية",amount:12e4,createdAt:"2024-01-15"},{id:"3",trackingNumber:"MRS006",recipientName:"فاطمة علي",recipientPhone:"07701234567",recipientAddress:"بغداد - المنصور",amount:65e3,createdAt:"2024-01-15"}];function y(){let[e,s]=(0,i.useState)([]),[r,y]=(0,i.useState)([]),[w,A]=(0,i.useState)([]),[k,C]=(0,i.useState)(""),[S,Z]=(0,i.useState)(!1),[B,E]=(0,i.useState)(""),[R,P]=(0,i.useState)([]),[W,_]=(0,i.useState)(!1),z=(0,i.useRef)(null);(0,i.useEffect)(()=>{M(),O()},[]),(0,i.useEffect)(()=>{B.trim()?T():(P([]),_(!1))},[B]);let M=async()=>{Z(!0);try{let e=await j.pv.getByStatus("pending");s(e)}catch(e){console.error("Error loading unassigned orders:",e),s(v)}finally{Z(!1)}},O=async()=>{try{let e=JSON.parse(localStorage.getItem("users")||"[]").filter(e=>"courier"===e.role&&!1!==e.isActive);if(e.length>0){let s=e.map(e=>({id:e.id,name:e.name,phone:e.phone,username:e.username,role:e.role,isActive:!1!==e.isActive}));y(s)}else y(b)}catch(e){console.error("Error loading couriers:",e),y(b)}},T=async()=>{try{let e=(await j.pv.search(B)).filter(e=>"pending"===e.status);P(e),_(!0)}catch(e){console.error("Error searching orders:",e),P(v.filter(e=>e.trackingNumber.toLowerCase().includes(B.toLowerCase())||e.recipientPhone.includes(B))),_(!0)}},$=e=>{q(e.id),E(""),_(!1)},q=e=>{A(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},I=async()=>{if(0===w.length||!k)return void alert("يرجى اختيار طلبات ومندوب");Z(!0);try{await j.pv.assignToCourier(w,k,"dispatcher"),alert("تم إسناد ".concat(w.length," طلب بنجاح")),A([]),C(""),M()}catch(e){console.error("Error assigning orders:",e),alert("حدث خطأ أثناء إسناد الطلبات")}finally{Z(!1)}},L=e=>{A(s=>s.filter(s=>s!==e))},U=e.filter(e=>!w.includes(e.id)),F=()=>e.filter(e=>w.includes(e.id)),J=r.find(e=>e.id===k);return(0,t.jsxs)(N.A,{requiredSection:"dispatch",children:[(0,t.jsx)(c.A,{}),(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 animated-bg p-6",dir:"rtl",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-3xl shadow-2xl mb-4",children:(0,t.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,t.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2",children:"إسناد الطلبات"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-lg",children:"توزيع الطلبات الجديدة على المندوبين"})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsxs)(a.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"h-5 w-5"}),"البحث عن الطلبات"]}),(0,t.jsx)(a.BT,{children:"ابحث عن الطلبات برقم الوصل أو رقم الموبايل واضغط Enter للتحديد التلقائي"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.p,{ref:z,placeholder:"ابحث برقم الوصل أو رقم الموبايل...",value:B,onChange:e=>E(e.target.value),onKeyPress:e=>{"Enter"===e.key&&R.length>0&&(q(R[0].id),E(""),_(!1),z.current&&z.current.blur())},className:"w-full"}),W&&R.length>0&&(0,t.jsx)("div",{className:"absolute top-full left-0 right-0 bg-background border border-border rounded-md mt-1 shadow-lg z-10 max-h-60 overflow-y-auto",children:R.map(e=>(0,t.jsx)("div",{className:"p-3 hover:bg-accent cursor-pointer border-b border-border last:border-b-0",onClick:()=>$(e),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:e.trackingNumber}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.recipientName}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:e.recipientPhone})]}),(0,t.jsx)("div",{className:"text-left",children:(0,t.jsx)("p",{className:"font-semibold",children:(0,f.vv)(e.amount)})})]})},e.id))})]})})]}),w.length>0&&(0,t.jsxs)(a.Zp,{className:"border-blue-200 bg-blue-50/50",children:[(0,t.jsx)(a.aR,{children:(0,t.jsxs)(a.ZB,{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"h-5 w-5"}),"الطلبات المحددة (",w.length,")"]}),(0,t.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>A([]),children:[(0,t.jsx)(x.A,{className:"h-4 w-4 ml-1"}),"إلغاء التحديد"]})]})}),(0,t.jsx)(a.Wu,{children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:F().map(e=>(0,t.jsx)("div",{className:"p-3 bg-background border rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium",children:e.trackingNumber}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.recipientName}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:e.recipientPhone})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-sm font-semibold",children:(0,f.vv)(e.amount)}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>L(e.id),children:(0,t.jsx)(x.A,{className:"h-3 w-3"})})]})]})},e.id))})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsxs)(a.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5"}),"اختيار المندوب"]}),(0,t.jsx)(a.BT,{children:"اختر المندوب الذي تريد إسناد الطلبات إليه"})]}),(0,t.jsx)(a.Wu,{className:"space-y-4",children:r.map(e=>(0,t.jsx)("div",{className:"p-4 border rounded-lg cursor-pointer transition-colors ".concat(k===e.id?"border-primary bg-primary/5":"border-border hover:border-primary/50"),onClick:()=>C(e.id),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center",children:(0,t.jsx)(u.A,{className:"h-5 w-5 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.phone})]})]}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"الطلبات النشطة"}),(0,t.jsx)("p",{className:"font-semibold",children:"0"})]}),k===e.id&&(0,t.jsx)(p.A,{className:"h-5 w-5 text-primary"})]})},e.id))})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsxs)(a.ZB,{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"h-5 w-5"}),"الطلبات غير المسندة (",U.length,")"]}),(0,t.jsx)("div",{className:"flex gap-2",children:(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{w.length===U.length?A([]):A(U.map(e=>e.id))},children:w.length===U.length?"إلغاء تحديد الكل":"تحديد الكل"})})]}),(0,t.jsx)(a.BT,{children:"اختر الطلبات التي تريد إسنادها للمندوب المختار"})]}),(0,t.jsx)(a.Wu,{children:S?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,t.jsx)("p",{className:"mt-2 text-muted-foreground",children:"جاري التحميل..."})]}):0===U.length?(0,t.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,t.jsx)(m.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,t.jsx)("p",{children:"لا توجد طلبات غير مسندة"})]}):(0,t.jsx)("div",{className:"space-y-4",children:U.map(e=>(0,t.jsx)("div",{className:"p-4 border rounded-lg cursor-pointer transition-colors ".concat(w.includes(e.id)?"border-primary bg-primary/5":"border-border hover:border-primary/50"),onClick:()=>q(e.id),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)("span",{className:"font-semibold",children:e.trackingNumber}),w.includes(e.id)&&(0,t.jsx)(p.A,{className:"h-4 w-4 text-primary"})]}),(0,t.jsx)("p",{className:"text-sm font-medium",children:e.recipientName}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.recipientPhone}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.recipientAddress}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["تاريخ الإنشاء: ",(0,f.Yq)(e.createdAt)]})]}),(0,t.jsx)("div",{className:"text-left",children:(0,t.jsx)("p",{className:"font-semibold",children:(0,f.vv)(e.amount)})})]})},e.id))})})]})]}),k&&w.length>0&&(0,t.jsx)(a.Zp,{className:"border-primary bg-primary/5",children:(0,t.jsx)(a.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"font-medium",children:["إسناد ",w.length," طلب إلى"," ",null==J?void 0:J.name]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"تأكد من صحة الاختيار قبل الإسناد"}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["إجمالي المبلغ: ",(0,f.vv)(F().reduce((e,s)=>e+s.amount,0))]})})]}),(0,t.jsxs)(l.$,{onClick:I,disabled:S,className:"flex items-center gap-2",children:[S?"جاري الإسناد...":"تأكيد الإسناد",(0,t.jsx)(g.A,{className:"h-4 w-4"})]})]})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,t.jsx)(a.Zp,{children:(0,t.jsxs)(a.Wu,{className:"p-6 text-center",children:[(0,t.jsx)(m.A,{className:"h-8 w-8 text-blue-600 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:e.length}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"طلبات غير مسندة"})]})}),(0,t.jsx)(a.Zp,{children:(0,t.jsxs)(a.Wu,{className:"p-6 text-center",children:[(0,t.jsx)(h.A,{className:"h-8 w-8 text-green-600 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:r.length}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"مندوبين متاحين"})]})}),(0,t.jsx)(a.Zp,{children:(0,t.jsxs)(a.Wu,{className:"p-6 text-center",children:[(0,t.jsx)(p.A,{className:"h-8 w-8 text-purple-600 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:w.length}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"طلبات محددة"})]})}),(0,t.jsx)(a.Zp,{children:(0,t.jsxs)(a.Wu,{className:"p-6 text-center",children:[(0,t.jsx)(d.A,{className:"h-8 w-8 text-orange-600 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:+!!k}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"مندوب مختار"})]})})]})]})})]})}},1243:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2138:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},3649:(e,s,r)=>{Promise.resolve().then(r.bind(r,735))},4044:(e,s,r)=>{"use strict";r.d(s,{A:()=>x});var t=r(5155),i=r(2044),a=r(9599),l=r(5695),n=r(2115),c=r(6695),d=r(285),o=r(1243),m=r(2138);function x(e){let{children:s,requiredSection:r,fallbackPath:x="/"}=e,{user:h,isAuthenticated:u}=(0,i.A)(),p=(0,l.useRouter)();return((0,n.useEffect)(()=>{if(!u)return void p.push("/login");(null==h?void 0:h.role)&&!(0,a._m)(h.role,r)&&p.push(x)},[h,u,r,x,p]),u&&h)?(0,a._m)(h.role,r)?(0,t.jsx)(t.Fragment,{children:s}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center p-4",dir:"rtl",children:(0,t.jsxs)(c.Zp,{className:"max-w-md w-full shadow-xl border-2 border-red-200",children:[(0,t.jsxs)(c.aR,{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-full shadow-lg mb-4 mx-auto",children:(0,t.jsx)(o.A,{className:"h-8 w-8 text-white"})}),(0,t.jsx)(c.ZB,{className:"text-2xl text-red-600",children:"غير مصرح بالدخول"}),(0,t.jsx)(c.BT,{className:"text-lg text-red-500",children:"ليس لديك صلاحية للوصول إلى هذا القسم"})]}),(0,t.jsxs)(c.Wu,{className:"text-center space-y-4",children:[(0,t.jsxs)("p",{className:"text-gray-600 leading-relaxed",children:["عذراً، دورك الحالي (","manager"===h.role?"مدير":"supervisor"===h.role?"متابع":"مندوب",") لا يسمح بالوصول إلى هذا القسم."]}),(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-sm text-yellow-800",children:"إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع المدير لمراجعة صلاحياتك."})}),(0,t.jsxs)(d.$,{onClick:()=>p.push(x),className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:["العودة إلى الصفحة الرئيسية",(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"})]})]})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse",children:(0,t.jsx)("div",{className:"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"مرسال"}),(0,t.jsx)("p",{className:"text-gray-500",children:"جاري التحقق من الصلاحيات..."})]})})}},9599:(e,s,r)=>{"use strict";r.d(s,{BC:()=>l,_m:()=>i});let t={manager:["orders","dispatch","returns","accounting","statistics","archive","users","import-export","notifications","settings"],supervisor:["orders","statistics","archive","users","notifications","settings"],courier:["orders","archive","notifications","statistics"]};function i(e,s){var r;return(null==(r=t[e])?void 0:r.includes(s))||!1}let a=[{id:"orders",href:"/orders",label:"إدارة الطلبات",description:"عرض ومتابعة وتعديل الطلبات",icon:"Package",color:"from-blue-500 to-blue-600",roles:["manager","supervisor","courier"]},{id:"dispatch",href:"/dispatch",label:"إسناد الطلبات",description:"توزيع الطلبات على المندوبين",icon:"TruckIcon",color:"from-green-500 to-emerald-600",roles:["manager"]},{id:"returns",href:"/returns",label:"إدارة الرواجع",description:"استلام الطلبات الراجعة",icon:"RotateCcw",color:"from-orange-500 to-amber-600",roles:["manager"]},{id:"accounting",href:"/accounting",label:"المحاسبة",description:"التسويات المالية مع المندوبين",icon:"Calculator",color:"from-purple-500 to-violet-600",roles:["manager"]},{id:"statistics",href:"/statistics",label:"الإحصائيات",description:"التقارير والإحصائيات التفصيلية",icon:"BarChart3",color:"from-cyan-500 to-blue-600",roles:["manager","supervisor"]},{id:"archive",href:"/archive",label:"الأرشيف",description:"السجلات المنتهية",icon:"Archive",color:"from-gray-500 to-slate-600",roles:["manager","supervisor","courier"]},{id:"users",href:"/users",label:"إدارة الموظفين",description:"إدارة حسابات المستخدمين",icon:"Users",color:"from-indigo-500 to-purple-600",roles:["manager","supervisor"]},{id:"import-export",href:"/import-export",label:"استيراد وتصدير",description:"عمليات مجمعة على الطلبات",icon:"Upload",color:"from-teal-500 to-cyan-600",roles:["manager"]},{id:"notifications",href:"/notifications",label:"الإشعارات",description:"التنبيهات والتحديثات",icon:"Bell",color:"from-yellow-500 to-orange-600",roles:["manager","supervisor","courier"]},{id:"settings",href:"/settings",label:"الإعدادات",description:"تخصيص التطبيق",icon:"Settings",color:"from-pink-500 to-rose-600",roles:["manager","supervisor","courier"]}];function l(e){return a.filter(s=>s.roles.includes(e))}}},e=>{var s=s=>e(e.s=s);e.O(0,[992,811,455,134,568,874,647,533,345,44,192,103,441,684,358],()=>s(3649)),_N_E=e.O()}]);