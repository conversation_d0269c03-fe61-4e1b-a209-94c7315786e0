"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1124],{5696:(t,e,n)=>{var r=n(19106),o=n(16148),i=n(66950),a=n(38972),c=n(71534),s=n(438),u=n(64510),d=n(10997),l=n(38988),h=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),p=function(t){function e(n,r){void 0===r&&(r=!1);var o=t.call(this,n.width,n.height)||this;return o.canvas=n,o.tempCanvasElement=null,o.buffer=e.makeBufferFromCanvasImageData(n,r),o}return h(e,t),e.makeBufferFromCanvasImageData=function(t,n){void 0===n&&(n=!1);var r=t.getContext("2d").getImageData(0,0,t.width,t.height);return e.toGrayscaleBuffer(r.data,t.width,t.height,n)},e.toGrayscaleBuffer=function(t,n,r,o){void 0===o&&(o=!1);var i=new Uint8ClampedArray(n*r);if(e.FRAME_INDEX=!e.FRAME_INDEX,e.FRAME_INDEX||!o)for(var a=0,c=0,s=t.length;a<s;a+=4,c++){var u=void 0,d=t[a+3];if(0===d)u=255;else{var l=t[a],h=t[a+1],p=t[a+2];u=306*l+601*h+117*p+512>>10}i[c]=u}else for(var a=0,c=0,f=t.length;a<f;a+=4,c++){var u=void 0,d=t[a+3];if(0===d)u=255;else{var l=t[a],h=t[a+1],p=t[a+2];u=306*l+601*h+117*p+512>>10}i[c]=255-u}return i},e.prototype.getRow=function(t,e){if(t<0||t>=this.getHeight())throw new l.A("Requested row is outside the image: "+t);var n=this.getWidth(),r=t*n;return null===e?e=this.buffer.slice(r,r+n):(e.length<n&&(e=new Uint8ClampedArray(n)),e.set(this.buffer.slice(r,r+n))),e},e.prototype.getMatrix=function(){return this.buffer},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(e,n,r,o){return t.prototype.crop.call(this,e,n,r,o),this},e.prototype.isRotateSupported=function(){return!0},e.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},e.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},e.prototype.getTempCanvasElement=function(){if(null===this.tempCanvasElement){var t=this.canvas.ownerDocument.createElement("canvas");t.width=this.canvas.width,t.height=this.canvas.height,this.tempCanvasElement=t}return this.tempCanvasElement},e.prototype.rotate=function(t){var n=this.getTempCanvasElement(),r=n.getContext("2d"),o=t*e.DEGREE_TO_RADIANS,i=this.canvas.width,a=this.canvas.height,c=Math.ceil(Math.abs(Math.cos(o))*i+Math.abs(Math.sin(o))*a),s=Math.ceil(Math.abs(Math.sin(o))*i+Math.abs(Math.cos(o))*a);return n.width=c,n.height=s,r.translate(c/2,s/2),r.rotate(o),r.drawImage(this.canvas,-(i/2),-(a/2)),this.buffer=e.makeBufferFromCanvasImageData(n),this},e.prototype.invert=function(){return new u.A(this)},e.DEGREE_TO_RADIANS=Math.PI/180,e.FRAME_INDEX=!0,e}(d.A),f=function(){function t(t,e,n){this.deviceId=t,this.label=e,this.kind="videoinput",this.groupId=n||void 0}return t.prototype.toJSON=function(){return{kind:this.kind,groupId:this.groupId,deviceId:this.deviceId,label:this.label}},t}(),v=function(t,e,n,r){return new(n||(n=Promise))(function(o,i){function a(t){try{s(r.next(t))}catch(t){i(t)}}function c(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof n?e:new n(function(t){t(e)})).then(a,c)}s((r=r.apply(t,e||[])).next())})},m=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){var s=[i,c];if(n)throw TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}}},y=function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},g=function(){function t(t,e,n){void 0===e&&(e=500),this.reader=t,this.timeBetweenScansMillis=e,this._hints=n,this._stopContinuousDecode=!1,this._stopAsyncDecode=!1,this._timeBetweenDecodingAttempts=0}return Object.defineProperty(t.prototype,"hasNavigator",{get:function(){return"undefined"!=typeof navigator},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isMediaDevicesSuported",{get:function(){return this.hasNavigator&&!!navigator.mediaDevices},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"canEnumerateDevices",{get:function(){return!!(this.isMediaDevicesSuported&&navigator.mediaDevices.enumerateDevices)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"timeBetweenDecodingAttempts",{get:function(){return this._timeBetweenDecodingAttempts},set:function(t){this._timeBetweenDecodingAttempts=t<0?0:t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"hints",{get:function(){return this._hints},set:function(t){this._hints=t||null},enumerable:!1,configurable:!0}),t.prototype.listVideoInputDevices=function(){return v(this,void 0,void 0,function(){var t,e,n,r,o,i,a,c,s,u,d,l;return m(this,function(h){switch(h.label){case 0:if(!this.hasNavigator)throw Error("Can't enumerate devices, navigator is not present.");if(!this.canEnumerateDevices)throw Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:t=h.sent(),e=[];try{for(r=(n=y(t)).next();!r.done;r=n.next())o=r.value,i="video"===o.kind?"videoinput":o.kind,"videoinput"===i&&(a=o.deviceId||o.id,c=o.label||"Video device "+(e.length+1),s=o.groupId,u={deviceId:a,label:c,kind:i,groupId:s},e.push(u))}catch(t){d={error:t}}finally{try{r&&!r.done&&(l=n.return)&&l.call(n)}finally{if(d)throw d.error}}return[2,e]}})})},t.prototype.getVideoInputDevices=function(){return v(this,void 0,void 0,function(){return m(this,function(t){switch(t.label){case 0:return[4,this.listVideoInputDevices()];case 1:return[2,t.sent().map(function(t){return new f(t.deviceId,t.label)})]}})})},t.prototype.findDeviceById=function(t){return v(this,void 0,void 0,function(){var e;return m(this,function(n){switch(n.label){case 0:return[4,this.listVideoInputDevices()];case 1:if(!(e=n.sent()))return[2,null];return[2,e.find(function(e){return e.deviceId===t})]}})})},t.prototype.decodeFromInputVideoDevice=function(t,e){return v(this,void 0,void 0,function(){return m(this,function(n){switch(n.label){case 0:return[4,this.decodeOnceFromVideoDevice(t,e)];case 1:return[2,n.sent()]}})})},t.prototype.decodeOnceFromVideoDevice=function(t,e){return v(this,void 0,void 0,function(){var n,r;return m(this,function(n){switch(n.label){case 0:return this.reset(),r={video:t?{deviceId:{exact:t}}:{facingMode:"environment"}},[4,this.decodeOnceFromConstraints(r,e)];case 1:return[2,n.sent()]}})})},t.prototype.decodeOnceFromConstraints=function(t,e){return v(this,void 0,void 0,function(){var n;return m(this,function(r){switch(r.label){case 0:return[4,navigator.mediaDevices.getUserMedia(t)];case 1:return n=r.sent(),[4,this.decodeOnceFromStream(n,e)];case 2:return[2,r.sent()]}})})},t.prototype.decodeOnceFromStream=function(t,e){return v(this,void 0,void 0,function(){var n;return m(this,function(r){switch(r.label){case 0:return this.reset(),[4,this.attachStreamToVideo(t,e)];case 1:return n=r.sent(),[4,this.decodeOnce(n)];case 2:return[2,r.sent()]}})})},t.prototype.decodeFromInputVideoDeviceContinuously=function(t,e,n){return v(this,void 0,void 0,function(){return m(this,function(r){switch(r.label){case 0:return[4,this.decodeFromVideoDevice(t,e,n)];case 1:return[2,r.sent()]}})})},t.prototype.decodeFromVideoDevice=function(t,e,n){return v(this,void 0,void 0,function(){var r,o;return m(this,function(r){switch(r.label){case 0:return o={video:t?{deviceId:{exact:t}}:{facingMode:"environment"}},[4,this.decodeFromConstraints(o,e,n)];case 1:return[2,r.sent()]}})})},t.prototype.decodeFromConstraints=function(t,e,n){return v(this,void 0,void 0,function(){var r;return m(this,function(o){switch(o.label){case 0:return[4,navigator.mediaDevices.getUserMedia(t)];case 1:return r=o.sent(),[4,this.decodeFromStream(r,e,n)];case 2:return[2,o.sent()]}})})},t.prototype.decodeFromStream=function(t,e,n){return v(this,void 0,void 0,function(){var r;return m(this,function(o){switch(o.label){case 0:return this.reset(),[4,this.attachStreamToVideo(t,e)];case 1:return r=o.sent(),[4,this.decodeContinuously(r,n)];case 2:return[2,o.sent()]}})})},t.prototype.stopAsyncDecode=function(){this._stopAsyncDecode=!0},t.prototype.stopContinuousDecode=function(){this._stopContinuousDecode=!0},t.prototype.attachStreamToVideo=function(t,e){return v(this,void 0,void 0,function(){var n;return m(this,function(r){switch(r.label){case 0:return n=this.prepareVideoElement(e),this.addVideoSource(n,t),this.videoElement=n,this.stream=t,[4,this.playVideoOnLoadAsync(n)];case 1:return r.sent(),[2,n]}})})},t.prototype.playVideoOnLoadAsync=function(t){var e=this;return new Promise(function(n,r){return e.playVideoOnLoad(t,function(){return n()})})},t.prototype.playVideoOnLoad=function(t,e){var n=this;this.videoEndedListener=function(){return n.stopStreams()},this.videoCanPlayListener=function(){return n.tryPlayVideo(t)},t.addEventListener("ended",this.videoEndedListener),t.addEventListener("canplay",this.videoCanPlayListener),t.addEventListener("playing",e),this.tryPlayVideo(t)},t.prototype.isVideoPlaying=function(t){return t.currentTime>0&&!t.paused&&!t.ended&&t.readyState>2},t.prototype.tryPlayVideo=function(t){return v(this,void 0,void 0,function(){return m(this,function(e){switch(e.label){case 0:if(this.isVideoPlaying(t))return console.warn("Trying to play video that is already playing."),[2];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,t.play()];case 2:return e.sent(),[3,4];case 3:return e.sent(),console.warn("It was not possible to play the video."),[3,4];case 4:return[2]}})})},t.prototype.getMediaElement=function(t,e){var n=document.getElementById(t);if(!n)throw new r.A("element with id '"+t+"' not found");if(n.nodeName.toLowerCase()!==e.toLowerCase())throw new r.A("element with id '"+t+"' must be an "+e+" element");return n},t.prototype.decodeFromImage=function(t,e){if(!t&&!e)throw new r.A("either imageElement with a src set or an url must be provided");return e&&!t?this.decodeFromImageUrl(e):this.decodeFromImageElement(t)},t.prototype.decodeFromVideo=function(t,e){if(!t&&!e)throw new r.A("Either an element with a src set or an URL must be provided");return e&&!t?this.decodeFromVideoUrl(e):this.decodeFromVideoElement(t)},t.prototype.decodeFromVideoContinuously=function(t,e,n){if(void 0===t&&void 0===e)throw new r.A("Either an element with a src set or an URL must be provided");return e&&!t?this.decodeFromVideoUrlContinuously(e,n):this.decodeFromVideoElementContinuously(t,n)},t.prototype.decodeFromImageElement=function(t){if(!t)throw new r.A("An image element must be provided.");this.reset();var e,n=this.prepareImageElement(t);return this.imageElement=n,this.isImageLoaded(n)?this.decodeOnce(n,!1,!0):this._decodeOnLoadImage(n)},t.prototype.decodeFromVideoElement=function(t){var e=this._decodeFromVideoElementSetup(t);return this._decodeOnLoadVideo(e)},t.prototype.decodeFromVideoElementContinuously=function(t,e){var n=this._decodeFromVideoElementSetup(t);return this._decodeOnLoadVideoContinuously(n,e)},t.prototype._decodeFromVideoElementSetup=function(t){if(!t)throw new r.A("A video element must be provided.");this.reset();var e=this.prepareVideoElement(t);return this.videoElement=e,e},t.prototype.decodeFromImageUrl=function(t){if(!t)throw new r.A("An URL must be provided.");this.reset();var e=this.prepareImageElement();this.imageElement=e;var n=this._decodeOnLoadImage(e);return e.src=t,n},t.prototype.decodeFromVideoUrl=function(t){if(!t)throw new r.A("An URL must be provided.");this.reset();var e=this.prepareVideoElement(),n=this.decodeFromVideoElement(e);return e.src=t,n},t.prototype.decodeFromVideoUrlContinuously=function(t,e){if(!t)throw new r.A("An URL must be provided.");this.reset();var n=this.prepareVideoElement(),o=this.decodeFromVideoElementContinuously(n,e);return n.src=t,o},t.prototype._decodeOnLoadImage=function(t){var e=this;return new Promise(function(n,r){e.imageLoadedListener=function(){return e.decodeOnce(t,!1,!0).then(n,r)},t.addEventListener("load",e.imageLoadedListener)})},t.prototype._decodeOnLoadVideo=function(t){return v(this,void 0,void 0,function(){return m(this,function(e){switch(e.label){case 0:return[4,this.playVideoOnLoadAsync(t)];case 1:return e.sent(),[4,this.decodeOnce(t)];case 2:return[2,e.sent()]}})})},t.prototype._decodeOnLoadVideoContinuously=function(t,e){return v(this,void 0,void 0,function(){return m(this,function(n){switch(n.label){case 0:return[4,this.playVideoOnLoadAsync(t)];case 1:return n.sent(),this.decodeContinuously(t,e),[2]}})})},t.prototype.isImageLoaded=function(t){return!!t.complete&&0!==t.naturalWidth},t.prototype.prepareImageElement=function(t){var e;return void 0===t&&((e=document.createElement("img")).width=200,e.height=200),"string"==typeof t&&(e=this.getMediaElement(t,"img")),t instanceof HTMLImageElement&&(e=t),e},t.prototype.prepareVideoElement=function(t){var e;return t||"undefined"==typeof document||((e=document.createElement("video")).width=200,e.height=200),"string"==typeof t&&(e=this.getMediaElement(t,"video")),t instanceof HTMLVideoElement&&(e=t),e.setAttribute("autoplay","true"),e.setAttribute("muted","true"),e.setAttribute("playsinline","true"),e},t.prototype.decodeOnce=function(t,e,n){var r=this;void 0===e&&(e=!0),void 0===n&&(n=!0),this._stopAsyncDecode=!1;var o=function(a,u){if(r._stopAsyncDecode){u(new s.A("Video stream has ended before any code could be detected.")),r._stopAsyncDecode=void 0;return}try{var d=r.decode(t);a(d)}catch(t){var l=e&&t instanceof s.A,h=(t instanceof i.A||t instanceof c.A)&&n;if(l||h)return setTimeout(o,r._timeBetweenDecodingAttempts,a,u);u(t)}};return new Promise(function(t,e){return o(t,e)})},t.prototype.decodeContinuously=function(t,e){var n=this;this._stopContinuousDecode=!1;var r=function(){if(n._stopContinuousDecode){n._stopContinuousDecode=void 0;return}try{var o=n.decode(t);e(o,null),setTimeout(r,n.timeBetweenScansMillis)}catch(t){e(null,t);var a=t instanceof i.A||t instanceof c.A,u=t instanceof s.A;(a||u)&&setTimeout(r,n._timeBetweenDecodingAttempts)}};r()},t.prototype.decode=function(t){var e=this.createBinaryBitmap(t);return this.decodeBitmap(e)},t.prototype.createBinaryBitmap=function(t){this.getCaptureCanvasContext(t);var e=!1;t instanceof HTMLVideoElement?(this.drawFrameOnCanvas(t),e=!0):this.drawImageOnCanvas(t);var n=new p(this.getCaptureCanvas(t),e),r=new a.A(n);return new o.A(r)},t.prototype.getCaptureCanvasContext=function(t){if(!this.captureCanvasContext){var e=this.getCaptureCanvas(t),n=void 0;try{n=e.getContext("2d",{willReadFrequently:!0})}catch(t){n=e.getContext("2d")}this.captureCanvasContext=n}return this.captureCanvasContext},t.prototype.getCaptureCanvas=function(t){if(!this.captureCanvas){var e=this.createCaptureCanvas(t);this.captureCanvas=e}return this.captureCanvas},t.prototype.drawFrameOnCanvas=function(t,e,n){void 0===e&&(e={sx:0,sy:0,sWidth:t.videoWidth,sHeight:t.videoHeight,dx:0,dy:0,dWidth:t.videoWidth,dHeight:t.videoHeight}),void 0===n&&(n=this.captureCanvasContext),n.drawImage(t,e.sx,e.sy,e.sWidth,e.sHeight,e.dx,e.dy,e.dWidth,e.dHeight)},t.prototype.drawImageOnCanvas=function(t,e,n){void 0===e&&(e={sx:0,sy:0,sWidth:t.naturalWidth,sHeight:t.naturalHeight,dx:0,dy:0,dWidth:t.naturalWidth,dHeight:t.naturalHeight}),void 0===n&&(n=this.captureCanvasContext),n.drawImage(t,e.sx,e.sy,e.sWidth,e.sHeight,e.dx,e.dy,e.dWidth,e.dHeight)},t.prototype.decodeBitmap=function(t){return this.reader.decode(t,this._hints)},t.prototype.createCaptureCanvas=function(t){if("undefined"==typeof document)return this._destroyCaptureCanvas(),null;var e,n,r=document.createElement("canvas");return void 0!==t&&(t instanceof HTMLVideoElement?(e=t.videoWidth,n=t.videoHeight):t instanceof HTMLImageElement&&(e=t.naturalWidth||t.width,n=t.naturalHeight||t.height)),r.style.width=e+"px",r.style.height=n+"px",r.width=e,r.height=n,r},t.prototype.stopStreams=function(){this.stream&&(this.stream.getVideoTracks().forEach(function(t){return t.stop()}),this.stream=void 0),!1===this._stopAsyncDecode&&this.stopAsyncDecode(),!1===this._stopContinuousDecode&&this.stopContinuousDecode()},t.prototype.reset=function(){this.stopStreams(),this._destroyVideoElement(),this._destroyImageElement(),this._destroyCaptureCanvas()},t.prototype._destroyVideoElement=function(){this.videoElement&&(void 0!==this.videoEndedListener&&this.videoElement.removeEventListener("ended",this.videoEndedListener),void 0!==this.videoPlayingEventListener&&this.videoElement.removeEventListener("playing",this.videoPlayingEventListener),void 0!==this.videoCanPlayListener&&this.videoElement.removeEventListener("loadedmetadata",this.videoCanPlayListener),this.cleanVideoSource(this.videoElement),this.videoElement=void 0)},t.prototype._destroyImageElement=function(){this.imageElement&&(void 0!==this.imageLoadedListener&&this.imageElement.removeEventListener("load",this.imageLoadedListener),this.imageElement.src=void 0,this.imageElement.removeAttribute("src"),this.imageElement=void 0)},t.prototype._destroyCaptureCanvas=function(){this.captureCanvasContext=void 0,this.captureCanvas=void 0},t.prototype.addVideoSource=function(t,e){try{t.srcObject=e}catch(n){t.src=URL.createObjectURL(e)}},t.prototype.cleanVideoSource=function(t){try{t.srcObject=null}catch(e){t.src=""}this.videoElement.removeAttribute("src")},t}(),w=n(72106),E=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){E(function(e){return void 0===e&&(e=500),t.call(this,new w.A,e)||this},t)}(g);var b=n(10692),C=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){C(function(e,n){return void 0===e&&(e=500),t.call(this,new b.A(n),e,n)||this},t)}(g);var _=n(41205),S=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){S(function(e){return void 0===e&&(e=500),t.call(this,new _.A,e)||this},t)}(g);var O=n(92251),A=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){function e(e,n){void 0===e&&(e=null),void 0===n&&(n=500);var r=this,o=new O.A;return o.setHints(e),t.call(this,o,n)||this}A(e,t),e.prototype.decodeBitmap=function(t){return this.reader.decodeWithState(t)}}(g);var I=n(39188),V=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){V(function(e){return void 0===e&&(e=500),t.call(this,new I.A,e)||this},t)}(g);var T=n(85469),x=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){x(function(e){return void 0===e&&(e=500),t.call(this,new T.A,e)||this},t)}(g);var M=n(27217),R=n(35168),F=n(61536),L=n(39778);!function(){function t(){}t.prototype.write=function(e,n,r,o){if(void 0===o&&(o=null),0===e.length)throw new l.A("Found empty contents");if(n<0||r<0)throw new l.A("Requested dimensions are too small: "+n+"x"+r);var i=F.A.L,a=t.QUIET_ZONE_SIZE;null!==o&&(void 0!==o.get(M.A.ERROR_CORRECTION)&&(i=F.A.fromString(o.get(M.A.ERROR_CORRECTION).toString())),void 0!==o.get(M.A.MARGIN)&&(a=Number.parseInt(o.get(M.A.MARGIN).toString(),10)));var c=R.A.encode(e,i,o);return this.renderResult(c,n,r,a)},t.prototype.writeToDom=function(t,e,n,r,o){void 0===o&&(o=null),"string"==typeof t&&(t=document.querySelector(t));var i=this.write(e,n,r,o);t&&t.appendChild(i)},t.prototype.renderResult=function(t,e,n,r){var o=t.getMatrix();if(null===o)throw new L.A;for(var i=o.getWidth(),a=o.getHeight(),c=i+2*r,s=a+2*r,u=Math.max(e,c),d=Math.max(n,s),l=Math.min(Math.floor(u/c),Math.floor(d/s)),h=Math.floor((u-i*l)/2),p=Math.floor((d-a*l)/2),f=this.createSVGElement(u,d),v=0,m=p;v<a;v++,m+=l)for(var y=0,g=h;y<i;y++,g+=l)if(1===o.get(y,v)){var w=this.createSvgRectElement(g,m,l,l);f.appendChild(w)}return f},t.prototype.createSVGElement=function(e,n){var r=document.createElementNS(t.SVG_NS,"svg");return r.setAttributeNS(null,"height",e.toString()),r.setAttributeNS(null,"width",n.toString()),r},t.prototype.createSvgRectElement=function(e,n,r,o){var i=document.createElementNS(t.SVG_NS,"rect");return i.setAttributeNS(null,"x",e.toString()),i.setAttributeNS(null,"y",n.toString()),i.setAttributeNS(null,"height",r.toString()),i.setAttributeNS(null,"width",o.toString()),i.setAttributeNS(null,"fill","#000000"),i},t.QUIET_ZONE_SIZE=4,t.SVG_NS="http://www.w3.org/2000/svg"}()},27145:(t,e,n)=>{n.d(e,{sx:()=>y});var r=n(76142),o=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=function(t){function e(n){var r=t.call(this,n.width,n.height)||this;return r.canvas=n,r.tempCanvasElement=null,r.buffer=e.makeBufferFromCanvasImageData(n),r}return o(e,t),e.makeBufferFromCanvasImageData=function(t){try{n=t.getContext("2d",{willReadFrequently:!0})}catch(e){n=t.getContext("2d")}if(!n)throw Error("Couldn't get canvas context.");var n,r=n.getImageData(0,0,t.width,t.height);return e.toGrayscaleBuffer(r.data,t.width,t.height)},e.toGrayscaleBuffer=function(t,e,n){for(var r=new Uint8ClampedArray(e*n),o=0,i=0,a=t.length;o<a;o+=4,i++){var c=void 0;c=0===t[o+3]?255:306*t[o]+601*t[o+1]+117*t[o+2]+512>>10,r[i]=c}return r},e.prototype.getRow=function(t,e){if(t<0||t>=this.getHeight())throw new r.IllegalArgumentException("Requested row is outside the image: "+t);var n=this.getWidth(),o=t*n;return null===e?e=this.buffer.slice(o,o+n):(e.length<n&&(e=new Uint8ClampedArray(n)),e.set(this.buffer.slice(o,o+n))),e},e.prototype.getMatrix=function(){return this.buffer},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(e,n,r,o){return t.prototype.crop.call(this,e,n,r,o),this},e.prototype.isRotateSupported=function(){return!0},e.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},e.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},e.prototype.invert=function(){return new r.InvertedLuminanceSource(this)},e.prototype.getTempCanvasElement=function(){if(null===this.tempCanvasElement){var t=this.canvas.ownerDocument.createElement("canvas");t.width=this.canvas.width,t.height=this.canvas.height,this.tempCanvasElement=t}return this.tempCanvasElement},e.prototype.rotate=function(t){var n=this.getTempCanvasElement();if(!n)throw Error("Could not create a Canvas element.");var r=t*e.DEGREE_TO_RADIANS,o=this.canvas.width,i=this.canvas.height,a=Math.ceil(Math.abs(Math.cos(r))*o+Math.abs(Math.sin(r))*i),c=Math.ceil(Math.abs(Math.sin(r))*o+Math.abs(Math.cos(r))*i);n.width=a,n.height=c;var s=n.getContext("2d");if(!s)throw Error("Could not create a Canvas Context element.");return s.translate(a/2,c/2),s.rotate(r),s.drawImage(this.canvas,-(o/2),-(i/2)),this.buffer=e.makeBufferFromCanvasImageData(n),this},e.DEGREE_TO_RADIANS=Math.PI/180,e}(r.LuminanceSource);function a(){return"undefined"!=typeof navigator}var c=function(){return(c=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},s=function(t,e,n,r){return new(n||(n=Promise))(function(o,i){function a(t){try{s(r.next(t))}catch(t){i(t)}}function c(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof n?e:new n(function(t){t(e)})).then(a,c)}s((r=r.apply(t,e||[])).next())})},u=function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){var s=[i,c];if(n)throw TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}}},d=function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},l={delayBetweenScanAttempts:500,delayBetweenScanSuccess:500,tryPlayVideoTimeout:5e3},h=function(){function t(t,e,n){void 0===e&&(e=new Map),void 0===n&&(n={}),this.reader=t,this.hints=e,this.options=c(c({},l),n)}return Object.defineProperty(t.prototype,"possibleFormats",{set:function(t){this.hints.set(r.DecodeHintType.POSSIBLE_FORMATS,t)},enumerable:!1,configurable:!0}),t.addVideoSource=function(t,e){try{t.srcObject=e}catch(t){console.error("got interrupted by new loading request")}},t.mediaStreamSetTorch=function(t,e){return s(this,void 0,void 0,function(){return u(this,function(n){switch(n.label){case 0:return[4,t.applyConstraints({advanced:[{fillLightMode:e?"flash":"off",torch:!!e}]})];case 1:return n.sent(),[2]}})})},t.mediaStreamIsTorchCompatible=function(e){var n,r,o=e.getVideoTracks();try{for(var i=d(o),a=i.next();!a.done;a=i.next()){var c=a.value;if(t.mediaStreamIsTorchCompatibleTrack(c))return!0}}catch(t){n={error:t}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return!1},t.mediaStreamIsTorchCompatibleTrack=function(t){try{var e=t.getCapabilities();return"torch"in e}catch(t){return console.error(t),console.warn("Your browser may be not fully compatible with WebRTC and/or ImageCapture specs. Torch will not be available."),!1}},t.isVideoPlaying=function(t){return t.currentTime>0&&!t.paused&&t.readyState>2},t.getMediaElement=function(t,e){var n=document.getElementById(t);if(!n)throw new r.ArgumentException("element with id '".concat(t,"' not found"));if(n.nodeName.toLowerCase()!==e.toLowerCase())throw new r.ArgumentException("element with id '".concat(t,"' must be an ").concat(e," element"));return n},t.createVideoElement=function(e){if(e instanceof HTMLVideoElement)return e;if("string"==typeof e)return t.getMediaElement(e,"video");if(!e&&"undefined"!=typeof document){var n=document.createElement("video");return n.width=200,n.height=200,n}throw Error("Couldn't get videoElement from videoSource!")},t.prepareImageElement=function(e){if(e instanceof HTMLImageElement)return e;if("string"==typeof e)return t.getMediaElement(e,"img");if(void 0===e){var n=document.createElement("img");return n.width=200,n.height=200,n}throw Error("Couldn't get imageElement from imageSource!")},t.prepareVideoElement=function(e){var n=t.createVideoElement(e);return n.setAttribute("autoplay","true"),n.setAttribute("muted","true"),n.setAttribute("playsinline","true"),n},t.isImageLoaded=function(t){return!!t.complete&&0!==t.naturalWidth},t.createBinaryBitmapFromCanvas=function(t){var e=new i(t),n=new r.HybridBinarizer(e);return new r.BinaryBitmap(n)},t.drawImageOnCanvas=function(t,e){t.drawImage(e,0,0)},t.getMediaElementDimensions=function(t){if(t instanceof HTMLVideoElement)return{height:t.videoHeight,width:t.videoWidth};if(t instanceof HTMLImageElement)return{height:t.naturalHeight||t.height,width:t.naturalWidth||t.width};throw Error("Couldn't find the Source's dimensions!")},t.createCaptureCanvas=function(e){if(!e)throw new r.ArgumentException("Cannot create a capture canvas without a media element.");if("undefined"==typeof document)throw Error('The page "Document" is undefined, make sure you\'re running in a browser.');var n=document.createElement("canvas"),o=t.getMediaElementDimensions(e),i=o.width,a=o.height;return n.style.width=i+"px",n.style.height=a+"px",n.width=i,n.height=a,n},t.tryPlayVideo=function(e){return s(this,void 0,void 0,function(){return u(this,function(n){switch(n.label){case 0:if(null==e?void 0:e.ended)return console.error("Trying to play video that has ended."),[2,!1];if(t.isVideoPlaying(e))return console.warn("Trying to play video that is already playing."),[2,!0];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,e.play()];case 2:return n.sent(),[2,!0];case 3:return console.warn("It was not possible to play the video.",n.sent()),[2,!1];case 4:return[2]}})})},t.createCanvasFromMediaElement=function(e){var n=t.createCaptureCanvas(e),r=n.getContext("2d");if(!r)throw Error("Couldn't find Canvas 2D Context.");return t.drawImageOnCanvas(r,e),n},t.createBinaryBitmapFromMediaElem=function(e){var n=t.createCanvasFromMediaElement(e);return t.createBinaryBitmapFromCanvas(n)},t.destroyImageElement=function(t){t.src="",t.removeAttribute("src"),t=void 0},t.listVideoInputDevices=function(){return s(this,void 0,void 0,function(){var t,e,n,r,o,i,c,s,l,h,p,f;return u(this,function(u){switch(u.label){case 0:if(!a())throw Error("Can't enumerate devices, navigator is not present.");if(!(a()&&navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices))throw Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:t=u.sent(),e=[];try{for(r=(n=d(t)).next();!r.done;r=n.next())o=r.value,i="video"===o.kind?"videoinput":o.kind,"videoinput"===i&&(c=o.deviceId||o.id,s=o.label||"Video device ".concat(e.length+1),l=o.groupId,h={deviceId:c,label:s,kind:i,groupId:l},e.push(h))}catch(t){p={error:t}}finally{try{r&&!r.done&&(f=n.return)&&f.call(n)}finally{if(p)throw p.error}}return[2,e]}})})},t.findDeviceById=function(e){return s(this,void 0,void 0,function(){var n;return u(this,function(r){switch(r.label){case 0:return[4,t.listVideoInputDevices()];case 1:if(!(n=r.sent()))return[2];return[2,n.find(function(t){return t.deviceId===e})]}})})},t.cleanVideoSource=function(t){if(t){try{t.srcObject=null}catch(e){t.src=""}t&&t.removeAttribute("src")}},t.releaseAllStreams=function(){0!==t.streamTracker.length&&t.streamTracker.forEach(function(t){t.getTracks().forEach(function(t){return t.stop()})}),t.streamTracker=[]},t.playVideoOnLoadAsync=function(e,n){return s(this,void 0,void 0,function(){return u(this,function(r){switch(r.label){case 0:return[4,t.tryPlayVideo(e)];case 1:if(r.sent())return[2,!0];return[2,new Promise(function(r,o){var i=setTimeout(function(){t.isVideoPlaying(e)||(o(!1),e.removeEventListener("canplay",a))},n),a=function(){t.tryPlayVideo(e).then(function(t){clearTimeout(i),e.removeEventListener("canplay",a),r(t)})};e.addEventListener("canplay",a)})]}})})},t.attachStreamToVideo=function(e,n,r){return void 0===r&&(r=5e3),s(this,void 0,void 0,function(){var o;return u(this,function(i){switch(i.label){case 0:return o=t.prepareVideoElement(n),t.addVideoSource(o,e),[4,t.playVideoOnLoadAsync(o,r)];case 1:return i.sent(),[2,o]}})})},t._waitImageLoad=function(e){return new Promise(function(n,r){var o=setTimeout(function(){t.isImageLoaded(e)||(e.removeEventListener("load",i),r())},1e4),i=function(){clearTimeout(o),e.removeEventListener("load",i),n()};e.addEventListener("load",i)})},t.checkCallbackFnOrThrow=function(t){if(!t)throw new r.ArgumentException("`callbackFn` is a required parameter, you cannot capture results without it.")},t.disposeMediaStream=function(t){t.getVideoTracks().forEach(function(t){return t.stop()}),t=void 0},t.prototype.decode=function(e){var n=t.createCanvasFromMediaElement(e);return this.decodeFromCanvas(n)},t.prototype.decodeBitmap=function(t){return this.reader.decode(t,this.hints)},t.prototype.decodeFromCanvas=function(e){var n=t.createBinaryBitmapFromCanvas(e);return this.decodeBitmap(n)},t.prototype.decodeFromImageElement=function(e){return s(this,void 0,void 0,function(){var n;return u(this,function(o){switch(o.label){case 0:if(!e)throw new r.ArgumentException("An image element must be provided.");return n=t.prepareImageElement(e),[4,this._decodeOnLoadImage(n)];case 1:return[2,o.sent()]}})})},t.prototype.decodeFromImageUrl=function(e){return s(this,void 0,void 0,function(){var n;return u(this,function(o){switch(o.label){case 0:if(!e)throw new r.ArgumentException("An URL must be provided.");(n=t.prepareImageElement()).src=e,o.label=1;case 1:return o.trys.push([1,,3,4]),[4,this.decodeFromImageElement(n)];case 2:return[2,o.sent()];case 3:return t.destroyImageElement(n),[7];case 4:return[2]}})})},t.prototype.decodeFromConstraints=function(e,n,r){return s(this,void 0,void 0,function(){var o,i;return u(this,function(a){switch(a.label){case 0:return t.checkCallbackFnOrThrow(r),[4,this.getUserMedia(e)];case 1:o=a.sent(),a.label=2;case 2:return a.trys.push([2,4,,5]),[4,this.decodeFromStream(o,n,r)];case 3:return[2,a.sent()];case 4:throw i=a.sent(),t.disposeMediaStream(o),i;case 5:return[2]}})})},t.prototype.decodeFromStream=function(e,n,r){return s(this,void 0,void 0,function(){var o,i,a,l,h,p,f,v,m=this;return u(this,function(y){switch(y.label){case 0:return t.checkCallbackFnOrThrow(r),o=this.options.tryPlayVideoTimeout,[4,t.attachStreamToVideo(e,n,o)];case 1:return i=y.sent(),a=function(){t.disposeMediaStream(e),t.cleanVideoSource(i)},l=this.scan(i,r,a),h=e.getVideoTracks(),p=c(c({},l),{stop:function(){l.stop()},streamVideoConstraintsApply:function(t,e){return s(this,void 0,void 0,function(){var n,r,o,i,a;return u(this,function(c){switch(c.label){case 0:n=e?h.filter(e):h,c.label=1;case 1:c.trys.push([1,6,7,8]),o=(r=d(n)).next(),c.label=2;case 2:if(o.done)return[3,5];return[4,o.value.applyConstraints(t)];case 3:c.sent(),c.label=4;case 4:return o=r.next(),[3,2];case 5:return[3,8];case 6:return i={error:c.sent()},[3,8];case 7:try{o&&!o.done&&(a=r.return)&&a.call(r)}finally{if(i)throw i.error}return[7];case 8:return[2]}})})},streamVideoConstraintsGet:function(t){return h.find(t).getConstraints()},streamVideoSettingsGet:function(t){return h.find(t).getSettings()},streamVideoCapabilitiesGet:function(t){return h.find(t).getCapabilities()}}),t.mediaStreamIsTorchCompatible(e)&&(f=null==h?void 0:h.find(function(e){return t.mediaStreamIsTorchCompatibleTrack(e)}),p.switchTorch=v=function(e){return s(m,void 0,void 0,function(){return u(this,function(n){switch(n.label){case 0:return[4,t.mediaStreamSetTorch(f,e)];case 1:return n.sent(),[2]}})})},p.stop=function(){return s(m,void 0,void 0,function(){return u(this,function(t){switch(t.label){case 0:return l.stop(),[4,v(!1)];case 1:return t.sent(),[2]}})})}),[2,p]}})})},t.prototype.decodeFromVideoDevice=function(e,n,r){return s(this,void 0,void 0,function(){var o,i;return u(this,function(o){switch(o.label){case 0:return t.checkCallbackFnOrThrow(r),i={video:e?{deviceId:{exact:e}}:{facingMode:"environment"}},[4,this.decodeFromConstraints(i,n,r)];case 1:return[2,o.sent()]}})})},t.prototype.decodeFromVideoElement=function(e,n){return s(this,void 0,void 0,function(){var o,i;return u(this,function(a){switch(a.label){case 0:if(t.checkCallbackFnOrThrow(n),!e)throw new r.ArgumentException("A video element must be provided.");return o=t.prepareVideoElement(e),i=this.options.tryPlayVideoTimeout,[4,t.playVideoOnLoadAsync(o,i)];case 1:return a.sent(),[2,this.scan(o,n)]}})})},t.prototype.decodeFromVideoUrl=function(e,n){return s(this,void 0,void 0,function(){var o,i,a;return u(this,function(c){switch(c.label){case 0:if(t.checkCallbackFnOrThrow(n),!e)throw new r.ArgumentException("An URL must be provided.");return(o=t.prepareVideoElement()).src=e,i=function(){t.cleanVideoSource(o)},a=this.options.tryPlayVideoTimeout,[4,t.playVideoOnLoadAsync(o,a)];case 1:return c.sent(),[2,this.scan(o,n,i)]}})})},t.prototype.decodeOnceFromConstraints=function(t,e){return s(this,void 0,void 0,function(){var n;return u(this,function(r){switch(r.label){case 0:return[4,this.getUserMedia(t)];case 1:return n=r.sent(),[4,this.decodeOnceFromStream(n,e)];case 2:return[2,r.sent()]}})})},t.prototype.decodeOnceFromStream=function(e,n){return s(this,void 0,void 0,function(){var r,o;return u(this,function(i){switch(i.label){case 0:return r=!!n,[4,t.attachStreamToVideo(e,n)];case 1:o=i.sent(),i.label=2;case 2:return i.trys.push([2,,4,5]),[4,this.scanOneResult(o)];case 3:return[2,i.sent()];case 4:return r||t.cleanVideoSource(o),[7];case 5:return[2]}})})},t.prototype.decodeOnceFromVideoDevice=function(t,e){return s(this,void 0,void 0,function(){var n,r;return u(this,function(n){switch(n.label){case 0:return r={video:t?{deviceId:{exact:t}}:{facingMode:"environment"}},[4,this.decodeOnceFromConstraints(r,e)];case 1:return[2,n.sent()]}})})},t.prototype.decodeOnceFromVideoElement=function(e){return s(this,void 0,void 0,function(){var n,o;return u(this,function(i){switch(i.label){case 0:if(!e)throw new r.ArgumentException("A video element must be provided.");return n=t.prepareVideoElement(e),o=this.options.tryPlayVideoTimeout,[4,t.playVideoOnLoadAsync(n,o)];case 1:return i.sent(),[4,this.scanOneResult(n)];case 2:return[2,i.sent()]}})})},t.prototype.decodeOnceFromVideoUrl=function(e){return s(this,void 0,void 0,function(){var n,o;return u(this,function(i){switch(i.label){case 0:if(!e)throw new r.ArgumentException("An URL must be provided.");(n=t.prepareVideoElement()).src=e,o=this.decodeOnceFromVideoElement(n),i.label=1;case 1:return i.trys.push([1,,3,4]),[4,o];case 2:return[2,i.sent()];case 3:return t.cleanVideoSource(n),[7];case 4:return[2]}})})},t.prototype.scanOneResult=function(t,e,n,o){var i=this;return void 0===e&&(e=!0),void 0===n&&(n=!0),void 0===o&&(o=!0),new Promise(function(a,c){i.scan(t,function(t,i,s){if(t){a(t),s.stop();return}if(i){if(i instanceof r.NotFoundException&&e||i instanceof r.ChecksumException&&n||i instanceof r.FormatException&&o)return;s.stop(),c(i)}})})},t.prototype.scan=function(e,n,o){var i,a,c=this;t.checkCallbackFnOrThrow(n);var s=t.createCaptureCanvas(e);try{i=s.getContext("2d",{willReadFrequently:!0})}catch(t){i=s.getContext("2d")}if(!i)throw Error("Couldn't create canvas for visual element scan.");var u=function(){i=void 0,s=void 0},d=!1,l={stop:function(){d=!0,clearTimeout(a),u(),o&&o()}},h=function(){if(!d)try{t.drawImageOnCanvas(i,e);var p=c.decodeFromCanvas(s);n(p,void 0,l),a=setTimeout(h,c.options.delayBetweenScanSuccess)}catch(t){n(void 0,t,l);var f=t instanceof r.ChecksumException,v=t instanceof r.FormatException,m=t instanceof r.NotFoundException;if(f||v||m){a=setTimeout(h,c.options.delayBetweenScanAttempts);return}u(),o&&o(t)}};return h(),l},t.prototype._decodeOnLoadImage=function(e){return s(this,void 0,void 0,function(){return u(this,function(n){switch(n.label){case 0:if(t.isImageLoaded(e))return[3,2];return[4,t._waitImageLoad(e)];case 1:n.sent(),n.label=2;case 2:return[2,this.decode(e)]}})})},t.prototype.getUserMedia=function(e){return s(this,void 0,void 0,function(){var n;return u(this,function(r){switch(r.label){case 0:return[4,navigator.mediaDevices.getUserMedia(e)];case 1:return n=r.sent(),t.streamTracker.push(n),[2,n]}})})},t.streamTracker=[],t}(),p=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){p(function(e,n){return t.call(this,new r.AztecCodeReader,e,n)||this},t)}(h);var f=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){f(function(e,n){return t.call(this,new r.MultiFormatOneDReader(e),e,n)||this},t)}(h);var v=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){v(function(e,n){return t.call(this,new r.DataMatrixReader,e,n)||this},t)}(h);var m=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),y=function(t){function e(e,n){var o=this,i=new r.MultiFormatReader;return i.setHints(e),(o=t.call(this,i,e,n)||this).reader=i,o}return m(e,t),Object.defineProperty(e.prototype,"possibleFormats",{set:function(t){this.hints.set(r.DecodeHintType.POSSIBLE_FORMATS,t),this.reader.setHints(this.hints)},enumerable:!1,configurable:!0}),e.prototype.decodeBitmap=function(t){return this.reader.decodeWithState(t)},e.prototype.setHints=function(t){this.hints=t,this.reader.setHints(this.hints)},e}(h),g=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){g(function(e,n){return t.call(this,new r.PDF417Reader,e,n)||this},t)}(h);var w=function(){var t=function(e,n){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};return function(e,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(t){w(function(e,n){return t.call(this,new r.QRCodeReader,e,n)||this},t)}(h);var E="http://www.w3.org/2000/svg";!function(){function t(t){if("string"==typeof t){var e=document.getElementById(t);if(!e)throw Error("Could not find a Container element with '".concat(t,"'."));this.containerElement=e}else this.containerElement=t}t.prototype.write=function(e,n,o,i){if(0===e.length)throw new r.IllegalArgumentException("Found empty contents");if(n<0||o<0)throw new r.IllegalArgumentException("Requested dimensions are too small: "+n+"x"+o);var a=i&&void 0!==i.get(r.EncodeHintType.MARGIN)?Number.parseInt(i.get(r.EncodeHintType.MARGIN).toString(),10):t.QUIET_ZONE_SIZE,c=this.encode(i,e);return this.renderResult(c,n,o,a)},t.prototype.createSVGElement=function(e,n){var r=document.createElementNS(t.SVG_NS,"svg");return r.setAttributeNS(E,"width",n.toString()),r.setAttributeNS(E,"height",e.toString()),r},t.prototype.createSvgPathPlaceholderElement=function(e,n){var r=document.createElementNS(t.SVG_NS,"path");return r.setAttributeNS(E,"d","M0 0h".concat(e,"v").concat(n,"H0z")),r.setAttributeNS(E,"fill","none"),r},t.prototype.createSvgRectElement=function(e,n,r,o){var i=document.createElementNS(t.SVG_NS,"rect");return i.setAttributeNS(E,"x",e.toString()),i.setAttributeNS(E,"y",n.toString()),i.setAttributeNS(E,"height",r.toString()),i.setAttributeNS(E,"width",o.toString()),i.setAttributeNS(E,"fill","#000000"),i},t.prototype.encode=function(t,e){var n=r.QRCodeDecoderErrorCorrectionLevel.L;if(t&&void 0!==t.get(r.EncodeHintType.ERROR_CORRECTION)){var o=t.get(r.EncodeHintType.ERROR_CORRECTION).toString();n=r.QRCodeDecoderErrorCorrectionLevel.fromString(o)}return r.QRCodeEncoder.encode(e,n,t)},t.prototype.renderResult=function(t,e,n,o){var i=t.getMatrix();if(null===i)throw new r.IllegalStateException;var a=i.getWidth(),c=i.getHeight(),s=a+2*o,u=c+2*o,d=Math.max(e,s),l=Math.max(n,u),h=Math.min(Math.floor(d/s),Math.floor(l/u)),p=Math.floor((d-a*h)/2),f=Math.floor((l-c*h)/2),v=this.createSVGElement(d,l),m=this.createSvgPathPlaceholderElement(e,n);v.appendChild(m),this.containerElement.appendChild(v);for(var y=0,g=f;y<c;y++,g+=h)for(var w=0,E=p;w<a;w++,E+=h)if(1===i.get(w,y)){var b=this.createSvgRectElement(E,g,h,h);v.appendChild(b)}return v},t.QUIET_ZONE_SIZE=4,t.SVG_NS="http://www.w3.org/2000/svg"}();var b="http://www.w3.org/2000/svg";!function(){function t(){}t.prototype.write=function(e,n,o,i){if(0===e.length)throw new r.IllegalArgumentException("Found empty contents");if(n<0||o<0)throw new r.IllegalArgumentException("Requested dimensions are too small: "+n+"x"+o);var a=r.QRCodeDecoderErrorCorrectionLevel.L,c=t.QUIET_ZONE_SIZE;if(i){if(void 0!==i.get(r.EncodeHintType.ERROR_CORRECTION)){var s=i.get(r.EncodeHintType.ERROR_CORRECTION).toString();a=r.QRCodeDecoderErrorCorrectionLevel.fromString(s)}void 0!==i.get(r.EncodeHintType.MARGIN)&&(c=Number.parseInt(i.get(r.EncodeHintType.MARGIN).toString(),10))}var u=r.QRCodeEncoder.encode(e,a,i);return this.renderResult(u,n,o,c)},t.prototype.writeToDom=function(t,e,n,r,o){if("string"==typeof t){var i=document.querySelector(t);if(!i)throw Error("Could no find the target HTML element.");t=i}var a=this.write(e,n,r,o);t instanceof HTMLElement&&t.appendChild(a)},t.prototype.renderResult=function(t,e,n,o){var i=t.getMatrix();if(null===i)throw new r.IllegalStateException;for(var a=i.getWidth(),c=i.getHeight(),s=a+2*o,u=c+2*o,d=Math.max(e,s),l=Math.max(n,u),h=Math.min(Math.floor(d/s),Math.floor(l/u)),p=Math.floor((d-a*h)/2),f=Math.floor((l-c*h)/2),v=this.createSVGElement(d,l),m=0,y=f;m<c;m++,y+=h)for(var g=0,w=p;g<a;g++,w+=h)if(1===i.get(g,m)){var E=this.createSvgRectElement(w,y,h,h);v.appendChild(E)}return v},t.prototype.createSVGElement=function(t,e){var n=document.createElementNS(b,"svg"),r=t.toString(),o=e.toString();return n.setAttribute("height",o),n.setAttribute("width",r),n.setAttribute("viewBox","0 0 "+r+" "+o),n},t.prototype.createSvgRectElement=function(t,e,n,r){var o=document.createElementNS(b,"rect");return o.setAttribute("x",t.toString()),o.setAttribute("y",e.toString()),o.setAttribute("height",n.toString()),o.setAttribute("width",r.toString()),o.setAttribute("fill","#000000"),o},t.QUIET_ZONE_SIZE=4}()}}]);