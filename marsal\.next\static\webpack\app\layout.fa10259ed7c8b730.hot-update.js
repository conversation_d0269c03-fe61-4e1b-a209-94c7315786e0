"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b7a164526010\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJFOlxcTWFyc2FsXFxtYXJzYWxcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImI3YTE2NDUyNjAxMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/firebase-auth.ts":
/*!**********************************!*\
  !*** ./src/lib/firebase-auth.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   firebaseAuthService: () => (/* binding */ firebaseAuthService)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n// Firebase Authentication service for Marsal Delivery App\n\n\n\n// Default users for initial setup\nconst defaultUsers = [\n    {\n        username: 'azad95',\n        email: '<EMAIL>',\n        name: 'أزاد - مدير النظام الرئيسي',\n        phone: '07801234567',\n        role: 'manager',\n        password: 'Azad@1995'\n    },\n    {\n        username: 'manager',\n        email: '<EMAIL>',\n        name: 'مدير النظام',\n        phone: '07801234568',\n        role: 'manager',\n        password: '123456'\n    },\n    {\n        username: 'supervisor',\n        email: '<EMAIL>',\n        name: 'المشرف العام',\n        phone: '07801234569',\n        role: 'supervisor',\n        password: '123456'\n    },\n    {\n        username: 'courier',\n        email: '<EMAIL>',\n        name: 'مندوب التوصيل',\n        phone: '07801234570',\n        role: 'courier',\n        password: '123456'\n    }\n];\nconst firebaseAuthService = {\n    // Initialize default users (run once) - Local storage fallback\n    async initializeDefaultUsers () {\n        try {\n            console.log('🔧 إعداد المستخدمين الافتراضيين...');\n            // Check if Firebase is available\n            if (!_firebase__WEBPACK_IMPORTED_MODULE_1__.auth || !_firebase__WEBPACK_IMPORTED_MODULE_1__.db) {\n                console.log('⚠️ Firebase غير متاح، استخدام التخزين المحلي');\n                this.initializeLocalUsers();\n                return;\n            }\n            for (const userData of defaultUsers){\n                // Check if user already exists\n                const existingUser = await this.getUserByUsername(userData.username);\n                if (existingUser) {\n                    console.log(\"✅ المستخدم \".concat(userData.username, \" موجود مسبقاً\"));\n                    continue;\n                }\n                // Create Firebase Auth user\n                try {\n                    const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, userData.email, userData.password);\n                    // Create user document in Firestore\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n                        username: userData.username,\n                        email: userData.email,\n                        name: userData.name,\n                        phone: userData.phone,\n                        role: userData.role,\n                        isActive: true,\n                        createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                        updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                        createdBy: 'system'\n                    });\n                    console.log(\"✅ تم إنشاء المستخدم: \".concat(userData.username));\n                } catch (error) {\n                    if (error.code === 'auth/email-already-in-use') {\n                        console.log(\"⚠️ البريد الإلكتروني \".concat(userData.email, \" مستخدم مسبقاً\"));\n                    } else {\n                        console.error(\"❌ خطأ في إنشاء المستخدم \".concat(userData.username, \":\"), error);\n                        // Fallback to local storage\n                        this.initializeLocalUsers();\n                        return;\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ خطأ في إعداد المستخدمين الافتراضيين:', error);\n            // Fallback to local storage\n            this.initializeLocalUsers();\n        }\n    },\n    // Initialize users in local storage as fallback\n    initializeLocalUsers () {\n        try {\n            const existingUsers = localStorage.getItem('marsal_users');\n            if (existingUsers) {\n                console.log('✅ المستخدمين موجودين في التخزين المحلي');\n                return;\n            }\n            const localUsers = defaultUsers.map((userData, index)=>({\n                    id: \"local_\".concat(index + 1),\n                    username: userData.username,\n                    email: userData.email,\n                    name: userData.name,\n                    phone: userData.phone,\n                    role: userData.role,\n                    isActive: true,\n                    createdAt: new Date(),\n                    updatedAt: new Date(),\n                    createdBy: 'system',\n                    password: userData.password // Store password for local auth\n                }));\n            localStorage.setItem('marsal_users', JSON.stringify(localUsers));\n            console.log('✅ تم إنشاء المستخدمين في التخزين المحلي');\n        } catch (error) {\n            console.error('❌ خطأ في إنشاء المستخدمين المحليين:', error);\n        }\n    },\n    // Login with username/password - Firebase or Local\n    async login (username, password) {\n        try {\n            console.log('🔐 محاولة تسجيل الدخول للمستخدم:', username);\n            // Try Firebase first\n            if (_firebase__WEBPACK_IMPORTED_MODULE_1__.auth && _firebase__WEBPACK_IMPORTED_MODULE_1__.db) {\n                try {\n                    return await this.loginWithFirebase(username, password);\n                } catch (error) {\n                    console.warn('⚠️ فشل تسجيل الدخول مع Firebase، محاولة التخزين المحلي');\n                }\n            }\n            // Fallback to local storage\n            return await this.loginWithLocalStorage(username, password);\n        } catch (error) {\n            console.error('❌ خطأ في تسجيل الدخول:', error);\n            return {\n                success: false,\n                error: error.message || 'خطأ في تسجيل الدخول'\n            };\n        }\n    },\n    // Login with Firebase\n    async loginWithFirebase (username, password) {\n        // Get user by username to find email\n        const user = await this.getUserByUsername(username);\n        if (!user) {\n            throw new Error('اسم المستخدم غير موجود');\n        }\n        if (!user.isActive) {\n            throw new Error('الحساب غير مفعل');\n        }\n        // Sign in with email and password\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, user.email, password);\n        // Update last login\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n            lastLogin: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)()\n        }, {\n            merge: true\n        });\n        console.log('✅ تم تسجيل الدخول مع Firebase بنجاح');\n        return {\n            success: true,\n            user: {\n                ...user,\n                id: userCredential.user.uid\n            }\n        };\n    },\n    // Login with local storage\n    async loginWithLocalStorage (username, password) {\n        const usersData = localStorage.getItem('marsal_users');\n        if (!usersData) {\n            throw new Error('لا يوجد مستخدمين مسجلين');\n        }\n        const users = JSON.parse(usersData);\n        const user = users.find((u)=>u.username === username);\n        if (!user) {\n            throw new Error('اسم المستخدم غير موجود');\n        }\n        if (!user.isActive) {\n            throw new Error('الحساب غير مفعل');\n        }\n        if (user.password !== password) {\n            throw new Error('كلمة المرور غير صحيحة');\n        }\n        // Update last login\n        user.lastLogin = new Date();\n        const updatedUsers = users.map((u)=>u.id === user.id ? user : u);\n        localStorage.setItem('marsal_users', JSON.stringify(updatedUsers));\n        console.log('✅ تم تسجيل الدخول مع التخزين المحلي بنجاح');\n        return {\n            success: true,\n            user: {\n                id: user.id,\n                username: user.username,\n                email: user.email,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                isActive: user.isActive,\n                createdAt: new Date(user.createdAt),\n                updatedAt: new Date(user.updatedAt),\n                createdBy: user.createdBy,\n                lastLogin: user.lastLogin ? new Date(user.lastLogin) : undefined\n            }\n        };\n    },\n    // Get user by username\n    async getUserByUsername (username) {\n        try {\n            var _data_createdAt, _data_updatedAt, _data_lastLogin;\n            const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users');\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('username', '==', username));\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (snapshot.empty) {\n                return null;\n            }\n            const doc = snapshot.docs[0];\n            const data = doc.data();\n            return {\n                id: doc.id,\n                username: data.username,\n                email: data.email,\n                name: data.name,\n                phone: data.phone,\n                role: data.role,\n                isActive: data.isActive,\n                createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date(),\n                updatedAt: ((_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()) || new Date(),\n                createdBy: data.createdBy,\n                lastLogin: (_data_lastLogin = data.lastLogin) === null || _data_lastLogin === void 0 ? void 0 : _data_lastLogin.toDate()\n            };\n        } catch (error) {\n            console.error('Error getting user by username:', error);\n            return null;\n        }\n    },\n    // Get current user\n    async getCurrentUser () {\n        try {\n            var _data_createdAt, _data_updatedAt, _data_lastLogin;\n            const firebaseUser = _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n            if (!firebaseUser) {\n                return null;\n            }\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', firebaseUser.uid));\n            if (!userDoc.exists()) {\n                return null;\n            }\n            const data = userDoc.data();\n            return {\n                id: userDoc.id,\n                username: data.username,\n                email: data.email,\n                name: data.name,\n                phone: data.phone,\n                role: data.role,\n                isActive: data.isActive,\n                createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date(),\n                updatedAt: ((_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()) || new Date(),\n                createdBy: data.createdBy,\n                lastLogin: (_data_lastLogin = data.lastLogin) === null || _data_lastLogin === void 0 ? void 0 : _data_lastLogin.toDate()\n            };\n        } catch (error) {\n            console.error('Error getting current user:', error);\n            return null;\n        }\n    },\n    // Logout\n    async logout () {\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth);\n            console.log('✅ تم تسجيل الخروج بنجاح');\n        } catch (error) {\n            console.error('❌ خطأ في تسجيل الخروج:', error);\n            throw error;\n        }\n    },\n    // Listen to auth state changes\n    onAuthStateChanged (callback) {\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, async (firebaseUser)=>{\n            if (firebaseUser) {\n                const user = await this.getCurrentUser();\n                callback(user);\n            } else {\n                callback(null);\n            }\n        });\n    },\n    // Create new user (only for managers)\n    async createUser (userData) {\n        try {\n            console.log('👤 إنشاء مستخدم جديد:', userData.username);\n            // Create Firebase Auth user\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, userData.email, userData.password);\n            // Create user document in Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n                username: userData.username,\n                email: userData.email,\n                name: userData.name,\n                phone: userData.phone,\n                role: userData.role,\n                isActive: true,\n                createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                createdBy: userData.createdBy\n            });\n            console.log('✅ تم إنشاء المستخدم بنجاح:', userData.username);\n            const newUser = {\n                id: userCredential.user.uid,\n                username: userData.username,\n                email: userData.email,\n                name: userData.name,\n                phone: userData.phone,\n                role: userData.role,\n                isActive: true,\n                createdAt: new Date(),\n                updatedAt: new Date(),\n                createdBy: userData.createdBy\n            };\n            return {\n                success: true,\n                user: newUser\n            };\n        } catch (error) {\n            console.error('❌ خطأ في إنشاء المستخدم:', error);\n            let errorMessage = 'خطأ في إنشاء المستخدم';\n            switch(error.code){\n                case 'auth/email-already-in-use':\n                    errorMessage = 'البريد الإلكتروني مستخدم مسبقاً';\n                    break;\n                case 'auth/weak-password':\n                    errorMessage = 'كلمة المرور ضعيفة';\n                    break;\n                case 'auth/invalid-email':\n                    errorMessage = 'البريد الإلكتروني غير صحيح';\n                    break;\n                default:\n                    errorMessage = error.message || 'خطأ غير معروف';\n            }\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    },\n    // Get all users (for managers)\n    async getAllUsers () {\n        try {\n            const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users');\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(usersRef);\n            return snapshot.docs.map((doc)=>{\n                var _data_createdAt, _data_updatedAt, _data_lastLogin;\n                const data = doc.data();\n                return {\n                    id: doc.id,\n                    username: data.username,\n                    email: data.email,\n                    name: data.name,\n                    phone: data.phone,\n                    role: data.role,\n                    isActive: data.isActive,\n                    createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date(),\n                    updatedAt: ((_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()) || new Date(),\n                    createdBy: data.createdBy,\n                    lastLogin: (_data_lastLogin = data.lastLogin) === null || _data_lastLogin === void 0 ? void 0 : _data_lastLogin.toDate()\n                };\n            });\n        } catch (error) {\n            console.error('Error getting all users:', error);\n            return [];\n        }\n    },\n    // Update user (for managers)\n    async updateUser (userId, updates) {\n        try {\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId), {\n                ...updates,\n                updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)()\n            }, {\n                merge: true\n            });\n            console.log('✅ تم تحديث المستخدم:', userId);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('❌ خطأ في تحديث المستخدم:', error);\n            return {\n                success: false,\n                error: error.message || 'خطأ في تحديث المستخدم'\n            };\n        }\n    },\n    // Delete user (for managers)\n    async deleteUser (userId) {\n        try {\n            // Delete from Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId));\n            // Note: Deleting from Firebase Auth requires Admin SDK\n            // For now, we just deactivate the user\n            await this.updateUser(userId, {\n                isActive: false\n            });\n            console.log('✅ تم حذف المستخدم:', userId);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('❌ خطأ في حذف المستخدم:', error);\n            return {\n                success: false,\n                error: error.message || 'خطأ في حذف المستخدم'\n            };\n        }\n    },\n    // Check if Firebase is connected\n    async checkConnection () {\n        try {\n            // Try to get current user\n            const user = _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n            console.log('✅ Firebase Auth متصل');\n            return {\n                connected: true,\n                message: 'Firebase Auth متصل بنجاح'\n            };\n        } catch (error) {\n            console.error('❌ Firebase Auth غير متصل:', error);\n            return {\n                connected: false,\n                message: \"خطأ في الاتصال: \".concat(error.message)\n            };\n        }\n    }\n};\n// Auto-initialize default users when the module loads\nfirebaseAuthService.initializeDefaultUsers().catch(console.error);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/firebase-auth.ts\n"));

/***/ })

});