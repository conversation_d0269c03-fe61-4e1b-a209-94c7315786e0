(()=>{var e={};e.id=40,e.ids=[40],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21067:(e,s,r)=>{Promise.resolve().then(r.bind(r,50307))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30566:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\orders\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\page.tsx","default")},31158:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34729:(e,s,r)=>{"use strict";r.d(s,{T:()=>i});var t=r(60687),a=r(43210),l=r(4780);let i=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...s}));i.displayName="Textarea"},37366:e=>{"use strict";e.exports=require("dns")},48340:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},50307:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>Y});var t=r(60687),a=r(43210),l=r(44493),i=r(29523),n=r(19080),d=r(70334),c=r(63143),o=r(62688);let x=(0,o.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),m=(0,o.A)("share",[["path",{d:"M12 2v13",key:"1km8f5"}],["path",{d:"m16 6-4-4-4 4",key:"13yo43"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}]]);var u=r(71444),h=r(31158);let p=(0,o.A)("ticket",[["path",{d:"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z",key:"qn84l0"}],["path",{d:"M13 5v2",key:"dyzc3o"}],["path",{d:"M13 17v2",key:"1ont0d"}],["path",{d:"M13 11v2",key:"1wjjxi"}]]);var g=r(32192),b=r(58869),v=r(48340),j=r(97992),f=r(23928);let N=(0,o.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var y=r(33872),w=r(37730),k=r(89667),A=r(34729),$=r(96834),q=r(11860),C=r(48730);let _=(0,o.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);function R({orderId:e,isOpen:s,onClose:r}){let[n,d]=(0,a.useState)([]),[c,o]=(0,a.useState)(null),[x,m]=(0,a.useState)(""),[u,h]=(0,a.useState)(!1),[p,g]=(0,a.useState)(""),[v,j]=(0,a.useState)(""),[f,N]=(0,a.useState)("medium"),w=(0,a.useRef)(null);Date.now(),Date.now(),Date.now();let R=()=>{if(!x.trim()||!c)return;let e={id:Date.now().toString(),senderId:"current_user",senderName:"المستخدم الحالي",senderRole:"مدير",content:x,timestamp:new Date};o(s=>s?{...s,messages:[...s.messages,e]}:null),m("")},P=e=>{switch(e){case"open":return"bg-red-100 text-red-800";case"in_progress":return"bg-yellow-100 text-yellow-800";case"resolved":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},M=e=>{switch(e){case"urgent":return"bg-red-500 text-white";case"high":return"bg-orange-500 text-white";case"medium":return"bg-blue-500 text-white";default:return"bg-gray-500 text-white"}},S=e=>{switch(e){case"urgent":return"عاجل";case"high":return"عالي";case"medium":default:return"متوسط";case"low":return"منخفض"}},E=e=>{switch(e){case"open":default:return"مفتوح";case"in_progress":return"قيد المعالجة";case"resolved":return"تم الحل";case"closed":return"مغلق"}};return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",dir:"rtl",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl w-full max-w-6xl h-[80vh] flex overflow-hidden",children:[(0,t.jsxs)("div",{className:"w-1/3 border-l border-gray-200 bg-gray-50",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200 bg-white",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center gap-2",children:[(0,t.jsx)(y.A,{className:"h-6 w-6 text-blue-600"}),"التذاكر"]}),(0,t.jsx)(i.$,{onClick:r,variant:"ghost",size:"sm",className:"text-gray-500 hover:text-gray-700",children:(0,t.jsx)(q.A,{className:"h-5 w-5"})})]}),(0,t.jsx)(i.$,{onClick:()=>h(!0),className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:"إنشاء تذكرة جديدة"})]}),(0,t.jsx)("div",{className:"p-4 space-y-3 overflow-y-auto h-full",children:n.map(e=>(0,t.jsx)(l.Zp,{className:`cursor-pointer transition-all duration-200 hover:shadow-md ${c?.id===e.id?"ring-2 ring-blue-500 bg-blue-50":"hover:bg-white"}`,onClick:()=>o(e),children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"font-semibold text-sm text-gray-800 truncate",children:e.title}),(0,t.jsx)($.E,{className:M(e.priority),children:S(e.priority)})]}),(0,t.jsx)("p",{className:"text-xs text-gray-600 line-clamp-2",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)($.E,{variant:"outline",className:P(e.status),children:E(e.status)}),(0,t.jsxs)("span",{className:"text-xs text-gray-500 flex items-center gap-1",children:[(0,t.jsx)(C.A,{className:"h-3 w-3"}),e.createdAt.toLocaleDateString("ar-IQ")]})]})]})})},e.id))})]}),(0,t.jsx)("div",{className:"flex-1 flex flex-col",children:u?(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:"إنشاء تذكرة جديدة"}),(0,t.jsx)(i.$,{onClick:()=>h(!1),variant:"ghost",size:"sm",children:"إلغاء"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"عنوان التذكرة"}),(0,t.jsx)(k.p,{value:p,onChange:e=>g(e.target.value),placeholder:"أدخل عنوان التذكرة",className:"w-full"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الوصف"}),(0,t.jsx)(A.T,{value:v,onChange:e=>j(e.target.value),placeholder:"اشرح المشكلة أو الاستفسار بالتفصيل",rows:4,className:"w-full"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الأولوية"}),(0,t.jsxs)("select",{value:f,onChange:e=>N(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"low",children:"منخفض"}),(0,t.jsx)("option",{value:"medium",children:"متوسط"}),(0,t.jsx)("option",{value:"high",children:"عالي"}),(0,t.jsx)("option",{value:"urgent",children:"عاجل"})]})]}),(0,t.jsx)(i.$,{onClick:()=>{if(!p.trim()||!v.trim())return;let s={id:Date.now().toString(),orderId:e,title:p,description:v,status:"open",priority:f,createdBy:"current_user",createdAt:new Date,messages:[]};d(e=>[s,...e]),o(s),h(!1),g(""),j(""),N("medium")},className:"w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700",disabled:!p.trim()||!v.trim(),children:"إنشاء التذكرة"})]})]}):c?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200 bg-white",children:(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:c.title}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:c.description})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)($.E,{className:M(c.priority),children:S(c.priority)}),(0,t.jsx)($.E,{variant:"outline",className:P(c.status),children:E(c.status)})]})]})}),(0,t.jsx)("div",{className:"flex-1 p-6 overflow-y-auto bg-gray-50",children:(0,t.jsxs)("div",{className:"space-y-4",children:[c.messages.map(e=>(0,t.jsx)("div",{className:`flex ${"current_user"===e.senderId?"justify-end":"justify-start"}`,children:(0,t.jsxs)("div",{className:`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl ${"current_user"===e.senderId?"bg-gradient-to-r from-blue-500 to-purple-600 text-white":"bg-white text-gray-800 shadow-sm border"}`,children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:e.senderName}),(0,t.jsx)($.E,{variant:"outline",className:"text-xs",children:e.senderRole})]}),(0,t.jsx)("p",{className:"text-sm",children:e.content}),(0,t.jsx)("p",{className:`text-xs mt-2 ${"current_user"===e.senderId?"text-blue-100":"text-gray-500"}`,children:e.timestamp.toLocaleTimeString("ar-IQ")})]})},e.id)),(0,t.jsx)("div",{ref:w})]})}),(0,t.jsx)("div",{className:"p-6 border-t border-gray-200 bg-white",children:(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)(k.p,{value:x,onChange:e=>m(e.target.value),placeholder:"اكتب رسالتك هنا...",className:"flex-1",onKeyPress:e=>"Enter"===e.key&&R()}),(0,t.jsx)(i.$,{onClick:R,disabled:!x.trim(),className:"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:(0,t.jsx)(_,{className:"h-4 w-4"})})]})})]}):(0,t.jsx)("div",{className:"flex-1 flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(y.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-600 mb-2",children:"اختر تذكرة للعرض"}),(0,t.jsx)("p",{className:"text-gray-500",children:"أو أنشئ تذكرة جديدة للبدء"})]})})})]})}):null}var P=r(5336),M=r(93613),S=r(88059),E=r(16023),z=r(51361),I=r(67958),G=r(48126);let Z=[{value:"delivered",label:"تم التسليم",icon:P.A,color:"bg-green-500",description:"تم تسليم الطلب بنجاح",requiresReason:!1},{value:"returned_to_courier",label:"راجع عند المندوب",icon:M.A,color:"bg-amber-500",description:"الطلب راجع عند المندوب",requiresReason:!0},{value:"postponed",label:"مؤجل",icon:C.A,color:"bg-orange-500",description:"تم تأجيل التسليم",requiresReason:!0},{value:"partial_delivery",label:"تسليم جزئي",icon:n.A,color:"bg-cyan-500",description:"تم تسليم جزء من الطلب",requiresReason:!1},{value:"price_change",label:"تغيير سعر فقط",icon:f.A,color:"bg-indigo-500",description:"تغيير سعر الطلب فقط",requiresReason:!1}];function T({orderId:e,currentStatus:s,onUpdate:r,onStatusUpdate:d,onClose:c}){let{user:o}=(0,I.A)(),[x,m]=(0,a.useState)(s),[u,h]=(0,a.useState)(""),[p,g]=(0,a.useState)(null),[b,v]=(0,a.useState)(null),[j,f]=(0,a.useState)(!1),[N,y]=(0,a.useState)(""),[w,k]=(0,a.useState)(""),[q,C]=(0,a.useState)(""),_=(()=>{let e=[...Z];if("delivered"===s)if(o?.role!==G.gG.SUPERVISOR&&o?.role!==G.gG.MANAGER)return[];else e.unshift({value:"out_for_delivery",label:"إرجاع إلى قيد التوصيل",icon:S.A,color:"bg-indigo-500",description:"إرجاع الطلب المسلم إلى حالة قيد التوصيل",requiresReason:!0});return["returned","cancelled","archived"].includes(s)&&o?.role!==G.gG.MANAGER?[]:e})(),R=_.length>0,T=()=>"delivered"===s&&o?.role===G.gG.COURIER?"لا يمكن للمندوب تحديث الطلبات التي تم تسليمها. يرجى التواصل مع المشرف أو المدير.":["returned","cancelled","archived"].includes(s)&&o?.role!==G.gG.MANAGER?"لا يمكن تحديث هذا الطلب. يرجى التواصل مع المدير.":"لا يمكن تحديث حالة هذا الطلب حالياً.",B=async()=>{let e=Z.find(e=>e.value===x);if(x===s&&!u&&!p)return void alert("يرجى تغيير الحالة أو إضافة ملاحظات أو صورة");if(e?.requiresReason&&(!N.trim()||!p))return void alert("هذه الحالة تتطلب إضافة سبب وصورة");f(!0);try{await new Promise(e=>setTimeout(e,1e3));let s=e?.requiresReason?`${N}
${u}`:u;if("partial_delivery"===x){let e=[];w&&e.push(`عدد القطع الراجعة: ${w}`),q&&e.push(`السعر الجديد: ${q} دينار عراقي`),e.length>0&&(s=s?`${s}
${e.join("\n")}`:e.join("\n"))}let t=r||d;t&&t(x,s,p||void 0),alert("تم تحديث حالة الطلب بنجاح!"),c()}catch(e){alert("حدث خطأ أثناء تحديث الحالة")}finally{f(!1)}},D=Z.find(e=>e.value===s);return Z.find(e=>e.value===x),(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",dir:"rtl",children:(0,t.jsxs)(l.Zp,{className:"w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white/95 backdrop-blur-sm border-0 shadow-2xl",children:[(0,t.jsxs)(l.aR,{className:"bg-gradient-to-r from-blue-50 to-purple-50 border-b",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(l.ZB,{className:"text-xl font-bold text-gray-800 flex items-center gap-2",children:[(0,t.jsx)(n.A,{className:"h-6 w-6 text-blue-600"}),"تحديث حالة الطلب ",e]}),(0,t.jsx)(i.$,{onClick:c,variant:"ghost",size:"sm",className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,t.jsxs)(l.BT,{className:"text-gray-600",children:["الحالة الحالية:",(0,t.jsx)($.E,{className:`mr-2 ${D?.color} text-white`,children:D?.label}),!R&&(0,t.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,t.jsxs)("p",{className:"text-yellow-800 text-sm",children:["⚠️ ",T()]})})]})]}),(0,t.jsx)(l.Wu,{className:"p-6 space-y-6",children:R?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4 text-gray-800",children:"اختيار الحالة الجديدة"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:_.map(e=>{let r=e.icon,a=x===e.value,l=s===e.value;return(0,t.jsx)("button",{onClick:()=>m(e.value),disabled:l,className:`p-4 rounded-xl border-2 transition-all duration-200 text-right ${a?"border-blue-500 bg-blue-50 shadow-md":l?"border-gray-300 bg-gray-100 opacity-50 cursor-not-allowed":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}`,children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:`p-2 rounded-lg ${e.color}`,children:(0,t.jsx)(r,{className:"h-5 w-5 text-white"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"font-medium text-gray-800",children:e.label}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]}),a&&(0,t.jsx)(P.A,{className:"h-5 w-5 text-blue-500"}),l&&(0,t.jsx)($.E,{variant:"secondary",className:"text-xs",children:"حالية"})]})},e.value)})})]}),Z.find(e=>e.value===x)?.requiresReason&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-3 text-gray-800 flex items-center gap-2",children:[(0,t.jsx)(M.A,{className:"h-5 w-5 text-red-500"}),"السبب (مطلوب)"]}),(0,t.jsx)(A.T,{value:N,onChange:e=>y(e.target.value),placeholder:"يرجى توضيح سبب هذه الحالة...",className:"min-h-[80px] resize-none border-red-200 focus:ring-red-500 focus:border-red-500",required:!0})]}),"partial_delivery"===x&&(0,t.jsxs)("div",{className:"space-y-4 p-4 bg-cyan-50 rounded-lg border border-cyan-200",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-cyan-800",children:"تفاصيل التسليم الجزئي"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2 text-gray-700",children:"عدد القطع الراجعة"}),(0,t.jsx)("input",{type:"number",value:w,onChange:e=>k(e.target.value),placeholder:"أدخل عدد القطع الراجعة",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent",min:"0"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2 text-gray-700",children:"السعر الجديد (دينار عراقي)"}),(0,t.jsx)("input",{type:"number",value:q,onChange:e=>C(e.target.value),placeholder:"أدخل السعر الجديد",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent",min:"0"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-800",children:"ملاحظات إضافية"}),(0,t.jsx)(A.T,{value:u,onChange:e=>h(e.target.value),placeholder:"أضف ملاحظات حول تحديث الحالة...",className:"min-h-[100px] resize-none"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-800",children:"إرفاق صورة"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>document.getElementById("image-upload")?.click(),className:"flex items-center gap-2",children:[(0,t.jsx)(E.A,{className:"h-4 w-4"}),"اختيار صورة"]}),(0,t.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{document.getElementById("image-upload")?.click()},className:"flex items-center gap-2",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),"التقاط صورة"]})]}),(0,t.jsx)("input",{id:"image-upload",type:"file",accept:"image/*",onChange:e=>{let s=e.target.files?.[0];if(s){g(s);let e=new FileReader;e.onload=e=>{v(e.target?.result)},e.readAsDataURL(s)}},className:"hidden"}),b&&(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("img",{src:b,alt:"معاينة الصورة",className:"w-full max-w-xs h-48 object-cover rounded-lg border"}),(0,t.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>{g(null),v(null)},className:"absolute top-2 left-2",children:"حذف"})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-3 pt-4 border-t",children:[(0,t.jsx)(i.$,{onClick:B,disabled:j,className:"flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:j?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"جاري التحديث..."]}):"تحديث الحالة"}),(0,t.jsx)(i.$,{onClick:c,variant:"outline",disabled:j,className:"flex-1",children:"إلغاء"})]})]}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(n.A,{className:"h-8 w-8 text-yellow-600"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"لا يمكن تحديث هذا الطلب"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:T()})]}),(0,t.jsx)(i.$,{onClick:c,variant:"outline",className:"w-full max-w-xs",children:"إغلاق"})]})})]})})}var B=r(85814),D=r.n(B),U=r(4780);let L={id:"1",trackingNumber:"MRS001",senderName:"أحمد محمد",senderPhone:"07901234567",senderAddress:"بغداد - الكرادة",recipientName:"فاطمة علي",recipientPhone:"07801234567",recipientAddress:"بغداد - الجادرية - شارع الأطباء - بناية 15 - الطابق الثالث",amount:15e4,status:"delivered",notes:"يرجى التسليم بعد الساعة 2 ظهراً",createdAt:"2024-01-15T10:30:00Z",updatedAt:"2024-01-15T14:45:00Z",courierName:"علي حسين",courierPhone:"07701234567"},O={pending:"في الانتظار",processing:"قيد المعالجة",shipped:"تم الشحن",out_for_delivery:"قيد التوصيل",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"},V={pending:"bg-yellow-100 text-yellow-800 border-yellow-200",processing:"bg-blue-100 text-blue-800 border-blue-200",shipped:"bg-purple-100 text-purple-800 border-purple-200",out_for_delivery:"bg-indigo-100 text-indigo-800 border-indigo-200",delivered:"bg-green-100 text-green-800 border-green-200",returned:"bg-orange-100 text-orange-800 border-orange-200",cancelled:"bg-red-100 text-red-800 border-red-200",postponed:"bg-gray-100 text-gray-800 border-gray-200"};function Y({params:e}){let{user:s}=(0,I.A)(),[r,o]=(0,a.useState)(L),[k,A]=(0,a.useState)(!1),[$,q]=(0,a.useState)(!1);return(0,t.jsx)("div",{className:"min-h-screen bg-background p-4 md:p-6 lg:p-8",dir:"rtl",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto space-y-8",children:[(0,t.jsx)(w.A,{}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl shadow-2xl mb-4",children:(0,t.jsx)(n.A,{className:"h-8 w-8 text-white"})}),(0,t.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2",children:"تفاصيل الطلب"}),(0,t.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 text-lg",children:["رقم التتبع: ",r.trackingNumber]})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center mb-6",children:[(0,t.jsx)(D(),{href:"/orders",children:(0,t.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),"العودة للطلبات"]})}),s?.role!=="courier"&&(0,t.jsx)(D(),{href:`/orders/${r.id}/edit`,children:(0,t.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4"}),"تعديل الطلب"]})}),(0,t.jsxs)(i.$,{onClick:()=>A(!0),className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4"}),"تحديث الحالة"]}),(0,t.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2",onClick:()=>{let e=`📦 تفاصيل الطلب ${r.trackingNumber}

👤 المرسل: ${r.senderName}
📱 هاتف المرسل: ${r.senderPhone}
📍 عنوان المرسل: ${r.senderAddress}

👥 المستلم: ${r.recipientName}
📱 هاتف المستلم: ${r.recipientPhone}
📍 عنوان المستلم: ${r.recipientAddress}

💰 المبلغ: ${(0,U.vv)(r.amount)}
📊 الحالة: ${O[r.status]}
📅 تاريخ الإنشاء: ${(0,U.Yq)(r.createdAt)}

📝 ملاحظات: ${r.notes||"لا توجد ملاحظات"}`.trim();navigator.clipboard.writeText(e).then(()=>{alert("تم نسخ التفاصيل بنجاح!")}).catch(()=>{alert("فشل في نسخ التفاصيل")})},children:[(0,t.jsx)(x,{className:"h-4 w-4"}),"نسخ التفاصيل"]}),(0,t.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2",onClick:()=>{let e=`🚚 *تحديث حالة الطلب*

📦 *رقم التتبع:* ${r.trackingNumber}
🔄 *الحالة الحالية:* ${O[r.status]}

👤 *المستلم:* ${r.recipientName}
📱 *الهاتف:* ${r.recipientPhone}
📍 *العنوان:* ${r.recipientAddress}

💰 *المبلغ:* ${(0,U.vv)(r.amount)}
📅 *تاريخ الإنشاء:* ${(0,U.Yq)(r.createdAt)}

${r.notes?`📝 *ملاحظات:* ${r.notes}`:""}

---
🚛 *مرسال - خدمة توصيل موثوقة*`,s=`https://wa.me/?text=${encodeURIComponent(e)}`;window.open(s,"_blank")},children:[(0,t.jsx)(m,{className:"h-4 w-4"}),"مشاركة"]}),s?.role==="manager"&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2",onClick:()=>{let e=`
      <div style="width: 110mm; height: 130mm; padding: 10mm; font-family: Arial, sans-serif; direction: rtl;">
        <div style="text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 10px;">
          <h2>مكتب علي الشيباني للتوصيل السريع</h2>
          <p>وصل استلام</p>
        </div>
        <div style="margin-bottom: 10px;">
          <strong>رقم التتبع:</strong> ${r.trackingNumber}
        </div>
        <div style="margin-bottom: 10px;">
          <strong>المرسل:</strong> ${r.senderName}
        </div>
        <div style="margin-bottom: 10px;">
          <strong>المستلم:</strong> ${r.recipientName}
        </div>
        <div style="margin-bottom: 10px;">
          <strong>رقم الهاتف:</strong> ${r.recipientPhone}
        </div>
        <div style="margin-bottom: 10px;">
          <strong>العنوان:</strong> ${r.recipientAddress}
        </div>
        <div style="margin-bottom: 10px;">
          <strong>المبلغ:</strong> ${(0,U.vv)(r.amount)}
        </div>
        <div style="margin-bottom: 10px;">
          <strong>التاريخ:</strong> ${(0,U.Yq)(r.createdAt)}
        </div>
        <div style="text-align: center; margin-top: 20px;">
          <div style="border: 1px solid #000; padding: 5px; display: inline-block;">
            ${r.trackingNumber}
          </div>
        </div>
      </div>
    `,s=window.open("","_blank");s&&(s.document.write(e),s.document.close(),s.print())},children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),"طباعة الوصل"]}),(0,t.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2",onClick:()=>{let e=new Blob([`
مكتب علي الشيباني للتوصيل السريع
وصل استلام

رقم التتبع: ${r.trackingNumber}
المرسل: ${r.senderName}
المستلم: ${r.recipientName}
رقم الهاتف: ${r.recipientPhone}
العنوان: ${r.recipientAddress}
المبلغ: ${(0,U.vv)(r.amount)}
التاريخ: ${(0,U.Yq)(r.createdAt)}
    `],{type:"text/plain;charset=utf-8"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download=`receipt-${r.trackingNumber}.txt`,document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(s)},children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),"تحميل الوصل"]})]}),(0,t.jsxs)(i.$,{onClick:()=>q(!0),variant:"outline",className:"flex items-center gap-2",children:[(0,t.jsx)(p,{className:"h-4 w-4"}),"نظام التذاكر"]}),(0,t.jsx)(D(),{href:"/",children:(0,t.jsxs)(i.$,{className:"flex items-center gap-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),"الصفحة الرئيسية"]})})]}),(0,t.jsx)(l.Zp,{className:"border-2 border-primary/20 shadow-xl",children:(0,t.jsx)(l.aR,{className:"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(n.A,{className:"h-6 w-6 text-primary"}),(0,t.jsxs)("div",{children:[(0,t.jsx)(l.ZB,{className:"text-xl",children:"حالة الطلب"}),(0,t.jsxs)(l.BT,{children:["آخر تحديث: ",(0,U.Yq)(r.updatedAt)]})]})]}),(0,t.jsx)("div",{className:"flex items-center gap-3",children:(0,t.jsx)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${V[r.status]}`,children:O[r.status]})})]})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)(l.Zp,{className:"shadow-lg hover:shadow-xl transition-shadow",children:[(0,t.jsx)(l.aR,{className:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950",children:(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2 text-green-700 dark:text-green-300",children:[(0,t.jsx)(b.A,{className:"h-5 w-5"}),"معلومات المرسل"]})}),(0,t.jsxs)(l.Wu,{className:"space-y-4 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(b.A,{className:"h-5 w-5 text-gray-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"الاسم"}),(0,t.jsx)("p",{className:"font-medium",children:r.senderName})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 text-gray-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"رقم الهاتف"}),(0,t.jsx)("p",{className:"font-medium",children:r.senderPhone})]})]}),(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 text-gray-500 mt-1"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"العنوان"}),(0,t.jsx)("p",{className:"font-medium",children:r.senderAddress})]})]})]})]}),(0,t.jsxs)(l.Zp,{className:"shadow-lg hover:shadow-xl transition-shadow",children:[(0,t.jsx)(l.aR,{className:"bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950 dark:to-cyan-950",children:(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2 text-blue-700 dark:text-blue-300",children:[(0,t.jsx)(b.A,{className:"h-5 w-5"}),"معلومات المستلم"]})}),(0,t.jsxs)(l.Wu,{className:"space-y-4 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(b.A,{className:"h-5 w-5 text-gray-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"الاسم"}),(0,t.jsx)("p",{className:"font-medium",children:r.recipientName})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 text-gray-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"رقم الهاتف"}),(0,t.jsxs)("p",{className:"font-medium",children:[(0,t.jsx)("a",{href:`tel:${r.recipientPhone}`,className:"text-blue-600 hover:underline",children:r.recipientPhone}),(0,t.jsx)("span",{className:"mx-2",children:"|"}),(0,t.jsx)("a",{href:`https://wa.me/${r.recipientPhone.replace(/^0/,"964")}`,target:"_blank",rel:"noopener noreferrer",className:"text-green-600 hover:underline",children:"واتساب"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 text-gray-500 mt-1"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"العنوان"}),(0,t.jsx)("p",{className:"font-medium",children:r.recipientAddress})]})]})]})]})]}),(0,t.jsxs)(l.Zp,{className:"shadow-lg hover:shadow-xl transition-shadow",children:[(0,t.jsx)(l.aR,{className:"bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950 dark:to-pink-950",children:(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2 text-purple-700 dark:text-purple-300",children:[(0,t.jsx)(n.A,{className:"h-5 w-5"}),"معلومات الطلب"]})}),(0,t.jsxs)(l.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(f.A,{className:"h-5 w-5 text-gray-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"المبلغ"}),(0,t.jsx)("p",{className:"font-bold text-lg text-green-600",children:(0,U.vv)(r.amount)})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(N,{className:"h-5 w-5 text-gray-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"تاريخ الإنشاء"}),(0,t.jsx)("p",{className:"font-medium",children:(0,U.Yq)(r.createdAt)})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(b.A,{className:"h-5 w-5 text-gray-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"المندوب"}),(0,t.jsx)("p",{className:"font-medium",children:r.courierName||"غير محدد"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 text-gray-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"هاتف المندوب"}),(0,t.jsx)("p",{className:"font-medium",children:r.courierPhone||"غير محدد"})]})]})]}),r.notes&&(0,t.jsx)("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 text-gray-500 mt-1"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"ملاحظات"}),(0,t.jsx)("p",{className:"font-medium",children:r.notes})]})]})})]})]}),k&&(0,t.jsx)(T,{orderId:r.id,currentStatus:r.status,onUpdate:(e,s,t)=>{o({...r,status:e,notes:s,updatedAt:new Date().toISOString()}),A(!1),alert("تم تحديث حالة الطلب بنجاح!")},onClose:()=>A(!1)}),$&&(0,t.jsx)(R,{orderId:r.id,isOpen:$,onClose:()=>q(!1)})]})})}},51361:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70334:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71444:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},71777:(e,s,r)=>{"use strict";async function t(){return[{id:"1"},{id:"2"},{id:"3"}]}function a({children:e}){return e}r.r(s),r.d(s,{default:()=>a,generateStaticParams:()=>t})},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82151:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=r(65239),a=r(48088),l=r(88170),i=r.n(l),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let c={children:["",{children:["orders",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,30566)),"E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71777)),"E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\layout.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/orders/[id]/page",pathname:"/orders/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},84115:(e,s,r)=>{Promise.resolve().then(r.bind(r,30566))},89667:(e,s,r)=>{"use strict";r.d(s,{p:()=>l});var t=r(60687);r(43210);var a=r(4780);function l({className:e,type:s,...r}){return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},97992:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,863,860,814,451,610,809],()=>r(82151));module.exports=t})();