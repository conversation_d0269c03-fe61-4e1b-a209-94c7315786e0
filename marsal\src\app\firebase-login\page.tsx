'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Eye, EyeOff, Database, Wifi, WifiOff } from 'lucide-react';
import { firebaseAuthService } from '@/lib/firebase-auth';
import { testFirebaseConnection } from '@/lib/firebase';

export default function FirebaseLoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const router = useRouter();

  // Test Firebase connection on component mount
  useEffect(() => {
    checkConnection();
  }, []);

  const checkConnection = async () => {
    try {
      setConnectionStatus('checking');
      const result = await testFirebaseConnection();
      setConnectionStatus(result.success ? 'connected' : 'disconnected');
      
      if (!result.success) {
        setError(`فشل الاتصال بـ Firebase: ${result.message}`);
      }
    } catch (error) {
      setConnectionStatus('disconnected');
      setError('فشل في اختبار الاتصال بـ Firebase');
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (connectionStatus !== 'connected') {
      setError('يجب الاتصال بـ Firebase أولاً');
      return;
    }

    if (!username.trim() || !password.trim()) {
      setError('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }

    setLoading(true);
    setError('');

    try {
      console.log('🔐 محاولة تسجيل الدخول مع Firebase...');
      
      const result = await firebaseAuthService.login(username.trim(), password);
      
      if (result.success && result.user) {
        console.log('✅ تم تسجيل الدخول بنجاح:', result.user.name);
        
        // Store user data in localStorage
        localStorage.setItem('currentUser', JSON.stringify(result.user));
        localStorage.setItem('isAuthenticated', 'true');
        
        // Redirect to main app
        router.push('/');
      } else {
        setError(result.error || 'فشل في تسجيل الدخول');
      }
    } catch (error: any) {
      console.error('❌ خطأ في تسجيل الدخول:', error);
      setError(error.message || 'حدث خطأ غير متوقع');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickLogin = async (userType: 'manager' | 'supervisor' | 'courier') => {
    const credentials = {
      manager: { username: 'manager', password: '123456' },
      supervisor: { username: 'supervisor', password: '123456' },
      courier: { username: 'courier', password: '123456' }
    };

    setUsername(credentials[userType].username);
    setPassword(credentials[userType].password);
    
    // Auto-submit after setting values
    setTimeout(() => {
      const form = document.querySelector('form');
      if (form) {
        form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
      }
    }, 100);
  };

  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'checking':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'connected':
        return <Wifi className="h-4 w-4 text-green-600" />;
      case 'disconnected':
        return <WifiOff className="h-4 w-4 text-red-600" />;
    }
  };

  const getConnectionText = () => {
    switch (connectionStatus) {
      case 'checking':
        return 'جاري فحص الاتصال بـ Firebase...';
      case 'connected':
        return 'متصل بـ Firebase بنجاح';
      case 'disconnected':
        return 'غير متصل بـ Firebase';
    }
  };

  const getConnectionColor = () => {
    switch (connectionStatus) {
      case 'checking':
        return 'border-yellow-200 bg-yellow-50';
      case 'connected':
        return 'border-green-200 bg-green-50';
      case 'disconnected':
        return 'border-red-200 bg-red-50';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="mx-auto h-12 w-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4">
            <Database className="h-6 w-6 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900">تطبيق مرسال</h1>
          <p className="text-gray-600 mt-2">تسجيل الدخول مع Firebase</p>
        </div>

        {/* Connection Status */}
        <Card className={`${getConnectionColor()}`}>
          <CardContent className="pt-4">
            <div className="flex items-center gap-2">
              {getConnectionIcon()}
              <span className="text-sm font-medium">{getConnectionText()}</span>
            </div>
            {connectionStatus === 'disconnected' && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={checkConnection}
                className="mt-2 w-full"
              >
                🔄 إعادة المحاولة
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Login Form */}
        <Card>
          <CardHeader>
            <CardTitle>تسجيل الدخول</CardTitle>
            <CardDescription>
              أدخل بيانات الدخول للوصول إلى التطبيق
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">اسم المستخدم</Label>
                <Input
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="أدخل اسم المستخدم"
                  disabled={loading || connectionStatus !== 'connected'}
                  className="text-right"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">كلمة المرور</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="أدخل كلمة المرور"
                    disabled={loading || connectionStatus !== 'connected'}
                    className="text-right pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={loading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button 
                type="submit" 
                className="w-full" 
                disabled={loading || connectionStatus !== 'connected'}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    جاري تسجيل الدخول...
                  </>
                ) : (
                  'تسجيل الدخول'
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Quick Login Buttons */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">تسجيل دخول سريع (للاختبار)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => handleQuickLogin('manager')}
              disabled={loading || connectionStatus !== 'connected'}
            >
              👑 مدير النظام (manager / 123456)
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => handleQuickLogin('supervisor')}
              disabled={loading || connectionStatus !== 'connected'}
            >
              👨‍💼 المتابع (supervisor / 123456)
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => handleQuickLogin('courier')}
              disabled={loading || connectionStatus !== 'connected'}
            >
              🚚 المندوب (courier / 123456)
            </Button>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>تطبيق مرسال - نظام إدارة التوصيل</p>
          <p>مدعوم بـ Firebase</p>
        </div>
      </div>
    </div>
  );
}
