(()=>{var e={};e.id=171,e.ids=[171],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,r,t)=>{"use strict";t.d(r,{Yq:()=>d,cn:()=>a,ps:()=>p,qY:()=>u,r6:()=>o,vv:()=>i,y7:()=>l,zC:()=>c});var s=t(49384),n=t(82348);function a(...e){return(0,n.QP)((0,s.$)(e))}function i(e){return`${e.toLocaleString("ar-IQ")} د.ع`}function d(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function o(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function l(){let e=Date.now().toString().slice(-6),r=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return`MRS${e}${r}`}function c(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function u(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function p(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}},8819:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19080:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(60687);t(43210);var n=t(8730),a=t(24224),i=t(4780);let d=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:r,size:t,asChild:a=!1,...o}){let l=a?n.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(d({variant:r,size:t,className:e})),...o})}},32668:(e,r,t)=>{Promise.resolve().then(t.bind(t,97941))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>a,aR:()=>i});var s=t(60687);t(43210);var n=t(4780);function a({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...r})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69153:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=t(65239),n=t(48088),a=t(88170),i=t.n(a),d=t(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(r,o);let l={children:["",{children:["orders",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,75692)),"E:\\Marsal\\marsal\\src\\app\\orders\\new\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\Marsal\\marsal\\src\\app\\orders\\new\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/orders/new/page",pathname:"/orders/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},70334:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74524:(e,r,t)=>{Promise.resolve().then(t.bind(t,75692))},75692:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\orders\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\orders\\new\\page.tsx","default")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>a});var s=t(60687);t(43210);var n=t(4780);function a({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97941:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var s=t(60687),n=t(43210),a=t(44493),i=t(29523),d=t(89667),o=t(19080),l=t(70334),c=t(8819),u=t(85814),p=t.n(u);function x(){let[e,r]=(0,n.useState)({senderName:"",senderPhone:"",senderAddress:"",recipientName:"",recipientPhone:"",recipientAddress:"",amount:"",notes:""}),t=e=>{let{name:t,value:s}=e.target;r(e=>({...e,[t]:s}))};return(0,s.jsx)("div",{className:"min-h-screen bg-background p-4 md:p-6 lg:p-8",dir:"rtl",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-primary flex items-center gap-2",children:[(0,s.jsx)(o.A,{className:"h-8 w-8"}),"طلب جديد"]}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:"إضافة طلب توصيل جديد إلى النظام"})]}),(0,s.jsx)(p(),{href:"/orders",children:(0,s.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),"العودة للطلبات"]})})]}),(0,s.jsxs)("form",{onSubmit:r=>{r.preventDefault(),console.log("Order data:",e),alert("تم إضافة الطلب بنجاح!")},className:"space-y-6",children:[(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"بيانات المرسل"}),(0,s.jsx)(a.BT,{children:"معلومات الشخص أو الشركة المرسلة"})]}),(0,s.jsxs)(a.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اسم المرسل *"}),(0,s.jsx)(d.p,{name:"senderName",value:e.senderName,onChange:t,placeholder:"أدخل اسم المرسل",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"هاتف المرسل *"}),(0,s.jsx)(d.p,{name:"senderPhone",value:e.senderPhone,onChange:t,placeholder:"07xxxxxxxxx",required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"عنوان المرسل *"}),(0,s.jsx)(d.p,{name:"senderAddress",value:e.senderAddress,onChange:t,placeholder:"العنوان الكامل للمرسل",required:!0})]})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"بيانات المستلم"}),(0,s.jsx)(a.BT,{children:"معلومات الشخص المستلم للطلب"})]}),(0,s.jsxs)(a.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اسم المستلم *"}),(0,s.jsx)(d.p,{name:"recipientName",value:e.recipientName,onChange:t,placeholder:"أدخل اسم المستلم",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"هاتف المستلم *"}),(0,s.jsx)(d.p,{name:"recipientPhone",value:e.recipientPhone,onChange:t,placeholder:"07xxxxxxxxx",required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"عنوان المستلم *"}),(0,s.jsx)(d.p,{name:"recipientAddress",value:e.recipientAddress,onChange:t,placeholder:"العنوان الكامل للمستلم",required:!0})]})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"تفاصيل الطلب"}),(0,s.jsx)(a.BT,{children:"المبلغ والملاحظات الإضافية"})]}),(0,s.jsxs)(a.Wu,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"المبلغ (دينار عراقي) *"}),(0,s.jsx)(d.p,{name:"amount",type:"number",value:e.amount,onChange:t,placeholder:"0",required:!0})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"ملاحظات"}),(0,s.jsx)("textarea",{name:"notes",value:e.notes,onChange:t,placeholder:"أي ملاحظات إضافية...",className:"w-full p-3 border border-border rounded-md bg-background resize-none",rows:3})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,s.jsx)(p(),{href:"/orders",children:(0,s.jsx)(i.$,{variant:"outline",children:"إلغاء"})}),(0,s.jsxs)(i.$,{type:"submit",className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),"حفظ الطلب"]})]})]})]})})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,863,860,814,610],()=>t(69153));module.exports=s})();