"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8779],{22475:(t,e,r)=>{r.d(e,{UE:()=>tR,ll:()=>tb,rD:()=>tU,UU:()=>tA,jD:()=>tL,ER:()=>tT,cY:()=>tx,BN:()=>tE,Ej:()=>tB});let n=["top","right","bottom","left"],i=Math.min,o=Math.max,f=Math.round,u=Math.floor,a=t=>({x:t,y:t}),s={left:"right",right:"left",bottom:"top",top:"bottom"},l={start:"end",end:"start"};function c(t,e){return"function"==typeof t?t(e):t}function h(t){return t.split("-")[0]}function p(t){return t.split("-")[1]}function d(t){return"x"===t?"y":"x"}function y(t){return"y"===t?"height":"width"}let g=new Set(["top","bottom"]);function m(t){return g.has(h(t))?"y":"x"}function v(t){return t.replace(/start|end/g,t=>l[t])}let w=["left","right"],b=["right","left"],x=["top","bottom"],E=["bottom","top"];function A(t){return t.replace(/left|right|bottom|top/g,t=>s[t])}function B(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function L(t){let{x:e,y:r,width:n,height:i}=t;return{width:n,height:i,top:r,left:e,right:e+n,bottom:r+i,x:e,y:r}}function R(t,e,r){let n,{reference:i,floating:o}=t,f=m(e),u=d(m(e)),a=y(u),s=h(e),l="y"===f,c=i.x+i.width/2-o.width/2,g=i.y+i.height/2-o.height/2,v=i[a]/2-o[a]/2;switch(s){case"top":n={x:c,y:i.y-o.height};break;case"bottom":n={x:c,y:i.y+i.height};break;case"right":n={x:i.x+i.width,y:g};break;case"left":n={x:i.x-o.width,y:g};break;default:n={x:i.x,y:i.y}}switch(p(e)){case"start":n[u]-=v*(r&&l?-1:1);break;case"end":n[u]+=v*(r&&l?-1:1)}return n}let T=async(t,e,r)=>{let{placement:n="bottom",strategy:i="absolute",middleware:o=[],platform:f}=r,u=o.filter(Boolean),a=await (null==f.isRTL?void 0:f.isRTL(e)),s=await f.getElementRects({reference:t,floating:e,strategy:i}),{x:l,y:c}=R(s,n,a),h=n,p={},d=0;for(let r=0;r<u.length;r++){let{name:o,fn:y}=u[r],{x:g,y:m,data:v,reset:w}=await y({x:l,y:c,initialPlacement:n,placement:h,strategy:i,middlewareData:p,rects:s,platform:f,elements:{reference:t,floating:e}});l=null!=g?g:l,c=null!=m?m:c,p={...p,[o]:{...p[o],...v}},w&&d<=50&&(d++,"object"==typeof w&&(w.placement&&(h=w.placement),w.rects&&(s=!0===w.rects?await f.getElementRects({reference:t,floating:e,strategy:i}):w.rects),{x:l,y:c}=R(s,h,a)),r=-1)}return{x:l,y:c,placement:h,strategy:i,middlewareData:p}};async function U(t,e){var r;void 0===e&&(e={});let{x:n,y:i,platform:o,rects:f,elements:u,strategy:a}=t,{boundary:s="clippingAncestors",rootBoundary:l="viewport",elementContext:h="floating",altBoundary:p=!1,padding:d=0}=c(e,t),y=B(d),g=u[p?"floating"===h?"reference":"floating":h],m=L(await o.getClippingRect({element:null==(r=await (null==o.isElement?void 0:o.isElement(g)))||r?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(u.floating)),boundary:s,rootBoundary:l,strategy:a})),v="floating"===h?{x:n,y:i,width:f.floating.width,height:f.floating.height}:f.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(u.floating)),b=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},x=L(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:u,rect:v,offsetParent:w,strategy:a}):v);return{top:(m.top-x.top+y.top)/b.y,bottom:(x.bottom-m.bottom+y.bottom)/b.y,left:(m.left-x.left+y.left)/b.x,right:(x.right-m.right+y.right)/b.x}}function S(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function I(t){return n.some(e=>t[e]>=0)}let O=new Set(["left","top"]);async function C(t,e){let{placement:r,platform:n,elements:i}=t,o=await (null==n.isRTL?void 0:n.isRTL(i.floating)),f=h(r),u=p(r),a="y"===m(r),s=O.has(f)?-1:1,l=o&&a?-1:1,d=c(e,t),{mainAxis:y,crossAxis:g,alignmentAxis:v}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return u&&"number"==typeof v&&(g="end"===u?-1*v:v),a?{x:g*l,y:y*s}:{x:y*s,y:g*l}}function D(){return"undefined"!=typeof window}function k(t){return M(t)?(t.nodeName||"").toLowerCase():"#document"}function P(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function j(t){var e;return null==(e=(M(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function M(t){return!!D()&&(t instanceof Node||t instanceof P(t).Node)}function N(t){return!!D()&&(t instanceof Element||t instanceof P(t).Element)}function W(t){return!!D()&&(t instanceof HTMLElement||t instanceof P(t).HTMLElement)}function F(t){return!!D()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof P(t).ShadowRoot)}let V=new Set(["inline","contents"]);function H(t){let{overflow:e,overflowX:r,overflowY:n,display:i}=Q(t);return/auto|scroll|overlay|hidden|clip/.test(e+n+r)&&!V.has(i)}let z=new Set(["table","td","th"]),_=[":popover-open",":modal"];function q(t){return _.some(e=>{try{return t.matches(e)}catch(t){return!1}})}let Y=["transform","translate","scale","rotate","perspective"],$=["transform","translate","scale","rotate","perspective","filter"],K=["paint","layout","strict","content"];function J(t){let e=X(),r=N(t)?Q(t):t;return Y.some(t=>!!r[t]&&"none"!==r[t])||!!r.containerType&&"normal"!==r.containerType||!e&&!!r.backdropFilter&&"none"!==r.backdropFilter||!e&&!!r.filter&&"none"!==r.filter||$.some(t=>(r.willChange||"").includes(t))||K.some(t=>(r.contain||"").includes(t))}function X(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Z=new Set(["html","body","#document"]);function G(t){return Z.has(k(t))}function Q(t){return P(t).getComputedStyle(t)}function tt(t){return N(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function te(t){if("html"===k(t))return t;let e=t.assignedSlot||t.parentNode||F(t)&&t.host||j(t);return F(e)?e.host:e}function tr(t,e,r){var n;void 0===e&&(e=[]),void 0===r&&(r=!0);let i=function t(e){let r=te(e);return G(r)?e.ownerDocument?e.ownerDocument.body:e.body:W(r)&&H(r)?r:t(r)}(t),o=i===(null==(n=t.ownerDocument)?void 0:n.body),f=P(i);if(o){let t=tn(f);return e.concat(f,f.visualViewport||[],H(i)?i:[],t&&r?tr(t):[])}return e.concat(i,tr(i,[],r))}function tn(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function ti(t){let e=Q(t),r=parseFloat(e.width)||0,n=parseFloat(e.height)||0,i=W(t),o=i?t.offsetWidth:r,u=i?t.offsetHeight:n,a=f(r)!==o||f(n)!==u;return a&&(r=o,n=u),{width:r,height:n,$:a}}function to(t){return N(t)?t:t.contextElement}function tf(t){let e=to(t);if(!W(e))return a(1);let r=e.getBoundingClientRect(),{width:n,height:i,$:o}=ti(e),u=(o?f(r.width):r.width)/n,s=(o?f(r.height):r.height)/i;return u&&Number.isFinite(u)||(u=1),s&&Number.isFinite(s)||(s=1),{x:u,y:s}}let tu=a(0);function ta(t){let e=P(t);return X()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:tu}function ts(t,e,r,n){var i;void 0===e&&(e=!1),void 0===r&&(r=!1);let o=t.getBoundingClientRect(),f=to(t),u=a(1);e&&(n?N(n)&&(u=tf(n)):u=tf(t));let s=(void 0===(i=r)&&(i=!1),n&&(!i||n===P(f))&&i)?ta(f):a(0),l=(o.left+s.x)/u.x,c=(o.top+s.y)/u.y,h=o.width/u.x,p=o.height/u.y;if(f){let t=P(f),e=n&&N(n)?P(n):n,r=t,i=tn(r);for(;i&&n&&e!==r;){let t=tf(i),e=i.getBoundingClientRect(),n=Q(i),o=e.left+(i.clientLeft+parseFloat(n.paddingLeft))*t.x,f=e.top+(i.clientTop+parseFloat(n.paddingTop))*t.y;l*=t.x,c*=t.y,h*=t.x,p*=t.y,l+=o,c+=f,i=tn(r=P(i))}}return L({width:h,height:p,x:l,y:c})}function tl(t,e){let r=tt(t).scrollLeft;return e?e.left+r:ts(j(t)).left+r}function tc(t,e,r){void 0===r&&(r=!1);let n=t.getBoundingClientRect();return{x:n.left+e.scrollLeft-(r?0:tl(t,n)),y:n.top+e.scrollTop}}let th=new Set(["absolute","fixed"]);function tp(t,e,r){let n;if("viewport"===e)n=function(t,e){let r=P(t),n=j(t),i=r.visualViewport,o=n.clientWidth,f=n.clientHeight,u=0,a=0;if(i){o=i.width,f=i.height;let t=X();(!t||t&&"fixed"===e)&&(u=i.offsetLeft,a=i.offsetTop)}return{width:o,height:f,x:u,y:a}}(t,r);else if("document"===e)n=function(t){let e=j(t),r=tt(t),n=t.ownerDocument.body,i=o(e.scrollWidth,e.clientWidth,n.scrollWidth,n.clientWidth),f=o(e.scrollHeight,e.clientHeight,n.scrollHeight,n.clientHeight),u=-r.scrollLeft+tl(t),a=-r.scrollTop;return"rtl"===Q(n).direction&&(u+=o(e.clientWidth,n.clientWidth)-i),{width:i,height:f,x:u,y:a}}(j(t));else if(N(e))n=function(t,e){let r=ts(t,!0,"fixed"===e),n=r.top+t.clientTop,i=r.left+t.clientLeft,o=W(t)?tf(t):a(1),f=t.clientWidth*o.x,u=t.clientHeight*o.y;return{width:f,height:u,x:i*o.x,y:n*o.y}}(e,r);else{let r=ta(t);n={x:e.x-r.x,y:e.y-r.y,width:e.width,height:e.height}}return L(n)}function td(t){return"static"===Q(t).position}function ty(t,e){if(!W(t)||"fixed"===Q(t).position)return null;if(e)return e(t);let r=t.offsetParent;return j(t)===r&&(r=r.ownerDocument.body),r}function tg(t,e){var r;let n=P(t);if(q(t))return n;if(!W(t)){let e=te(t);for(;e&&!G(e);){if(N(e)&&!td(e))return e;e=te(e)}return n}let i=ty(t,e);for(;i&&(r=i,z.has(k(r)))&&td(i);)i=ty(i,e);return i&&G(i)&&td(i)&&!J(i)?n:i||function(t){let e=te(t);for(;W(e)&&!G(e);){if(J(e))return e;if(q(e))break;e=te(e)}return null}(t)||n}let tm=async function(t){let e=this.getOffsetParent||tg,r=this.getDimensions,n=await r(t.floating);return{reference:function(t,e,r){let n=W(e),i=j(e),o="fixed"===r,f=ts(t,!0,o,e),u={scrollLeft:0,scrollTop:0},s=a(0);if(n||!n&&!o)if(("body"!==k(e)||H(i))&&(u=tt(e)),n){let t=ts(e,!0,o,e);s.x=t.x+e.clientLeft,s.y=t.y+e.clientTop}else i&&(s.x=tl(i));o&&!n&&i&&(s.x=tl(i));let l=!i||n||o?a(0):tc(i,u);return{x:f.left+u.scrollLeft-s.x-l.x,y:f.top+u.scrollTop-s.y-l.y,width:f.width,height:f.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},tv={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:r,offsetParent:n,strategy:i}=t,o="fixed"===i,f=j(n),u=!!e&&q(e.floating);if(n===f||u&&o)return r;let s={scrollLeft:0,scrollTop:0},l=a(1),c=a(0),h=W(n);if((h||!h&&!o)&&(("body"!==k(n)||H(f))&&(s=tt(n)),W(n))){let t=ts(n);l=tf(n),c.x=t.x+n.clientLeft,c.y=t.y+n.clientTop}let p=!f||h||o?a(0):tc(f,s,!0);return{width:r.width*l.x,height:r.height*l.y,x:r.x*l.x-s.scrollLeft*l.x+c.x+p.x,y:r.y*l.y-s.scrollTop*l.y+c.y+p.y}},getDocumentElement:j,getClippingRect:function(t){let{element:e,boundary:r,rootBoundary:n,strategy:f}=t,u=[..."clippingAncestors"===r?q(e)?[]:function(t,e){let r=e.get(t);if(r)return r;let n=tr(t,[],!1).filter(t=>N(t)&&"body"!==k(t)),i=null,o="fixed"===Q(t).position,f=o?te(t):t;for(;N(f)&&!G(f);){let e=Q(f),r=J(f);r||"fixed"!==e.position||(i=null),(o?!r&&!i:!r&&"static"===e.position&&!!i&&th.has(i.position)||H(f)&&!r&&function t(e,r){let n=te(e);return!(n===r||!N(n)||G(n))&&("fixed"===Q(n).position||t(n,r))}(t,f))?n=n.filter(t=>t!==f):i=e,f=te(f)}return e.set(t,n),n}(e,this._c):[].concat(r),n],a=u[0],s=u.reduce((t,r)=>{let n=tp(e,r,f);return t.top=o(n.top,t.top),t.right=i(n.right,t.right),t.bottom=i(n.bottom,t.bottom),t.left=o(n.left,t.left),t},tp(e,a,f));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:tg,getElementRects:tm,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:r}=ti(t);return{width:e,height:r}},getScale:tf,isElement:N,isRTL:function(t){return"rtl"===Q(t).direction}};function tw(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function tb(t,e,r,n){let f;void 0===n&&(n={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:h=!1}=n,p=to(t),d=a||s?[...p?tr(p):[],...tr(e)]:[];d.forEach(t=>{a&&t.addEventListener("scroll",r,{passive:!0}),s&&t.addEventListener("resize",r)});let y=p&&c?function(t,e){let r,n=null,f=j(t);function a(){var t;clearTimeout(r),null==(t=n)||t.disconnect(),n=null}return!function s(l,c){void 0===l&&(l=!1),void 0===c&&(c=1),a();let h=t.getBoundingClientRect(),{left:p,top:d,width:y,height:g}=h;if(l||e(),!y||!g)return;let m=u(d),v=u(f.clientWidth-(p+y)),w={rootMargin:-m+"px "+-v+"px "+-u(f.clientHeight-(d+g))+"px "+-u(p)+"px",threshold:o(0,i(1,c))||1},b=!0;function x(e){let n=e[0].intersectionRatio;if(n!==c){if(!b)return s();n?s(!1,n):r=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==n||tw(h,t.getBoundingClientRect())||s(),b=!1}try{n=new IntersectionObserver(x,{...w,root:f.ownerDocument})}catch(t){n=new IntersectionObserver(x,w)}n.observe(t)}(!0),a}(p,r):null,g=-1,m=null;l&&(m=new ResizeObserver(t=>{let[n]=t;n&&n.target===p&&m&&(m.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=m)||t.observe(e)})),r()}),p&&!h&&m.observe(p),m.observe(e));let v=h?ts(t):null;return h&&function e(){let n=ts(t);v&&!tw(v,n)&&r(),v=n,f=requestAnimationFrame(e)}(),r(),()=>{var t;d.forEach(t=>{a&&t.removeEventListener("scroll",r),s&&t.removeEventListener("resize",r)}),null==y||y(),null==(t=m)||t.disconnect(),m=null,h&&cancelAnimationFrame(f)}}let tx=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var r,n;let{x:i,y:o,placement:f,middlewareData:u}=e,a=await C(e,t);return f===(null==(r=u.offset)?void 0:r.placement)&&null!=(n=u.arrow)&&n.alignmentOffset?{}:{x:i+a.x,y:o+a.y,data:{...a,placement:f}}}}},tE=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:r,y:n,placement:f}=e,{mainAxis:u=!0,crossAxis:a=!1,limiter:s={fn:t=>{let{x:e,y:r}=t;return{x:e,y:r}}},...l}=c(t,e),p={x:r,y:n},y=await U(e,l),g=m(h(f)),v=d(g),w=p[v],b=p[g];if(u){let t="y"===v?"top":"left",e="y"===v?"bottom":"right",r=w+y[t],n=w-y[e];w=o(r,i(w,n))}if(a){let t="y"===g?"top":"left",e="y"===g?"bottom":"right",r=b+y[t],n=b-y[e];b=o(r,i(b,n))}let x=s.fn({...e,[v]:w,[g]:b});return{...x,data:{x:x.x-r,y:x.y-n,enabled:{[v]:u,[g]:a}}}}}},tA=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var r,n,i,o,f;let{placement:u,middlewareData:a,rects:s,initialPlacement:l,platform:g,elements:B}=e,{mainAxis:L=!0,crossAxis:R=!0,fallbackPlacements:T,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:I="none",flipAlignment:O=!0,...C}=c(t,e);if(null!=(r=a.arrow)&&r.alignmentOffset)return{};let D=h(u),k=m(l),P=h(l)===l,j=await (null==g.isRTL?void 0:g.isRTL(B.floating)),M=T||(P||!O?[A(l)]:function(t){let e=A(t);return[v(t),e,v(e)]}(l)),N="none"!==I;!T&&N&&M.push(...function(t,e,r,n){let i=p(t),o=function(t,e,r){switch(t){case"top":case"bottom":if(r)return e?b:w;return e?w:b;case"left":case"right":return e?x:E;default:return[]}}(h(t),"start"===r,n);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(v)))),o}(l,O,I,j));let W=[l,...M],F=await U(e,C),V=[],H=(null==(n=a.flip)?void 0:n.overflows)||[];if(L&&V.push(F[D]),R){let t=function(t,e,r){void 0===r&&(r=!1);let n=p(t),i=d(m(t)),o=y(i),f="x"===i?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return e.reference[o]>e.floating[o]&&(f=A(f)),[f,A(f)]}(u,s,j);V.push(F[t[0]],F[t[1]])}if(H=[...H,{placement:u,overflows:V}],!V.every(t=>t<=0)){let t=((null==(i=a.flip)?void 0:i.index)||0)+1,e=W[t];if(e&&("alignment"!==R||k===m(e)||H.every(t=>t.overflows[0]>0&&m(t.placement)===k)))return{data:{index:t,overflows:H},reset:{placement:e}};let r=null==(o=H.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!r)switch(S){case"bestFit":{let t=null==(f=H.filter(t=>{if(N){let e=m(t.placement);return e===k||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:f[0];t&&(r=t);break}case"initialPlacement":r=l}if(u!==r)return{reset:{placement:r}}}return{}}}},tB=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var r,n;let f,u,{placement:a,rects:s,platform:l,elements:d}=e,{apply:y=()=>{},...g}=c(t,e),v=await U(e,g),w=h(a),b=p(a),x="y"===m(a),{width:E,height:A}=s.floating;"top"===w||"bottom"===w?(f=w,u=b===(await (null==l.isRTL?void 0:l.isRTL(d.floating))?"start":"end")?"left":"right"):(u=w,f="end"===b?"top":"bottom");let B=A-v.top-v.bottom,L=E-v.left-v.right,R=i(A-v[f],B),T=i(E-v[u],L),S=!e.middlewareData.shift,I=R,O=T;if(null!=(r=e.middlewareData.shift)&&r.enabled.x&&(O=L),null!=(n=e.middlewareData.shift)&&n.enabled.y&&(I=B),S&&!b){let t=o(v.left,0),e=o(v.right,0),r=o(v.top,0),n=o(v.bottom,0);x?O=E-2*(0!==t||0!==e?t+e:o(v.left,v.right)):I=A-2*(0!==r||0!==n?r+n:o(v.top,v.bottom))}await y({...e,availableWidth:O,availableHeight:I});let C=await l.getDimensions(d.floating);return E!==C.width||A!==C.height?{reset:{rects:!0}}:{}}}},tL=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){let{rects:r}=e,{strategy:n="referenceHidden",...i}=c(t,e);switch(n){case"referenceHidden":{let t=S(await U(e,{...i,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:I(t)}}}case"escaped":{let t=S(await U(e,{...i,altBoundary:!0}),r.floating);return{data:{escapedOffsets:t,escaped:I(t)}}}default:return{}}}}},tR=t=>({name:"arrow",options:t,async fn(e){let{x:r,y:n,placement:f,rects:u,platform:a,elements:s,middlewareData:l}=e,{element:h,padding:g=0}=c(t,e)||{};if(null==h)return{};let v=B(g),w={x:r,y:n},b=d(m(f)),x=y(b),E=await a.getDimensions(h),A="y"===b,L=A?"clientHeight":"clientWidth",R=u.reference[x]+u.reference[b]-w[b]-u.floating[x],T=w[b]-u.reference[b],U=await (null==a.getOffsetParent?void 0:a.getOffsetParent(h)),S=U?U[L]:0;S&&await (null==a.isElement?void 0:a.isElement(U))||(S=s.floating[L]||u.floating[x]);let I=S/2-E[x]/2-1,O=i(v[A?"top":"left"],I),C=i(v[A?"bottom":"right"],I),D=S-E[x]-C,k=S/2-E[x]/2+(R/2-T/2),P=o(O,i(k,D)),j=!l.arrow&&null!=p(f)&&k!==P&&u.reference[x]/2-(k<O?O:C)-E[x]/2<0,M=j?k<O?k-O:k-D:0;return{[b]:w[b]+M,data:{[b]:P,centerOffset:k-P-M,...j&&{alignmentOffset:M}},reset:j}}}),tT=function(t){return void 0===t&&(t={}),{options:t,fn(e){let{x:r,y:n,placement:i,rects:o,middlewareData:f}=e,{offset:u=0,mainAxis:a=!0,crossAxis:s=!0}=c(t,e),l={x:r,y:n},p=m(i),y=d(p),g=l[y],v=l[p],w=c(u,e),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(a){let t="y"===y?"height":"width",e=o.reference[y]-o.floating[t]+b.mainAxis,r=o.reference[y]+o.reference[t]-b.mainAxis;g<e?g=e:g>r&&(g=r)}if(s){var x,E;let t="y"===y?"width":"height",e=O.has(h(i)),r=o.reference[p]-o.floating[t]+(e&&(null==(x=f.offset)?void 0:x[p])||0)+(e?0:b.crossAxis),n=o.reference[p]+o.reference[t]+(e?0:(null==(E=f.offset)?void 0:E[p])||0)-(e?b.crossAxis:0);v<r?v=r:v>n&&(v=n)}return{[y]:g,[p]:v}}}},tU=(t,e,r)=>{let n=new Map,i={platform:tv,...r},o={...i.platform,_c:n};return T(t,e,{...i,platform:o})}},38168:(t,e,r)=>{r.d(e,{Eq:()=>l});var n=function(t){return"undefined"==typeof document?null:(Array.isArray(t)?t[0]:t).ownerDocument.body},i=new WeakMap,o=new WeakMap,f={},u=0,a=function(t){return t&&(t.host||a(t.parentNode))},s=function(t,e,r,n){var s=(Array.isArray(t)?t:[t]).map(function(t){if(e.contains(t))return t;var r=a(t);return r&&e.contains(r)?r:(console.error("aria-hidden",t,"in not contained inside",e,". Doing nothing"),null)}).filter(function(t){return!!t});f[r]||(f[r]=new WeakMap);var l=f[r],c=[],h=new Set,p=new Set(s),d=function(t){!t||h.has(t)||(h.add(t),d(t.parentNode))};s.forEach(d);var y=function(t){!t||p.has(t)||Array.prototype.forEach.call(t.children,function(t){if(h.has(t))y(t);else try{var e=t.getAttribute(n),f=null!==e&&"false"!==e,u=(i.get(t)||0)+1,a=(l.get(t)||0)+1;i.set(t,u),l.set(t,a),c.push(t),1===u&&f&&o.set(t,!0),1===a&&t.setAttribute(r,"true"),f||t.setAttribute(n,"true")}catch(e){console.error("aria-hidden: cannot operate on ",t,e)}})};return y(e),h.clear(),u++,function(){c.forEach(function(t){var e=i.get(t)-1,f=l.get(t)-1;i.set(t,e),l.set(t,f),e||(o.has(t)||t.removeAttribute(n),o.delete(t)),f||t.removeAttribute(r)}),--u||(i=new WeakMap,i=new WeakMap,o=new WeakMap,f={})}},l=function(t,e,r){void 0===r&&(r="data-aria-hidden");var i=Array.from(Array.isArray(t)?t:[t]),o=e||n(t);return o?(i.push.apply(i,Array.from(o.querySelectorAll("[aria-live], script"))),s(i,o,r,"aria-hidden")):function(){return null}}},41050:(t,e,r)=>{r.d(e,{m:()=>i});var n,i=function(){return n||r.nc}},44134:(t,e,r)=>{var n=r(57719),i=r(7610),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function f(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,u.prototype),e}function u(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return l(t)}return a(t,e,r)}function a(t,e,r){if("string"==typeof t){var n=t,i=e;if(("string"!=typeof i||""===i)&&(i="utf8"),!u.isEncoding(i))throw TypeError("Unknown encoding: "+i);var o=0|d(n,i),a=f(o),s=a.write(n,i);return s!==o&&(a=a.slice(0,s)),a}if(ArrayBuffer.isView(t)){var l=t;if(S(l,Uint8Array)){var y=new Uint8Array(l);return h(y.buffer,y.byteOffset,y.byteLength)}return c(l)}if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(S(t,ArrayBuffer)||t&&S(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(S(t,SharedArrayBuffer)||t&&S(t.buffer,SharedArrayBuffer)))return h(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var g=t.valueOf&&t.valueOf();if(null!=g&&g!==t)return u.from(g,e,r);var m=function(t){if(u.isBuffer(t)){var e=0|p(t.length),r=f(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?f(0):c(t):"Buffer"===t.type&&Array.isArray(t.data)?c(t.data):void 0}(t);if(m)return m;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return u.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function s(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function l(t){return s(t),f(t<0?0:0|p(t))}function c(t){for(var e=t.length<0?0:0|p(t.length),r=f(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}function h(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),u.prototype),n}function p(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function d(t,e){if(u.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||S(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return R(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return T(t).length;default:if(i)return n?-1:R(t).length;e=(""+e).toLowerCase(),i=!0}}function y(t,e,r){var i,o,f,u=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=I[t[o]];return i}(this,e,r);case"utf8":case"utf-8":return w(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return i=this,o=e,f=r,0===o&&f===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(o,f));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length-1;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,e,r);default:if(u)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),u=!0}}function g(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function m(t,e,r,n,i){var o;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(o=r*=1)!=o&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length)if(i)return -1;else r=t.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof e&&(e=u.from(e,n)),u.isBuffer(e))return 0===e.length?-1:v(t,e,r,n,i);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(t,e,r);else return Uint8Array.prototype.lastIndexOf.call(t,e,r);return v(t,[e],r,n,i)}throw TypeError("val must be string, number or Buffer")}function v(t,e,r,n,i){var o,f=1,u=t.length,a=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;f=2,u/=2,a/=2,r/=2}function s(t,e){return 1===f?t[e]:t.readUInt16BE(e*f)}if(i){var l=-1;for(o=r;o<u;o++)if(s(t,o)===s(e,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===a)return l*f}else -1!==l&&(o-=o-l),l=-1}else for(r+a>u&&(r=u-a),o=r;o>=0;o--){for(var c=!0,h=0;h<a;h++)if(s(t,o+h)!==s(e,h)){c=!1;break}if(c)return o}return -1}function w(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,f,u,a,s=t[i],l=null,c=s>239?4:s>223?3:s>191?2:1;if(i+c<=r)switch(c){case 1:s<128&&(l=s);break;case 2:(192&(o=t[i+1]))==128&&(a=(31&s)<<6|63&o)>127&&(l=a);break;case 3:o=t[i+1],f=t[i+2],(192&o)==128&&(192&f)==128&&(a=(15&s)<<12|(63&o)<<6|63&f)>2047&&(a<55296||a>57343)&&(l=a);break;case 4:o=t[i+1],f=t[i+2],u=t[i+3],(192&o)==128&&(192&f)==128&&(192&u)==128&&(a=(15&s)<<18|(63&o)<<12|(63&f)<<6|63&u)>65535&&a<1114112&&(l=a)}null===l?(l=65533,c=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=c}var h=n,p=h.length;if(p<=4096)return String.fromCharCode.apply(String,h);for(var d="",y=0;y<p;)d+=String.fromCharCode.apply(String,h.slice(y,y+=4096));return d}function b(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function x(t,e,r,n,i,o){if(!u.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function E(t,e,r,n,i,o){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function A(t,e,r,n,o){return e*=1,r>>>=0,o||E(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function B(t,e,r,n,o){return e*=1,r>>>=0,o||E(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}e.hp=u,e.IS=50,u.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),u.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(u.prototype,"parent",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.buffer}}),Object.defineProperty(u.prototype,"offset",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.byteOffset}}),u.poolSize=8192,u.from=function(t,e,r){return a(t,e,r)},Object.setPrototypeOf(u.prototype,Uint8Array.prototype),Object.setPrototypeOf(u,Uint8Array),u.alloc=function(t,e,r){return(s(t),t<=0)?f(t):void 0!==e?"string"==typeof r?f(t).fill(e,r):f(t).fill(e):f(t)},u.allocUnsafe=function(t){return l(t)},u.allocUnsafeSlow=function(t){return l(t)},u.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==u.prototype},u.compare=function(t,e){if(S(t,Uint8Array)&&(t=u.from(t,t.offset,t.byteLength)),S(e,Uint8Array)&&(e=u.from(e,e.offset,e.byteLength)),!u.isBuffer(t)||!u.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:+(n<r)},u.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return u.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=u.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var o=t[r];if(S(o,Uint8Array))i+o.length>n.length?u.from(o).copy(n,i):Uint8Array.prototype.set.call(n,o,i);else if(u.isBuffer(o))o.copy(n,i);else throw TypeError('"list" argument must be an Array of Buffers');i+=o.length}return n},u.byteLength=d,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)g(this,e,e+1);return this},u.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)g(this,e,e+3),g(this,e+1,e+2);return this},u.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)g(this,e,e+7),g(this,e+1,e+6),g(this,e+2,e+5),g(this,e+3,e+4);return this},u.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?w(this,0,t):y.apply(this,arguments)},u.prototype.toLocaleString=u.prototype.toString,u.prototype.equals=function(t){if(!u.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===u.compare(this,t)},u.prototype.inspect=function(){var t="",r=e.IS;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},o&&(u.prototype[o]=u.prototype.inspect),u.prototype.compare=function(t,e,r,n,i){if(S(t,Uint8Array)&&(t=u.from(t,t.offset,t.byteLength)),!u.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;for(var o=i-n,f=r-e,a=Math.min(o,f),s=this.slice(n,i),l=t.slice(e,r),c=0;c<a;++c)if(s[c]!==l[c]){o=s[c],f=l[c];break}return o<f?-1:+(f<o)},u.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},u.prototype.indexOf=function(t,e,r){return m(this,t,e,r,!0)},u.prototype.lastIndexOf=function(t,e,r){return m(this,t,e,r,!1)},u.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,f,u,a,s,l,c,h=this.length-e;if((void 0===r||r>h)&&(r=h),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var p=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;n>o/2&&(n=o/2);for(var f=0;f<n;++f){var u,a=parseInt(e.substr(2*f,2),16);if((u=a)!=u)break;t[r+f]=a}return f}(this,t,e,r);case"utf8":case"utf-8":return i=e,o=r,U(R(t,this.length-i),this,i,o);case"ascii":case"latin1":case"binary":return f=e,u=r,U(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(t),this,f,u);case"base64":return a=e,s=r,U(T(t),this,a,s);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l=e,c=r,U(function(t,e){for(var r,n,i=[],o=0;o<t.length&&!((e-=2)<0);++o)n=(r=t.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(t,this.length-l),this,l,c);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},u.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,u.prototype),n},u.prototype.readUintLE=u.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},u.prototype.readUintBE=u.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},u.prototype.readUint8=u.prototype.readUInt8=function(t,e){return t>>>=0,e||b(t,1,this.length),this[t]},u.prototype.readUint16LE=u.prototype.readUInt16LE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]|this[t+1]<<8},u.prototype.readUint16BE=u.prototype.readUInt16BE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]<<8|this[t+1]},u.prototype.readUint32LE=u.prototype.readUInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},u.prototype.readUint32BE=u.prototype.readUInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},u.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},u.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},u.prototype.readInt8=function(t,e){return(t>>>=0,e||b(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},u.prototype.readInt16LE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},u.prototype.readInt16BE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},u.prototype.readInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},u.prototype.readInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},u.prototype.readFloatLE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!0,23,4)},u.prototype.readFloatBE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!1,23,4)},u.prototype.readDoubleLE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!0,52,8)},u.prototype.readDoubleBE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!1,52,8)},u.prototype.writeUintLE=u.prototype.writeUIntLE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;x(this,t,e,r,i,0)}var o=1,f=0;for(this[e]=255&t;++f<r&&(o*=256);)this[e+f]=t/o&255;return e+r},u.prototype.writeUintBE=u.prototype.writeUIntBE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;x(this,t,e,r,i,0)}var o=r-1,f=1;for(this[e+o]=255&t;--o>=0&&(f*=256);)this[e+o]=t/f&255;return e+r},u.prototype.writeUint8=u.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,1,255,0),this[e]=255&t,e+1},u.prototype.writeUint16LE=u.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},u.prototype.writeUint16BE=u.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},u.prototype.writeUint32LE=u.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},u.prototype.writeUint32BE=u.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},u.prototype.writeIntLE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);x(this,t,e,r,i-1,-i)}var o=0,f=1,u=0;for(this[e]=255&t;++o<r&&(f*=256);)t<0&&0===u&&0!==this[e+o-1]&&(u=1),this[e+o]=(t/f|0)-u&255;return e+r},u.prototype.writeIntBE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);x(this,t,e,r,i-1,-i)}var o=r-1,f=1,u=0;for(this[e+o]=255&t;--o>=0&&(f*=256);)t<0&&0===u&&0!==this[e+o+1]&&(u=1),this[e+o]=(t/f|0)-u&255;return e+r},u.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},u.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},u.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},u.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},u.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},u.prototype.writeFloatLE=function(t,e,r){return A(this,t,e,!0,r)},u.prototype.writeFloatBE=function(t,e,r){return A(this,t,e,!1,r)},u.prototype.writeDoubleLE=function(t,e,r){return B(this,t,e,!0,r)},u.prototype.writeDoubleBE=function(t,e,r){return B(this,t,e,!1,r)},u.prototype.copy=function(t,e,r,n){if(!u.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,r,n):Uint8Array.prototype.set.call(t,this.subarray(r,n),e),i},u.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!u.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var i,o=t.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(t=o)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var f=u.isBuffer(t)?t:u.from(t,n),a=f.length;if(0===a)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=f[i%a]}return this};var L=/[^+/0-9A-Za-z-_]/g;function R(t,e){e=e||1/0;for(var r,n=t.length,i=null,o=[],f=0;f<n;++f){if((r=t.charCodeAt(f))>55295&&r<57344){if(!i){if(r>56319||f+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function T(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(L,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function U(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function S(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var I=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()},46984:(t,e,r)=>{let n,i;r.d(e,{P2:()=>d});let o=(t,e)=>e.some(e=>t instanceof e),f=new WeakMap,u=new WeakMap,a=new WeakMap,s=new WeakMap,l=new WeakMap,c={get(t,e,r){if(t instanceof IDBTransaction){if("done"===e)return u.get(t);if("objectStoreNames"===e)return t.objectStoreNames||a.get(t);if("store"===e)return r.objectStoreNames[1]?void 0:r.objectStore(r.objectStoreNames[0])}return h(t[e])},set:(t,e,r)=>(t[e]=r,!0),has:(t,e)=>t instanceof IDBTransaction&&("done"===e||"store"===e)||e in t};function h(t){if(t instanceof IDBRequest){let e=new Promise((e,r)=>{let n=()=>{t.removeEventListener("success",i),t.removeEventListener("error",o)},i=()=>{e(h(t.result)),n()},o=()=>{r(t.error),n()};t.addEventListener("success",i),t.addEventListener("error",o)});return e.then(e=>{e instanceof IDBCursor&&f.set(e,t)}).catch(()=>{}),l.set(e,t),e}if(s.has(t))return s.get(t);let e=function(t){if("function"==typeof t)return t!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(i||(i=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(p(this),e),h(f.get(this))}:function(...e){return h(t.apply(p(this),e))}:function(e,...r){let n=t.call(p(this),e,...r);return a.set(n,e.sort?e.sort():[e]),h(n)};return(t instanceof IDBTransaction&&function(t){if(u.has(t))return;let e=new Promise((e,r)=>{let n=()=>{t.removeEventListener("complete",i),t.removeEventListener("error",o),t.removeEventListener("abort",o)},i=()=>{e(),n()},o=()=>{r(t.error||new DOMException("AbortError","AbortError")),n()};t.addEventListener("complete",i),t.addEventListener("error",o),t.addEventListener("abort",o)});u.set(t,e)}(t),o(t,n||(n=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])))?new Proxy(t,c):t}(t);return e!==t&&(s.set(t,e),l.set(e,t)),e}let p=t=>l.get(t);function d(t,e,{blocked:r,upgrade:n,blocking:i,terminated:o}={}){let f=indexedDB.open(t,e),u=h(f);return n&&f.addEventListener("upgradeneeded",t=>{n(h(f.result),t.oldVersion,t.newVersion,h(f.transaction),t)}),r&&f.addEventListener("blocked",t=>r(t.oldVersion,t.newVersion,t)),u.then(t=>{o&&t.addEventListener("close",()=>o()),i&&t.addEventListener("versionchange",t=>i(t.oldVersion,t.newVersion,t))}).catch(()=>{}),u}let y=["get","getKey","getAll","getAllKeys","count"],g=["put","add","delete","clear"],m=new Map;function v(t,e){if(!(t instanceof IDBDatabase&&!(e in t)&&"string"==typeof e))return;if(m.get(e))return m.get(e);let r=e.replace(/FromIndex$/,""),n=e!==r,i=g.includes(r);if(!(r in(n?IDBIndex:IDBObjectStore).prototype)||!(i||y.includes(r)))return;let o=async function(t,...e){let o=this.transaction(t,i?"readwrite":"readonly"),f=o.store;return n&&(f=f.index(e.shift())),(await Promise.all([f[r](...e),i&&o.done]))[0]};return m.set(e,o),o}c=(t=>({...t,get:(e,r,n)=>v(e,r)||t.get(e,r,n),has:(e,r)=>!!v(e,r)||t.has(e,r)}))(c)},52596:(t,e,r)=>{r.d(e,{$:()=>n});function n(){for(var t,e,r=0,n="",i=arguments.length;r<i;r++)(t=arguments[r])&&(e=function t(e){var r,n,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(r=0;r<o;r++)e[r]&&(n=t(e[r]))&&(i&&(i+=" "),i+=n)}else for(n in e)e[n]&&(i&&(i+=" "),i+=n);return i}(t))&&(n&&(n+=" "),n+=e);return n}},57719:(t,e)=>{e.byteLength=function(t){var e=a(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,o=a(t),f=o[0],u=o[1],s=new i((f+u)*3/4-u),l=0,c=u>0?f-4:f;for(r=0;r<c;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],s[l++]=e>>16&255,s[l++]=e>>8&255,s[l++]=255&e;return 2===u&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,s[l++]=255&e),1===u&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,s[l++]=e>>8&255,s[l++]=255&e),s},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],f=0,u=n-i;f<u;f+=16383)o.push(function(t,e,n){for(var i,o=[],f=e;f<n;f+=3)i=(t[f]<<16&0xff0000)+(t[f+1]<<8&65280)+(255&t[f+2]),o.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(t,f,f+16383>u?u:f+16383));return 1===i?o.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&o.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",f=0,u=o.length;f<u;++f)r[f]=o[f],n[o.charCodeAt(f)]=f;function a(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},74466:(t,e,r)=>{r.d(e,{F:()=>f});var n=r(52596);let i=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,o=n.$,f=(t,e)=>r=>{var n;if((null==e?void 0:e.variants)==null)return o(t,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:f,defaultVariants:u}=e,a=Object.keys(f).map(t=>{let e=null==r?void 0:r[t],n=null==u?void 0:u[t];if(null===e)return null;let o=i(e)||i(n);return f[t][o]}),s=r&&Object.entries(r).reduce((t,e)=>{let[r,n]=e;return void 0===n||(t[r]=n),t},{});return o(t,a,null==e||null==(n=e.compoundVariants)?void 0:n.reduce((t,e)=>{let{class:r,className:n,...i}=e;return Object.entries(i).every(t=>{let[e,r]=t;return Array.isArray(r)?r.includes({...u,...s}[e]):({...u,...s})[e]===r})?[...t,r,n]:t},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},84945:(t,e,r)=>{r.d(e,{BN:()=>d,ER:()=>y,Ej:()=>m,UE:()=>w,UU:()=>g,cY:()=>p,jD:()=>v,we:()=>c});var n=r(22475),i=r(12115),o=r(47650),f="undefined"!=typeof document?i.useLayoutEffect:function(){};function u(t,e){let r,n,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((r=t.length)!==e.length)return!1;for(n=r;0!=n--;)if(!u(t[n],e[n]))return!1;return!0}if((r=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(e,i[n]))return!1;for(n=r;0!=n--;){let r=i[n];if(("_owner"!==r||!t.$$typeof)&&!u(t[r],e[r]))return!1}return!0}return t!=t&&e!=e}function a(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function s(t,e){let r=a(t);return Math.round(e*r)/r}function l(t){let e=i.useRef(t);return f(()=>{e.current=t}),e}function c(t){void 0===t&&(t={});let{placement:e="bottom",strategy:r="absolute",middleware:c=[],platform:h,elements:{reference:p,floating:d}={},transform:y=!0,whileElementsMounted:g,open:m}=t,[v,w]=i.useState({x:0,y:0,strategy:r,placement:e,middlewareData:{},isPositioned:!1}),[b,x]=i.useState(c);u(b,c)||x(c);let[E,A]=i.useState(null),[B,L]=i.useState(null),R=i.useCallback(t=>{t!==I.current&&(I.current=t,A(t))},[]),T=i.useCallback(t=>{t!==O.current&&(O.current=t,L(t))},[]),U=p||E,S=d||B,I=i.useRef(null),O=i.useRef(null),C=i.useRef(v),D=null!=g,k=l(g),P=l(h),j=l(m),M=i.useCallback(()=>{if(!I.current||!O.current)return;let t={placement:e,strategy:r,middleware:b};P.current&&(t.platform=P.current),(0,n.rD)(I.current,O.current,t).then(t=>{let e={...t,isPositioned:!1!==j.current};N.current&&!u(C.current,e)&&(C.current=e,o.flushSync(()=>{w(e)}))})},[b,e,r,P,j]);f(()=>{!1===m&&C.current.isPositioned&&(C.current.isPositioned=!1,w(t=>({...t,isPositioned:!1})))},[m]);let N=i.useRef(!1);f(()=>(N.current=!0,()=>{N.current=!1}),[]),f(()=>{if(U&&(I.current=U),S&&(O.current=S),U&&S){if(k.current)return k.current(U,S,M);M()}},[U,S,M,k,D]);let W=i.useMemo(()=>({reference:I,floating:O,setReference:R,setFloating:T}),[R,T]),F=i.useMemo(()=>({reference:U,floating:S}),[U,S]),V=i.useMemo(()=>{let t={position:r,left:0,top:0};if(!F.floating)return t;let e=s(F.floating,v.x),n=s(F.floating,v.y);return y?{...t,transform:"translate("+e+"px, "+n+"px)",...a(F.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:e,top:n}},[r,y,F.floating,v.x,v.y]);return i.useMemo(()=>({...v,update:M,refs:W,elements:F,floatingStyles:V}),[v,M,W,F,V])}let h=t=>({name:"arrow",options:t,fn(e){let{element:r,padding:i}="function"==typeof t?t(e):t;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?(0,n.UE)({element:r.current,padding:i}).fn(e):{}:r?(0,n.UE)({element:r,padding:i}).fn(e):{}}}),p=(t,e)=>({...(0,n.cY)(t),options:[t,e]}),d=(t,e)=>({...(0,n.BN)(t),options:[t,e]}),y=(t,e)=>({...(0,n.ER)(t),options:[t,e]}),g=(t,e)=>({...(0,n.UU)(t),options:[t,e]}),m=(t,e)=>({...(0,n.Ej)(t),options:[t,e]}),v=(t,e)=>({...(0,n.jD)(t),options:[t,e]}),w=(t,e)=>({...h(t),options:[t,e]})}}]);