(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6040],{20213:(e,s,t)=>{Promise.resolve().then(t.bind(t,31875))},31875:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Y});var r=t(95155),a=t(12115),l=t(66695),n=t(30285),i=t(37108),c=t(92138),d=t(13717),o=t(24357),m=t(36683),x=t(81304),u=t(91788),h=t(61432),g=t(57340),p=t(71007),b=t(19420),j=t(4516),v=t(55868),N=t(69074),f=t(71366),y=t(37192),w=t(62523),D=t(88539),A=t(26126),k=t(54416),C=t(14186),R=t(12486);function _(e){let{orderId:s,isOpen:t,onClose:i}=e,[c,d]=(0,a.useState)([]),[o,m]=(0,a.useState)(null),[x,u]=(0,a.useState)(""),[h,g]=(0,a.useState)(!1),[b,j]=(0,a.useState)(""),[v,N]=(0,a.useState)(""),[y,_]=(0,a.useState)("medium"),S=(0,a.useRef)(null),E=()=>{var e;null==(e=S.current)||e.scrollIntoView({behavior:"smooth"})};(0,a.useEffect)(()=>{E()},[null==o?void 0:o.messages]);let $=[{id:"1",orderId:s,title:"مشكلة في التسليم",description:"العميل غير متواجد في العنوان المحدد",status:"open",priority:"high",createdBy:"courier_1",createdAt:new Date(Date.now()-72e5),messages:[{id:"1",senderId:"courier_1",senderName:"أحمد المندوب",senderRole:"مندوب",content:"العميل غير متواجد في العنوان المحدد. حاولت الاتصال عدة مرات ولم يرد.",timestamp:new Date(Date.now()-72e5)},{id:"2",senderId:"supervisor_1",senderName:"محمد المتابع",senderRole:"متابع",content:"جرب الاتصال مرة أخرى وإذا لم يرد أرجع الطلب للمكتب",timestamp:new Date(Date.now()-36e5)}]}];(0,a.useEffect)(()=>{t&&d($)},[t,s]);let I=()=>{if(!x.trim()||!o)return;let e={id:Date.now().toString(),senderId:"current_user",senderName:"المستخدم الحالي",senderRole:"مدير",content:x,timestamp:new Date};m(s=>s?{...s,messages:[...s.messages,e]}:null),u("")},P=e=>{switch(e){case"open":return"bg-red-100 text-red-800";case"in_progress":return"bg-yellow-100 text-yellow-800";case"resolved":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},q=e=>{switch(e){case"urgent":return"bg-red-500 text-white";case"high":return"bg-orange-500 text-white";case"medium":return"bg-blue-500 text-white";default:return"bg-gray-500 text-white"}},B=e=>{switch(e){case"urgent":return"عاجل";case"high":return"عالي";case"medium":default:return"متوسط";case"low":return"منخفض"}},Z=e=>{switch(e){case"open":default:return"مفتوح";case"in_progress":return"قيد المعالجة";case"resolved":return"تم الحل";case"closed":return"مغلق"}};return t?(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",dir:"rtl",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl w-full max-w-6xl h-[80vh] flex overflow-hidden",children:[(0,r.jsxs)("div",{className:"w-1/3 border-l border-gray-200 bg-gray-50",children:[(0,r.jsxs)("div",{className:"p-6 border-b border-gray-200 bg-white",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center gap-2",children:[(0,r.jsx)(f.A,{className:"h-6 w-6 text-blue-600"}),"التذاكر"]}),(0,r.jsx)(n.$,{onClick:i,variant:"ghost",size:"sm",className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)(k.A,{className:"h-5 w-5"})})]}),(0,r.jsx)(n.$,{onClick:()=>g(!0),className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:"إنشاء تذكرة جديدة"})]}),(0,r.jsx)("div",{className:"p-4 space-y-3 overflow-y-auto h-full",children:c.map(e=>(0,r.jsx)(l.Zp,{className:"cursor-pointer transition-all duration-200 hover:shadow-md ".concat((null==o?void 0:o.id)===e.id?"ring-2 ring-blue-500 bg-blue-50":"hover:bg-white"),onClick:()=>m(e),children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"font-semibold text-sm text-gray-800 truncate",children:e.title}),(0,r.jsx)(A.E,{className:q(e.priority),children:B(e.priority)})]}),(0,r.jsx)("p",{className:"text-xs text-gray-600 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(A.E,{variant:"outline",className:P(e.status),children:Z(e.status)}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 flex items-center gap-1",children:[(0,r.jsx)(C.A,{className:"h-3 w-3"}),e.createdAt.toLocaleDateString("ar-IQ")]})]})]})})},e.id))})]}),(0,r.jsx)("div",{className:"flex-1 flex flex-col",children:h?(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:"إنشاء تذكرة جديدة"}),(0,r.jsx)(n.$,{onClick:()=>g(!1),variant:"ghost",size:"sm",children:"إلغاء"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"عنوان التذكرة"}),(0,r.jsx)(w.p,{value:b,onChange:e=>j(e.target.value),placeholder:"أدخل عنوان التذكرة",className:"w-full"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الوصف"}),(0,r.jsx)(D.T,{value:v,onChange:e=>N(e.target.value),placeholder:"اشرح المشكلة أو الاستفسار بالتفصيل",rows:4,className:"w-full"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الأولوية"}),(0,r.jsxs)("select",{value:y,onChange:e=>_(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"low",children:"منخفض"}),(0,r.jsx)("option",{value:"medium",children:"متوسط"}),(0,r.jsx)("option",{value:"high",children:"عالي"}),(0,r.jsx)("option",{value:"urgent",children:"عاجل"})]})]}),(0,r.jsx)(n.$,{onClick:()=>{if(!b.trim()||!v.trim())return;let e={id:Date.now().toString(),orderId:s,title:b,description:v,status:"open",priority:y,createdBy:"current_user",createdAt:new Date,messages:[]};d(s=>[e,...s]),m(e),g(!1),j(""),N(""),_("medium")},className:"w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700",disabled:!b.trim()||!v.trim(),children:"إنشاء التذكرة"})]})]}):o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 bg-white",children:(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:o.title}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:o.description})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(A.E,{className:q(o.priority),children:B(o.priority)}),(0,r.jsx)(A.E,{variant:"outline",className:P(o.status),children:Z(o.status)})]})]})}),(0,r.jsx)("div",{className:"flex-1 p-6 overflow-y-auto bg-gray-50",children:(0,r.jsxs)("div",{className:"space-y-4",children:[o.messages.map(e=>(0,r.jsx)("div",{className:"flex ".concat("current_user"===e.senderId?"justify-end":"justify-start"),children:(0,r.jsxs)("div",{className:"max-w-xs lg:max-w-md px-4 py-3 rounded-2xl ".concat("current_user"===e.senderId?"bg-gradient-to-r from-blue-500 to-purple-600 text-white":"bg-white text-gray-800 shadow-sm border"),children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.senderName}),(0,r.jsx)(A.E,{variant:"outline",className:"text-xs",children:e.senderRole})]}),(0,r.jsx)("p",{className:"text-sm",children:e.content}),(0,r.jsx)("p",{className:"text-xs mt-2 ".concat("current_user"===e.senderId?"text-blue-100":"text-gray-500"),children:e.timestamp.toLocaleTimeString("ar-IQ")})]})},e.id)),(0,r.jsx)("div",{ref:S})]})}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 bg-white",children:(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)(w.p,{value:x,onChange:e=>u(e.target.value),placeholder:"اكتب رسالتك هنا...",className:"flex-1",onKeyPress:e=>"Enter"===e.key&&I()}),(0,r.jsx)(n.$,{onClick:I,disabled:!x.trim(),className:"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:(0,r.jsx)(R.A,{className:"h-4 w-4"})})]})})]}):(0,r.jsx)("div",{className:"flex-1 flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(f.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-600 mb-2",children:"اختر تذكرة للعرض"}),(0,r.jsx)("p",{className:"text-gray-500",children:"أو أنشئ تذكرة جديدة للبدء"})]})})})]})}):null}var S=t(40646),E=t(85339),$=t(29799),I=t(29869),P=t(84355),q=t(32044),B=t(26912);let Z=[{value:"delivered",label:"تم التسليم",icon:S.A,color:"bg-green-500",description:"تم تسليم الطلب بنجاح",requiresReason:!1},{value:"returned_to_courier",label:"راجع عند المندوب",icon:E.A,color:"bg-amber-500",description:"الطلب راجع عند المندوب",requiresReason:!0},{value:"postponed",label:"مؤجل",icon:C.A,color:"bg-orange-500",description:"تم تأجيل التسليم",requiresReason:!0},{value:"partial_delivery",label:"تسليم جزئي",icon:i.A,color:"bg-cyan-500",description:"تم تسليم جزء من الطلب",requiresReason:!1},{value:"price_change",label:"تغيير سعر فقط",icon:v.A,color:"bg-indigo-500",description:"تغيير سعر الطلب فقط",requiresReason:!1}];function T(e){var s;let{orderId:t,currentStatus:c,onUpdate:d,onStatusUpdate:o,onClose:m}=e,{user:x}=(0,q.A)(),[u,h]=(0,a.useState)(c),[g,p]=(0,a.useState)(""),[b,j]=(0,a.useState)(null),[v,N]=(0,a.useState)(null),[f,y]=(0,a.useState)(!1),[w,k]=(0,a.useState)(""),[C,R]=(0,a.useState)(""),[_,T]=(0,a.useState)(""),U=(()=>{let e=[...Z];if("delivered"===c)if((null==x?void 0:x.role)!==B.gG.SUPERVISOR&&(null==x?void 0:x.role)!==B.gG.MANAGER)return[];else e.unshift({value:"out_for_delivery",label:"إرجاع إلى قيد التوصيل",icon:$.A,color:"bg-indigo-500",description:"إرجاع الطلب المسلم إلى حالة قيد التوصيل",requiresReason:!0});return["returned","cancelled","archived"].includes(c)&&(null==x?void 0:x.role)!==B.gG.MANAGER?[]:e})(),z=U.length>0,G=()=>"delivered"===c&&(null==x?void 0:x.role)===B.gG.COURIER?"لا يمكن للمندوب تحديث الطلبات التي تم تسليمها. يرجى التواصل مع المشرف أو المدير.":["returned","cancelled","archived"].includes(c)&&(null==x?void 0:x.role)!==B.gG.MANAGER?"لا يمكن تحديث هذا الطلب. يرجى التواصل مع المدير.":"لا يمكن تحديث حالة هذا الطلب حالياً.",O=async()=>{let e=Z.find(e=>e.value===u);if(u===c&&!g&&!b)return void alert("يرجى تغيير الحالة أو إضافة ملاحظات أو صورة");if((null==e?void 0:e.requiresReason)&&(!w.trim()||!b))return void alert("هذه الحالة تتطلب إضافة سبب وصورة");y(!0);try{await new Promise(e=>setTimeout(e,1e3));let s=(null==e?void 0:e.requiresReason)?"".concat(w,"\n").concat(g):g;if("partial_delivery"===u){let e=[];C&&e.push("عدد القطع الراجعة: ".concat(C)),_&&e.push("السعر الجديد: ".concat(_," دينار عراقي")),e.length>0&&(s=s?"".concat(s,"\n").concat(e.join("\n")):e.join("\n"))}let t=d||o;t&&t(u,s,b||void 0),alert("تم تحديث حالة الطلب بنجاح!"),m()}catch(e){alert("حدث خطأ أثناء تحديث الحالة")}finally{y(!1)}},F=Z.find(e=>e.value===c);return Z.find(e=>e.value===u),(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",dir:"rtl",children:(0,r.jsxs)(l.Zp,{className:"w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white/95 backdrop-blur-sm border-0 shadow-2xl",children:[(0,r.jsxs)(l.aR,{className:"bg-gradient-to-r from-blue-50 to-purple-50 border-b",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(l.ZB,{className:"text-xl font-bold text-gray-800 flex items-center gap-2",children:[(0,r.jsx)(i.A,{className:"h-6 w-6 text-blue-600"}),"تحديث حالة الطلب ",t]}),(0,r.jsx)(n.$,{onClick:m,variant:"ghost",size:"sm",className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,r.jsxs)(l.BT,{className:"text-gray-600",children:["الحالة الحالية:",(0,r.jsx)(A.E,{className:"mr-2 ".concat(null==F?void 0:F.color," text-white"),children:null==F?void 0:F.label}),!z&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,r.jsxs)("p",{className:"text-yellow-800 text-sm",children:["⚠️ ",G()]})})]})]}),(0,r.jsx)(l.Wu,{className:"p-6 space-y-6",children:z?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4 text-gray-800",children:"اختيار الحالة الجديدة"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:U.map(e=>{let s=e.icon,t=u===e.value,a=c===e.value;return(0,r.jsx)("button",{onClick:()=>h(e.value),disabled:a,className:"p-4 rounded-xl border-2 transition-all duration-200 text-right ".concat(t?"border-blue-500 bg-blue-50 shadow-md":a?"border-gray-300 bg-gray-100 opacity-50 cursor-not-allowed":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"),children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg ".concat(e.color),children:(0,r.jsx)(s,{className:"h-5 w-5 text-white"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-gray-800",children:e.label}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]}),t&&(0,r.jsx)(S.A,{className:"h-5 w-5 text-blue-500"}),a&&(0,r.jsx)(A.E,{variant:"secondary",className:"text-xs",children:"حالية"})]})},e.value)})})]}),(null==(s=Z.find(e=>e.value===u))?void 0:s.requiresReason)&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-3 text-gray-800 flex items-center gap-2",children:[(0,r.jsx)(E.A,{className:"h-5 w-5 text-red-500"}),"السبب (مطلوب)"]}),(0,r.jsx)(D.T,{value:w,onChange:e=>k(e.target.value),placeholder:"يرجى توضيح سبب هذه الحالة...",className:"min-h-[80px] resize-none border-red-200 focus:ring-red-500 focus:border-red-500",required:!0})]}),"partial_delivery"===u&&(0,r.jsxs)("div",{className:"space-y-4 p-4 bg-cyan-50 rounded-lg border border-cyan-200",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-cyan-800",children:"تفاصيل التسليم الجزئي"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2 text-gray-700",children:"عدد القطع الراجعة"}),(0,r.jsx)("input",{type:"number",value:C,onChange:e=>R(e.target.value),placeholder:"أدخل عدد القطع الراجعة",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent",min:"0"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2 text-gray-700",children:"السعر الجديد (دينار عراقي)"}),(0,r.jsx)("input",{type:"number",value:_,onChange:e=>T(e.target.value),placeholder:"أدخل السعر الجديد",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent",min:"0"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-800",children:"ملاحظات إضافية"}),(0,r.jsx)(D.T,{value:g,onChange:e=>p(e.target.value),placeholder:"أضف ملاحظات حول تحديث الحالة...",className:"min-h-[100px] resize-none"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-800",children:"إرفاق صورة"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(n.$,{type:"button",variant:"outline",onClick:()=>{var e;return null==(e=document.getElementById("image-upload"))?void 0:e.click()},className:"flex items-center gap-2",children:[(0,r.jsx)(I.A,{className:"h-4 w-4"}),"اختيار صورة"]}),(0,r.jsxs)(n.$,{type:"button",variant:"outline",onClick:()=>{var e;null==(e=document.getElementById("image-upload"))||e.click()},className:"flex items-center gap-2",children:[(0,r.jsx)(P.A,{className:"h-4 w-4"}),"التقاط صورة"]})]}),(0,r.jsx)("input",{id:"image-upload",type:"file",accept:"image/*",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(t){j(t);let e=new FileReader;e.onload=e=>{var s;N(null==(s=e.target)?void 0:s.result)},e.readAsDataURL(t)}},className:"hidden"}),v&&(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("img",{src:v,alt:"معاينة الصورة",className:"w-full max-w-xs h-48 object-cover rounded-lg border"}),(0,r.jsx)(n.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>{j(null),N(null)},className:"absolute top-2 left-2",children:"حذف"})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-3 pt-4 border-t",children:[(0,r.jsx)(n.$,{onClick:O,disabled:f,className:"flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:f?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"جاري التحديث..."]}):"تحديث الحالة"}),(0,r.jsx)(n.$,{onClick:m,variant:"outline",disabled:f,className:"flex-1",children:"إلغاء"})]})]}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(i.A,{className:"h-8 w-8 text-yellow-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"لا يمكن تحديث هذا الطلب"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:G()})]}),(0,r.jsx)(n.$,{onClick:m,variant:"outline",className:"w-full max-w-xs",children:"إغلاق"})]})})]})})}var U=t(6874),z=t.n(U),G=t(59434);let O={id:"1",trackingNumber:"MRS001",senderName:"أحمد محمد",senderPhone:"07901234567",senderAddress:"بغداد - الكرادة",recipientName:"فاطمة علي",recipientPhone:"07801234567",recipientAddress:"بغداد - الجادرية - شارع الأطباء - بناية 15 - الطابق الثالث",amount:15e4,status:"delivered",notes:"يرجى التسليم بعد الساعة 2 ظهراً",createdAt:"2024-01-15T10:30:00Z",updatedAt:"2024-01-15T14:45:00Z",courierName:"علي حسين",courierPhone:"07701234567"},F={pending:"في الانتظار",processing:"قيد المعالجة",shipped:"تم الشحن",out_for_delivery:"قيد التوصيل",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"},L={pending:"bg-yellow-100 text-yellow-800 border-yellow-200",processing:"bg-blue-100 text-blue-800 border-blue-200",shipped:"bg-purple-100 text-purple-800 border-purple-200",out_for_delivery:"bg-indigo-100 text-indigo-800 border-indigo-200",delivered:"bg-green-100 text-green-800 border-green-200",returned:"bg-orange-100 text-orange-800 border-orange-200",cancelled:"bg-red-100 text-red-800 border-red-200",postponed:"bg-gray-100 text-gray-800 border-gray-200"};function Y(e){let{params:s}=e,{user:t}=(0,q.A)(),[w,D]=(0,a.useState)(O),[A,k]=(0,a.useState)(!1),[C,R]=(0,a.useState)(!1);return(0,r.jsx)("div",{className:"min-h-screen bg-background p-4 md:p-6 lg:p-8",dir:"rtl",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto space-y-8",children:[(0,r.jsx)(y.A,{}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl shadow-2xl mb-4",children:(0,r.jsx)(i.A,{className:"h-8 w-8 text-white"})}),(0,r.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2",children:"تفاصيل الطلب"}),(0,r.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 text-lg",children:["رقم التتبع: ",w.trackingNumber]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center mb-6",children:[(0,r.jsx)(z(),{href:"/orders",children:(0,r.jsxs)(n.$,{variant:"outline",className:"flex items-center gap-2",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),"العودة للطلبات"]})}),(null==t?void 0:t.role)!=="courier"&&(0,r.jsx)(z(),{href:"/orders/".concat(w.id,"/edit"),children:(0,r.jsxs)(n.$,{variant:"outline",className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),"تعديل الطلب"]})}),(0,r.jsxs)(n.$,{onClick:()=>k(!0),className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),"تحديث الحالة"]}),(0,r.jsxs)(n.$,{variant:"outline",className:"flex items-center gap-2",onClick:()=>{let e="\uD83D\uDCE6 تفاصيل الطلب ".concat(w.trackingNumber,"\n\n\uD83D\uDC64 المرسل: ").concat(w.senderName,"\n\uD83D\uDCF1 هاتف المرسل: ").concat(w.senderPhone,"\n\uD83D\uDCCD عنوان المرسل: ").concat(w.senderAddress,"\n\n\uD83D\uDC65 المستلم: ").concat(w.recipientName,"\n\uD83D\uDCF1 هاتف المستلم: ").concat(w.recipientPhone,"\n\uD83D\uDCCD عنوان المستلم: ").concat(w.recipientAddress,"\n\n\uD83D\uDCB0 المبلغ: ").concat((0,G.vv)(w.amount),"\n\uD83D\uDCCA الحالة: ").concat(F[w.status],"\n\uD83D\uDCC5 تاريخ الإنشاء: ").concat((0,G.Yq)(w.createdAt),"\n\n\uD83D\uDCDD ملاحظات: ").concat(w.notes||"لا توجد ملاحظات").trim();navigator.clipboard.writeText(e).then(()=>{alert("تم نسخ التفاصيل بنجاح!")}).catch(()=>{alert("فشل في نسخ التفاصيل")})},children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),"نسخ التفاصيل"]}),(0,r.jsxs)(n.$,{variant:"outline",className:"flex items-center gap-2",onClick:()=>{let e="\uD83D\uDE9A *تحديث حالة الطلب*\n\n\uD83D\uDCE6 *رقم التتبع:* ".concat(w.trackingNumber,"\n\uD83D\uDD04 *الحالة الحالية:* ").concat(F[w.status],"\n\n\uD83D\uDC64 *المستلم:* ").concat(w.recipientName,"\n\uD83D\uDCF1 *الهاتف:* ").concat(w.recipientPhone,"\n\uD83D\uDCCD *العنوان:* ").concat(w.recipientAddress,"\n\n\uD83D\uDCB0 *المبلغ:* ").concat((0,G.vv)(w.amount),"\n\uD83D\uDCC5 *تاريخ الإنشاء:* ").concat((0,G.Yq)(w.createdAt),"\n\n").concat(w.notes?"\uD83D\uDCDD *ملاحظات:* ".concat(w.notes):"","\n\n---\n\uD83D\uDE9B *مرسال - خدمة توصيل موثوقة*"),s="https://wa.me/?text=".concat(encodeURIComponent(e));window.open(s,"_blank")},children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),"مشاركة"]}),(null==t?void 0:t.role)==="manager"&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(n.$,{variant:"outline",className:"flex items-center gap-2",onClick:()=>{let e='\n      <div style="width: 110mm; height: 130mm; padding: 10mm; font-family: Arial, sans-serif; direction: rtl;">\n        <div style="text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 10px;">\n          <h2>مكتب علي الشيباني للتوصيل السريع</h2>\n          <p>وصل استلام</p>\n        </div>\n        <div style="margin-bottom: 10px;">\n          <strong>رقم التتبع:</strong> '.concat(w.trackingNumber,'\n        </div>\n        <div style="margin-bottom: 10px;">\n          <strong>المرسل:</strong> ').concat(w.senderName,'\n        </div>\n        <div style="margin-bottom: 10px;">\n          <strong>المستلم:</strong> ').concat(w.recipientName,'\n        </div>\n        <div style="margin-bottom: 10px;">\n          <strong>رقم الهاتف:</strong> ').concat(w.recipientPhone,'\n        </div>\n        <div style="margin-bottom: 10px;">\n          <strong>العنوان:</strong> ').concat(w.recipientAddress,'\n        </div>\n        <div style="margin-bottom: 10px;">\n          <strong>المبلغ:</strong> ').concat((0,G.vv)(w.amount),'\n        </div>\n        <div style="margin-bottom: 10px;">\n          <strong>التاريخ:</strong> ').concat((0,G.Yq)(w.createdAt),'\n        </div>\n        <div style="text-align: center; margin-top: 20px;">\n          <div style="border: 1px solid #000; padding: 5px; display: inline-block;">\n            ').concat(w.trackingNumber,"\n          </div>\n        </div>\n      </div>\n    "),s=window.open("","_blank");s&&(s.document.write(e),s.document.close(),s.print())},children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),"طباعة الوصل"]}),(0,r.jsxs)(n.$,{variant:"outline",className:"flex items-center gap-2",onClick:()=>{let e=new Blob(["\nمكتب علي الشيباني للتوصيل السريع\nوصل استلام\n\nرقم التتبع: ".concat(w.trackingNumber,"\nالمرسل: ").concat(w.senderName,"\nالمستلم: ").concat(w.recipientName,"\nرقم الهاتف: ").concat(w.recipientPhone,"\nالعنوان: ").concat(w.recipientAddress,"\nالمبلغ: ").concat((0,G.vv)(w.amount),"\nالتاريخ: ").concat((0,G.Yq)(w.createdAt),"\n    ")],{type:"text/plain;charset=utf-8"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="receipt-".concat(w.trackingNumber,".txt"),document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(s)},children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),"تحميل الوصل"]})]}),(0,r.jsxs)(n.$,{onClick:()=>R(!0),variant:"outline",className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),"نظام التذاكر"]}),(0,r.jsx)(z(),{href:"/",children:(0,r.jsxs)(n.$,{className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),"الصفحة الرئيسية"]})})]}),(0,r.jsx)(l.Zp,{className:"border-2 border-primary/20 shadow-xl",children:(0,r.jsx)(l.aR,{className:"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(i.A,{className:"h-6 w-6 text-primary"}),(0,r.jsxs)("div",{children:[(0,r.jsx)(l.ZB,{className:"text-xl",children:"حالة الطلب"}),(0,r.jsxs)(l.BT,{children:["آخر تحديث: ",(0,G.Yq)(w.updatedAt)]})]})]}),(0,r.jsx)("div",{className:"flex items-center gap-3",children:(0,r.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(L[w.status]),children:F[w.status]})})]})})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)(l.Zp,{className:"shadow-lg hover:shadow-xl transition-shadow",children:[(0,r.jsx)(l.aR,{className:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950",children:(0,r.jsxs)(l.ZB,{className:"flex items-center gap-2 text-green-700 dark:text-green-300",children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),"معلومات المرسل"]})}),(0,r.jsxs)(l.Wu,{className:"space-y-4 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"الاسم"}),(0,r.jsx)("p",{className:"font-medium",children:w.senderName})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(b.A,{className:"h-5 w-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"رقم الهاتف"}),(0,r.jsx)("p",{className:"font-medium",children:w.senderPhone})]})]}),(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-gray-500 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"العنوان"}),(0,r.jsx)("p",{className:"font-medium",children:w.senderAddress})]})]})]})]}),(0,r.jsxs)(l.Zp,{className:"shadow-lg hover:shadow-xl transition-shadow",children:[(0,r.jsx)(l.aR,{className:"bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950 dark:to-cyan-950",children:(0,r.jsxs)(l.ZB,{className:"flex items-center gap-2 text-blue-700 dark:text-blue-300",children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),"معلومات المستلم"]})}),(0,r.jsxs)(l.Wu,{className:"space-y-4 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"الاسم"}),(0,r.jsx)("p",{className:"font-medium",children:w.recipientName})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(b.A,{className:"h-5 w-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"رقم الهاتف"}),(0,r.jsxs)("p",{className:"font-medium",children:[(0,r.jsx)("a",{href:"tel:".concat(w.recipientPhone),className:"text-blue-600 hover:underline",children:w.recipientPhone}),(0,r.jsx)("span",{className:"mx-2",children:"|"}),(0,r.jsx)("a",{href:"https://wa.me/".concat(w.recipientPhone.replace(/^0/,"964")),target:"_blank",rel:"noopener noreferrer",className:"text-green-600 hover:underline",children:"واتساب"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-gray-500 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"العنوان"}),(0,r.jsx)("p",{className:"font-medium",children:w.recipientAddress})]})]})]})]})]}),(0,r.jsxs)(l.Zp,{className:"shadow-lg hover:shadow-xl transition-shadow",children:[(0,r.jsx)(l.aR,{className:"bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950 dark:to-pink-950",children:(0,r.jsxs)(l.ZB,{className:"flex items-center gap-2 text-purple-700 dark:text-purple-300",children:[(0,r.jsx)(i.A,{className:"h-5 w-5"}),"معلومات الطلب"]})}),(0,r.jsxs)(l.Wu,{className:"p-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(v.A,{className:"h-5 w-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"المبلغ"}),(0,r.jsx)("p",{className:"font-bold text-lg text-green-600",children:(0,G.vv)(w.amount)})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(N.A,{className:"h-5 w-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"تاريخ الإنشاء"}),(0,r.jsx)("p",{className:"font-medium",children:(0,G.Yq)(w.createdAt)})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"المندوب"}),(0,r.jsx)("p",{className:"font-medium",children:w.courierName||"غير محدد"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(b.A,{className:"h-5 w-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"هاتف المندوب"}),(0,r.jsx)("p",{className:"font-medium",children:w.courierPhone||"غير محدد"})]})]})]}),w.notes&&(0,r.jsx)("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(f.A,{className:"h-5 w-5 text-gray-500 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"ملاحظات"}),(0,r.jsx)("p",{className:"font-medium",children:w.notes})]})]})})]})]}),A&&(0,r.jsx)(T,{orderId:w.id,currentStatus:w.status,onUpdate:(e,s,t)=>{D({...w,status:e,notes:s,updatedAt:new Date().toISOString()}),k(!1),alert("تم تحديث حالة الطلب بنجاح!")},onClose:()=>k(!1)}),C&&(0,r.jsx)(_,{orderId:w.id,isOpen:C,onClose:()=>R(!1)})]})})}},62523:(e,s,t)=>{"use strict";t.d(s,{p:()=>l});var r=t(95155);t(12115);var a=t(59434);function l(e){let{className:s,type:t,...l}=e;return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...l})}},88539:(e,s,t)=>{"use strict";t.d(s,{T:()=>n});var r=t(95155),a=t(12115),l=t(59434);let n=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...a})});n.displayName="Textarea"}},e=>{var s=s=>e(e.s=s);e.O(0,[8541,1336,9948,4709,875,416,8779,622,2432,7979,1899,9744,4495,5138,6321,2652,5030,3719,9473,558,9401,6325,6077,4409,1124,9385,4927,37,2041,4979,8321,2900,3610,9988,2158,8058,2044,7192,7358],()=>s(20213)),_N_E=e.O()}]);