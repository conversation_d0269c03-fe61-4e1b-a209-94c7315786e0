(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9450],{11452:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var r=s(95155),n=s(12115),l=s(66695),a=s(30285),i=s(37192),o=s(6740),c=s(57340),d=s(33109),m=s(39785),x=s(81586),h=s(46308),g=s(17580),u=s(71007),p=s(57434),b=s(47924),f=s(40646),j=s(37108),N=s(81304),v=s(6874),w=s.n(v),y=s(64044);function k(){var e,t;let[s,v]=(0,n.useState)("couriers"),[k,S]=(0,n.useState)(""),[A,L]=(0,n.useState)([]),[C,Z]=(0,n.useState)(!1),[B,I]=(0,n.useState)(""),R=[{id:"1",name:"أحمد محمد",phone:"07701234567"},{id:"2",name:"علي حسن",phone:"07801234567"},{id:"3",name:"محمد علي",phone:"***********"}],_=[{id:"1",trackingNumber:"MRS001",recipientName:"سارة أحمد",recipientPhone:"07701111111",amount:5e4,status:"delivered",courierId:"1",courierName:"أحمد محمد",deliveredAt:new Date("2024-01-15")},{id:"2",trackingNumber:"MRS002",recipientName:"فاطمة علي",recipientPhone:"07702222222",amount:75e3,status:"delivered",courierId:"1",courierName:"أحمد محمد",deliveredAt:new Date("2024-01-15")},{id:"3",trackingNumber:"MRS003",recipientName:"زينب محمد",recipientPhone:"07703333333",amount:1e5,status:"delivered",courierId:"2",courierName:"علي حسن",deliveredAt:new Date("2024-01-16")}],D=k?_.filter(e=>e.courierId===k):[],W=e=>{L(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},$=e=>{let t=R.find(t=>t.id===e),s=_.filter(t=>t.courierId===e),r=s.reduce((e,t)=>e+t.amount,0),n=1e3*s.length,l=r-n,a='\n      <html dir="rtl">\n        <head>\n          <title>كشف حساب المندوب - الأرشيف</title>\n          <style>\n            body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }\n            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }\n            .company-name { font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 10px; }\n            .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n            .table th, .table td { border: 1px solid #ddd; padding: 12px; text-align: right; }\n            .table th { background-color: #f8f9fa; font-weight: bold; }\n            .total { font-weight: bold; background-color: #e3f2fd; }\n            .summary { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-top: 20px; }\n            .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }\n          </style>\n        </head>\n        <body>\n          <div class="header">\n            <div class="company-name">مكتب علي الشيباني للتوصيل السريع</div>\n            <h2>كشف حساب المندوب - الأرشيف</h2>\n            <p><strong>التاريخ:</strong> '.concat(new Date().toLocaleDateString("ar-IQ"),"</p>\n            <p><strong>المندوب:</strong> ").concat((null==t?void 0:t.name)||"غير محدد","</p>\n            <p><strong>رقم الهاتف:</strong> ").concat((null==t?void 0:t.phone)||"غير محدد",'</p>\n          </div>\n          <table class="table">\n            <thead>\n              <tr>\n                <th>رقم الوصل</th>\n                <th>اسم المستلم</th>\n                <th>رقم الهاتف</th>\n                <th>المبلغ</th>\n                <th>العمولة</th>\n                <th>الصافي</th>\n                <th>تاريخ التسليم</th>\n              </tr>\n            </thead>\n            <tbody>\n              ').concat(s.map(e=>"\n                <tr>\n                  <td>".concat(e.trackingNumber,"</td>\n                  <td>").concat(e.recipientName,"</td>\n                  <td>").concat(e.recipientPhone,"</td>\n                  <td>").concat(e.amount.toLocaleString()," د.ع</td>\n                  <td>1,000 د.ع</td>\n                  <td>").concat((e.amount-1e3).toLocaleString()," د.ع</td>\n                  <td>").concat(e.deliveredAt.toLocaleDateString("ar-IQ"),"</td>\n                </tr>\n              ")).join(""),'\n              <tr class="total">\n                <td colspan="3"><strong>المجموع الكلي</strong></td>\n                <td><strong>').concat(r.toLocaleString()," د.ع</strong></td>\n                <td><strong>").concat(n.toLocaleString()," د.ع</strong></td>\n                <td><strong>").concat(l.toLocaleString(),' د.ع</strong></td>\n                <td></td>\n              </tr>\n            </tbody>\n          </table>\n          <div class="summary">\n            <h3>ملخص الحساب:</h3>\n            <p><strong>عدد الطلبات المسلمة:</strong> ').concat(s.length," طلب</p>\n            <p><strong>إجمالي المبالغ المحصلة:</strong> ").concat(r.toLocaleString()," دينار عراقي</p>\n            <p><strong>إجمالي العمولات:</strong> ").concat(n.toLocaleString()," دينار عراقي</p>\n            <p><strong>صافي المبلغ للشركة:</strong> ").concat(l.toLocaleString(),' دينار عراقي</p>\n          </div>\n          <div class="footer">\n            <p>تم إنشاء هذا الكشف بواسطة نظام مرسال لإدارة التوصيل</p>\n            <p>للاستفسار: ***********</p>\n          </div>\n        </body>\n      </html>\n    '),i=window.open("","_blank");i&&(i.document.write(a),i.document.close(),i.focus(),i.print(),i.close())},P=_.filter(e=>A.includes(e.id)),T=P.reduce((e,t)=>e+t.amount,0),z=1e3*P.length,E=T-z;return(0,r.jsxs)(y.A,{requiredSection:"accounting",children:[(0,r.jsx)(i.A,{}),(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 p-4 md:p-6 lg:p-8",dir:"rtl",children:[(0,r.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-center flex-1 space-y-4",children:[(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-lg",children:(0,r.jsx)(o.A,{className:"h-8 w-8 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"المحاسبة المالية"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-2 text-lg",children:"إدارة المحاسبة والتسويات المالية بطريقة احترافية"})]})]}),(0,r.jsx)(w(),{href:"/",children:(0,r.jsxs)(a.$,{variant:"outline",className:"flex items-center gap-2 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 text-blue-700 hover:from-blue-100 hover:to-indigo-100",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),"الصفحة الرئيسية"]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)(l.Zp,{className:"bg-gradient-to-r from-green-500 to-emerald-600 border-0 text-white shadow-xl",children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-green-100 text-sm font-medium",children:"إجمالي المبيعات"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:"2,450,000 د.ع"})]}),(0,r.jsx)(d.A,{className:"h-8 w-8 text-green-100"})]})})}),(0,r.jsx)(l.Zp,{className:"bg-gradient-to-r from-blue-500 to-cyan-600 border-0 text-white shadow-xl",children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-blue-100 text-sm font-medium",children:"عمولات المندوبين"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:"245,000 د.ع"})]}),(0,r.jsx)(m.A,{className:"h-8 w-8 text-blue-100"})]})})}),(0,r.jsx)(l.Zp,{className:"bg-gradient-to-r from-purple-500 to-pink-600 border-0 text-white shadow-xl",children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-purple-100 text-sm font-medium",children:"صافي الأرباح"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:"2,205,000 د.ع"})]}),(0,r.jsx)(x.A,{className:"h-8 w-8 text-purple-100"})]})})}),(0,r.jsx)(l.Zp,{className:"bg-gradient-to-r from-orange-500 to-red-600 border-0 text-white shadow-xl",children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-orange-100 text-sm font-medium",children:"طلبات محاسبة"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:"156"})]}),(0,r.jsx)(h.A,{className:"h-8 w-8 text-orange-100"})]})})})]}),(0,r.jsxs)("div",{className:"flex space-x-1 bg-white/50 backdrop-blur-sm p-1 rounded-xl border border-white/20 shadow-lg",children:[(0,r.jsx)("button",{onClick:()=>v("couriers"),className:"flex-1 py-3 px-6 rounded-lg font-medium transition-all duration-200 ".concat("couriers"===s?"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:text-gray-800 hover:bg-white/50"),children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-5 w-5"}),"محاسبة المندوبين"]})}),(0,r.jsx)("button",{onClick:()=>v("customers"),className:"flex-1 py-3 px-6 rounded-lg font-medium transition-all duration-200 ".concat("customers"===s?"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:text-gray-800 hover:bg-white/50"),children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(u.A,{className:"h-5 w-5"}),"محاسبة العملاء"]})}),(0,r.jsx)("button",{onClick:()=>v("archive"),className:"flex-1 py-3 px-6 rounded-lg font-medium transition-all duration-200 ".concat("archive"===s?"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:text-gray-800 hover:bg-white/50"),children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),"أرشيف الحسابات"]})})]}),"couriers"===s&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(l.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-xl",children:[(0,r.jsxs)(l.aR,{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-t-lg",children:[(0,r.jsxs)(l.ZB,{className:"flex items-center gap-2 text-gray-800",children:[(0,r.jsx)("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg",children:(0,r.jsx)(u.A,{className:"h-5 w-5 text-white"})}),"اختيار المندوب"]}),(0,r.jsx)(l.BT,{className:"text-gray-600",children:"اختر المندوب لعرض طلباته المسلمة والمستحقة للمحاسبة"})]}),(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(b.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsxs)("select",{value:k,onChange:e=>S(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-200 rounded-xl bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-700",children:[(0,r.jsx)("option",{value:"",children:"اختر مندوب للمحاسبة"}),R.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.name," - ",e.phone]},e.id))]})]}),k&&(0,r.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-blue-50 rounded-lg",children:[(0,r.jsx)(f.A,{className:"h-5 w-5 text-blue-600"}),(0,r.jsxs)("span",{className:"text-blue-800 font-medium",children:["تم اختيار: ",null==(e=R.find(e=>e.id===k))?void 0:e.name]})]})]})})]}),k&&D.length>0&&(0,r.jsxs)(l.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-xl",children:[(0,r.jsx)(l.aR,{className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(l.ZB,{className:"flex items-center gap-2 text-gray-800",children:[(0,r.jsx)("div",{className:"p-2 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg",children:(0,r.jsx)(j.A,{className:"h-5 w-5 text-white"})}),"الطلبات المسلمة (",D.length,")"]}),(0,r.jsx)(a.$,{onClick:()=>{A.length===D.length?L([]):L(D.map(e=>e.id))},variant:"outline",size:"sm",className:"bg-white/50",children:A.length===D.length?"إلغاء تحديد الكل":"تحديد الكل"})]})}),(0,r.jsxs)(l.Wu,{className:"p-6",children:[(0,r.jsx)("div",{className:"space-y-3",children:D.map(e=>(0,r.jsx)("div",{onClick:()=>W(e.id),className:"p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ".concat(A.includes(e.id)?"border-blue-500 bg-blue-50 shadow-md":"border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm"),children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"w-5 h-5 rounded-full border-2 flex items-center justify-center ".concat(A.includes(e.id)?"border-blue-500 bg-blue-500":"border-gray-300"),children:A.includes(e.id)&&(0,r.jsx)(f.A,{className:"h-3 w-3 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-gray-800",children:e.trackingNumber}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.recipientName})]})]}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsxs)("p",{className:"font-bold text-green-600",children:[e.amount.toLocaleString()," د.ع"]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.deliveredAt.toLocaleDateString("ar-IQ")})]})]})},e.id))}),A.length>0&&(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:"ملخص المحاسبة"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"عدد الطلبات:"}),(0,r.jsx)("span",{className:"font-medium",children:A.length})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"إجمالي المبلغ:"}),(0,r.jsxs)("span",{className:"font-medium",children:[T.toLocaleString()," د.ع"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("span",{children:["العمولة (",A.length," \xd7 1000):"]}),(0,r.jsxs)("span",{className:"font-medium text-red-600",children:["-",z.toLocaleString()," د.ع"]})]}),(0,r.jsx)("hr",{className:"border-gray-300"}),(0,r.jsxs)("div",{className:"flex justify-between text-lg font-bold",children:[(0,r.jsx)("span",{children:"صافي المبلغ:"}),(0,r.jsxs)("span",{className:"text-green-600",children:[E.toLocaleString()," د.ع"]})]})]}),(0,r.jsxs)("div",{className:"flex gap-3 mt-4",children:[(0,r.jsxs)(a.$,{onClick:()=>{var e;let t='\n      <html dir="rtl">\n        <head>\n          <title>كشف حساب المندوب</title>\n          <style>\n            body { font-family: Arial, sans-serif; direction: rtl; }\n            .header { text-align: center; margin-bottom: 20px; }\n            .table { width: 100%; border-collapse: collapse; }\n            .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: right; }\n            .table th { background-color: #f2f2f2; }\n            .total { font-weight: bold; background-color: #e8f4fd; }\n          </style>\n        </head>\n        <body>\n          <div class="header">\n            <h1>كشف حساب المندوب</h1>\n            <p>التاريخ: '.concat(new Date().toLocaleDateString("ar-IQ"),"</p>\n            <p>المندوب: ").concat("all"===k?"جميع المندوبين":null==(e=R.find(e=>e.id===k))?void 0:e.name,'</p>\n          </div>\n          <table class="table">\n            <thead>\n              <tr>\n                <th>رقم الوصل</th>\n                <th>اسم المستلم</th>\n                <th>المبلغ</th>\n                <th>العمولة</th>\n                <th>الصافي</th>\n              </tr>\n            </thead>\n            <tbody>\n              ').concat(P.map(e=>"\n                <tr>\n                  <td>".concat(e.trackingNumber,"</td>\n                  <td>").concat(e.recipientName,"</td>\n                  <td>").concat(e.amount.toLocaleString()," د.ع</td>\n                  <td>1,000 د.ع</td>\n                  <td>").concat((e.amount-1e3).toLocaleString()," د.ع</td>\n                </tr>\n              ")).join(""),'\n              <tr class="total">\n                <td colspan="2">المجموع</td>\n                <td>').concat(T.toLocaleString()," د.ع</td>\n                <td>").concat(z.toLocaleString()," د.ع</td>\n                <td>").concat(E.toLocaleString()," د.ع</td>\n              </tr>\n            </tbody>\n          </table>\n        </body>\n      </html>\n    "),s=window.open("","_blank");s&&(s.document.write(t),s.document.close(),s.print())},variant:"outline",className:"flex-1 flex items-center gap-2",children:[(0,r.jsx)(N.A,{className:"h-4 w-4"}),"طباعة الكشف"]}),(0,r.jsx)(a.$,{onClick:()=>Z(!0),className:"flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700",children:"إنهاء المحاسبة"})]})]})]})]})]}),"archive"===s&&(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)(l.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-xl",children:[(0,r.jsxs)(l.aR,{className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg",children:[(0,r.jsxs)(l.ZB,{className:"flex items-center gap-2 text-gray-800",children:[(0,r.jsx)(p.A,{className:"h-6 w-6 text-green-600"}),"أرشيف الحسابات"]}),(0,r.jsx)(l.BT,{children:"اختر المندوب لعرض وطباعة كشف حسابه من الأرشيف"})]}),(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اختيار المندوب"}),(0,r.jsxs)("select",{value:B,onChange:e=>I(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"",children:"اختر المندوب"}),R.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.name," - ",e.phone]},e.id))]})]}),B&&(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold",children:["كشف حساب: ",null==(t=R.find(e=>e.id===B))?void 0:t.name]}),(0,r.jsxs)(a.$,{onClick:()=>$(B),className:"flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700",children:[(0,r.jsx)(N.A,{className:"h-4 w-4"}),"طباعة الكشف"]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:(()=>{let e=_.filter(e=>e.courierId===B),t=e.reduce((e,t)=>e+t.amount,0),s=1e3*e.length,n=t-s;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg text-center",children:[(0,r.jsx)("p",{className:"text-sm text-blue-600",children:"عدد الطلبات"}),(0,r.jsx)("p",{className:"text-xl font-bold text-blue-800",children:e.length})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg text-center",children:[(0,r.jsx)("p",{className:"text-sm text-green-600",children:"إجمالي المبالغ"}),(0,r.jsxs)("p",{className:"text-xl font-bold text-green-800",children:[t.toLocaleString()," د.ع"]})]}),(0,r.jsxs)("div",{className:"bg-orange-50 p-3 rounded-lg text-center",children:[(0,r.jsx)("p",{className:"text-sm text-orange-600",children:"العمولات"}),(0,r.jsxs)("p",{className:"text-xl font-bold text-orange-800",children:[s.toLocaleString()," د.ع"]})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-3 rounded-lg text-center",children:[(0,r.jsx)("p",{className:"text-sm text-purple-600",children:"الصافي للشركة"}),(0,r.jsxs)("p",{className:"text-xl font-bold text-purple-800",children:[n.toLocaleString()," د.ع"]})]})]})})()})]})]})})]})}),"customers"===s&&(0,r.jsx)(l.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-xl",children:(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"محاسبة العملاء"}),(0,r.jsx)(l.BT,{children:"قريباً..."})]})})]}),C&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,r.jsxs)(l.Zp,{className:"w-full max-w-md bg-white shadow-2xl",children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{className:"text-center",children:"تأكيد المحاسبة"})}),(0,r.jsxs)(l.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("p",{children:"هل أنت متأكد من إنهاء محاسبة هذه الطلبات؟"}),(0,r.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"عدد الطلبات:"})," ",A.length]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"صافي المبلغ:"})," ",E.toLocaleString()," د.ع"]})]})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)(a.$,{onClick:()=>Z(!1),variant:"outline",className:"flex-1",children:"إلغاء"}),(0,r.jsx)(a.$,{onClick:()=>{Z(!1),L([]),alert("تم إنهاء المحاسبة بنجاح!")},className:"flex-1 bg-green-600 hover:bg-green-700",children:"تأكيد"})]})]})]})})]})]})}},39599:(e,t,s)=>{"use strict";s.d(t,{BC:()=>a,_m:()=>n});let r={manager:["orders","dispatch","returns","accounting","statistics","archive","users","import-export","notifications","settings"],supervisor:["orders","statistics","archive","users","notifications","settings"],courier:["orders","archive","notifications","statistics"]};function n(e,t){var s;return(null==(s=r[e])?void 0:s.includes(t))||!1}let l=[{id:"orders",href:"/orders",label:"إدارة الطلبات",description:"عرض ومتابعة وتعديل الطلبات",icon:"Package",color:"from-blue-500 to-blue-600",roles:["manager","supervisor","courier"]},{id:"dispatch",href:"/dispatch",label:"إسناد الطلبات",description:"توزيع الطلبات على المندوبين",icon:"TruckIcon",color:"from-green-500 to-emerald-600",roles:["manager"]},{id:"returns",href:"/returns",label:"إدارة الرواجع",description:"استلام الطلبات الراجعة",icon:"RotateCcw",color:"from-orange-500 to-amber-600",roles:["manager"]},{id:"accounting",href:"/accounting",label:"المحاسبة",description:"التسويات المالية مع المندوبين",icon:"Calculator",color:"from-purple-500 to-violet-600",roles:["manager"]},{id:"statistics",href:"/statistics",label:"الإحصائيات",description:"التقارير والإحصائيات التفصيلية",icon:"BarChart3",color:"from-cyan-500 to-blue-600",roles:["manager","supervisor"]},{id:"archive",href:"/archive",label:"الأرشيف",description:"السجلات المنتهية",icon:"Archive",color:"from-gray-500 to-slate-600",roles:["manager","supervisor","courier"]},{id:"users",href:"/users",label:"إدارة الموظفين",description:"إدارة حسابات المستخدمين",icon:"Users",color:"from-indigo-500 to-purple-600",roles:["manager","supervisor"]},{id:"import-export",href:"/import-export",label:"استيراد وتصدير",description:"عمليات مجمعة على الطلبات",icon:"Upload",color:"from-teal-500 to-cyan-600",roles:["manager"]},{id:"notifications",href:"/notifications",label:"الإشعارات",description:"التنبيهات والتحديثات",icon:"Bell",color:"from-yellow-500 to-orange-600",roles:["manager","supervisor","courier"]},{id:"settings",href:"/settings",label:"الإعدادات",description:"تخصيص التطبيق",icon:"Settings",color:"from-pink-500 to-rose-600",roles:["manager","supervisor","courier"]}];function a(e){return l.filter(t=>t.roles.includes(e))}},64044:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(95155),n=s(32044),l=s(39599),a=s(35695),i=s(12115),o=s(66695),c=s(30285),d=s(1243),m=s(92138);function x(e){let{children:t,requiredSection:s,fallbackPath:x="/"}=e,{user:h,isAuthenticated:g}=(0,n.A)(),u=(0,a.useRouter)();return((0,i.useEffect)(()=>{if(!g)return void u.push("/login");(null==h?void 0:h.role)&&!(0,l._m)(h.role,s)&&u.push(x)},[h,g,s,x,u]),g&&h)?(0,l._m)(h.role,s)?(0,r.jsx)(r.Fragment,{children:t}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center p-4",dir:"rtl",children:(0,r.jsxs)(o.Zp,{className:"max-w-md w-full shadow-xl border-2 border-red-200",children:[(0,r.jsxs)(o.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-full shadow-lg mb-4 mx-auto",children:(0,r.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,r.jsx)(o.ZB,{className:"text-2xl text-red-600",children:"غير مصرح بالدخول"}),(0,r.jsx)(o.BT,{className:"text-lg text-red-500",children:"ليس لديك صلاحية للوصول إلى هذا القسم"})]}),(0,r.jsxs)(o.Wu,{className:"text-center space-y-4",children:[(0,r.jsxs)("p",{className:"text-gray-600 leading-relaxed",children:["عذراً، دورك الحالي (","manager"===h.role?"مدير":"supervisor"===h.role?"متابع":"مندوب",") لا يسمح بالوصول إلى هذا القسم."]}),(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-sm text-yellow-800",children:"إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع المدير لمراجعة صلاحياتك."})}),(0,r.jsxs)(c.$,{onClick:()=>u.push(x),className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:["العودة إلى الصفحة الرئيسية",(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"})]})]})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse",children:(0,r.jsx)("div",{className:"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"مرسال"}),(0,r.jsx)("p",{className:"text-gray-500",children:"جاري التحقق من الصلاحيات..."})]})})}},71482:(e,t,s)=>{Promise.resolve().then(s.bind(s,11452))}},e=>{var t=t=>e(e.s=t);e.O(0,[8541,1336,9948,4709,875,416,8779,622,2432,7979,1899,9744,4495,5138,6321,2652,5030,3719,9473,558,9401,6325,6077,4409,1124,9385,4927,37,2041,4979,8321,2900,3610,9988,2158,8058,2044,7192,7358],()=>t(71482)),_N_E=e.O()}]);