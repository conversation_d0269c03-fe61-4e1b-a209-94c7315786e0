"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7979],{3269:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_HEADER:function(){return n},FLIGHT_HEADERS:function(){return c},NEXT_DID_POSTPONE_HEADER:function(){return h},NEXT_HMR_REFRESH_HASH_COOKIE:function(){return i},NEXT_HMR_REFRESH_HEADER:function(){return u},NEXT_IS_PRERENDER_HEADER:function(){return y},NEXT_REWRITTEN_PATH_HEADER:function(){return _},NEXT_REWRITTEN_QUERY_HEADER:function(){return E},NEXT_ROUTER_PREFETCH_HEADER:function(){return l},NEXT_ROUTER_SEGMENT_PREFETCH_HEADER:function(){return a},NEXT_ROUTER_STALE_TIME_HEADER:function(){return p},NEXT_ROUTER_STATE_TREE_HEADER:function(){return o},NEXT_RSC_UNION_QUERY:function(){return f},NEXT_URL:function(){return s},RSC_CONTENT_TYPE_HEADER:function(){return d},RSC_HEADER:function(){return r}});let r="RSC",n="Next-Action",o="Next-Router-State-Tree",l="Next-Router-Prefetch",a="Next-Router-Segment-Prefetch",u="Next-HMR-Refresh",i="__next_hmr_refresh_hash__",s="Next-Url",d="text/x-component",c=[r,o,l,u,a],f="_rsc",p="x-nextjs-stale-time",h="x-nextjs-postponed",_="x-nextjs-rewritten-path",E="x-nextjs-rewritten-query",y="x-nextjs-prerender";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5449:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),r(13668);let n=r(20589);{let e=r.u;r.u=function(){for(var t=arguments.length,r=Array(t),o=0;o<t;o++)r[o]=arguments[o];return(0,n.encodeURIPath)(e(...r))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6002:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),(0,r(66905).patchConsoleError)(),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6634:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return _},dispatchNavigateAction:function(){return g},dispatchTraverseAction:function(){return b},getCurrentAppRouterState:function(){return E},publicAppRouterInstance:function(){return m}});let n=r(69818),o=r(29726),l=r(12115),a=r(95122);r(86005);let u=r(81027),i=r(85929),s=r(56158),d=r(89154),c=r(24930);function f(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:r,setState:n}=e,o=t.state;t.pending=r;let l=r.payload,u=t.action(o,l);function i(e){r.discarded||(t.state=e,f(t,n),r.resolve(e))}(0,a.isThenable)(u)?u.then(i,e=>{f(t,n),r.reject(e)}):i(u)}let h=null;function _(e,t){let r={state:e,dispatch:(e,t)=>(function(e,t,r){let o={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{o={resolve:e,reject:t}});(0,l.startTransition)(()=>{r(e)})}let a={payload:t,next:null,resolve:o.resolve,reject:o.reject};null===e.pending?(e.last=a,p({actionQueue:e,action:a,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,a.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:a,setState:r})):(null!==e.last&&(e.last.next=a),e.last=a)})(r,e,t),action:async(e,t)=>(0,o.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};if(null!==h)throw Object.defineProperty(Error("Internal Next.js Error: createMutableActionQueue was called more than once"),"__NEXT_ERROR_CODE",{value:"E624",enumerable:!1,configurable:!0});return h=r,r}function E(){return null!==h?h.state:null}function y(){return null!==h?h.onRouterTransitionStart:null}function g(e,t,r,o){let l=new URL((0,i.addBasePath)(e),location.href);(0,c.setLinkForCurrentNavigation)(o);let a=y();null!==a&&a(e,t),(0,u.dispatchAppRouterAction)({type:n.ACTION_NAVIGATE,url:l,isExternalUrl:(0,s.isExternalURL)(l),locationSearch:location.search,shouldScroll:r,navigateType:t,allowAliasing:!0})}function b(e,t){let r=y();null!==r&&r(e,"traverse"),(0,u.dispatchAppRouterAction)({type:n.ACTION_RESTORE,url:new URL(e),tree:t})}let m={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r=function(){if(null===h)throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0});return h}(),o=(0,s.createPrefetchURL)(e);if(null!==o){var l;(0,d.prefetchReducer)(r.state,{type:n.ACTION_PREFETCH,url:o,kind:null!=(l=null==t?void 0:t.kind)?l:n.PrefetchKind.FULL})}},replace:(e,t)=>{(0,l.startTransition)(()=>{var r;g(e,"replace",null==(r=null==t?void 0:t.scroll)||r,null)})},push:(e,t)=>{(0,l.startTransition)(()=>{var r;g(e,"push",null==(r=null==t?void 0:t.scroll)||r,null)})},refresh:()=>{(0,l.startTransition)(()=>{(0,u.dispatchAppRouterAction)({type:n.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};window.next&&(window.next.router=m),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},13950:(e,t)=>{function r(e,t){let r=e[e.length-1];r&&r.stack===t.stack||e.push(t)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"enqueueConsecutiveDedupedError",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21315:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return o}});let n=r(85929);function o(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22858:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return l}});let n=r(36494),o=r(62210);function l(e){return(0,o.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{IDLE_LINK_STATUS:function(){return s},PENDING_LINK_STATUS:function(){return i},mountFormInstance:function(){return g},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return m},onNavigationIntent:function(){return v},pingVisibleLinks:function(){return O},setLinkForCurrentNavigation:function(){return d},unmountLinkForCurrentNavigation:function(){return c},unmountPrefetchableInstance:function(){return b}}),r(6634);let n=r(56158),o=r(69818),l=r(86005),a=r(12115),u=null,i={pending:!0},s={pending:!1};function d(e){(0,a.startTransition)(()=>{null==u||u.setOptimisticLinkStatus(s),null==e||e.setOptimisticLinkStatus(i),u=e})}function c(e){u===e&&(u=null)}let f="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,h="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;m(t.target,e)}},{rootMargin:"200px"}):null;function _(e,t){void 0!==f.get(e)&&b(e),f.set(e,t),null!==h&&h.observe(e)}function E(e){try{return(0,n.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,r,n,o,l){if(o){let o=E(t);if(null!==o){let t={router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:l};return _(e,t),t}}return{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:l}}function g(e,t,r,n){let o=E(t);null!==o&&_(e,{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:null})}function b(e){let t=f.get(e);if(void 0!==t){f.delete(e),p.delete(t);let r=t.prefetchTask;null!==r&&(0,l.cancelPrefetchTask)(r)}null!==h&&h.unobserve(e)}function m(e,t){let r=f.get(e);void 0!==r&&(r.isVisible=t,t?p.add(r):p.delete(r),R(r))}function v(e,t){let r=f.get(e);void 0!==r&&void 0!==r&&(r.wasHoveredOrTouched=!0,R(r))}function R(e){var t;let r=e.prefetchTask;if(!e.isVisible){null!==r&&(0,l.cancelPrefetchTask)(r);return}t=e,(async()=>t.router.prefetch(t.prefetchHref,{kind:t.kind}))().catch(e=>{})}function O(e,t){let r=(0,l.getCurrentCacheVersion)();for(let n of p){let a=n.prefetchTask;if(null!==a&&n.cacheVersion===r&&a.key.nextUrl===e&&a.treeAtTimeOfPrefetch===t)continue;null!==a&&(0,l.cancelPrefetchTask)(a);let u=(0,l.createCacheKey)(n.prefetchHref,e),i=n.wasHoveredOrTouched?l.PrefetchPriority.Intent:l.PrefetchPriority.Default;n.prefetchTask=(0,l.schedulePrefetchTask)(u,t,n.kind===o.PrefetchKind.FULL,i),n.cacheVersion=(0,l.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26043:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createConsoleError:function(){return o},getConsoleErrorType:function(){return a},isConsoleError:function(){return l}});let r=Symbol.for("next.console.error.digest"),n=Symbol.for("next.console.error.type");function o(e,t){let o="string"==typeof e?Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0}):e;return o[r]="NEXT_CONSOLE_ERROR",o[n]="string"==typeof e?"string":"error",t&&!o.environmentName&&(o.environmentName=t),o}let l=e=>e&&"NEXT_CONSOLE_ERROR"===e[r],a=e=>e[n];("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26465:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NEXTJS_HYDRATION_ERROR_LINK:function(){return i},REACT_HYDRATION_ERROR_LINK:function(){return u},getDefaultHydrationErrorMessage:function(){return s},getHydrationErrorStackInfo:function(){return h},isHydrationError:function(){return d},isReactHydrationErrorMessage:function(){return c},testReactHydrationWarning:function(){return p}});let n=r(88229)._(r(15807)),o=/hydration failed|while hydrating|content does not match|did not match|HTML didn't match|text didn't match/i,l="Hydration failed because the server rendered HTML didn't match the client. As a result this tree will be regenerated on the client. This can happen if a SSR-ed Client Component used:",a=[l,"Hydration failed because the server rendered text didn't match the client. As a result this tree will be regenerated on the client. This can happen if a SSR-ed Client Component used:","A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:"],u="https://react.dev/link/hydration-mismatch",i="https://nextjs.org/docs/messages/react-hydration-error",s=()=>l;function d(e){return(0,n.default)(e)&&o.test(e.message)}function c(e){return a.some(t=>e.startsWith(t))}let f=[/^In HTML, (.+?) cannot be a child of <(.+?)>\.(.*)\nThis will cause a hydration error\.(.*)/,/^In HTML, (.+?) cannot be a descendant of <(.+?)>\.\nThis will cause a hydration error\.(.*)/,/^In HTML, text nodes cannot be a child of <(.+?)>\.\nThis will cause a hydration error\./,/^In HTML, whitespace text nodes cannot be a child of <(.+?)>\. Make sure you don't have any extra whitespace between tags on each line of your source code\.\nThis will cause a hydration error\./,/^Expected server HTML to contain a matching <(.+?)> in <(.+?)>\.(.*)/,/^Did not expect server HTML to contain a <(.+?)> in <(.+?)>\.(.*)/,/^Expected server HTML to contain a matching text node for "(.+?)" in <(.+?)>\.(.*)/,/^Did not expect server HTML to contain the text node "(.+?)" in <(.+?)>\.(.*)/,/^Text content did not match\. Server: "(.+?)" Client: "(.+?)"(.*)/];function p(e){return"string"==typeof e&&!!e&&(e.startsWith("Warning: ")&&(e=e.slice(9)),f.some(t=>t.test(e)))}function h(e){let t=p(e=(e=e.replace(/^Error: /,"")).replace("Warning: ",""));if(!c(e)&&!t)return{message:null,stack:e,diff:""};if(t){let[t,r]=e.split("\n\n");return{message:t.trim(),stack:"",diff:(r||"").trim()}}let r=e.indexOf("\n"),[n,o]=(e=e.slice(r+1).trim()).split(""+u),l=n.trim();if(!o||!(o.length>1))return{message:l,stack:o};{let e=[],t=[];return o.split("\n").forEach(r=>{""!==r.trim()&&(r.trim().startsWith("at ")?e.push(r):t.push(r))}),{message:l,diff:t.join("\n"),stack:e.join("\n")}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ErrorBoundary:function(){return h},ErrorBoundaryHandler:function(){return c},GlobalError:function(){return f},default:function(){return p}});let n=r(88229),o=r(95155),l=n._(r(12115)),a=r(19921),u=r(22858);r(38836);let i=void 0,s={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};function d(e){let{error:t}=e;if(i){let e=i.getStore();if((null==e?void 0:e.isRevalidate)||(null==e?void 0:e.isStaticGeneration))throw console.error(t),t}return null}class c extends l.default.Component{static getDerivedStateFromError(e){if((0,u.isNextRouterError)(e))throw e;return{error:e}}static getDerivedStateFromProps(e,t){let{error:r}=t;return e.pathname!==t.previousPathname&&t.error?{error:null,previousPathname:e.pathname}:{error:t.error,previousPathname:e.pathname}}render(){return this.state.error?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(d,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,o.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(e){super(e),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function f(e){let{error:t}=e,r=null==t?void 0:t.digest;return(0,o.jsxs)("html",{id:"__next_error__",children:[(0,o.jsx)("head",{}),(0,o.jsxs)("body",{children:[(0,o.jsx)(d,{error:t}),(0,o.jsx)("div",{style:s.error,children:(0,o.jsxs)("div",{children:[(0,o.jsxs)("h2",{style:s.text,children:["Application error: a ",r?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",r?"server logs":"browser console"," for more information)."]}),r?(0,o.jsx)("p",{style:s.text,children:"Digest: "+r}):null]})})]})]})}let p=f;function h(e){let{errorComponent:t,errorStyles:r,errorScripts:n,children:l}=e,u=(0,a.useUntrackedPathname)();return t?(0,o.jsx)(c,{pathname:u,errorComponent:t,errorStyles:r,errorScripts:n,children:l}):(0,o.jsx)(o.Fragment,{children:l})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35415:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),r(5449);let n=r(36188),o=r(51408);(0,n.appBootstrap)(()=>{let{hydrate:e}=r(64486);r(56158),r(87555),e(o)}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},36494:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return o},getAccessFallbackErrorTypeByStatus:function(){return u},getAccessFallbackHTTPStatus:function(){return a},isHTTPAccessFallbackError:function(){return l}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),o="NEXT_HTTP_ERROR_FALLBACK";function l(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===o&&n.has(Number(r))}function a(e){return Number(e.digest.split(";")[1])}function u(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},43954:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),(0,r(65444).handleGlobalErrors)(),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44181:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return o},getAccessFallbackErrorTypeByStatus:function(){return u},getAccessFallbackHTTPStatus:function(){return a},isHTTPAccessFallbackError:function(){return l}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),o="NEXT_HTTP_ERROR_FALLBACK";function l(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===o&&n.has(Number(r))}function a(e){return Number(e.digest.split(";")[1])}function u(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},46975:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTTPAccessFallbackBoundary",{enumerable:!0,get:function(){return d}});let n=r(6966),o=r(95155),l=n._(r(12115)),a=r(19921),u=r(36494);r(43230);let i=r(95227);class s extends l.default.Component{componentDidCatch(){}static getDerivedStateFromError(e){if((0,u.isHTTPAccessFallbackError)(e))return{triggeredStatus:(0,u.getAccessFallbackHTTPStatus)(e)};throw e}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.triggeredStatus?{triggeredStatus:void 0,previousPathname:e.pathname}:{triggeredStatus:t.triggeredStatus,previousPathname:e.pathname}}render(){let{notFound:e,forbidden:t,unauthorized:r,children:n}=this.props,{triggeredStatus:l}=this.state,a={[u.HTTPAccessErrorStatus.NOT_FOUND]:e,[u.HTTPAccessErrorStatus.FORBIDDEN]:t,[u.HTTPAccessErrorStatus.UNAUTHORIZED]:r};if(l){let i=l===u.HTTPAccessErrorStatus.NOT_FOUND&&e,s=l===u.HTTPAccessErrorStatus.FORBIDDEN&&t,d=l===u.HTTPAccessErrorStatus.UNAUTHORIZED&&r;return i||s||d?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("meta",{name:"robots",content:"noindex"}),!1,a[l]]}):n}return n}constructor(e){super(e),this.state={triggeredStatus:void 0,previousPathname:e.pathname}}}function d(e){let{notFound:t,forbidden:r,unauthorized:n,children:u}=e,d=(0,a.useUntrackedPathname)(),c=(0,l.useContext)(i.MissingSlotContext);return t||r||n?(0,o.jsx)(s,{pathname:d,notFound:t,forbidden:r,unauthorized:n,missingSlots:c,children:u}):(0,o.jsx)(o.Fragment,{children:u})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"attachHydrationErrorState",{enumerable:!0,get:function(){return l}});let n=r(26465),o=r(89771);function l(e){let t={},r=(0,n.testReactHydrationWarning)(e.message),l=(0,n.isHydrationError)(e);if(!(l||r))return;let a=(0,o.getReactHydrationDiffSegments)(e.message);if(a){let u=a[1];t={...e.details,...o.hydrationErrorState,warning:(u&&!r?null:o.hydrationErrorState.warning)||[(0,n.getDefaultHydrationErrorMessage)(),"",""],notes:r?"":a[0],reactOutputComponentDiff:u},!o.hydrationErrorState.reactOutputComponentDiff&&u&&(o.hydrationErrorState.reactOutputComponentDiff=u),!u&&l&&o.hydrationErrorState.reactOutputComponentDiff&&(t.reactOutputComponentDiff=o.hydrationErrorState.reactOutputComponentDiff)}else o.hydrationErrorState.warning&&(t={...e.details,...o.hydrationErrorState}),o.hydrationErrorState.reactOutputComponentDiff&&(t.reactOutputComponentDiff=o.hydrationErrorState.reactOutputComponentDiff);e.details=t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56158:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return S},createPrefetchURL:function(){return P},default:function(){return M},isExternalURL:function(){return x}});let n=r(6966),o=r(95155),l=n._(r(12115)),a=r(95227),u=r(69818),i=r(11139),s=r(886),d=r(81027),c=n._(r(26614)),f=r(10774),p=r(85929),h=r(67760),_=r(20686),E=r(72691),y=r(71822),g=r(44882),b=r(87102),m=r(68946),v=r(38836),R=r(6634),O=r(36825),T=r(62210);r(24930);let j={};function x(e){return e.origin!==window.location.origin}function P(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return x(t)?null:t}function w(e){let{appRouterState:t}=e;return(0,l.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,o={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,i.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(o,"",n)):window.history.replaceState(o,"",n)},[t]),(0,l.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function S(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function N(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function A(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,o=null!==n?n:r;return(0,l.useDeferredValue)(r,o)}function C(e){let t,{actionQueue:r,assetPrefix:n,globalError:i}=e,f=(0,d.useActionQueue)(r),{canonicalUrl:p}=f,{searchParams:v,pathname:x}=(0,l.useMemo)(()=>{let e=new URL(p,window.location.href);return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,g.removeBasePath)(e.pathname):e.pathname}},[p]);(0,l.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(j.pendingMpaPath=void 0,(0,d.dispatchAppRouterAction)({type:u.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,l.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,T.isRedirectError)(t)){e.preventDefault();let r=(0,O.getURLFromRedirectError)(t);(0,O.getRedirectTypeFromError)(t)===T.RedirectType.push?R.publicAppRouterInstance.push(r,{}):R.publicAppRouterInstance.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:P}=f;if(P.mpaNavigation){if(j.pendingMpaPath!==p){let e=window.location;P.pendingPush?e.assign(p):e.replace(p),j.pendingMpaPath=p}(0,l.use)(y.unresolvedThenable)}(0,l.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,l.startTransition)(()=>{(0,d.dispatchAppRouterAction)({type:u.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,o){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=N(t),o&&r(o)),e(t,n,o)},window.history.replaceState=function(e,n,o){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=N(e),o&&r(o)),t(e,n,o)};let n=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,l.startTransition)(()=>{(0,R.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[]);let{cache:S,tree:C,nextUrl:M,focusAndScrollRef:H}=f,L=(0,l.useMemo)(()=>(0,E.findHeadInCache)(S,C[1]),[S,C]),I=(0,l.useMemo)(()=>(0,m.getSelectedParams)(C),[C]),k=(0,l.useMemo)(()=>({parentTree:C,parentCacheNode:S,parentSegmentPath:null,url:p}),[C,S,p]),F=(0,l.useMemo)(()=>({tree:C,focusAndScrollRef:H,nextUrl:M}),[C,H,M]);if(null!==L){let[e,r]=L;t=(0,o.jsx)(A,{headCacheNode:e},r)}else t=null;let U=(0,o.jsxs)(_.RedirectBoundary,{children:[t,S.rsc,(0,o.jsx)(h.AppRouterAnnouncer,{tree:C})]});return U=(0,o.jsx)(c.ErrorBoundary,{errorComponent:i[0],errorStyles:i[1],children:U}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(w,{appRouterState:f}),(0,o.jsx)(D,{}),(0,o.jsx)(s.PathParamsContext.Provider,{value:I,children:(0,o.jsx)(s.PathnameContext.Provider,{value:x,children:(0,o.jsx)(s.SearchParamsContext.Provider,{value:v,children:(0,o.jsx)(a.GlobalLayoutRouterContext.Provider,{value:F,children:(0,o.jsx)(a.AppRouterContext.Provider,{value:R.publicAppRouterInstance,children:(0,o.jsx)(a.LayoutRouterContext.Provider,{value:k,children:U})})})})})})]})}function M(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:l}=e;return(0,v.useNavFailureHandler)(),(0,o.jsx)(c.ErrorBoundary,{errorComponent:c.default,children:(0,o.jsx)(C,{actionQueue:t,assetPrefix:l,globalError:[r,n]})})}let H=new Set,L=new Set;function D(){let[,e]=l.default.useState(0),t=H.size;return(0,l.useEffect)(()=>{let r=()=>e(e=>e+1);return L.add(r),t!==H.size&&r(),()=>{L.delete(r)}},[t,e]),[...H].map((e,t)=>(0,o.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=H.size;return H.add(e),H.size!==t&&L.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63678:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(36494).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65444:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleClientError:function(){return b},handleConsoleError:function(){return g},handleGlobalErrors:function(){return O},useErrorHandler:function(){return m}});let n=r(88229),o=r(12115),l=r(53506),a=r(22858),u=r(89771),i=r(85169),s=n._(r(15807)),d=r(26043),c=r(13950),f=r(95128),p=globalThis.queueMicrotask||(e=>Promise.resolve().then(e)),h=[],_=[],E=[],y=[];function g(e,t){let r,{environmentName:n}=(0,i.parseConsoleArgs)(t);for(let o of(r=(0,s.default)(e)?(0,d.createConsoleError)(e,n):(0,d.createConsoleError)((0,i.formatConsoleArgs)(t),n),r=(0,f.getReactStitchedError)(r),(0,u.storeHydrationErrorStateFromConsoleArgs)(...t),(0,l.attachHydrationErrorState)(r),(0,c.enqueueConsecutiveDedupedError)(h,r),_))p(()=>{o(r)})}function b(e){let t;for(let r of(t=(0,s.default)(e)?e:Object.defineProperty(Error(e+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0}),t=(0,f.getReactStitchedError)(t),(0,l.attachHydrationErrorState)(t),(0,c.enqueueConsecutiveDedupedError)(h,t),_))p(()=>{r(t)})}function m(e,t){(0,o.useEffect)(()=>(h.forEach(e),E.forEach(t),_.push(e),y.push(t),()=>{_.splice(_.indexOf(e),1),y.splice(y.indexOf(t),1),h.splice(0,h.length),E.splice(0,E.length)}),[e,t])}function v(e){if((0,a.isNextRouterError)(e.error))return e.preventDefault(),!1;e.error&&b(e.error)}function R(e){let t=null==e?void 0:e.reason;if((0,a.isNextRouterError)(t))return void e.preventDefault();let r=t;for(let e of(r&&!(0,s.default)(r)&&(r=Object.defineProperty(Error(r+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})),E.push(r),y))e(r)}function O(){try{Error.stackTraceLimit=50}catch(e){}window.addEventListener("error",v),window.addEventListener("unhandledrejection",R)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66395:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTTPAccessErrorFallback",{enumerable:!0,get:function(){return l}}),r(88229);let n=r(95155);r(12115);let o={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block"},h1:{display:"inline-block",margin:"0 20px 0 0",padding:"0 23px 0 0",fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:14,fontWeight:400,lineHeight:"49px",margin:0}};function l(e){let{status:t,message:r}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("title",{children:t+": "+r}),(0,n.jsx)("div",{style:o.error,children:(0,n.jsxs)("div",{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}),(0,n.jsx)("h1",{className:"next-error-h1",style:o.h1,children:t}),(0,n.jsx)("div",{style:o.desc,children:(0,n.jsx)("h2",{style:o.h2,children:r})})]})})]})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66905:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{originConsoleError:function(){return o},patchConsoleError:function(){return l}}),r(88229),r(15807);let n=r(22858);r(65444),r(85169);let o=globalThis.console.error;function l(){window.console.error=function(){let e;for(var t=arguments.length,r=Array(t),l=0;l<t;l++)r[l]=arguments[l];e=r[0],(0,n.isNextRouterError)(e)||o.apply(window.console,r)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67760:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let n=r(12115),o=r(47650),l="next-route-announcer";function a(e){let{tree:t}=e,[r,a]=(0,n.useState)(null);(0,n.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(l)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(l);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(l)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[u,i]=(0,n.useState)(""),s=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==s.current&&s.current!==e&&i(e),s.current=e},[t]),r?(0,o.createPortal)(u,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76999:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return l}});let n=r(44181),o=r(62591);function l(e){return(0,o.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83575:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getReactStitchedError",{enumerable:!0,get:function(){return s}});let n=r(64252),o=n._(r(14232)),l=n._(r(66240)),a=r(38089),u="react-stack-bottom-frame",i=RegExp("(at "+u+" )|("+u+"\\@)");function s(e){let t=(0,l.default)(e),r=t&&e.stack||"",n=t?e.message:"",u=r.split("\n"),s=u.findIndex(e=>i.test(e)),d=s>=0?u.slice(0,s).join("\n"):r,c=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return Object.assign(c,e),(0,a.copyNextErrorCode)(e,c),c.stack=d,function(e){if(!o.default.captureOwnerStack)return;let t=e.stack||"",r=o.default.captureOwnerStack();r&&!1===t.endsWith(r)&&(e.stack=t+=r)}(c),c}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87555:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return P}});let n=r(88229),o=r(6966),l=r(95155),a=r(69818),u=o._(r(12115)),i=n._(r(47650)),s=r(95227),d=r(88586),c=r(71822),f=r(26614),p=r(31127),h=r(24189),_=r(20686),E=r(46975),y=r(85637),g=r(4108),b=r(81027),m=i.default.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,v=["bottom","height","left","right","top","width","x","y"];function R(e,t){let r=e.getBoundingClientRect();return r.top>=0&&r.top<=t}class O extends u.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...e){super(...e),this.handlePotentialScroll=()=>{let{focusAndScrollRef:e,segmentPath:t}=this.props;if(e.apply){if(0!==e.segmentPaths.length&&!e.segmentPaths.some(e=>t.every((t,r)=>(0,p.matchSegment)(t,e[r]))))return;let r=null,n=e.hashFragment;if(n&&(r=function(e){var t;return"top"===e?document.body:null!=(t=document.getElementById(e))?t:document.getElementsByName(e)[0]}(n)),r||(r=(0,m.findDOMNode)(this)),!(r instanceof Element))return;for(;!(r instanceof HTMLElement)||function(e){if(["sticky","fixed"].includes(getComputedStyle(e).position))return!0;let t=e.getBoundingClientRect();return v.every(e=>0===t[e])}(r);){if(null===r.nextElementSibling)return;r=r.nextElementSibling}e.apply=!1,e.hashFragment=null,e.segmentPaths=[],(0,h.handleSmoothScroll)(()=>{if(n)return void r.scrollIntoView();let e=document.documentElement,t=e.clientHeight;!R(r,t)&&(e.scrollTop=0,R(r,t)||r.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:e.onlyHashChange}),e.onlyHashChange=!1,r.focus()}}}}function T(e){let{segmentPath:t,children:r}=e,n=(0,u.useContext)(s.GlobalLayoutRouterContext);if(!n)throw Object.defineProperty(Error("invariant global layout router not mounted"),"__NEXT_ERROR_CODE",{value:"E473",enumerable:!1,configurable:!0});return(0,l.jsx)(O,{segmentPath:t,focusAndScrollRef:n.focusAndScrollRef,children:r})}function j(e){let{tree:t,segmentPath:r,cacheNode:n,url:o}=e,i=(0,u.useContext)(s.GlobalLayoutRouterContext);if(!i)throw Object.defineProperty(Error("invariant global layout router not mounted"),"__NEXT_ERROR_CODE",{value:"E473",enumerable:!1,configurable:!0});let{tree:f}=i,h=null!==n.prefetchRsc?n.prefetchRsc:n.rsc,_=(0,u.useDeferredValue)(n.rsc,h),E="object"==typeof _&&null!==_&&"function"==typeof _.then?(0,u.use)(_):_;if(!E){let e=n.lazyData;if(null===e){let t=function e(t,r){if(t){let[n,o]=t,l=2===t.length;if((0,p.matchSegment)(r[0],n)&&r[1].hasOwnProperty(o)){if(l){let t=e(void 0,r[1][o]);return[r[0],{...r[1],[o]:[t[0],t[1],t[2],"refetch"]}]}return[r[0],{...r[1],[o]:e(t.slice(2),r[1][o])}]}}return r}(["",...r],f),l=(0,g.hasInterceptionRouteInCurrentTree)(f),s=Date.now();n.lazyData=e=(0,d.fetchServerResponse)(new URL(o,location.origin),{flightRouterState:t,nextUrl:l?i.nextUrl:null}).then(e=>((0,u.startTransition)(()=>{(0,b.dispatchAppRouterAction)({type:a.ACTION_SERVER_PATCH,previousTree:f,serverResponse:e,navigatedAt:s})}),e)),(0,u.use)(e)}(0,u.use)(c.unresolvedThenable)}return(0,l.jsx)(s.LayoutRouterContext.Provider,{value:{parentTree:t,parentCacheNode:n,parentSegmentPath:r,url:o},children:E})}function x(e){let t,{loading:r,children:n}=e;if(t="object"==typeof r&&null!==r&&"function"==typeof r.then?(0,u.use)(r):r){let e=t[0],r=t[1],o=t[2];return(0,l.jsx)(u.Suspense,{fallback:(0,l.jsxs)(l.Fragment,{children:[r,o,e]}),children:n})}return(0,l.jsx)(l.Fragment,{children:n})}function P(e){let{parallelRouterKey:t,error:r,errorStyles:n,errorScripts:o,templateStyles:a,templateScripts:i,template:d,notFound:c,forbidden:p,unauthorized:h}=e,g=(0,u.useContext)(s.LayoutRouterContext);if(!g)throw Object.defineProperty(Error("invariant expected layout router to be mounted"),"__NEXT_ERROR_CODE",{value:"E56",enumerable:!1,configurable:!0});let{parentTree:b,parentCacheNode:m,parentSegmentPath:v,url:R}=g,O=m.parallelRoutes,P=O.get(t);P||(P=new Map,O.set(t,P));let w=b[0],S=b[1][t],N=S[0],A=null===v?[t]:v.concat([w,t]),C=(0,y.createRouterCacheKey)(N),M=(0,y.createRouterCacheKey)(N,!0),H=P.get(C);if(void 0===H){let e={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};H=e,P.set(C,e)}let L=m.loading;return(0,l.jsxs)(s.TemplateContext.Provider,{value:(0,l.jsx)(T,{segmentPath:A,children:(0,l.jsx)(f.ErrorBoundary,{errorComponent:r,errorStyles:n,errorScripts:o,children:(0,l.jsx)(x,{loading:L,children:(0,l.jsx)(E.HTTPAccessFallbackBoundary,{notFound:c,forbidden:p,unauthorized:h,children:(0,l.jsx)(_.RedirectBoundary,{children:(0,l.jsx)(j,{url:R,tree:S,cacheNode:H,segmentPath:A})})})})})}),children:[a,i,d]},M)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89771:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getHydrationWarningType:function(){return u},getReactHydrationDiffSegments:function(){return d},hydrationErrorState:function(){return o},storeHydrationErrorStateFromConsoleArgs:function(){return c}});let n=r(26465),o={},l=new Set(["Warning: In HTML, %s cannot be a child of <%s>.%s\nThis will cause a hydration error.%s","Warning: In HTML, %s cannot be a descendant of <%s>.\nThis will cause a hydration error.%s","Warning: In HTML, text nodes cannot be a child of <%s>.\nThis will cause a hydration error.","Warning: In HTML, whitespace text nodes cannot be a child of <%s>. Make sure you don't have any extra whitespace between tags on each line of your source code.\nThis will cause a hydration error.","Warning: Expected server HTML to contain a matching <%s> in <%s>.%s","Warning: Did not expect server HTML to contain a <%s> in <%s>.%s"]),a=new Set(['Warning: Expected server HTML to contain a matching text node for "%s" in <%s>.%s','Warning: Did not expect server HTML to contain the text node "%s" in <%s>.%s']),u=e=>{if("string"!=typeof e)return"text";let t=e.startsWith("Warning: ")?e:"Warning: "+e;return i(t)?"tag":s(t)?"text-in-tag":"text"},i=e=>l.has(e),s=e=>a.has(e),d=e=>{if(e){let{message:t,diff:r}=(0,n.getHydrationErrorStackInfo)(e);if(t)return[t,r]}};function c(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[l,a,i,...s]=t;if((0,n.testReactHydrationWarning)(l)){let e=l.startsWith("Warning: ");3===t.length&&(i="");let r=[l,a,i],n=(s[s.length-1]||"").trim();e?o.reactOutputComponentDiff=function(e,t,r,n){let o=-1,l=-1,a=u(e),i=n.split("\n").map((e,n)=>{e=e.trim();let[,a,u]=/at (\w+)( \((.*)\))?/.exec(e)||[];return u||(a===t&&-1===o?o=n:a===r&&-1===l&&(l=n)),u?"":a}).filter(Boolean).reverse(),s="";for(let e=0;e<i.length;e++){let t=i[e],r="tag"===a&&e===i.length-o-1,n="tag"===a&&e===i.length-l-1;r||n?s+="> "+" ".repeat(Math.max(2*e-2,0)+2)+"<"+t+">\n":s+=" ".repeat(2*e+2)+"<"+t+">\n"}if("text"===a){let e=" ".repeat(2*i.length);s+="+ "+e+'"'+t+'"\n'+("- "+e+'"'+r)+'"\n'}else if("text-in-tag"===a){let e=" ".repeat(2*i.length);s+="> "+e+"<"+r+">\n"+(">   "+e+'"'+t)+'"\n'}return s}(l,a,i,n):o.reactOutputComponentDiff=n,o.warning=r,o.serverContent=a,o.clientContent=i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90894:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientPageRoot",{enumerable:!0,get:function(){return o}});let n=r(95155);function o(e){let{Component:t,searchParams:o,params:l,promises:a}=e;{let{createRenderSearchParamsFromClient:e}=r(67205),a=e(o),{createRenderParamsFromClient:u}=r(33558),i=u(l);return(0,n.jsx)(t,{params:i,searchParams:a})}}r(39837),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94970:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientSegmentRoot",{enumerable:!0,get:function(){return o}});let n=r(95155);function o(e){let{Component:t,slots:o,params:l,promise:a}=e;{let{createRenderParamsFromClient:e}=r(33558),a=e(l);return(0,n.jsx)(t,{...o,params:a})}}r(39837),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95128:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getReactStitchedError",{enumerable:!0,get:function(){return s}});let n=r(88229),o=n._(r(12115)),l=n._(r(15807)),a=r(29148),u="react-stack-bottom-frame",i=RegExp("(at "+u+" )|("+u+"\\@)");function s(e){let t=(0,l.default)(e),r=t&&e.stack||"",n=t?e.message:"",u=r.split("\n"),s=u.findIndex(e=>i.test(e)),d=s>=0?u.slice(0,s).join("\n"):r,c=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return Object.assign(c,e),(0,a.copyNextErrorCode)(e,c),c.stack=d,function(e){if(!o.default.captureOwnerStack)return;let t=e.stack||"",r=o.default.captureOwnerStack();r&&!1===t.endsWith(r)&&(e.stack=t+=r)}(c),c}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);