# 👥 دليل إضافة الحسابات في Firebase - تطبيق مرسال

## 🎯 نظرة عامة:
تم إعداد نظام شامل لإضافة وإدارة الحسابات في Firebase مع الحفظ التلقائي والمزامنة الفورية.

## 🚀 التطبيق يعمل حالياً على:

### 🌐 الروابط المفتوحة:
- **التطبيق الرئيسي**: http://localhost:3000
- **صفحة إعداد Firebase**: http://localhost:3000/firebase-setup
- **صفحة تسجيل الدخول**: http://localhost:3000/firebase-login

## 🔧 طرق إضافة الحسابات:

### 1. **الطريقة الأولى: إعداد تلقائي شامل**

#### الخطوة 1: إعداد البيانات الأولية
```
1. افتح: http://localhost:3000/firebase-setup (مفتوح حالياً)
2. انقر "بدء إعداد قاعدة البيانات"
3. انتظر حتى يكتمل الإعداد
4. سيتم إنشاء جميع الحسابات تلقائياً
```

#### الحسابات التي سيتم إنشاؤها تلقائياً:
```
👨‍💼 azad95 (المدير الرئيسي):
- البريد: <EMAIL>
- كلمة المرور: Azad@1995
- الصلاحيات: جميع الصلاحيات + إنشاء حسابات جديدة

👑 manager (مدير النظام):
- البريد: <EMAIL>
- كلمة المرور: 123456
- الصلاحيات: جميع الصلاحيات

👨‍💼 supervisor (المتابع):
- البريد: <EMAIL>
- كلمة المرور: 123456
- الصلاحيات: إدارة الطلبات والمندوبين

🚚 courier (المندوب):
- البريد: <EMAIL>
- كلمة المرور: 123456
- الصلاحيات: تحديث حالة الطلبات المسندة
```

### 2. **الطريقة الثانية: إضافة حسابات جديدة من التطبيق**

#### الخطوة 1: تسجيل الدخول كمدير
```
1. افتح: http://localhost:3000/firebase-login
2. انقر "👨‍💼 أزاد - المدير الرئيسي"
3. أو أدخل: azad95 / Azad@1995
4. سيتم توجيهك للصفحة الرئيسية
```

#### الخطوة 2: الذهاب لصفحة إدارة المستخدمين
```
1. من الصفحة الرئيسية، انقر على "إدارة المستخدمين"
2. أو اذهب مباشرة إلى: http://localhost:3000/users-management
3. ستظهر لك قائمة المستخدمين الحاليين
```

#### الخطوة 3: إضافة مستخدم جديد
```
1. انقر "إضافة مستخدم جديد"
2. املأ النموذج:
   - اسم المستخدم: (فريد، بدون مسافات)
   - البريد الإلكتروني: (صحيح وفريد)
   - الاسم الكامل: (اسم المستخدم بالكامل)
   - رقم الهاتف: (اختياري)
   - الدور: (manager/supervisor/courier)
   - كلمة المرور: (قوية، 6 أحرف على الأقل)
3. انقر "إنشاء المستخدم"
```

### 3. **الطريقة الثالثة: إضافة مباشرة في Firebase Console**

#### الخطوة 1: فتح Firebase Console
```
1. اذهب إلى: https://console.firebase.google.com
2. اختر مشروع: marsal-delivery-app
3. اذهب إلى Authentication > Users
```

#### الخطوة 2: إضافة مستخدم في Authentication
```
1. انقر "Add user"
2. أدخل البريد الإلكتروني
3. أدخل كلمة المرور
4. انقر "Add user"
```

#### الخطوة 3: إضافة بيانات المستخدم في Firestore
```
1. اذهب إلى Firestore Database
2. انقر على collection "users"
3. انقر "Add document"
4. استخدم User ID من Authentication كـ Document ID
5. أضف الحقول التالية:
   - username: (string) اسم المستخدم
   - email: (string) البريد الإلكتروني
   - name: (string) الاسم الكامل
   - phone: (string) رقم الهاتف
   - role: (string) manager/supervisor/courier
   - isActive: (boolean) true
   - createdAt: (timestamp) الآن
   - updatedAt: (timestamp) الآن
   - createdBy: (string) system أو اسم المنشئ
```

## 🔄 كيف يعمل الحفظ التلقائي:

### 1. **في Firebase Authentication:**
```javascript
// إنشاء المستخدم في Firebase Auth
const userCredential = await createUserWithEmailAndPassword(
  auth, 
  email, 
  password
);

// الحصول على User ID تلقائياً
const userId = userCredential.user.uid;
```

### 2. **في Firestore Database:**
```javascript
// حفظ بيانات المستخدم في Firestore
await setDoc(doc(db, 'users', userId), {
  username: userData.username,
  email: userData.email,
  name: userData.name,
  phone: userData.phone,
  role: userData.role,
  isActive: true,
  createdAt: serverTimestamp(), // تاريخ تلقائي
  updatedAt: serverTimestamp(), // تاريخ تلقائي
  createdBy: currentUser.username
});
```

### 3. **المزامنة الفورية:**
```javascript
// الاستماع للتحديثات الفورية
onSnapshot(collection(db, 'users'), (snapshot) => {
  const users = snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  }));
  
  // تحديث واجهة المستخدم فوراً
  updateUsersList(users);
});
```

## 📊 هيكل البيانات في Firebase:

### Authentication (المصادقة):
```
User ID: abc123def456
├── Email: <EMAIL>
├── Password: (مشفرة)
├── Email Verified: true/false
├── Created: timestamp
└── Last Sign In: timestamp
```

### Firestore Database (قاعدة البيانات):
```
Collection: users
├── Document: abc123def456 (نفس User ID من Auth)
    ├── username: "newuser"
    ├── email: "<EMAIL>"
    ├── name: "اسم المستخدم الجديد"
    ├── phone: "07701234567"
    ├── role: "courier"
    ├── isActive: true
    ├── createdAt: timestamp
    ├── updatedAt: timestamp
    ├── createdBy: "azad95"
    └── lastLogin: timestamp (يتم تحديثه عند تسجيل الدخول)
```

## 🔐 نظام الصلاحيات:

### صلاحيات إنشاء الحسابات:
```
👨‍💼 azad95: يمكنه إنشاء جميع أنواع الحسابات
👑 manager: يمكنه إنشاء supervisor و courier فقط
👨‍💼 supervisor: لا يمكنه إنشاء حسابات
🚚 courier: لا يمكنه إنشاء حسابات
```

### التحقق من الصلاحيات:
```javascript
// في صفحة إدارة المستخدمين
const isManager = currentUser?.role === 'manager';

if (!isManager) {
  return <AccessDenied />;
}
```

## 🧪 اختبار النظام:

### الخطوة 1: اختبار الإعداد التلقائي
```
1. افتح: http://localhost:3000/firebase-setup
2. انقر "بدء إعداد قاعدة البيانات"
3. انتظر رسالة "تم إعداد قاعدة البيانات بنجاح"
4. تحقق من الإحصائيات: 4 مستخدمين، 5 طلبات، 5 إشعارات
```

### الخطوة 2: اختبار تسجيل الدخول
```
1. افتح: http://localhost:3000/firebase-login
2. جرب تسجيل الدخول بحساب azad95
3. تأكد من الوصول للصفحة الرئيسية
4. تحقق من ظهور اسم المستخدم في الهيدر
```

### الخطوة 3: اختبار إضافة مستخدم جديد
```
1. سجل دخول بحساب azad95
2. اذهب إلى إدارة المستخدمين
3. أضف مستخدم جديد بالبيانات التالية:
   - اسم المستخدم: testuser
   - البريد: <EMAIL>
   - الاسم: مستخدم تجريبي
   - الهاتف: 07701234999
   - الدور: courier
   - كلمة المرور: 123456
4. تأكد من ظهور رسالة نجاح
5. تحقق من ظهور المستخدم في القائمة
```

### الخطوة 4: اختبار المزامنة
```
1. افتح التطبيق في نافذتين مختلفتين
2. في النافذة الأولى: أضف مستخدم جديد
3. في النافذة الثانية: تحقق من ظهور المستخدم فوراً
4. هذا يؤكد عمل المزامنة الفورية
```

## 🔧 استكشاف الأخطاء:

### مشاكل شائعة وحلولها:

#### 1. "البريد الإلكتروني مستخدم مسبقاً"
```
الحل:
- استخدم بريد إلكتروني مختلف
- أو احذف المستخدم القديم من Firebase Console
```

#### 2. "كلمة المرور ضعيفة"
```
الحل:
- استخدم كلمة مرور من 6 أحرف على الأقل
- يفضل استخدام أرقام وحروف
```

#### 3. "فشل في إنشاء المستخدم"
```
الحل:
- تحقق من الاتصال بالإنترنت
- تأكد من صحة إعدادات Firebase
- تحقق من قواعد الأمان في Firestore
```

#### 4. "لا يظهر المستخدم في القائمة"
```
الحل:
- انتظر قليلاً للمزامنة
- أعد تحميل الصفحة
- تحقق من Firebase Console
```

## 📱 المزامنة بين المنصات:

### كيف تعمل:
```
1. إنشاء حساب على الويب
   ↓
2. Firebase يحفظ البيانات في السحابة
   ↓
3. تطبيق الموبايل يستقبل التحديث فوراً
   ↓
4. تطبيق سطح المكتب يستقبل التحديث فوراً
   ↓
5. جميع المنصات متزامنة
```

### الفوائد:
- ✅ **حساب واحد** يعمل على جميع المنصات
- ✅ **تحديثات فورية** عند تغيير البيانات
- ✅ **نسخ احتياطي تلقائي** في السحابة
- ✅ **أمان متقدم** مع Firebase

## 🎯 النتيجة النهائية:

**✅ نظام إضافة الحسابات يعمل بالكامل!**

الآن لديك:
- 🔧 **إعداد تلقائي** للحسابات الافتراضية
- 👥 **إضافة حسابات جديدة** من التطبيق
- 🔄 **حفظ تلقائي** في Firebase
- 📱 **مزامنة فورية** بين جميع الأجهزة
- 🔐 **نظام صلاحيات** متدرج
- 📊 **إحصائيات مباشرة** للمستخدمين

**🚀 التطبيق جاهز للاستخدام مع Firebase!**

---

**📅 تاريخ الإعداد**: 7 يوليو 2025  
**🌐 التطبيق**: http://localhost:3000 (يعمل حالياً)  
**🔧 إعداد Firebase**: http://localhost:3000/firebase-setup  
**👨‍💼 حساب المدير**: azad95 / Azad@1995  
**👥 إدارة المستخدمين**: http://localhost:3000/users-management
