// Real-time updates service using Firebase Firestore
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  onSnapshot, 
  doc,
  addDoc,
  serverTimestamp,
  Unsubscribe
} from 'firebase/firestore';
import { db } from './firebase';
import { Order, User } from '@/types';

export interface RealtimeSubscription {
  unsubscribe: Unsubscribe;
  id: string;
}

export interface NotificationData {
  title: string;
  message: string;
  type: 'order_created' | 'order_updated' | 'order_assigned' | 'status_changed' | 'general';
  userId: string;
  orderId?: string;
  data?: any;
}

class RealtimeService {
  private subscriptions: Map<string, Unsubscribe> = new Map();

  // Listen to all orders changes
  subscribeToOrders(callback: (orders: Order[]) => void): RealtimeSubscription {
    const subscriptionId = `orders_${Date.now()}`;
    
    const ordersRef = collection(db, 'orders');
    const q = query(ordersRef, orderBy('createdAt', 'desc'));
    
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const orders = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          trackingNumber: data.trackingNumber,
          customerName: data.customerName,
          customerPhone: data.customerPhone,
          address: data.address,
          amount: data.amount,
          status: data.status,
          courierName: data.courierName || '',
          assignedTo: data.assignedTo || '',
          notes: data.notes || '',
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          deliveredAt: data.deliveredAt?.toDate(),
          statusHistory: data.statusHistory || []
        } as Order;
      });
      
      console.log(`🔄 تحديث فوري: ${orders.length} طلب`);
      callback(orders);
    }, (error) => {
      console.error('❌ خطأ في الاستماع للطلبات:', error);
    });

    this.subscriptions.set(subscriptionId, unsubscribe);
    
    return {
      unsubscribe,
      id: subscriptionId
    };
  },

  // Listen to orders for specific courier
  subscribeToCourierOrders(courierId: string, callback: (orders: Order[]) => void): RealtimeSubscription {
    const subscriptionId = `courier_orders_${courierId}_${Date.now()}`;
    
    const ordersRef = collection(db, 'orders');
    const q = query(
      ordersRef, 
      where('assignedTo', '==', courierId),
      orderBy('createdAt', 'desc')
    );
    
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const orders = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          trackingNumber: data.trackingNumber,
          customerName: data.customerName,
          customerPhone: data.customerPhone,
          address: data.address,
          amount: data.amount,
          status: data.status,
          courierName: data.courierName || '',
          assignedTo: data.assignedTo || '',
          notes: data.notes || '',
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          deliveredAt: data.deliveredAt?.toDate(),
          statusHistory: data.statusHistory || []
        } as Order;
      });
      
      console.log(`🔄 تحديث فوري للمندوب ${courierId}: ${orders.length} طلب`);
      callback(orders);
    }, (error) => {
      console.error('❌ خطأ في الاستماع لطلبات المندوب:', error);
    });

    this.subscriptions.set(subscriptionId, unsubscribe);
    
    return {
      unsubscribe,
      id: subscriptionId
    };
  },

  // Listen to specific order changes
  subscribeToOrder(orderId: string, callback: (order: Order | null) => void): RealtimeSubscription {
    const subscriptionId = `order_${orderId}_${Date.now()}`;
    
    const orderRef = doc(db, 'orders', orderId);
    
    const unsubscribe = onSnapshot(orderRef, (doc) => {
      if (doc.exists()) {
        const data = doc.data();
        const order = {
          id: doc.id,
          trackingNumber: data.trackingNumber,
          customerName: data.customerName,
          customerPhone: data.customerPhone,
          address: data.address,
          amount: data.amount,
          status: data.status,
          courierName: data.courierName || '',
          assignedTo: data.assignedTo || '',
          notes: data.notes || '',
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          deliveredAt: data.deliveredAt?.toDate(),
          statusHistory: data.statusHistory || []
        } as Order;
        
        console.log(`🔄 تحديث فوري للطلب ${orderId}`);
        callback(order);
      } else {
        callback(null);
      }
    }, (error) => {
      console.error('❌ خطأ في الاستماع للطلب:', error);
    });

    this.subscriptions.set(subscriptionId, unsubscribe);
    
    return {
      unsubscribe,
      id: subscriptionId
    };
  },

  // Listen to user notifications
  subscribeToNotifications(userId: string, callback: (notifications: any[]) => void): RealtimeSubscription {
    const subscriptionId = `notifications_${userId}_${Date.now()}`;
    
    const notificationsRef = collection(db, 'notifications');
    const q = query(
      notificationsRef,
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );
    
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const notifications = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      }));
      
      console.log(`🔔 تحديث فوري للإشعارات: ${notifications.length} إشعار`);
      callback(notifications);
    }, (error) => {
      console.error('❌ خطأ في الاستماع للإشعارات:', error);
    });

    this.subscriptions.set(subscriptionId, unsubscribe);
    
    return {
      unsubscribe,
      id: subscriptionId
    };
  },

  // Send notification to user
  async sendNotification(notification: NotificationData): Promise<void> {
    try {
      const notificationsRef = collection(db, 'notifications');
      await addDoc(notificationsRef, {
        ...notification,
        isRead: false,
        createdAt: serverTimestamp()
      });
      
      console.log(`📤 تم إرسال إشعار إلى المستخدم ${notification.userId}`);
    } catch (error) {
      console.error('❌ خطأ في إرسال الإشعار:', error);
      throw error;
    }
  },

  // Send notification to multiple users
  async sendNotificationToUsers(userIds: string[], notification: Omit<NotificationData, 'userId'>): Promise<void> {
    try {
      const promises = userIds.map(userId => 
        this.sendNotification({ ...notification, userId })
      );
      
      await Promise.all(promises);
      console.log(`📤 تم إرسال إشعار إلى ${userIds.length} مستخدم`);
    } catch (error) {
      console.error('❌ خطأ في إرسال الإشعارات:', error);
      throw error;
    }
  },

  // Send notification to all users with specific role
  async sendNotificationToRole(role: string, notification: Omit<NotificationData, 'userId'>): Promise<void> {
    try {
      // Get all users with the specified role
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('role', '==', role), where('isActive', '==', true));
      
      const snapshot = await getDocs(q);
      const userIds = snapshot.docs.map(doc => doc.id);
      
      if (userIds.length > 0) {
        await this.sendNotificationToUsers(userIds, notification);
      }
    } catch (error) {
      console.error('❌ خطأ في إرسال الإشعار للدور:', error);
      throw error;
    }
  },

  // Unsubscribe from specific subscription
  unsubscribe(subscriptionId: string): void {
    const unsubscribe = this.subscriptions.get(subscriptionId);
    if (unsubscribe) {
      unsubscribe();
      this.subscriptions.delete(subscriptionId);
      console.log(`🔇 تم إلغاء الاشتراك: ${subscriptionId}`);
    }
  },

  // Unsubscribe from all subscriptions
  unsubscribeAll(): void {
    this.subscriptions.forEach((unsubscribe, id) => {
      unsubscribe();
      console.log(`🔇 تم إلغاء الاشتراك: ${id}`);
    });
    this.subscriptions.clear();
    console.log('🔇 تم إلغاء جميع الاشتراكات');
  },

  // Get active subscriptions count
  getActiveSubscriptionsCount(): number {
    return this.subscriptions.size;
  }
}

// Export singleton instance
export const realtimeService = new RealtimeService();

// Auto-cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    realtimeService.unsubscribeAll();
  });
}
