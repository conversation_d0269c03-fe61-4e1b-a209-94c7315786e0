'use client';

import { useEffect, useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { WifiOff, Wifi } from 'lucide-react';

export default function NetworkMonitor() {
  const [isOnline, setIsOnline] = useState(true);
  const [showOfflineAlert, setShowOfflineAlert] = useState(false);

  useEffect(() => {
    // Check initial status
    setIsOnline(navigator.onLine);
    setShowOfflineAlert(!navigator.onLine);

    // Listen for online/offline events
    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineAlert(false);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineAlert(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (!showOfflineAlert) {
    return null;
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      <Alert variant="destructive" className="rounded-none border-0 bg-red-600 text-white">
        <WifiOff className="h-4 w-4" />
        <AlertDescription className="font-medium">
          ⚠️ لا يوجد اتصال بالإنترنت - التطبيق يتطلب اتصال للعمل مع قاعدة البيانات السحابية Firebase
        </AlertDescription>
      </Alert>
    </div>
  );
}
