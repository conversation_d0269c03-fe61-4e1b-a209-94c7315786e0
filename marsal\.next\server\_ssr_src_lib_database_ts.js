"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_database_ts";
exports.ids = ["_ssr_src_lib_database_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   OrderService: () => (/* binding */ OrderService),\n/* harmony export */   UserService: () => (/* binding */ UserService),\n/* harmony export */   localDB: () => (/* binding */ localDB),\n/* harmony export */   notificationService: () => (/* binding */ notificationService),\n/* harmony export */   orderService: () => (/* binding */ orderService),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n// Local Database Service using IndexedDB\n// خدمة قاعدة البيانات المحلية باستخدام IndexedDB\nclass LocalDatabase {\n    async init() {\n        return new Promise((resolve, reject)=>{\n            const request = indexedDB.open(this.dbName, this.version);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>{\n                this.db = request.result;\n                resolve();\n            };\n            request.onupgradeneeded = (event)=>{\n                const db = event.target.result;\n                // Create object stores\n                if (!db.objectStoreNames.contains('users')) {\n                    const userStore = db.createObjectStore('users', {\n                        keyPath: 'id'\n                    });\n                    userStore.createIndex('username', 'username', {\n                        unique: true\n                    });\n                    userStore.createIndex('role', 'role', {\n                        unique: false\n                    });\n                }\n                if (!db.objectStoreNames.contains('orders')) {\n                    const orderStore = db.createObjectStore('orders', {\n                        keyPath: 'id'\n                    });\n                    orderStore.createIndex('trackingNumber', 'trackingNumber', {\n                        unique: true\n                    });\n                    orderStore.createIndex('status', 'status', {\n                        unique: false\n                    });\n                    orderStore.createIndex('courierId', 'courierId', {\n                        unique: false\n                    });\n                    orderStore.createIndex('createdAt', 'createdAt', {\n                        unique: false\n                    });\n                }\n                if (!db.objectStoreNames.contains('settings')) {\n                    db.createObjectStore('settings', {\n                        keyPath: 'id'\n                    });\n                }\n                if (!db.objectStoreNames.contains('notifications')) {\n                    const notificationStore = db.createObjectStore('notifications', {\n                        keyPath: 'id'\n                    });\n                    notificationStore.createIndex('userId', 'userId', {\n                        unique: false\n                    });\n                    notificationStore.createIndex('read', 'read', {\n                        unique: false\n                    });\n                }\n                if (!db.objectStoreNames.contains('statistics')) {\n                    const statStore = db.createObjectStore('statistics', {\n                        keyPath: 'id'\n                    });\n                    statStore.createIndex('type', 'type', {\n                        unique: false\n                    });\n                    statStore.createIndex('date', 'date', {\n                        unique: false\n                    });\n                }\n            };\n        });\n    }\n    // Generic CRUD operations\n    async add(storeName, data) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readwrite');\n            const store = transaction.objectStore(storeName);\n            const request = store.add(data);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve();\n        });\n    }\n    async get(storeName, id) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readonly');\n            const store = transaction.objectStore(storeName);\n            const request = store.get(id);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve(request.result || null);\n        });\n    }\n    async getAll(storeName) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readonly');\n            const store = transaction.objectStore(storeName);\n            const request = store.getAll();\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve(request.result || []);\n        });\n    }\n    async update(storeName, data) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readwrite');\n            const store = transaction.objectStore(storeName);\n            const request = store.put(data);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve();\n        });\n    }\n    async delete(storeName, id) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readwrite');\n            const store = transaction.objectStore(storeName);\n            const request = store.delete(id);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve();\n        });\n    }\n    // Query by index\n    async getByIndex(storeName, indexName, value) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readonly');\n            const store = transaction.objectStore(storeName);\n            const index = store.index(indexName);\n            const request = index.getAll(value);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve(request.result || []);\n        });\n    }\n    // Clear all data\n    async clear(storeName) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readwrite');\n            const store = transaction.objectStore(storeName);\n            const request = store.clear();\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve();\n        });\n    }\n    // Count records\n    async count(storeName) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readonly');\n            const store = transaction.objectStore(storeName);\n            const request = store.count();\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve(request.result);\n        });\n    }\n    constructor(){\n        this.dbName = 'MarsalDB';\n        this.version = 1;\n        this.db = null;\n    }\n}\n// Create singleton instance\nconst localDB = new LocalDatabase();\n// Initialize database on module load\nif (false) {}\n// Specialized services for each data type\nclass UserService {\n    async createUser(userData) {\n        const user = {\n            ...userData,\n            id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            createdAt: new Date()\n        };\n        await localDB.add('users', user);\n        return user;\n    }\n    async getUserByUsername(username) {\n        const users = await localDB.getByIndex('users', 'username', username);\n        return users[0] || null;\n    }\n    async getAllUsers() {\n        return localDB.getAll('users');\n    }\n    async updateUser(id, updates) {\n        const user = await localDB.get('users', id);\n        if (!user) throw new Error('User not found');\n        const updatedUser = {\n            ...user,\n            ...updates\n        };\n        await localDB.update('users', updatedUser);\n    }\n    async deleteUser(id) {\n        await localDB.delete('users', id);\n    }\n    async getUsersByRole(role) {\n        return localDB.getByIndex('users', 'role', role);\n    }\n}\nclass OrderService {\n    async createOrder(orderData) {\n        const order = {\n            ...orderData,\n            id: `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        await localDB.add('orders', order);\n        return order;\n    }\n    async getOrderByTrackingNumber(trackingNumber) {\n        const orders = await localDB.getByIndex('orders', 'trackingNumber', trackingNumber);\n        return orders[0] || null;\n    }\n    async getAllOrders() {\n        return localDB.getAll('orders');\n    }\n    async getOrdersByStatus(status) {\n        return localDB.getByIndex('orders', 'status', status);\n    }\n    async getOrdersByCourier(courierId) {\n        return localDB.getByIndex('orders', 'courierId', courierId);\n    }\n    async updateOrder(id, updates) {\n        const order = await localDB.get('orders', id);\n        if (!order) throw new Error('Order not found');\n        const updatedOrder = {\n            ...order,\n            ...updates,\n            updatedAt: new Date()\n        };\n        await localDB.update('orders', updatedOrder);\n    }\n    async deleteOrder(id) {\n        await localDB.delete('orders', id);\n    }\n    async searchOrders(query) {\n        const allOrders = await this.getAllOrders();\n        return allOrders.filter((order)=>order.trackingNumber.includes(query) || order.customerName.includes(query) || order.customerPhone.includes(query) || order.address.includes(query));\n    }\n}\nclass NotificationService {\n    async createNotification(notificationData) {\n        const notification = {\n            ...notificationData,\n            id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            createdAt: new Date()\n        };\n        await localDB.add('notifications', notification);\n        return notification;\n    }\n    async getNotificationsByUser(userId) {\n        return localDB.getByIndex('notifications', 'userId', userId);\n    }\n    async getAllNotifications() {\n        return localDB.getAll('notifications');\n    }\n    async markAsRead(id) {\n        const notification = await localDB.get('notifications', id);\n        if (!notification) throw new Error('Notification not found');\n        notification.read = true;\n        await localDB.update('notifications', notification);\n    }\n    async deleteNotification(id) {\n        await localDB.delete('notifications', id);\n    }\n}\n// Create service instances\nconst userService = new UserService();\nconst orderService = new OrderService();\nconst notificationService = new NotificationService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/database.ts\n");

/***/ })

};
;