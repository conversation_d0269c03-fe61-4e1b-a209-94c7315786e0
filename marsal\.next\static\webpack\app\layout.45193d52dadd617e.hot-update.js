"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fd8f7ee3c53a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJFOlxcTWFyc2FsXFxtYXJzYWxcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZkOGY3ZWUzYzUzYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/firebase-auth.ts":
/*!**********************************!*\
  !*** ./src/lib/firebase-auth.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   firebaseAuthService: () => (/* binding */ firebaseAuthService)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n// Firebase Authentication service for Marsal Delivery App\n\n\n\n// Default users for initial setup\nconst defaultUsers = [\n    {\n        username: 'azad95',\n        email: '<EMAIL>',\n        name: 'أزاد - مدير النظام الرئيسي',\n        phone: '07801234567',\n        role: 'manager',\n        password: 'Azad@1995'\n    },\n    {\n        username: 'manager',\n        email: '<EMAIL>',\n        name: 'مدير النظام',\n        phone: '07801234568',\n        role: 'manager',\n        password: '123456'\n    },\n    {\n        username: 'supervisor',\n        email: '<EMAIL>',\n        name: 'المشرف العام',\n        phone: '07801234569',\n        role: 'supervisor',\n        password: '123456'\n    },\n    {\n        username: 'courier',\n        email: '<EMAIL>',\n        name: 'مندوب التوصيل',\n        phone: '07801234570',\n        role: 'courier',\n        password: '123456'\n    }\n];\nconst firebaseAuthService = {\n    // Initialize default users (run once) - Local storage fallback\n    async initializeDefaultUsers () {\n        try {\n            console.log('🔧 إعداد المستخدمين الافتراضيين...');\n            // Check if Firebase is available\n            if (!_firebase__WEBPACK_IMPORTED_MODULE_1__.auth || !_firebase__WEBPACK_IMPORTED_MODULE_1__.db) {\n                console.log('⚠️ Firebase غير متاح، استخدام التخزين المحلي');\n                this.initializeLocalUsers();\n                return;\n            }\n            for (const userData of defaultUsers){\n                // Check if user already exists\n                const existingUser = await this.getUserByUsername(userData.username);\n                if (existingUser) {\n                    console.log(\"✅ المستخدم \".concat(userData.username, \" موجود مسبقاً\"));\n                    continue;\n                }\n                // Create Firebase Auth user\n                try {\n                    const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, userData.email, userData.password);\n                    // Create user document in Firestore\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n                        username: userData.username,\n                        email: userData.email,\n                        name: userData.name,\n                        phone: userData.phone,\n                        role: userData.role,\n                        isActive: true,\n                        createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                        updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                        createdBy: 'system'\n                    });\n                    console.log(\"✅ تم إنشاء المستخدم: \".concat(userData.username));\n                } catch (error) {\n                    if (error.code === 'auth/email-already-in-use') {\n                        console.log(\"⚠️ البريد الإلكتروني \".concat(userData.email, \" مستخدم مسبقاً\"));\n                    } else {\n                        console.error(\"❌ خطأ في إنشاء المستخدم \".concat(userData.username, \":\"), error);\n                        // Fallback to local storage\n                        this.initializeLocalUsers();\n                        return;\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ خطأ في إعداد المستخدمين الافتراضيين:', error);\n            // Fallback to local storage\n            this.initializeLocalUsers();\n        }\n    },\n    // Initialize users in local storage as fallback\n    initializeLocalUsers () {\n        try {\n            const existingUsers = localStorage.getItem('marsal_users');\n            if (existingUsers) {\n                console.log('✅ المستخدمين موجودين في التخزين المحلي');\n                return;\n            }\n            const localUsers = defaultUsers.map((userData, index)=>({\n                    id: \"local_\".concat(index + 1),\n                    username: userData.username,\n                    email: userData.email,\n                    name: userData.name,\n                    phone: userData.phone,\n                    role: userData.role,\n                    isActive: true,\n                    createdAt: new Date(),\n                    updatedAt: new Date(),\n                    createdBy: 'system',\n                    password: userData.password // Store password for local auth\n                }));\n            localStorage.setItem('marsal_users', JSON.stringify(localUsers));\n            console.log('✅ تم إنشاء المستخدمين في التخزين المحلي');\n        } catch (error) {\n            console.error('❌ خطأ في إنشاء المستخدمين المحليين:', error);\n        }\n    },\n    // Login with username/password\n    async login (username, password) {\n        try {\n            console.log('🔐 محاولة تسجيل الدخول للمستخدم:', username);\n            // Get user by username to find email\n            const user = await this.getUserByUsername(username);\n            if (!user) {\n                return {\n                    success: false,\n                    error: 'اسم المستخدم غير موجود'\n                };\n            }\n            if (!user.isActive) {\n                return {\n                    success: false,\n                    error: 'الحساب غير مفعل'\n                };\n            }\n            // Sign in with email and password\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, user.email, password);\n            // Update last login\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n                lastLogin: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)()\n            }, {\n                merge: true\n            });\n            console.log('✅ تم تسجيل الدخول بنجاح');\n            return {\n                success: true,\n                user: {\n                    ...user,\n                    id: userCredential.user.uid\n                }\n            };\n        } catch (error) {\n            console.error('❌ خطأ في تسجيل الدخول:', error);\n            let errorMessage = 'خطأ في تسجيل الدخول';\n            switch(error.code){\n                case 'auth/user-not-found':\n                    errorMessage = 'المستخدم غير موجود';\n                    break;\n                case 'auth/wrong-password':\n                    errorMessage = 'كلمة المرور غير صحيحة';\n                    break;\n                case 'auth/invalid-email':\n                    errorMessage = 'البريد الإلكتروني غير صحيح';\n                    break;\n                case 'auth/user-disabled':\n                    errorMessage = 'الحساب معطل';\n                    break;\n                case 'auth/too-many-requests':\n                    errorMessage = 'محاولات كثيرة، يرجى المحاولة لاحقاً';\n                    break;\n                default:\n                    errorMessage = error.message || 'خطأ غير معروف';\n            }\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    },\n    // Get user by username\n    async getUserByUsername (username) {\n        try {\n            var _data_createdAt, _data_updatedAt, _data_lastLogin;\n            const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users');\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('username', '==', username));\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (snapshot.empty) {\n                return null;\n            }\n            const doc = snapshot.docs[0];\n            const data = doc.data();\n            return {\n                id: doc.id,\n                username: data.username,\n                email: data.email,\n                name: data.name,\n                phone: data.phone,\n                role: data.role,\n                isActive: data.isActive,\n                createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date(),\n                updatedAt: ((_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()) || new Date(),\n                createdBy: data.createdBy,\n                lastLogin: (_data_lastLogin = data.lastLogin) === null || _data_lastLogin === void 0 ? void 0 : _data_lastLogin.toDate()\n            };\n        } catch (error) {\n            console.error('Error getting user by username:', error);\n            return null;\n        }\n    },\n    // Get current user\n    async getCurrentUser () {\n        try {\n            var _data_createdAt, _data_updatedAt, _data_lastLogin;\n            const firebaseUser = _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n            if (!firebaseUser) {\n                return null;\n            }\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', firebaseUser.uid));\n            if (!userDoc.exists()) {\n                return null;\n            }\n            const data = userDoc.data();\n            return {\n                id: userDoc.id,\n                username: data.username,\n                email: data.email,\n                name: data.name,\n                phone: data.phone,\n                role: data.role,\n                isActive: data.isActive,\n                createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date(),\n                updatedAt: ((_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()) || new Date(),\n                createdBy: data.createdBy,\n                lastLogin: (_data_lastLogin = data.lastLogin) === null || _data_lastLogin === void 0 ? void 0 : _data_lastLogin.toDate()\n            };\n        } catch (error) {\n            console.error('Error getting current user:', error);\n            return null;\n        }\n    },\n    // Logout\n    async logout () {\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth);\n            console.log('✅ تم تسجيل الخروج بنجاح');\n        } catch (error) {\n            console.error('❌ خطأ في تسجيل الخروج:', error);\n            throw error;\n        }\n    },\n    // Listen to auth state changes\n    onAuthStateChanged (callback) {\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, async (firebaseUser)=>{\n            if (firebaseUser) {\n                const user = await this.getCurrentUser();\n                callback(user);\n            } else {\n                callback(null);\n            }\n        });\n    },\n    // Create new user (only for managers)\n    async createUser (userData) {\n        try {\n            console.log('👤 إنشاء مستخدم جديد:', userData.username);\n            // Create Firebase Auth user\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, userData.email, userData.password);\n            // Create user document in Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n                username: userData.username,\n                email: userData.email,\n                name: userData.name,\n                phone: userData.phone,\n                role: userData.role,\n                isActive: true,\n                createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                createdBy: userData.createdBy\n            });\n            console.log('✅ تم إنشاء المستخدم بنجاح:', userData.username);\n            const newUser = {\n                id: userCredential.user.uid,\n                username: userData.username,\n                email: userData.email,\n                name: userData.name,\n                phone: userData.phone,\n                role: userData.role,\n                isActive: true,\n                createdAt: new Date(),\n                updatedAt: new Date(),\n                createdBy: userData.createdBy\n            };\n            return {\n                success: true,\n                user: newUser\n            };\n        } catch (error) {\n            console.error('❌ خطأ في إنشاء المستخدم:', error);\n            let errorMessage = 'خطأ في إنشاء المستخدم';\n            switch(error.code){\n                case 'auth/email-already-in-use':\n                    errorMessage = 'البريد الإلكتروني مستخدم مسبقاً';\n                    break;\n                case 'auth/weak-password':\n                    errorMessage = 'كلمة المرور ضعيفة';\n                    break;\n                case 'auth/invalid-email':\n                    errorMessage = 'البريد الإلكتروني غير صحيح';\n                    break;\n                default:\n                    errorMessage = error.message || 'خطأ غير معروف';\n            }\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    },\n    // Get all users (for managers)\n    async getAllUsers () {\n        try {\n            const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users');\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(usersRef);\n            return snapshot.docs.map((doc)=>{\n                var _data_createdAt, _data_updatedAt, _data_lastLogin;\n                const data = doc.data();\n                return {\n                    id: doc.id,\n                    username: data.username,\n                    email: data.email,\n                    name: data.name,\n                    phone: data.phone,\n                    role: data.role,\n                    isActive: data.isActive,\n                    createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date(),\n                    updatedAt: ((_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()) || new Date(),\n                    createdBy: data.createdBy,\n                    lastLogin: (_data_lastLogin = data.lastLogin) === null || _data_lastLogin === void 0 ? void 0 : _data_lastLogin.toDate()\n                };\n            });\n        } catch (error) {\n            console.error('Error getting all users:', error);\n            return [];\n        }\n    },\n    // Update user (for managers)\n    async updateUser (userId, updates) {\n        try {\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId), {\n                ...updates,\n                updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)()\n            }, {\n                merge: true\n            });\n            console.log('✅ تم تحديث المستخدم:', userId);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('❌ خطأ في تحديث المستخدم:', error);\n            return {\n                success: false,\n                error: error.message || 'خطأ في تحديث المستخدم'\n            };\n        }\n    },\n    // Delete user (for managers)\n    async deleteUser (userId) {\n        try {\n            // Delete from Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId));\n            // Note: Deleting from Firebase Auth requires Admin SDK\n            // For now, we just deactivate the user\n            await this.updateUser(userId, {\n                isActive: false\n            });\n            console.log('✅ تم حذف المستخدم:', userId);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('❌ خطأ في حذف المستخدم:', error);\n            return {\n                success: false,\n                error: error.message || 'خطأ في حذف المستخدم'\n            };\n        }\n    },\n    // Check if Firebase is connected\n    async checkConnection () {\n        try {\n            // Try to get current user\n            const user = _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n            console.log('✅ Firebase Auth متصل');\n            return {\n                connected: true,\n                message: 'Firebase Auth متصل بنجاح'\n            };\n        } catch (error) {\n            console.error('❌ Firebase Auth غير متصل:', error);\n            return {\n                connected: false,\n                message: \"خطأ في الاتصال: \".concat(error.message)\n            };\n        }\n    }\n};\n// Auto-initialize default users when the module loads\nfirebaseAuthService.initializeDefaultUsers().catch(console.error);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/firebase-auth.ts\n"));

/***/ })

});