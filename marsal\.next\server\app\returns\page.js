(()=>{var e={};e.id=762,e.ids=[762],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,r,t)=>{"use strict";t.d(r,{Yq:()=>o,cn:()=>i,ps:()=>p,qY:()=>u,r6:()=>l,vv:()=>n,y7:()=>d,zC:()=>c});var s=t(49384),a=t(82348);function i(...e){return(0,a.QP)((0,s.$)(e))}function n(e){return`${e.toLocaleString("ar-IQ")} د.ع`}function o(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function l(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function d(){let e=Date.now().toString().slice(-6),r=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return`MRS${e}${r}`}function c(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function u(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function p(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}},5336:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7108:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(60687),a=t(67958),i=t(10957),n=t(16189);t(43210);var o=t(44493),l=t(29523),d=t(43649),c=t(70334);function u({children:e,requiredSection:r,fallbackPath:t="/"}){let{user:u,isAuthenticated:p}=(0,a.A)(),x=(0,n.useRouter)();return p&&u?(0,i._m)(u.role,r)?(0,s.jsx)(s.Fragment,{children:e}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center p-4",dir:"rtl",children:(0,s.jsxs)(o.Zp,{className:"max-w-md w-full shadow-xl border-2 border-red-200",children:[(0,s.jsxs)(o.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-full shadow-lg mb-4 mx-auto",children:(0,s.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,s.jsx)(o.ZB,{className:"text-2xl text-red-600",children:"غير مصرح بالدخول"}),(0,s.jsx)(o.BT,{className:"text-lg text-red-500",children:"ليس لديك صلاحية للوصول إلى هذا القسم"})]}),(0,s.jsxs)(o.Wu,{className:"text-center space-y-4",children:[(0,s.jsxs)("p",{className:"text-gray-600 leading-relaxed",children:["عذراً، دورك الحالي (","manager"===u.role?"مدير":"supervisor"===u.role?"متابع":"مندوب",") لا يسمح بالوصول إلى هذا القسم."]}),(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-sm text-yellow-800",children:"إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع المدير لمراجعة صلاحياتك."})}),(0,s.jsxs)(l.$,{onClick:()=>x.push(t),className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:["العودة إلى الصفحة الرئيسية",(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"})]})]})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse",children:(0,s.jsx)("div",{className:"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"مرسال"}),(0,s.jsx)("p",{className:"text-gray-500",children:"جاري التحقق من الصلاحيات..."})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10957:(e,r,t)=>{"use strict";t.d(r,{BC:()=>n,_m:()=>a});let s={manager:["orders","dispatch","returns","accounting","statistics","archive","users","import-export","notifications","settings"],supervisor:["orders","statistics","archive","users","notifications","settings"],courier:["orders","archive","notifications","statistics"]};function a(e,r){return s[e]?.includes(r)||!1}let i=[{id:"orders",href:"/orders",label:"إدارة الطلبات",description:"عرض ومتابعة وتعديل الطلبات",icon:"Package",color:"from-blue-500 to-blue-600",roles:["manager","supervisor","courier"]},{id:"dispatch",href:"/dispatch",label:"إسناد الطلبات",description:"توزيع الطلبات على المندوبين",icon:"TruckIcon",color:"from-green-500 to-emerald-600",roles:["manager"]},{id:"returns",href:"/returns",label:"إدارة الرواجع",description:"استلام الطلبات الراجعة",icon:"RotateCcw",color:"from-orange-500 to-amber-600",roles:["manager"]},{id:"accounting",href:"/accounting",label:"المحاسبة",description:"التسويات المالية مع المندوبين",icon:"Calculator",color:"from-purple-500 to-violet-600",roles:["manager"]},{id:"statistics",href:"/statistics",label:"الإحصائيات",description:"التقارير والإحصائيات التفصيلية",icon:"BarChart3",color:"from-cyan-500 to-blue-600",roles:["manager","supervisor"]},{id:"archive",href:"/archive",label:"الأرشيف",description:"السجلات المنتهية",icon:"Archive",color:"from-gray-500 to-slate-600",roles:["manager","supervisor","courier"]},{id:"users",href:"/users",label:"إدارة الموظفين",description:"إدارة حسابات المستخدمين",icon:"Users",color:"from-indigo-500 to-purple-600",roles:["manager","supervisor"]},{id:"import-export",href:"/import-export",label:"استيراد وتصدير",description:"عمليات مجمعة على الطلبات",icon:"Upload",color:"from-teal-500 to-cyan-600",roles:["manager"]},{id:"notifications",href:"/notifications",label:"الإشعارات",description:"التنبيهات والتحديثات",icon:"Bell",color:"from-yellow-500 to-orange-600",roles:["manager","supervisor","courier"]},{id:"settings",href:"/settings",label:"الإعدادات",description:"تخصيص التطبيق",icon:"Settings",color:"from-pink-500 to-rose-600",roles:["manager","supervisor","courier"]}];function n(e){return i.filter(r=>r.roles.includes(e))}},11997:e=>{"use strict";e.exports=require("punycode")},13943:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},19080:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(60687);t(43210);var a=t(8730),i=t(24224),n=t(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:i=!1,...l}){let d=i?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:t,className:e})),...l})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},38774:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var s=t(60687),a=t(43210),i=t(44493),n=t(29523),o=t(89667),l=t(28559),d=t(13943),c=t(77026),u=t(88059),p=t(58869),x=t(99270),m=t(19080),h=t(5336),g=t(85814),f=t.n(g),v=t(79396),b=t(4780),j=t(7108);function y(){let[e,r]=(0,a.useState)("receive"),[t,g]=(0,a.useState)([]),[y,N]=(0,a.useState)(""),[w,k]=(0,a.useState)([]),[A,q]=(0,a.useState)([]),[M,_]=(0,a.useState)(!1),[C,P]=(0,a.useState)(""),S=async()=>{if(y){_(!0);try{let e=(await v.pv.getByCourier(y)).filter(e=>"returned"===e.status);k(e)}catch(e){console.error("Error loading returned orders:",e)}finally{_(!1)}}},z=e=>{q(r=>r.includes(e)?r.filter(r=>r!==e):[...r,e])},$=async()=>{if(0!==A.length){_(!0);try{for(let e of A)await v.pv.updateStatus(e,"cancelled","تم استلام الطلب في المخزن وأرشفته","system");alert(`تم استلام ${A.length} طلب بنجاح وأرشفتها`),q([]),S()}catch(e){console.error("Error receiving orders:",e),alert("حدث خطأ أثناء استلام الطلبات")}finally{_(!1)}}},B=w.filter(e=>e.trackingNumber.toLowerCase().includes(C.toLowerCase())||e.recipientName.toLowerCase().includes(C.toLowerCase())||e.recipientPhone.includes(C));return(0,s.jsx)(j.A,{requiredSection:"returns",children:(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 animated-bg p-6",dir:"rtl",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,s.jsx)("div",{className:"flex items-center gap-4 mb-6",children:(0,s.jsx)(f(),{href:"/",children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex items-center gap-2 glass",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),"العودة للرئيسية"]})})}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-3xl shadow-2xl mb-4",children:(0,s.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,s.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-2",children:"إدارة الرواجع"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-lg",children:"استلام وتسليم الطلبات المرتجعة"})]}),(0,s.jsxs)("div",{className:"flex space-x-1 bg-muted p-1 rounded-lg w-fit",children:[(0,s.jsx)("button",{onClick:()=>r("receive"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"receive"===e?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"}`,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),"استلام الرواجع من المندوبين"]})}),(0,s.jsx)("button",{onClick:()=>r("deliver"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"deliver"===e?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"}`,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),"تسليم الرواجع للعملاء"]})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-5 w-5"}),"اختيار المندوب"]}),(0,s.jsx)(i.BT,{children:"اختر المندوب لعرض طلباته الراجعة"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("select",{value:y,onChange:e=>N(e.target.value),className:"w-full p-3 border border-border rounded-md bg-background",children:[(0,s.jsx)("option",{value:"",children:"اختر المندوب"}),t.map(e=>(0,s.jsxs)("option",{value:e.id,children:[e.name," - ",e.phone]},e.id))]})})]}),"receive"===e?(0,s.jsx)(s.Fragment,{children:y&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.Zp,{children:(0,s.jsx)(i.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(o.p,{placeholder:"البحث برقم الوصل أو اسم المستلم أو الهاتف...",value:C,onChange:e=>P(e.target.value),className:"flex-1"}),(0,s.jsx)(n.$,{variant:"outline",children:(0,s.jsx)(x.A,{className:"h-4 w-4"})})]})})}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"h-5 w-5"}),"الطلبات الراجعة (",B.length,")"]}),(0,s.jsx)(i.BT,{children:"الطلبات الراجعة من المندوب المختار"})]}),(0,s.jsx)(i.Wu,{children:M?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,s.jsx)("p",{className:"mt-2 text-muted-foreground",children:"جاري التحميل..."})]}):0===B.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)(m.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,s.jsx)("p",{children:"لا توجد طلبات راجعة للمندوب المختار"})]}):(0,s.jsx)("div",{className:"space-y-4",children:B.map(e=>(0,s.jsx)("div",{className:`p-4 border rounded-lg cursor-pointer transition-colors ${A.includes(e.id)?"border-primary bg-primary/5":"border-border hover:border-primary/50"}`,onClick:()=>z(e.id),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("span",{className:"font-semibold",children:e.trackingNumber}),A.includes(e.id)&&(0,s.jsx)(h.A,{className:"h-4 w-4 text-primary"}),(0,s.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${(0,b.qY)(e.status)}`,children:(0,b.ps)(e.status)})]}),(0,s.jsx)("p",{className:"text-sm font-medium",children:e.recipientName}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.recipientPhone}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.recipientAddress}),e.returnedAt&&(0,s.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["تاريخ الإرجاع: ",(0,b.Yq)(e.returnedAt)]})]}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("p",{className:"font-semibold",children:(0,b.vv)(e.amount)}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:(0,b.Yq)(e.createdAt)})]})]})},e.id))})})]}),A.length>0&&(0,s.jsx)(i.Zp,{className:"border-primary",children:(0,s.jsx)(i.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"font-medium",children:["استلام ",A.length," طلب راجع"]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"سيتم نقل الطلبات إلى الأرشيف بعد الاستلام"})]}),(0,s.jsxs)(n.$,{onClick:$,disabled:M,className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),"تأكيد الاستلام"]})]})})})]})}):(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(u.A,{className:"h-5 w-5"}),"تسليم الرواجع للعملاء"]}),(0,s.jsx)(i.BT,{children:"إدارة تسليم الطلبات الراجعة للعملاء الأصليين"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(u.A,{className:"h-16 w-16 text-muted-foreground mx-auto mb-4 opacity-50"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"تسليم الرواجع للعملاء"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"هذا القسم مخصص لإدارة تسليم الطلبات الراجعة للعملاء الأصليين"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm text-muted-foreground",children:[(0,s.jsx)("p",{children:"• عرض الطلبات الراجعة المستلمة من المندوبين"}),(0,s.jsx)("p",{children:"• تنظيم عملية التسليم للعملاء"}),(0,s.jsx)("p",{children:"• تتبع حالة التسليم"}),(0,s.jsx)("p",{children:"• أرشفة الطلبات المسلمة"})]}),(0,s.jsx)(n.$,{className:"mt-6",disabled:!0,children:"قريباً - قيد التطوير"})]})})]})]})})})}},43649:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=t(60687);t(43210);var a=t(4780);function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},50700:(e,r,t)=>{Promise.resolve().then(t.bind(t,38774))},52879:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["returns",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94272)),"E:\\Marsal\\marsal\\src\\app\\returns\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\Marsal\\marsal\\src\\app\\returns\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/returns/page",pathname:"/returns",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70334:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},77026:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80204:(e,r,t)=>{Promise.resolve().then(t.bind(t,94272))},81630:e=>{"use strict";e.exports=require("http")},88059:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},91645:e=>{"use strict";e.exports=require("net")},94272:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\returns\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\returns\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,863,860,814,610,112],()=>t(52879));module.exports=s})();