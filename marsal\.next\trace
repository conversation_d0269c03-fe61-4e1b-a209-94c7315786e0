[{"name": "generate-buildid", "duration": 303, "timestamp": 88268095159, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751919385262, "traceId": "8ab38e4e2499fc6f"}, {"name": "load-custom-routes", "duration": 468, "timestamp": 88268095606, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751919385263, "traceId": "8ab38e4e2499fc6f"}, {"name": "create-dist-dir", "duration": 574, "timestamp": 88268182109, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751919385349, "traceId": "8ab38e4e2499fc6f"}, {"name": "create-pages-mapping", "duration": 281, "timestamp": 88268187811, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751919385355, "traceId": "8ab38e4e2499fc6f"}, {"name": "collect-app-paths", "duration": 4726, "timestamp": 88268188153, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751919385355, "traceId": "8ab38e4e2499fc6f"}, {"name": "create-app-mapping", "duration": 3748, "timestamp": 88268192927, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751919385360, "traceId": "8ab38e4e2499fc6f"}, {"name": "public-dir-conflict-check", "duration": 812, "timestamp": 88268197474, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751919385365, "traceId": "8ab38e4e2499fc6f"}, {"name": "generate-routes-manifest", "duration": 4925, "timestamp": 88268198522, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751919385366, "traceId": "8ab38e4e2499fc6f"}, {"name": "next-build", "duration": 6172889, "timestamp": 88267914111, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.3.4", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1751919385081, "traceId": "8ab38e4e2499fc6f"}]