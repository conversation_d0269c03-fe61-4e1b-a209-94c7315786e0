/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/firebase-login/page";
exports.ids = ["app/firebase-login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffirebase-login%2Fpage&page=%2Ffirebase-login%2Fpage&appPaths=%2Ffirebase-login%2Fpage&pagePath=private-next-app-dir%2Ffirebase-login%2Fpage.tsx&appDir=E%3A%5CMarsal%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMarsal%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffirebase-login%2Fpage&page=%2Ffirebase-login%2Fpage&appPaths=%2Ffirebase-login%2Fpage&pagePath=private-next-app-dir%2Ffirebase-login%2Fpage.tsx&appDir=E%3A%5CMarsal%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMarsal%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/firebase-login/page.tsx */ \"(rsc)/./src/app/firebase-login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'firebase-login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/firebase-login/page\",\n        pathname: \"/firebase-login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffirebase-login%2Fpage&page=%2Ffirebase-login%2Fpage&appPaths=%2Ffirebase-login%2Fpage&pagePath=private-next-app-dir%2Ffirebase-login%2Fpage.tsx&appDir=E%3A%5CMarsal%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMarsal%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth-provider.tsx */ \"(rsc)/./src/components/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNNYXJzYWwlNUMlNUNtYXJzYWwlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTJDJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtaW50ZXIlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDTWFyc2FsJTVDJTVDbWFyc2FsJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q01hcnNhbCU1QyU1Q21hcnNhbCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0xBQTZIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJFOlxcXFxNYXJzYWxcXFxcbWFyc2FsXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cfirebase-login%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cfirebase-login%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/firebase-login/page.tsx */ \"(rsc)/./src/app/firebase-login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNNYXJzYWwlNUMlNUNtYXJzYWwlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNmaXJlYmFzZS1sb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4S0FBMkYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXE1hcnNhbFxcXFxtYXJzYWxcXFxcc3JjXFxcXGFwcFxcXFxmaXJlYmFzZS1sb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cfirebase-login%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFxNYXJzYWxcXG1hcnNhbFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/firebase-login/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/firebase-login/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Marsal\\marsal\\src\\app\\firebase-login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a7c038f9bc65\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxcTWFyc2FsXFxtYXJzYWxcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE3YzAzOGY5YmM2NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   generateViewport: () => (/* binding */ generateViewport),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth-provider */ \"(rsc)/./src/components/auth-provider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"مرسال - نظام إدارة التوصيل\",\n    description: \"نظام إدارة عمليات شركات التوصيل السريع\",\n    manifest: \"/manifest.json\"\n};\nfunction generateViewport() {\n    return {\n        width: 'device-width',\n        initialScale: 1,\n        maximumScale: 5,\n        userScalable: true,\n        themeColor: '#3B82F6',\n        colorScheme: 'light dark',\n        viewportFit: 'cover'\n    };\n}\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased h-full w-full overflow-x-hidden`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_provider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen w-full max-w-full overflow-x-hidden\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/auth-provider.tsx":
/*!******************************************!*\
  !*** ./src/components/auth-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Marsal\\marsal\\src\\components\\auth-provider.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Marsal\\marsal\\src\\components\\auth-provider.tsx",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth-provider.tsx */ \"(ssr)/./src/components/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNNYXJzYWwlNUMlNUNtYXJzYWwlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTJDJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtaW50ZXIlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDTWFyc2FsJTVDJTVDbWFyc2FsJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q01hcnNhbCU1QyU1Q21hcnNhbCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0xBQTZIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJFOlxcXFxNYXJzYWxcXFxcbWFyc2FsXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cfirebase-login%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cfirebase-login%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/firebase-login/page.tsx */ \"(ssr)/./src/app/firebase-login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNNYXJzYWwlNUMlNUNtYXJzYWwlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNmaXJlYmFzZS1sb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4S0FBMkYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXE1hcnNhbFxcXFxtYXJzYWxcXFxcc3JjXFxcXGFwcFxcXFxmaXJlYmFzZS1sb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cfirebase-login%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/firebase-login/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/firebase-login/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FirebaseLoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,EyeOff,Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,EyeOff,Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,EyeOff,Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,EyeOff,Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,EyeOff,Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Database,Eye,EyeOff,Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _lib_firebase_auth__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/firebase-auth */ \"(ssr)/./src/lib/firebase-auth.ts\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction FirebaseLoginPage() {\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('checking');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Test Firebase connection on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FirebaseLoginPage.useEffect\": ()=>{\n            checkConnection();\n        }\n    }[\"FirebaseLoginPage.useEffect\"], []);\n    const checkConnection = async ()=>{\n        try {\n            setConnectionStatus('checking');\n            const result = await (0,_lib_firebase__WEBPACK_IMPORTED_MODULE_9__.testFirebaseConnection)();\n            setConnectionStatus(result.success ? 'connected' : 'disconnected');\n            if (!result.success) {\n                setError(`فشل الاتصال بـ Firebase: ${result.message}`);\n            }\n        } catch (error) {\n            setConnectionStatus('disconnected');\n            setError('فشل في اختبار الاتصال بـ Firebase');\n        }\n    };\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        if (connectionStatus !== 'connected') {\n            setError('يجب الاتصال بـ Firebase أولاً');\n            return;\n        }\n        if (!username.trim() || !password.trim()) {\n            setError('يرجى إدخال اسم المستخدم وكلمة المرور');\n            return;\n        }\n        setLoading(true);\n        setError('');\n        try {\n            console.log('🔐 محاولة تسجيل الدخول مع Firebase...');\n            const result = await _lib_firebase_auth__WEBPACK_IMPORTED_MODULE_8__.firebaseAuthService.login(username.trim(), password);\n            if (result.success && result.user) {\n                console.log('✅ تم تسجيل الدخول بنجاح:', result.user.name);\n                // Store user data in localStorage\n                localStorage.setItem('currentUser', JSON.stringify(result.user));\n                localStorage.setItem('isAuthenticated', 'true');\n                // Redirect to main app\n                router.push('/');\n            } else {\n                setError(result.error || 'فشل في تسجيل الدخول');\n            }\n        } catch (error) {\n            console.error('❌ خطأ في تسجيل الدخول:', error);\n            setError(error.message || 'حدث خطأ غير متوقع');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleQuickLogin = async (userType)=>{\n        const credentials = {\n            azad95: {\n                username: 'azad95',\n                password: 'Azad@1995'\n            },\n            manager: {\n                username: 'manager',\n                password: '123456'\n            },\n            supervisor: {\n                username: 'supervisor',\n                password: '123456'\n            },\n            courier: {\n                username: 'courier',\n                password: '123456'\n            }\n        };\n        setUsername(credentials[userType].username);\n        setPassword(credentials[userType].password);\n        // Auto-submit after setting values\n        setTimeout(()=>{\n            const form = document.querySelector('form');\n            if (form) {\n                form.dispatchEvent(new Event('submit', {\n                    cancelable: true,\n                    bubbles: true\n                }));\n            }\n        }, 100);\n    };\n    const getConnectionIcon = ()=>{\n        switch(connectionStatus){\n            case 'checking':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 16\n                }, this);\n            case 'connected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 16\n                }, this);\n            case 'disconnected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getConnectionText = ()=>{\n        switch(connectionStatus){\n            case 'checking':\n                return 'جاري فحص الاتصال بـ Firebase...';\n            case 'connected':\n                return 'متصل بـ Firebase بنجاح';\n            case 'disconnected':\n                return 'غير متصل بـ Firebase';\n        }\n    };\n    const getConnectionColor = ()=>{\n        switch(connectionStatus){\n            case 'checking':\n                return 'border-yellow-200 bg-yellow-50';\n            case 'connected':\n                return 'border-green-200 bg-green-50';\n            case 'disconnected':\n                return 'border-red-200 bg-red-50';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto h-12 w-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-6 w-6 text-white\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"تطبيق مرسال\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"تسجيل الدخول مع Firebase\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: `${getConnectionColor()}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    getConnectionIcon(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: getConnectionText()\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            connectionStatus === 'disconnected' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: checkConnection,\n                                className: \"mt-2 w-full\",\n                                children: \"\\uD83D\\uDD04 إعادة المحاولة\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"تسجيل الدخول\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: \"أدخل بيانات الدخول للوصول إلى التطبيق\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleLogin,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"username\",\n                                                children: \"اسم المستخدم\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"username\",\n                                                type: \"text\",\n                                                value: username,\n                                                onChange: (e)=>setUsername(e.target.value),\n                                                placeholder: \"أدخل اسم المستخدم\",\n                                                disabled: loading || connectionStatus !== 'connected',\n                                                className: \"text-right\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"password\",\n                                                children: \"كلمة المرور\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"password\",\n                                                        type: showPassword ? 'text' : 'password',\n                                                        value: password,\n                                                        onChange: (e)=>setPassword(e.target.value),\n                                                        placeholder: \"أدخل كلمة المرور\",\n                                                        disabled: loading || connectionStatus !== 'connected',\n                                                        className: \"text-right pr-10\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                        disabled: loading,\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                        variant: \"destructive\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full\",\n                                        disabled: loading || connectionStatus !== 'connected',\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_Eye_EyeOff_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"جاري تسجيل الدخول...\"\n                                            ]\n                                        }, void 0, true) : 'تسجيل الدخول'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"تسجيل دخول سريع (للاختبار)\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full justify-start bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200\",\n                                    onClick: ()=>handleQuickLogin('azad95'),\n                                    disabled: loading || connectionStatus !== 'connected',\n                                    children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC أزاد - المدير الرئيسي (azad95 / Azad@1995)\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full justify-start\",\n                                    onClick: ()=>handleQuickLogin('manager'),\n                                    disabled: loading || connectionStatus !== 'connected',\n                                    children: \"\\uD83D\\uDC51 مدير النظام (manager / 123456)\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full justify-start\",\n                                    onClick: ()=>handleQuickLogin('supervisor'),\n                                    disabled: loading || connectionStatus !== 'connected',\n                                    children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC المتابع (supervisor / 123456)\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full justify-start\",\n                                    onClick: ()=>handleQuickLogin('courier'),\n                                    disabled: loading || connectionStatus !== 'connected',\n                                    children: \"\\uD83D\\uDE9A المندوب (courier / 123456)\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"تطبيق مرسال - نظام إدارة التوصيل\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"مدعوم بـ Firebase\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/firebase-login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth-provider.tsx":
/*!******************************************!*\
  !*** ./src/components/auth-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* harmony import */ var _lib_firebase_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase-auth */ \"(ssr)/./src/lib/firebase-auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check if user is logged in on app start\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": async ()=>{\n                    try {\n                        // Demo mode auto-login - تسجيل دخول تلقائي في الوضع التجريبي\n                        if (_lib_config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.demo.enabled && _lib_config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.demo.autoLogin) {\n                            try {\n                                console.log('Starting demo auto-login...');\n                                // استخدام timeout لتجنب التعليق\n                                const loginPromise = _lib_auth__WEBPACK_IMPORTED_MODULE_3__.authService.login({\n                                    username: _lib_config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.demo.defaultUser,\n                                    password: '123456'\n                                });\n                                const timeoutPromise = new Promise({\n                                    \"AuthProvider.useEffect.checkAuth\": (_, reject)=>setTimeout({\n                                            \"AuthProvider.useEffect.checkAuth\": ()=>reject(new Error('Login timeout'))\n                                        }[\"AuthProvider.useEffect.checkAuth\"], 3000)\n                                }[\"AuthProvider.useEffect.checkAuth\"]);\n                                const demoUser = await Promise.race([\n                                    loginPromise,\n                                    timeoutPromise\n                                ]);\n                                console.log('Demo user logged in:', demoUser);\n                                setUser(demoUser);\n                                setIsAuthenticated(true);\n                                if (false) {}\n                                setLoading(false);\n                                return;\n                            } catch (error) {\n                                console.error('Demo auto-login failed:', error);\n                                // في حالة فشل التسجيل التلقائي، استخدم النظام الاحتياطي\n                                console.log('Using fallback authentication...');\n                                // إنشاء مستخدم تجريبي مباشرة\n                                const fallbackUser = {\n                                    id: 'manager_fallback',\n                                    username: 'manager',\n                                    name: 'مدير النظام',\n                                    phone: '07901234567',\n                                    role: 'manager',\n                                    permissions: [],\n                                    locationId: 'main_center',\n                                    location: {\n                                        id: 'main_center',\n                                        name: 'المركز العام',\n                                        type: 'company'\n                                    },\n                                    createdBy: 'system',\n                                    createdAt: new Date(),\n                                    isActive: true,\n                                    accessToken: 'fallback_token',\n                                    refreshToken: 'fallback_refresh'\n                                };\n                                setUser(fallbackUser);\n                                setIsAuthenticated(true);\n                                if (false) {}\n                                setLoading(false);\n                                return;\n                            }\n                        }\n                        if (false) {}\n                    } catch (error) {\n                        console.error('Error checking authentication:', error);\n                        // Clear invalid data\n                        if (false) {}\n                    } finally{\n                        // Always set loading to false after a short delay to ensure UI updates\n                        setTimeout({\n                            \"AuthProvider.useEffect.checkAuth\": ()=>{\n                                setLoading(false);\n                            }\n                        }[\"AuthProvider.useEffect.checkAuth\"], 300);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (username, password)=>{\n        try {\n            console.log('🔐 AuthProvider: محاولة تسجيل الدخول مع Firebase...');\n            // استخدام Firebase Auth Service مباشرة\n            const result = await _lib_firebase_auth__WEBPACK_IMPORTED_MODULE_5__.firebaseAuthService.login(username, password);\n            if (!result.success || !result.user) {\n                console.error('❌ فشل تسجيل الدخول:', result.error);\n                return false;\n            }\n            // تحويل بيانات المستخدم إلى AuthUser\n            const userData = {\n                ...result.user,\n                accessToken: 'firebase_token',\n                refreshToken: 'firebase_refresh'\n            };\n            // Update state\n            setUser(userData);\n            setIsAuthenticated(true);\n            // Store in localStorage\n            if (false) {}\n            console.log('✅ تم تسجيل الدخول بنجاح:', result.user.name);\n            return true;\n        } catch (error) {\n            console.error('❌ خطأ في تسجيل الدخول:', error);\n            return false;\n        }\n    };\n    const logout = async ()=>{\n        try {\n            console.log('🚪 AuthProvider: تسجيل الخروج من Firebase...');\n            // Use Firebase Auth Service logout\n            await _lib_firebase_auth__WEBPACK_IMPORTED_MODULE_5__.firebaseAuthService.logout();\n            // Update state\n            setUser(null);\n            setIsAuthenticated(false);\n            // Clear localStorage\n            if (false) {}\n            // Redirect to login\n            router.push('/login');\n        } catch (error) {\n            console.error('Logout error:', error);\n            // Force redirect even if there's an error\n            router.push('/login');\n        }\n    };\n    const value = {\n        user,\n        isAuthenticated,\n        login,\n        logout,\n        loading\n    };\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-700 mb-2\",\n                        children: \"مرسال\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"جاري التحقق من بيانات الدخول...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                lineNumber: 228,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Input({ className, type, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        \"data-slot\": \"input\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\", \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLFNBQVNFLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxJQUFJLEVBQUUsR0FBR0MsT0FBc0M7SUFDekUscUJBQ0UsOERBQUNDO1FBQ0NGLE1BQU1BO1FBQ05HLGFBQVU7UUFDVkosV0FBV0YsOENBQUVBLENBQ1gsbWNBQ0EsaUZBQ0EsMEdBQ0FFO1FBRUQsR0FBR0UsS0FBSzs7Ozs7O0FBR2Y7QUFFZ0IiLCJzb3VyY2VzIjpbIkU6XFxNYXJzYWxcXG1hcnNhbFxcc3JjXFxjb21wb25lbnRzXFx1aVxcaW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZnVuY3Rpb24gSW5wdXQoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH06IFJlYWN0LkNvbXBvbmVudFByb3BzPFwiaW5wdXRcIj4pIHtcbiAgcmV0dXJuIChcbiAgICA8aW5wdXRcbiAgICAgIHR5cGU9e3R5cGV9XG4gICAgICBkYXRhLXNsb3Q9XCJpbnB1dFwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBzZWxlY3Rpb246YmctcHJpbWFyeSBzZWxlY3Rpb246dGV4dC1wcmltYXJ5LWZvcmVncm91bmQgZGFyazpiZy1pbnB1dC8zMCBib3JkZXItaW5wdXQgZmxleCBoLTkgdy1mdWxsIG1pbi13LTAgcm91bmRlZC1tZCBib3JkZXIgYmctdHJhbnNwYXJlbnQgcHgtMyBweS0xIHRleHQtYmFzZSBzaGFkb3cteHMgdHJhbnNpdGlvbi1bY29sb3IsYm94LXNoYWRvd10gb3V0bGluZS1ub25lIGZpbGU6aW5saW5lLWZsZXggZmlsZTpoLTcgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxuICAgICAgICBcImZvY3VzLXZpc2libGU6Ym9yZGVyLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcvNTAgZm9jdXMtdmlzaWJsZTpyaW5nLVszcHhdXCIsXG4gICAgICAgIFwiYXJpYS1pbnZhbGlkOnJpbmctZGVzdHJ1Y3RpdmUvMjAgZGFyazphcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS80MCBhcmlhLWludmFsaWQ6Ym9yZGVyLWRlc3RydWN0aXZlXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJpbnB1dCIsImRhdGEtc2xvdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ3lCO0FBQ1U7QUFFakM7QUFFaEMsTUFBTUksZ0JBQWdCRiw2REFBR0EsQ0FDdkI7QUFHRixNQUFNRyxzQkFBUUwsNkNBQWdCLENBSTVCLENBQUMsRUFBRU8sU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDUix1REFBbUI7UUFDbEJRLEtBQUtBO1FBQ0xGLFdBQVdKLDhDQUFFQSxDQUFDQyxpQkFBaUJHO1FBQzlCLEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxNQUFNTSxXQUFXLEdBQUdWLHVEQUFtQixDQUFDVSxXQUFXO0FBRW5DIiwic291cmNlcyI6WyJFOlxcTWFyc2FsXFxtYXJzYWxcXHNyY1xcY29tcG9uZW50c1xcdWlcXGxhYmVsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0ICogYXMgTGFiZWxQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1sYWJlbFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgbGFiZWxWYXJpYW50cyA9IGN2YShcbiAgXCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGxlYWRpbmctbm9uZSBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNzBcIlxuKVxuXG5jb25zdCBMYWJlbCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+ICZcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGxhYmVsVmFyaWFudHM+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxMYWJlbFByaW1pdGl2ZS5Sb290XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihsYWJlbFZhcmlhbnRzKCksIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkxhYmVsLmRpc3BsYXlOYW1lID0gTGFiZWxQcmltaXRpdmUuUm9vdC5kaXNwbGF5TmFtZVxuXG5leHBvcnQgeyBMYWJlbCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMYWJlbFByaW1pdGl2ZSIsImN2YSIsImNuIiwibGFiZWxWYXJpYW50cyIsIkxhYmVsIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwiUm9vdCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types_roles__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/roles */ \"(ssr)/./src/types/roles.ts\");\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n// نظام المصادقة والصلاحيات\n\n\n\nclass AuthService {\n    // تسجيل الدخول مع Firebase\n    async login(credentials) {\n        try {\n            console.log('🔐 تسجيل الدخول مع Firebase...');\n            // استخدام Firebase Auth Service\n            const { firebaseAuthService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./firebase-auth */ \"(ssr)/./src/lib/firebase-auth.ts\"));\n            const result = await firebaseAuthService.login(credentials.username, credentials.password);\n            if (!result.success || !result.user) {\n                throw new Error(result.error || 'فشل في تسجيل الدخول');\n            }\n            const user = result.user;\n            try {\n                user = await _supabase__WEBPACK_IMPORTED_MODULE_2__.userService.getUserByUsername(credentials.username);\n            } catch (dbError) {\n                console.warn('Database not available, using fallback auth:', dbError);\n                // في حالة عدم توفر قاعدة البيانات، استخدم المستخدمين التجريبيين\n                return this.fallbackLogin(credentials);\n            }\n            if (!user) {\n                // إذا لم يوجد المستخدم في قاعدة البيانات، جرب المستخدمين التجريبيين\n                return this.fallbackLogin(credentials);\n            }\n            if (!user.is_active) {\n                throw new Error('الحساب غير مفعل');\n            }\n            // في الوضع التجريبي، نقبل كلمة المرور 123456 لجميع المستخدمين\n            if (credentials.password !== '123456') {\n                throw new Error('كلمة المرور غير صحيحة');\n            }\n            // تحويل بيانات المستخدم من Supabase إلى AuthUser\n            const authUser = {\n                id: user.id,\n                username: user.username,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                permissions: [],\n                locationId: user.location_id || 'main_center',\n                location: user.location || {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: user.created_by,\n                createdAt: new Date(user.created_at),\n                isActive: user.is_active,\n                accessToken: 'supabase_access_token',\n                refreshToken: 'supabase_refresh_token'\n            };\n            this.currentUser = authUser;\n            this.notifyListeners();\n            // حفظ في localStorage\n            if (false) {}\n            return authUser;\n        } catch (error) {\n            console.error('Login error:', error);\n            if (error instanceof Error) {\n                throw error;\n            }\n            throw new Error('فشل في تسجيل الدخول');\n        }\n    }\n    // تسجيل الدخول الاحتياطي (في حالة عدم توفر قاعدة البيانات)\n    async fallbackLogin(credentials) {\n        // بيانات تجريبية للمستخدمين\n        const mockUsers = {\n            'manager': {\n                id: 'manager_1',\n                username: 'manager',\n                name: 'مدير النظام',\n                phone: '07901234567',\n                role: 'manager',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'system',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token',\n                refreshToken: 'fallback_refresh_token'\n            },\n            'supervisor': {\n                id: 'supervisor_1',\n                username: 'supervisor',\n                name: 'متابع النظام',\n                phone: '07901234568',\n                role: 'supervisor',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'manager_1',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token_supervisor',\n                refreshToken: 'fallback_refresh_token_supervisor'\n            },\n            'courier': {\n                id: 'courier_1',\n                username: 'courier',\n                name: 'مندوب التوصيل',\n                phone: '07901234570',\n                role: 'courier',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'manager_1',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token_courier',\n                refreshToken: 'fallback_refresh_token_courier'\n            }\n        };\n        const user = mockUsers[credentials.username];\n        if (!user || credentials.password !== '123456') {\n            throw new Error('بيانات الدخول غير صحيحة');\n        }\n        this.currentUser = user;\n        this.notifyListeners();\n        // حفظ في localStorage\n        if (false) {}\n        return user;\n    }\n    // تسجيل الخروج\n    async logout() {\n        this.currentUser = null;\n        if (false) {}\n        this.notifyListeners();\n    }\n    // الحصول على المستخدم الحالي\n    getCurrentUser() {\n        if (!this.currentUser && \"undefined\" !== 'undefined') {}\n        return this.currentUser;\n    }\n    // التحقق من الصلاحية\n    hasPermission(permission) {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        return (0,_types_roles__WEBPACK_IMPORTED_MODULE_1__.hasPermission)(user.role, permission);\n    }\n    // الحصول على الأقسام المتاحة\n    getAccessibleSections() {\n        const user = this.getCurrentUser();\n        if (!user) return [];\n        return (0,_types_roles__WEBPACK_IMPORTED_MODULE_1__.getAccessibleSections)(user.role);\n    }\n    // التحقق من إمكانية إنشاء دور معين\n    canCreateRole(targetRole) {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        const rolePermissions = {\n            'manager': [\n                'supervisor',\n                'courier'\n            ],\n            'supervisor': [\n                'courier'\n            ],\n            'courier': []\n        };\n        return rolePermissions[user.role]?.includes(targetRole) || false;\n    }\n    // إضافة مستمع للتغييرات\n    addListener(listener) {\n        this.listeners.push(listener);\n    }\n    // إزالة مستمع\n    removeListener(listener) {\n        this.listeners = this.listeners.filter((l)=>l !== listener);\n    }\n    notifyListeners() {\n        this.listeners.forEach((listener)=>listener(this.currentUser));\n    }\n    // تحديث بيانات المستخدم\n    async updateProfile(data) {\n        if (!this.currentUser) {\n            throw new Error('لم يتم تسجيل الدخول');\n        }\n        // محاكاة API call\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        this.currentUser = {\n            ...this.currentUser,\n            ...data\n        };\n        if (false) {}\n        this.notifyListeners();\n        return this.currentUser;\n    }\n    // تغيير كلمة المرور\n    async changePassword(currentPassword, newPassword) {\n        if (!this.currentUser) {\n            throw new Error('لم يتم تسجيل الدخول');\n        }\n        // محاكاة API call\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // في التطبيق الحقيقي، سيتم التحقق من كلمة المرور الحالية\n        if (currentPassword !== '123456') {\n            throw new Error('كلمة المرور الحالية غير صحيحة');\n        }\n        // تحديث كلمة المرور (في التطبيق الحقيقي)\n        console.log('تم تغيير كلمة المرور بنجاح');\n    }\n    // التحقق من صحة الجلسة\n    async validateSession() {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        try {\n            // محاكاة التحقق من صحة الجلسة\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            return true;\n        } catch (error) {\n            await this.logout();\n            return false;\n        }\n    }\n    constructor(){\n        this.currentUser = null;\n        this.listeners = [];\n    }\n}\nconst authService = new AuthService();\n// Hook للاستخدام في React\nfunction useAuth() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(authService.getCurrentUser());\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const listener = {\n                \"useAuth.useEffect.listener\": (newUser)=>setUser(newUser)\n            }[\"useAuth.useEffect.listener\"];\n            authService.addListener(listener);\n            return ({\n                \"useAuth.useEffect\": ()=>authService.removeListener(listener)\n            })[\"useAuth.useEffect\"];\n        }\n    }[\"useAuth.useEffect\"], []);\n    return {\n        user,\n        login: authService.login.bind(authService),\n        logout: authService.logout.bind(authService),\n        hasPermission: authService.hasPermission.bind(authService),\n        getAccessibleSections: authService.getAccessibleSections.bind(authService),\n        canCreateRole: authService.canCreateRole.bind(authService),\n        updateProfile: authService.updateProfile.bind(authService),\n        changePassword: authService.changePassword.bind(authService),\n        validateSession: authService.validateSession.bind(authService)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   STATUS_COLORS: () => (/* binding */ STATUS_COLORS),\n/* harmony export */   STATUS_LABELS: () => (/* binding */ STATUS_LABELS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES)\n/* harmony export */ });\n// Application configuration\nconst APP_CONFIG = {\n    name: \"مرسال\",\n    description: \"نظام إدارة عمليات التوصيل السريع\",\n    version: \"1.1.0\",\n    // Colors\n    colors: {\n        primary: \"#41a7ff\",\n        background: \"#f0f3f5\",\n        accent: \"#b19cd9\"\n    },\n    // Business rules\n    business: {\n        commissionPerOrder: 1000,\n        currency: \"د.ع\",\n        defaultOrderStatuses: [\n            \"pending\",\n            \"assigned\",\n            \"picked_up\",\n            \"in_transit\",\n            \"delivered\",\n            \"returned\",\n            \"cancelled\",\n            \"postponed\"\n        ]\n    },\n    // API endpoints (when backend is ready)\n    api: {\n        baseUrl: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\",\n        endpoints: {\n            orders: \"/api/orders\",\n            users: \"/api/users\",\n            couriers: \"/api/couriers\",\n            settlements: \"/api/settlements\"\n        }\n    },\n    // Firebase config - إعدادات قاعدة البيانات السحابية الحقيقية\n    firebase: {\n        apiKey: \"AIzaSyDemoKeyForMarsalDeliveryApp123456789\" || 0,\n        authDomain: \"marsal-delivery-app.firebaseapp.com\" || 0,\n        projectId: \"marsal-delivery-app\" || 0,\n        storageBucket: \"marsal-delivery-app.appspot.com\" || 0,\n        messagingSenderId: \"123456789012\" || 0,\n        appId: \"1:123456789012:web:abcdef123456789012345\" || 0,\n        measurementId: \"G-XXXXXXXXXX\" || 0\n    },\n    // Features flags\n    features: {\n        enableNotifications: true,\n        enableReports: true,\n        enableBulkOperations: true,\n        enableImageUpload: true\n    },\n    // Firebase Production mode - وضع الإنتاج مع Firebase حصرياً\n    demo: {\n        enabled: false,\n        autoLogin: false,\n        defaultUser: null,\n        skipFirebase: false,\n        showDemoNotice: false // إخفاء تنبيه الوضع التجريبي\n    },\n    // Company info\n    company: {\n        name: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        phone: '+964 ************',\n        address: 'بغداد، العراق'\n    },\n    // Receipt settings - إعدادات الوصل\n    receipt: {\n        dimensions: {\n            width: '110mm',\n            height: '130mm'\n        },\n        companyName: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        showBarcode: true,\n        showDate: true,\n        priceFormat: 'en-US',\n        currency: 'IQD',\n        fields: {\n            trackingNumber: true,\n            customerPhone: true,\n            status: true,\n            courierName: true,\n            amount: true,\n            barcode: true,\n            date: true\n        }\n    }\n};\n// Status labels in Arabic\nconst STATUS_LABELS = {\n    pending: \"في الانتظار\",\n    assigned: \"مسند\",\n    picked_up: \"تم الاستلام\",\n    in_transit: \"في الطريق\",\n    delivered: \"تم التسليم\",\n    returned: \"راجع\",\n    cancelled: \"ملغي\",\n    postponed: \"مؤجل\"\n};\n// Status colors for UI\nconst STATUS_COLORS = {\n    pending: \"text-yellow-600 bg-yellow-100\",\n    assigned: \"text-blue-600 bg-blue-100\",\n    picked_up: \"text-purple-600 bg-purple-100\",\n    in_transit: \"text-orange-600 bg-orange-100\",\n    delivered: \"text-green-600 bg-green-100\",\n    returned: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    postponed: \"text-gray-600 bg-gray-100\"\n};\n// User roles\nconst USER_ROLES = {\n    admin: \"مدير\",\n    manager: \"مدير فرع\",\n    dispatcher: \"موزع\",\n    courier: \"مندوب\",\n    accountant: \"محاسب\",\n    viewer: \"مشاهد\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase-auth.ts":
/*!**********************************!*\
  !*** ./src/lib/firebase-auth.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   firebaseAuthService: () => (/* binding */ firebaseAuthService)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n// Firebase Authentication service for Marsal Delivery App\n\n\n\n// Default users for initial setup\nconst defaultUsers = [\n    {\n        username: 'azad95',\n        email: '<EMAIL>',\n        name: 'أزاد - مدير النظام الرئيسي',\n        phone: '07801234567',\n        role: 'manager',\n        password: 'Azad@1995'\n    },\n    {\n        username: 'manager',\n        email: '<EMAIL>',\n        name: 'مدير النظام',\n        phone: '07801234568',\n        role: 'manager',\n        password: '123456'\n    },\n    {\n        username: 'supervisor',\n        email: '<EMAIL>',\n        name: 'المشرف العام',\n        phone: '07801234569',\n        role: 'supervisor',\n        password: '123456'\n    },\n    {\n        username: 'courier',\n        email: '<EMAIL>',\n        name: 'مندوب التوصيل',\n        phone: '07801234570',\n        role: 'courier',\n        password: '123456'\n    }\n];\nconst firebaseAuthService = {\n    // Initialize default users (run once) - Local storage fallback\n    async initializeDefaultUsers () {\n        try {\n            console.log('🔧 إعداد المستخدمين الافتراضيين...');\n            // Check if Firebase is available\n            if (!_firebase__WEBPACK_IMPORTED_MODULE_1__.auth || !_firebase__WEBPACK_IMPORTED_MODULE_1__.db) {\n                console.log('⚠️ Firebase غير متاح، استخدام التخزين المحلي');\n                this.initializeLocalUsers();\n                return;\n            }\n            for (const userData of defaultUsers){\n                // Check if user already exists\n                const existingUser = await this.getUserByUsername(userData.username);\n                if (existingUser) {\n                    console.log(`✅ المستخدم ${userData.username} موجود مسبقاً`);\n                    continue;\n                }\n                // Create Firebase Auth user\n                try {\n                    const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, userData.email, userData.password);\n                    // Create user document in Firestore\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n                        username: userData.username,\n                        email: userData.email,\n                        name: userData.name,\n                        phone: userData.phone,\n                        role: userData.role,\n                        isActive: true,\n                        createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                        updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                        createdBy: 'system'\n                    });\n                    console.log(`✅ تم إنشاء المستخدم: ${userData.username}`);\n                } catch (error) {\n                    if (error.code === 'auth/email-already-in-use') {\n                        console.log(`⚠️ البريد الإلكتروني ${userData.email} مستخدم مسبقاً`);\n                    } else {\n                        console.error(`❌ خطأ في إنشاء المستخدم ${userData.username}:`, error);\n                        // Fallback to local storage\n                        this.initializeLocalUsers();\n                        return;\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ خطأ في إعداد المستخدمين الافتراضيين:', error);\n            // Fallback to local storage\n            this.initializeLocalUsers();\n        }\n    },\n    // Initialize users in local storage as fallback\n    initializeLocalUsers () {\n        try {\n            const existingUsers = localStorage.getItem('marsal_users');\n            if (existingUsers) {\n                console.log('✅ المستخدمين موجودين في التخزين المحلي');\n                return;\n            }\n            const localUsers = defaultUsers.map((userData, index)=>({\n                    id: `local_${index + 1}`,\n                    username: userData.username,\n                    email: userData.email,\n                    name: userData.name,\n                    phone: userData.phone,\n                    role: userData.role,\n                    isActive: true,\n                    createdAt: new Date(),\n                    updatedAt: new Date(),\n                    createdBy: 'system',\n                    password: userData.password // Store password for local auth\n                }));\n            localStorage.setItem('marsal_users', JSON.stringify(localUsers));\n            console.log('✅ تم إنشاء المستخدمين في التخزين المحلي');\n        } catch (error) {\n            console.error('❌ خطأ في إنشاء المستخدمين المحليين:', error);\n        }\n    },\n    // Login with username/password - Firebase or Local\n    async login (username, password) {\n        try {\n            console.log('🔐 محاولة تسجيل الدخول للمستخدم:', username);\n            // Try Firebase first\n            if (_firebase__WEBPACK_IMPORTED_MODULE_1__.auth && _firebase__WEBPACK_IMPORTED_MODULE_1__.db) {\n                try {\n                    return await this.loginWithFirebase(username, password);\n                } catch (error) {\n                    console.warn('⚠️ فشل تسجيل الدخول مع Firebase، محاولة التخزين المحلي');\n                }\n            }\n            // Fallback to local storage\n            return await this.loginWithLocalStorage(username, password);\n        } catch (error) {\n            console.error('❌ خطأ في تسجيل الدخول:', error);\n            return {\n                success: false,\n                error: error.message || 'خطأ في تسجيل الدخول'\n            };\n        }\n    },\n    // Login with Firebase\n    async loginWithFirebase (username, password) {\n        // Get user by username to find email\n        const user = await this.getUserByUsername(username);\n        if (!user) {\n            throw new Error('اسم المستخدم غير موجود');\n        }\n        if (!user.isActive) {\n            throw new Error('الحساب غير مفعل');\n        }\n        // Sign in with email and password\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, user.email, password);\n        // Update last login\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n            lastLogin: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)()\n        }, {\n            merge: true\n        });\n        console.log('✅ تم تسجيل الدخول مع Firebase بنجاح');\n        return {\n            success: true,\n            user: {\n                ...user,\n                id: userCredential.user.uid\n            }\n        };\n    },\n    // Login with local storage\n    async loginWithLocalStorage (username, password) {\n        const usersData = localStorage.getItem('marsal_users');\n        if (!usersData) {\n            throw new Error('لا يوجد مستخدمين مسجلين');\n        }\n        const users = JSON.parse(usersData);\n        const user = users.find((u)=>u.username === username);\n        if (!user) {\n            throw new Error('اسم المستخدم غير موجود');\n        }\n        if (!user.isActive) {\n            throw new Error('الحساب غير مفعل');\n        }\n        if (user.password !== password) {\n            throw new Error('كلمة المرور غير صحيحة');\n        }\n        // Update last login\n        user.lastLogin = new Date();\n        const updatedUsers = users.map((u)=>u.id === user.id ? user : u);\n        localStorage.setItem('marsal_users', JSON.stringify(updatedUsers));\n        console.log('✅ تم تسجيل الدخول مع التخزين المحلي بنجاح');\n        return {\n            success: true,\n            user: {\n                id: user.id,\n                username: user.username,\n                email: user.email,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                isActive: user.isActive,\n                createdAt: new Date(user.createdAt),\n                updatedAt: new Date(user.updatedAt),\n                createdBy: user.createdBy,\n                lastLogin: user.lastLogin ? new Date(user.lastLogin) : undefined\n            }\n        };\n    },\n    // Get user by username - Firebase or Local\n    async getUserByUsername (username) {\n        try {\n            // Try Firebase first\n            if (_firebase__WEBPACK_IMPORTED_MODULE_1__.db) {\n                try {\n                    const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users');\n                    const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('username', '==', username));\n                    const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n                    if (!snapshot.empty) {\n                        const doc = snapshot.docs[0];\n                        const data = doc.data();\n                        return {\n                            id: doc.id,\n                            username: data.username,\n                            email: data.email,\n                            name: data.name,\n                            phone: data.phone,\n                            role: data.role,\n                            isActive: data.isActive,\n                            createdAt: data.createdAt?.toDate() || new Date(),\n                            updatedAt: data.updatedAt?.toDate() || new Date(),\n                            createdBy: data.createdBy,\n                            lastLogin: data.lastLogin?.toDate()\n                        };\n                    }\n                } catch (error) {\n                    console.warn('⚠️ فشل البحث في Firebase، محاولة التخزين المحلي');\n                }\n            }\n            // Fallback to local storage\n            const usersData = localStorage.getItem('marsal_users');\n            if (!usersData) {\n                return null;\n            }\n            const users = JSON.parse(usersData);\n            const user = users.find((u)=>u.username === username);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                username: user.username,\n                email: user.email,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                isActive: user.isActive,\n                createdAt: new Date(user.createdAt),\n                updatedAt: new Date(user.updatedAt),\n                createdBy: user.createdBy,\n                lastLogin: user.lastLogin ? new Date(user.lastLogin) : undefined\n            };\n        } catch (error) {\n            console.error('Error getting user by username:', error);\n            return null;\n        }\n    },\n    // Get current user\n    async getCurrentUser () {\n        try {\n            const firebaseUser = _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n            if (!firebaseUser) {\n                return null;\n            }\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', firebaseUser.uid));\n            if (!userDoc.exists()) {\n                return null;\n            }\n            const data = userDoc.data();\n            return {\n                id: userDoc.id,\n                username: data.username,\n                email: data.email,\n                name: data.name,\n                phone: data.phone,\n                role: data.role,\n                isActive: data.isActive,\n                createdAt: data.createdAt?.toDate() || new Date(),\n                updatedAt: data.updatedAt?.toDate() || new Date(),\n                createdBy: data.createdBy,\n                lastLogin: data.lastLogin?.toDate()\n            };\n        } catch (error) {\n            console.error('Error getting current user:', error);\n            return null;\n        }\n    },\n    // Logout\n    async logout () {\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth);\n            console.log('✅ تم تسجيل الخروج بنجاح');\n        } catch (error) {\n            console.error('❌ خطأ في تسجيل الخروج:', error);\n            throw error;\n        }\n    },\n    // Listen to auth state changes\n    onAuthStateChanged (callback) {\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, async (firebaseUser)=>{\n            if (firebaseUser) {\n                const user = await this.getCurrentUser();\n                callback(user);\n            } else {\n                callback(null);\n            }\n        });\n    },\n    // Create new user (only for managers)\n    async createUser (userData) {\n        try {\n            console.log('👤 إنشاء مستخدم جديد:', userData.username);\n            // Create Firebase Auth user\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, userData.email, userData.password);\n            // Create user document in Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n                username: userData.username,\n                email: userData.email,\n                name: userData.name,\n                phone: userData.phone,\n                role: userData.role,\n                isActive: true,\n                createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                createdBy: userData.createdBy\n            });\n            console.log('✅ تم إنشاء المستخدم بنجاح:', userData.username);\n            const newUser = {\n                id: userCredential.user.uid,\n                username: userData.username,\n                email: userData.email,\n                name: userData.name,\n                phone: userData.phone,\n                role: userData.role,\n                isActive: true,\n                createdAt: new Date(),\n                updatedAt: new Date(),\n                createdBy: userData.createdBy\n            };\n            return {\n                success: true,\n                user: newUser\n            };\n        } catch (error) {\n            console.error('❌ خطأ في إنشاء المستخدم:', error);\n            let errorMessage = 'خطأ في إنشاء المستخدم';\n            switch(error.code){\n                case 'auth/email-already-in-use':\n                    errorMessage = 'البريد الإلكتروني مستخدم مسبقاً';\n                    break;\n                case 'auth/weak-password':\n                    errorMessage = 'كلمة المرور ضعيفة';\n                    break;\n                case 'auth/invalid-email':\n                    errorMessage = 'البريد الإلكتروني غير صحيح';\n                    break;\n                default:\n                    errorMessage = error.message || 'خطأ غير معروف';\n            }\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    },\n    // Get all users (for managers)\n    async getAllUsers () {\n        try {\n            const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users');\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(usersRef);\n            return snapshot.docs.map((doc)=>{\n                const data = doc.data();\n                return {\n                    id: doc.id,\n                    username: data.username,\n                    email: data.email,\n                    name: data.name,\n                    phone: data.phone,\n                    role: data.role,\n                    isActive: data.isActive,\n                    createdAt: data.createdAt?.toDate() || new Date(),\n                    updatedAt: data.updatedAt?.toDate() || new Date(),\n                    createdBy: data.createdBy,\n                    lastLogin: data.lastLogin?.toDate()\n                };\n            });\n        } catch (error) {\n            console.error('Error getting all users:', error);\n            return [];\n        }\n    },\n    // Update user (for managers)\n    async updateUser (userId, updates) {\n        try {\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId), {\n                ...updates,\n                updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)()\n            }, {\n                merge: true\n            });\n            console.log('✅ تم تحديث المستخدم:', userId);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('❌ خطأ في تحديث المستخدم:', error);\n            return {\n                success: false,\n                error: error.message || 'خطأ في تحديث المستخدم'\n            };\n        }\n    },\n    // Delete user (for managers)\n    async deleteUser (userId) {\n        try {\n            // Delete from Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId));\n            // Note: Deleting from Firebase Auth requires Admin SDK\n            // For now, we just deactivate the user\n            await this.updateUser(userId, {\n                isActive: false\n            });\n            console.log('✅ تم حذف المستخدم:', userId);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('❌ خطأ في حذف المستخدم:', error);\n            return {\n                success: false,\n                error: error.message || 'خطأ في حذف المستخدم'\n            };\n        }\n    },\n    // Check if Firebase is connected\n    async checkConnection () {\n        try {\n            // Try to get current user\n            const user = _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n            console.log('✅ Firebase Auth متصل');\n            return {\n                connected: true,\n                message: 'Firebase Auth متصل بنجاح'\n            };\n        } catch (error) {\n            console.error('❌ Firebase Auth غير متصل:', error);\n            return {\n                connected: false,\n                message: `خطأ في الاتصال: ${error.message}`\n            };\n        }\n    }\n};\n// Auto-initialize default users when the module loads\nfirebaseAuthService.initializeDefaultUsers().catch(console.error);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase-auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   storage: () => (/* binding */ storage),\n/* harmony export */   testFirebaseConnection: () => (/* binding */ testFirebaseConnection)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/config.ts\");\n\n\n\n\n\n// Firebase configuration - إعدادات قاعدة البيانات السحابية\nconst firebaseConfig = {\n    apiKey: _config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.firebase.apiKey || \"AIzaSyDemoKeyForMarsalDeliveryApp123456789\",\n    authDomain: _config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.firebase.authDomain || \"marsal-delivery.firebaseapp.com\",\n    projectId: _config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.firebase.projectId || \"marsal-delivery-system\",\n    storageBucket: _config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.firebase.storageBucket || \"marsal-delivery.appspot.com\",\n    messagingSenderId: _config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.firebase.messagingSenderId || \"987654321\",\n    appId: _config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.firebase.appId || \"1:987654321:web:abc123def456ghi789\"\n};\n// Initialize Firebase only if not in demo mode or if Firebase is not skipped\nlet app = null;\nlet db = null;\nlet auth = null;\nlet storage = null;\nif (!_config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.demo.skipFirebase) {\n    try {\n        app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n        db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)(app);\n        auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)(app);\n        storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n    } catch (error) {\n        console.warn('Firebase initialization failed, running in demo mode:', error);\n    }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n// Test connection function - اختبار الاتصال بقاعدة البيانات السحابية\nconst testFirebaseConnection = async ()=>{\n    // في الوضع التجريبي، نعيد نجاح وهمي\n    if (_config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.demo.skipFirebase || !db) {\n        return {\n            success: true,\n            message: 'تم الاتصال بقاعدة البيانات التجريبية بنجاح ✅ (وضع تجريبي)'\n        };\n    }\n    try {\n        // Test Firestore connection with timeout\n        const { doc, getDoc, setDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\"));\n        // Create a test document to verify write access\n        const testDocRef = doc(db, 'system', 'connection_test');\n        await setDoc(testDocRef, {\n            timestamp: new Date(),\n            status: 'connected',\n            app: 'marsal-delivery'\n        });\n        // Try to read the document back\n        const testDoc = await getDoc(testDocRef);\n        if (testDoc.exists()) {\n            return {\n                success: true,\n                message: 'تم الاتصال بقاعدة البيانات السحابية بنجاح ✅'\n            };\n        } else {\n            return {\n                success: false,\n                message: 'تم الاتصال ولكن فشل في قراءة البيانات'\n            };\n        }\n    } catch (error) {\n        console.error('Firebase connection test failed:', error);\n        // Return a more user-friendly error message\n        let errorMessage = 'فشل في الاتصال بقاعدة البيانات السحابية';\n        if (error instanceof Error) {\n            if (error.message.includes('network')) {\n                errorMessage += ' - تحقق من الاتصال بالإنترنت';\n            } else if (error.message.includes('permission')) {\n                errorMessage += ' - مشكلة في الصلاحيات';\n            } else {\n                errorMessage += ': ' + error.message;\n            }\n        }\n        return {\n            success: false,\n            message: errorMessage\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseOrderService: () => (/* binding */ SupabaseOrderService),\n/* harmony export */   SupabaseUserService: () => (/* binding */ SupabaseUserService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   orderService: () => (/* binding */ orderService),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   testSupabaseConnection: () => (/* binding */ testSupabaseConnection),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Supabase configuration - إعدادات قاعدة البيانات السحابية\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';\n// Create Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Test connection function - اختبار الاتصال بقاعدة البيانات السحابية\nconst testSupabaseConnection = async ()=>{\n    try {\n        // Test connection by trying to fetch from a system table\n        const { data, error } = await supabase.from('users').select('count').limit(1);\n        if (error) {\n            console.error('Supabase connection test failed:', error);\n            return {\n                success: false,\n                message: `فشل في الاتصال بقاعدة البيانات السحابية: ${error.message}`\n            };\n        }\n        return {\n            success: true,\n            message: 'تم الاتصال بقاعدة البيانات السحابية بنجاح ✅'\n        };\n    } catch (error) {\n        console.error('Supabase connection test failed:', error);\n        let errorMessage = 'فشل في الاتصال بقاعدة البيانات السحابية';\n        if (error instanceof Error) {\n            if (error.message.includes('network')) {\n                errorMessage += ' - تحقق من الاتصال بالإنترنت';\n            } else if (error.message.includes('permission')) {\n                errorMessage += ' - مشكلة في الصلاحيات';\n            } else {\n                errorMessage += ': ' + error.message;\n            }\n        }\n        return {\n            success: false,\n            message: errorMessage\n        };\n    }\n};\n// User Service - خدمة المستخدمين\nclass SupabaseUserService {\n    async createUser(userData) {\n        const { data, error } = await supabase.from('users').insert([\n            userData\n        ]).select().single();\n        if (error) throw new Error(`Failed to create user: ${error.message}`);\n        return data;\n    }\n    async getUserByUsername(username) {\n        const { data, error } = await supabase.from('users').select('*').eq('username', username).single();\n        if (error && error.code !== 'PGRST116') {\n            throw new Error(`Failed to get user: ${error.message}`);\n        }\n        return data || null;\n    }\n    async getAllUsers() {\n        const { data, error } = await supabase.from('users').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get users: ${error.message}`);\n        return data || [];\n    }\n    async updateUser(id, updates) {\n        const { error } = await supabase.from('users').update(updates).eq('id', id);\n        if (error) throw new Error(`Failed to update user: ${error.message}`);\n    }\n    async deleteUser(id) {\n        const { error } = await supabase.from('users').delete().eq('id', id);\n        if (error) throw new Error(`Failed to delete user: ${error.message}`);\n    }\n    async getUsersByRole(role) {\n        const { data, error } = await supabase.from('users').select('*').eq('role', role).order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get users by role: ${error.message}`);\n        return data || [];\n    }\n}\n// Order Service - خدمة الطلبات\nclass SupabaseOrderService {\n    async createOrder(orderData) {\n        const { data, error } = await supabase.from('orders').insert([\n            {\n                ...orderData,\n                updated_at: new Date().toISOString()\n            }\n        ]).select().single();\n        if (error) throw new Error(`Failed to create order: ${error.message}`);\n        return data;\n    }\n    async getOrderByTrackingNumber(trackingNumber) {\n        const { data, error } = await supabase.from('orders').select('*').eq('tracking_number', trackingNumber).single();\n        if (error && error.code !== 'PGRST116') {\n            throw new Error(`Failed to get order: ${error.message}`);\n        }\n        return data || null;\n    }\n    async getAllOrders() {\n        const { data, error } = await supabase.from('orders').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get orders: ${error.message}`);\n        return data || [];\n    }\n    async getOrdersByStatus(status) {\n        const { data, error } = await supabase.from('orders').select('*').eq('status', status).order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get orders by status: ${error.message}`);\n        return data || [];\n    }\n    async getOrdersByCourier(courierId) {\n        const { data, error } = await supabase.from('orders').select('*').eq('courier_id', courierId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get orders by courier: ${error.message}`);\n        return data || [];\n    }\n    async updateOrder(id, updates) {\n        const { error } = await supabase.from('orders').update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq('id', id);\n        if (error) throw new Error(`Failed to update order: ${error.message}`);\n    }\n    async deleteOrder(id) {\n        const { error } = await supabase.from('orders').delete().eq('id', id);\n        if (error) throw new Error(`Failed to delete order: ${error.message}`);\n    }\n    async searchOrders(query) {\n        const { data, error } = await supabase.from('orders').select('*').or(`tracking_number.ilike.%${query}%,customer_name.ilike.%${query}%,customer_phone.ilike.%${query}%,address.ilike.%${query}%`).order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to search orders: ${error.message}`);\n        return data || [];\n    }\n}\n// Create service instances\nconst userService = new SupabaseUserService();\nconst orderService = new SupabaseOrderService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateCommission: () => (/* binding */ calculateCommission),\n/* harmony export */   calculateNetAmount: () => (/* binding */ calculateNetAmount),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatPhone: () => (/* binding */ formatPhone),\n/* harmony export */   generateTrackingNumber: () => (/* binding */ generateTrackingNumber),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getStatusLabel: () => (/* binding */ getStatusLabel),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateIraqiPhone: () => (/* binding */ validateIraqiPhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Format currency in Iraqi Dinar\nfunction formatCurrency(amount) {\n    return `${amount.toLocaleString('ar-IQ')} د.ع`;\n}\n// Format date in Arabic\nfunction formatDate(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleDateString('ar-IQ', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n}\n// Format date and time in Arabic\nfunction formatDateTime(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleString('ar-IQ', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    });\n}\n// Generate tracking number\nfunction generateTrackingNumber() {\n    const prefix = 'MRS';\n    const timestamp = Date.now().toString().slice(-6);\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n    return `${prefix}${timestamp}${random}`;\n}\n// Validate Iraqi phone number\nfunction validateIraqiPhone(phone) {\n    const phoneRegex = /^(07[3-9]|075)\\d{8}$/;\n    return phoneRegex.test(phone.replace(/\\s+/g, ''));\n}\n// Format phone number\nfunction formatPhone(phone) {\n    const cleaned = phone.replace(/\\D/g, '');\n    if (cleaned.length === 11 && cleaned.startsWith('07')) {\n        return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;\n    }\n    return phone;\n}\n// Calculate commission\nfunction calculateCommission(orderCount, commissionPerOrder = 1000) {\n    return orderCount * commissionPerOrder;\n}\n// Calculate net amount after commission\nfunction calculateNetAmount(totalAmount, commission) {\n    return totalAmount - commission;\n}\n// Truncate text\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n}\n// Get status color class\nfunction getStatusColor(status) {\n    const colors = {\n        pending: \"text-yellow-600 bg-yellow-100\",\n        assigned: \"text-blue-600 bg-blue-100\",\n        picked_up: \"text-purple-600 bg-purple-100\",\n        in_transit: \"text-orange-600 bg-orange-100\",\n        delivered: \"text-green-600 bg-green-100\",\n        returned: \"text-red-600 bg-red-100\",\n        cancelled: \"text-gray-600 bg-gray-100\",\n        postponed: \"text-gray-600 bg-gray-100\"\n    };\n    return colors[status] || \"text-gray-600 bg-gray-100\";\n}\n// Get status label in Arabic\nfunction getStatusLabel(status) {\n    const labels = {\n        pending: \"في الانتظار\",\n        assigned: \"مسند\",\n        picked_up: \"تم الاستلام\",\n        in_transit: \"في الطريق\",\n        delivered: \"تم التسليم\",\n        returned: \"راجع\",\n        cancelled: \"ملغي\",\n        postponed: \"مؤجل\"\n    };\n    return labels[status] || status;\n}\n// Sleep function for demos\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n// Check if running on mobile\nfunction isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n// Copy text to clipboard\nasync function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (err) {\n        console.error('Failed to copy text: ', err);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/roles.ts":
/*!****************************!*\
  !*** ./src/types/roles.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ORDER_STATUSES: () => (/* binding */ ORDER_STATUSES),\n/* harmony export */   Permission: () => (/* binding */ Permission),\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   canCreateRole: () => (/* binding */ canCreateRole),\n/* harmony export */   getAccessibleSections: () => (/* binding */ getAccessibleSections),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\n// نظام الأدوار والصلاحيات الشامل\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    // الأدوار الأساسية فقط\n    UserRole[\"MANAGER\"] = \"manager\";\n    UserRole[\"COURIER\"] = \"courier\";\n    UserRole[\"SUPERVISOR\"] = \"supervisor\"; // متابع\n    return UserRole;\n}({});\nvar Permission = /*#__PURE__*/ function(Permission) {\n    // إدارة الطلبات\n    Permission[\"CREATE_ORDER\"] = \"create_order\";\n    Permission[\"VIEW_ORDER\"] = \"view_order\";\n    Permission[\"UPDATE_ORDER\"] = \"update_order\";\n    Permission[\"DELETE_ORDER\"] = \"delete_order\";\n    Permission[\"ASSIGN_ORDER\"] = \"assign_order\";\n    Permission[\"TRANSFER_ORDER\"] = \"transfer_order\";\n    // إدارة المستخدمين\n    Permission[\"CREATE_USER\"] = \"create_user\";\n    Permission[\"VIEW_USER\"] = \"view_user\";\n    Permission[\"UPDATE_USER\"] = \"update_user\";\n    Permission[\"DELETE_USER\"] = \"delete_user\";\n    // إدارة الفروع والمراكز\n    Permission[\"MANAGE_BRANCHES\"] = \"manage_branches\";\n    Permission[\"MANAGE_PROVINCES\"] = \"manage_provinces\";\n    // المحاسبة\n    Permission[\"VIEW_ACCOUNTING\"] = \"view_accounting\";\n    Permission[\"PROCESS_ACCOUNTING\"] = \"process_accounting\";\n    // الإحصائيات\n    Permission[\"VIEW_STATISTICS\"] = \"view_statistics\";\n    Permission[\"VIEW_ALL_STATISTICS\"] = \"view_all_statistics\";\n    // الأرشيف\n    Permission[\"VIEW_ARCHIVE\"] = \"view_archive\";\n    Permission[\"MANAGE_ARCHIVE\"] = \"manage_archive\";\n    // التذاكر\n    Permission[\"CREATE_TICKET\"] = \"create_ticket\";\n    Permission[\"MANAGE_TICKETS\"] = \"manage_tickets\";\n    // الإعدادات\n    Permission[\"MANAGE_SETTINGS\"] = \"manage_settings\";\n    // المخزن\n    Permission[\"MANAGE_WAREHOUSE\"] = \"manage_warehouse\";\n    // الاستيراد والتصدير\n    Permission[\"IMPORT_ORDERS\"] = \"import_orders\";\n    Permission[\"EXPORT_ORDERS\"] = \"export_orders\";\n    return Permission;\n}({});\nconst ROLE_PERMISSIONS = {\n    [\"manager\"]: {\n        role: \"manager\",\n        permissions: [\n            \"create_order\",\n            \"view_order\",\n            \"update_order\",\n            \"delete_order\",\n            \"assign_order\",\n            \"transfer_order\",\n            \"create_user\",\n            \"view_user\",\n            \"update_user\",\n            \"delete_user\",\n            \"manage_branches\",\n            \"manage_provinces\",\n            \"view_accounting\",\n            \"process_accounting\",\n            \"view_all_statistics\",\n            \"view_archive\",\n            \"manage_archive\",\n            \"manage_tickets\",\n            \"manage_settings\",\n            \"manage_warehouse\",\n            \"import_orders\",\n            \"export_orders\"\n        ],\n        canCreateRoles: [\n            \"courier\",\n            \"supervisor\"\n        ],\n        accessibleSections: [\n            'orders',\n            'dispatch',\n            'returns',\n            'accounting',\n            'statistics',\n            'archive',\n            'users',\n            'import-export',\n            'notifications',\n            'settings'\n        ]\n    },\n    [\"supervisor\"]: {\n        role: \"supervisor\",\n        permissions: [\n            \"view_order\",\n            \"update_order\",\n            \"assign_order\",\n            \"manage_tickets\",\n            \"view_statistics\",\n            \"view_archive\"\n        ],\n        canCreateRoles: [],\n        accessibleSections: [\n            'orders',\n            'statistics',\n            'archive',\n            'notifications',\n            'settings'\n        ]\n    },\n    [\"courier\"]: {\n        role: \"courier\",\n        permissions: [\n            \"view_order\",\n            \"update_order\"\n        ],\n        canCreateRoles: [],\n        accessibleSections: [\n            'orders',\n            'archive',\n            'notifications',\n            'settings'\n        ]\n    }\n};\nconst ORDER_STATUSES = [\n    {\n        id: 'delivered',\n        name: 'تم التسليم',\n        color: 'bg-green-600',\n        description: 'تم تسليم الطلب بنجاح',\n        requiresPhoto: true,\n        requiresReason: false,\n        isDelivered: true,\n        isReturned: false\n    },\n    {\n        id: 'returned_to_courier',\n        name: 'راجع عند المندوب',\n        color: 'bg-amber-600',\n        description: 'الطلب راجع عند المندوب',\n        requiresPhoto: true,\n        requiresReason: true,\n        isDelivered: false,\n        isReturned: true\n    },\n    {\n        id: 'partial_delivery',\n        name: 'تسليم جزئي',\n        color: 'bg-blue-500',\n        description: 'تم تسليم جزء من الطلب',\n        requiresPhoto: true,\n        requiresReason: false,\n        isDelivered: true,\n        isReturned: false\n    },\n    {\n        id: 'price_change',\n        name: 'تغيير السعر',\n        color: 'bg-purple-500',\n        description: 'تم تغيير سعر الطلب',\n        requiresPhoto: false,\n        requiresReason: true,\n        isDelivered: false,\n        isReturned: false\n    },\n    {\n        id: 'postponed',\n        name: 'مؤجل',\n        color: 'bg-gray-500',\n        description: 'تم تأجيل التسليم',\n        requiresPhoto: true,\n        requiresReason: true,\n        isDelivered: false,\n        isReturned: false\n    }\n];\nfunction hasPermission(userRole, permission) {\n    return ROLE_PERMISSIONS[userRole]?.permissions.includes(permission) || false;\n}\nfunction canCreateRole(userRole, targetRole) {\n    return ROLE_PERMISSIONS[userRole]?.canCreateRoles.includes(targetRole) || false;\n}\nfunction getAccessibleSections(userRole) {\n    return ROLE_PERMISSIONS[userRole]?.accessibleSections || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/roles.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/@supabase","vendor-chunks/protobufjs","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/tailwind-merge","vendor-chunks/long","vendor-chunks/whatwg-url","vendor-chunks/@protobufjs","vendor-chunks/lucide-react","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/@radix-ui","vendor-chunks/webidl-conversions","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/firebase","vendor-chunks/isows","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffirebase-login%2Fpage&page=%2Ffirebase-login%2Fpage&appPaths=%2Ffirebase-login%2Fpage&pagePath=private-next-app-dir%2Ffirebase-login%2Fpage.tsx&appDir=E%3A%5CMarsal%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMarsal%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();