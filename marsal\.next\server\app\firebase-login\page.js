(()=>{var e={};e.id=894,e.ids=[894],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12597:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14163:(e,r,t)=>{"use strict";t.d(r,{hO:()=>d,sG:()=>o});var s=t(43210),a=t(51215),i=t(8730),n=t(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,i.TL)(`Primitive.${r}`),a=s.forwardRef((e,s)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?t:r,{...i,ref:s})});return a.displayName=`Primitive.${r}`,{...e,[r]:a}},{});function d(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(60687);t(43210);var a=t(8730),i=t(24224),n=t(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:t,asChild:i=!1,...d}){let c=i?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:t,className:e})),...d})}},32926:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\firebase-login\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34986:(e,r,t)=>{Promise.resolve().then(t.bind(t,32926))},37366:e=>{"use strict";e.exports=require("dns")},41862:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=t(60687);t(43210);var a=t(4780);function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function c({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},54300:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var s=t(60687),a=t(43210),i=t(14163),n=a.forwardRef((e,r)=>(0,s.jsx)(i.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var o=t(24224),d=t(4780);let c=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(n,{ref:t,className:(0,d.cn)(c(),e),...r}));l.displayName=n.displayName},55511:e=>{"use strict";e.exports=require("crypto")},56775:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let c={children:["",{children:["firebase-login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,32926)),"E:\\Marsal\\marsal\\src\\app\\firebase-login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["E:\\Marsal\\marsal\\src\\app\\firebase-login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/firebase-login/page",pathname:"/firebase-login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},61611:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74260:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var s=t(60687),a=t(43210),i=t(16189),n=t(44493),o=t(29523),d=t(89667),c=t(54300),l=t(91821),u=t(41862),p=t(87891),x=t(70663),m=t(61611),h=t(12597),f=t(13861),b=t(39903),g=t(33784);function v(){let[e,r]=(0,a.useState)(""),[t,v]=(0,a.useState)(""),[y,w]=(0,a.useState)(!1),[j,k]=(0,a.useState)(!1),[N,A]=(0,a.useState)(""),[D,q]=(0,a.useState)("checking"),z=(0,i.useRouter)(),C=async()=>{try{q("checking");let e=await (0,g.testFirebaseConnection)();q(e.success?"connected":"disconnected"),e.success||A(`فشل الاتصال بـ Firebase: ${e.message}`)}catch(e){q("disconnected"),A("فشل في اختبار الاتصال بـ Firebase")}},M=async r=>{if(r.preventDefault(),"connected"!==D)return void A("يجب الاتصال بـ Firebase أولاً");if(!e.trim()||!t.trim())return void A("يرجى إدخال اسم المستخدم وكلمة المرور");k(!0),A("");try{console.log("\uD83D\uDD10 محاولة تسجيل الدخول مع Firebase...");let r=await b.firebaseAuthService.login(e.trim(),t);r.success&&r.user?(console.log("✅ تم تسجيل الدخول بنجاح:",r.user.name),localStorage.setItem("currentUser",JSON.stringify(r.user)),localStorage.setItem("isAuthenticated","true"),z.push("/")):A(r.error||"فشل في تسجيل الدخول")}catch(e){console.error("❌ خطأ في تسجيل الدخول:",e),A(e.message||"حدث خطأ غير متوقع")}finally{k(!1)}},P=async e=>{let t={azad95:{username:"azad95",password:"Azad@1995"},manager:{username:"manager",password:"123456"},supervisor:{username:"supervisor",password:"123456"},courier:{username:"courier",password:"123456"}};r(t[e].username),v(t[e].password),setTimeout(()=>{let e=document.querySelector("form");e&&e.dispatchEvent(new Event("submit",{cancelable:!0,bubbles:!0}))},100)};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(m.A,{className:"h-6 w-6 text-white"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"تطبيق مرسال"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"تسجيل الدخول مع Firebase"})]}),(0,s.jsx)(n.Zp,{className:`${(()=>{switch(D){case"checking":return"border-yellow-200 bg-yellow-50";case"connected":return"border-green-200 bg-green-50";case"disconnected":return"border-red-200 bg-red-50"}})()}`,children:(0,s.jsxs)(n.Wu,{className:"pt-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(()=>{switch(D){case"checking":return(0,s.jsx)(u.A,{className:"h-4 w-4 animate-spin"});case"connected":return(0,s.jsx)(p.A,{className:"h-4 w-4 text-green-600"});case"disconnected":return(0,s.jsx)(x.A,{className:"h-4 w-4 text-red-600"})}})(),(0,s.jsx)("span",{className:"text-sm font-medium",children:(()=>{switch(D){case"checking":return"جاري فحص الاتصال بـ Firebase...";case"connected":return"متصل بـ Firebase بنجاح";case"disconnected":return"غير متصل بـ Firebase"}})()})]}),"disconnected"===D&&(0,s.jsx)(o.$,{variant:"outline",size:"sm",onClick:C,className:"mt-2 w-full",children:"\uD83D\uDD04 إعادة المحاولة"})]})}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"تسجيل الدخول"}),(0,s.jsx)(n.BT,{children:"أدخل بيانات الدخول للوصول إلى التطبيق"})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("form",{onSubmit:M,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"username",children:"اسم المستخدم"}),(0,s.jsx)(d.p,{id:"username",type:"text",value:e,onChange:e=>r(e.target.value),placeholder:"أدخل اسم المستخدم",disabled:j||"connected"!==D,className:"text-right"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"password",children:"كلمة المرور"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{id:"password",type:y?"text":"password",value:t,onChange:e=>v(e.target.value),placeholder:"أدخل كلمة المرور",disabled:j||"connected"!==D,className:"text-right pr-10"}),(0,s.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>w(!y),disabled:j,children:y?(0,s.jsx)(h.A,{className:"h-4 w-4"}):(0,s.jsx)(f.A,{className:"h-4 w-4"})})]})]}),N&&(0,s.jsx)(l.Fc,{variant:"destructive",children:(0,s.jsx)(l.TN,{children:N})}),(0,s.jsx)(o.$,{type:"submit",className:"w-full",disabled:j||"connected"!==D,children:j?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"جاري تسجيل الدخول..."]}):"تسجيل الدخول"})]})})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)(n.ZB,{className:"text-sm",children:"تسجيل دخول سريع (للاختبار)"})}),(0,s.jsxs)(n.Wu,{className:"space-y-2",children:[(0,s.jsx)(o.$,{variant:"outline",className:"w-full justify-start bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200",onClick:()=>P("azad95"),disabled:j||"connected"!==D,children:"\uD83D\uDC68‍\uD83D\uDCBC أزاد - المدير الرئيسي (azad95 / Azad@1995)"}),(0,s.jsx)(o.$,{variant:"outline",className:"w-full justify-start",onClick:()=>P("manager"),disabled:j||"connected"!==D,children:"\uD83D\uDC51 مدير النظام (manager / 123456)"}),(0,s.jsx)(o.$,{variant:"outline",className:"w-full justify-start",onClick:()=>P("supervisor"),disabled:j||"connected"!==D,children:"\uD83D\uDC68‍\uD83D\uDCBC المتابع (supervisor / 123456)"}),(0,s.jsx)(o.$,{variant:"outline",className:"w-full justify-start",onClick:()=>P("courier"),disabled:j||"connected"!==D,children:"\uD83D\uDE9A المندوب (courier / 123456)"})]})]}),(0,s.jsxs)("div",{className:"text-center text-sm text-gray-500",children:[(0,s.jsx)("p",{children:"تطبيق مرسال - نظام إدارة التوصيل"}),(0,s.jsx)("p",{children:"مدعوم بـ Firebase"})]})]})})}},77972:e=>{"use strict";e.exports=require("https")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87891:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687);t(43210);var a=t(4780);function i({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98034:(e,r,t)=>{Promise.resolve().then(t.bind(t,74260))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,437,839,690],()=>t(56775));module.exports=s})();