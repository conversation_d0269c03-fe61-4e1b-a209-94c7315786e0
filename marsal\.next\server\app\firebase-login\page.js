(()=>{var e={};e.id=894,e.ids=[894],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>o,cn:()=>i,ps:()=>p,qY:()=>u,r6:()=>d,vv:()=>n,y7:()=>l,zC:()=>c});var s=r(49384),a=r(82348);function i(...e){return(0,a.QP)((0,s.$)(e))}function n(e){return`${e.toLocaleString("ar-IQ")} د.ع`}function o(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function d(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function l(){let e=Date.now().toString().slice(-6),t=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return`MRS${e}${t}`}function c(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function u(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function p(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14163:(e,t,r)=>{"use strict";r.d(t,{hO:()=>d,sG:()=>o});var s=r(43210),a=r(51215),i=r(8730),n=r(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),a=s.forwardRef((e,s)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?r:t,{...i,ref:s})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function d(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(60687);r(43210);var a=r(8730),i=r(24224),n=r(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:i=!1,...d}){let l=i?a.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:r,className:e})),...d})}},32926:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\firebase-login\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34986:(e,t,r)=>{Promise.resolve().then(r.bind(r,32926))},37366:e=>{"use strict";e.exports=require("dns")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var s=r(60687),a=r(43210),i=r(14163),n=a.forwardRef((e,t)=>(0,s.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=r(24224),d=r(4780);let l=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n,{ref:r,className:(0,d.cn)(l(),e),...t}));c.displayName=n.displayName},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56775:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["firebase-login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32926)),"E:\\Marsal\\marsal\\src\\app\\firebase-login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\Marsal\\marsal\\src\\app\\firebase-login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/firebase-login/page",pathname:"/firebase-login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},61611:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70663:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74260:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(60687),a=r(43210),i=r(16189),n=r(44493),o=r(29523),d=r(89667),l=r(54300),c=r(91821),u=r(41862),p=r(87891),x=r(70663),m=r(61611),g=r(12597),f=r(13861),h=r(39903),v=r(33784);function b(){let[e,t]=(0,a.useState)(""),[r,b]=(0,a.useState)(""),[y,w]=(0,a.useState)(!1),[j,k]=(0,a.useState)(!1),[N,A]=(0,a.useState)(""),[D,M]=(0,a.useState)("checking"),q=(0,i.useRouter)(),z=async()=>{try{M("checking");let e=await (0,v.testFirebaseConnection)();M(e.success?"connected":"disconnected"),e.success||A(`فشل الاتصال بـ Firebase: ${e.message}`)}catch(e){M("disconnected"),A("فشل في اختبار الاتصال بـ Firebase")}},_=async t=>{if(t.preventDefault(),"connected"!==D)return void A("يجب الاتصال بـ Firebase أولاً");if(!e.trim()||!r.trim())return void A("يرجى إدخال اسم المستخدم وكلمة المرور");k(!0),A("");try{console.log("\uD83D\uDD10 محاولة تسجيل الدخول مع Firebase...");let t=await h.firebaseAuthService.login(e.trim(),r);t.success&&t.user?(console.log("✅ تم تسجيل الدخول بنجاح:",t.user.name),localStorage.setItem("currentUser",JSON.stringify(t.user)),localStorage.setItem("isAuthenticated","true"),q.push("/")):A(t.error||"فشل في تسجيل الدخول")}catch(e){console.error("❌ خطأ في تسجيل الدخول:",e),A(e.message||"حدث خطأ غير متوقع")}finally{k(!1)}},C=async e=>{let r={azad95:{username:"azad95",password:"Azad@1995"},manager:{username:"manager",password:"123456"},supervisor:{username:"supervisor",password:"123456"},courier:{username:"courier",password:"123456"}};t(r[e].username),b(r[e].password),setTimeout(()=>{let e=document.querySelector("form");e&&e.dispatchEvent(new Event("submit",{cancelable:!0,bubbles:!0}))},100)};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(m.A,{className:"h-6 w-6 text-white"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"تطبيق مرسال"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"تسجيل الدخول مع Firebase"})]}),(0,s.jsx)(n.Zp,{className:`${(()=>{switch(D){case"checking":return"border-yellow-200 bg-yellow-50";case"connected":return"border-green-200 bg-green-50";case"disconnected":return"border-red-200 bg-red-50"}})()}`,children:(0,s.jsxs)(n.Wu,{className:"pt-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(()=>{switch(D){case"checking":return(0,s.jsx)(u.A,{className:"h-4 w-4 animate-spin"});case"connected":return(0,s.jsx)(p.A,{className:"h-4 w-4 text-green-600"});case"disconnected":return(0,s.jsx)(x.A,{className:"h-4 w-4 text-red-600"})}})(),(0,s.jsx)("span",{className:"text-sm font-medium",children:(()=>{switch(D){case"checking":return"جاري فحص الاتصال بـ Firebase...";case"connected":return"متصل بـ Firebase بنجاح";case"disconnected":return"غير متصل بـ Firebase"}})()})]}),"disconnected"===D&&(0,s.jsx)(o.$,{variant:"outline",size:"sm",onClick:z,className:"mt-2 w-full",children:"\uD83D\uDD04 إعادة المحاولة"})]})}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"تسجيل الدخول"}),(0,s.jsx)(n.BT,{children:"أدخل بيانات الدخول للوصول إلى التطبيق"})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("form",{onSubmit:_,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"username",children:"اسم المستخدم"}),(0,s.jsx)(d.p,{id:"username",type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"أدخل اسم المستخدم",disabled:j||"connected"!==D,className:"text-right"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"password",children:"كلمة المرور"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{id:"password",type:y?"text":"password",value:r,onChange:e=>b(e.target.value),placeholder:"أدخل كلمة المرور",disabled:j||"connected"!==D,className:"text-right pr-10"}),(0,s.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>w(!y),disabled:j,children:y?(0,s.jsx)(g.A,{className:"h-4 w-4"}):(0,s.jsx)(f.A,{className:"h-4 w-4"})})]})]}),N&&(0,s.jsx)(c.Fc,{variant:"destructive",children:(0,s.jsx)(c.TN,{children:N})}),(0,s.jsx)(o.$,{type:"submit",className:"w-full",disabled:j||"connected"!==D,children:j?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"جاري تسجيل الدخول..."]}):"تسجيل الدخول"})]})})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)(n.ZB,{className:"text-sm",children:"تسجيل دخول سريع (للاختبار)"})}),(0,s.jsxs)(n.Wu,{className:"space-y-2",children:[(0,s.jsx)(o.$,{variant:"outline",className:"w-full justify-start bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200",onClick:()=>C("azad95"),disabled:j||"connected"!==D,children:"\uD83D\uDC68‍\uD83D\uDCBC أزاد - المدير الرئيسي (azad95 / Azad@1995)"}),(0,s.jsx)(o.$,{variant:"outline",className:"w-full justify-start",onClick:()=>C("manager"),disabled:j||"connected"!==D,children:"\uD83D\uDC51 مدير النظام (manager / 123456)"}),(0,s.jsx)(o.$,{variant:"outline",className:"w-full justify-start",onClick:()=>C("supervisor"),disabled:j||"connected"!==D,children:"\uD83D\uDC68‍\uD83D\uDCBC المتابع (supervisor / 123456)"}),(0,s.jsx)(o.$,{variant:"outline",className:"w-full justify-start",onClick:()=>C("courier"),disabled:j||"connected"!==D,children:"\uD83D\uDE9A المندوب (courier / 123456)"})]})]}),(0,s.jsxs)("div",{className:"text-center text-sm text-gray-500",children:[(0,s.jsx)("p",{children:"تطبيق مرسال - نظام إدارة التوصيل"}),(0,s.jsx)("p",{children:"مدعوم بـ Firebase"})]})]})})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87891:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91645:e=>{"use strict";e.exports=require("net")},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>l});var s=r(60687),a=r(43210),i=r(24224),n=r(4780);let o=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=a.forwardRef(({className:e,variant:t,...r},a)=>(0,s.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(o({variant:t}),e),...r}));d.displayName="Alert",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h5",{ref:r,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...t}));l.displayName="AlertDescription"},94735:e=>{"use strict";e.exports=require("events")},98034:(e,t,r)=>{Promise.resolve().then(r.bind(r,74260))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,863,860,610],()=>r(56775));module.exports=s})();