(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10957:(e,r,t)=>{"use strict";t.d(r,{BC:()=>n,_m:()=>a});let s={manager:["orders","dispatch","returns","accounting","statistics","archive","users","import-export","notifications","settings"],supervisor:["orders","statistics","archive","users","notifications","settings"],courier:["orders","archive","notifications","statistics"]};function a(e,r){return s[e]?.includes(r)||!1}let i=[{id:"orders",href:"/orders",label:"إدارة الطلبات",description:"عرض ومتابعة وتعديل الطلبات",icon:"Package",color:"from-blue-500 to-blue-600",roles:["manager","supervisor","courier"]},{id:"dispatch",href:"/dispatch",label:"إسناد الطلبات",description:"توزيع الطلبات على المندوبين",icon:"TruckIcon",color:"from-green-500 to-emerald-600",roles:["manager"]},{id:"returns",href:"/returns",label:"إدارة الرواجع",description:"استلام الطلبات الراجعة",icon:"RotateCcw",color:"from-orange-500 to-amber-600",roles:["manager"]},{id:"accounting",href:"/accounting",label:"المحاسبة",description:"التسويات المالية مع المندوبين",icon:"Calculator",color:"from-purple-500 to-violet-600",roles:["manager"]},{id:"statistics",href:"/statistics",label:"الإحصائيات",description:"التقارير والإحصائيات التفصيلية",icon:"BarChart3",color:"from-cyan-500 to-blue-600",roles:["manager","supervisor"]},{id:"archive",href:"/archive",label:"الأرشيف",description:"السجلات المنتهية",icon:"Archive",color:"from-gray-500 to-slate-600",roles:["manager","supervisor","courier"]},{id:"users",href:"/users",label:"إدارة الموظفين",description:"إدارة حسابات المستخدمين",icon:"Users",color:"from-indigo-500 to-purple-600",roles:["manager","supervisor"]},{id:"import-export",href:"/import-export",label:"استيراد وتصدير",description:"عمليات مجمعة على الطلبات",icon:"Upload",color:"from-teal-500 to-cyan-600",roles:["manager"]},{id:"notifications",href:"/notifications",label:"الإشعارات",description:"التنبيهات والتحديثات",icon:"Bell",color:"from-yellow-500 to-orange-600",roles:["manager","supervisor","courier"]},{id:"settings",href:"/settings",label:"الإعدادات",description:"تخصيص التطبيق",icon:"Settings",color:"from-pink-500 to-rose-600",roles:["manager","supervisor","courier"]}];function n(e){return i.filter(r=>r.roles.includes(e))}},11997:e=>{"use strict";e.exports=require("punycode")},16511:(e,r,t)=>{Promise.resolve().then(t.bind(t,96619))},17403:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21204)),"E:\\Marsal\\marsal\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\Marsal\\marsal\\src\\app\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36464:(e,r,t)=>{"use strict";t.d(r,{Ys:()=>s,rB:()=>a});let s=[{id:"1",trackingNumber:"MRS001",senderName:"أحمد محمد",senderPhone:"07901234567",senderAddress:"بغداد - الكرادة - شارع الرشيد",recipientName:"فاطمة علي",recipientPhone:"07801234567",recipientAddress:"بغداد - الجادرية - قرب الجامعة",amount:5e4,status:"pending",notes:"يرجى التسليم في المساء",createdAt:new Date("2024-01-15T10:30:00"),updatedAt:new Date("2024-01-15T10:30:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-15T10:30:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"}]},{id:"2",trackingNumber:"MRS002",senderName:"سارة أحمد",senderPhone:"07701234567",senderAddress:"بغداد - المنصور - شارع الأميرات",recipientName:"محمد حسن",recipientPhone:"07601234567",recipientAddress:"بغداد - الكاظمية - شارع الإمام",amount:75e3,status:"assigned",assignedTo:"courier1",createdAt:new Date("2024-01-15T09:15:00"),updatedAt:new Date("2024-01-15T11:00:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-15T09:15:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"},{status:"assigned",timestamp:new Date("2024-01-15T11:00:00"),updatedBy:"dispatcher1",notes:"تم إسناد الطلب للمندوب"}]},{id:"3",trackingNumber:"MRS003",senderName:"خالد عبدالله",senderPhone:"07501234567",senderAddress:"بغداد - الدورة - شارع الصناعة",recipientName:"زينب محمد",recipientPhone:"07401234567",recipientAddress:"بغداد - الشعلة - قرب المجمع",amount:12e4,status:"delivered",assignedTo:"courier2",deliveredAt:new Date("2024-01-14T16:30:00"),createdAt:new Date("2024-01-14T08:00:00"),updatedAt:new Date("2024-01-14T16:30:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-14T08:00:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"},{status:"assigned",timestamp:new Date("2024-01-14T09:00:00"),updatedBy:"dispatcher1",notes:"تم إسناد الطلب للمندوب"},{status:"delivered",timestamp:new Date("2024-01-14T16:30:00"),updatedBy:"courier2",notes:"تم تسليم الطلب بنجاح"}]}],a=[{id:"admin1",email:"<EMAIL>",name:"أحمد المدير",username:"admin",phone:"07901234567",role:"admin",isActive:!0,createdAt:new Date("2024-01-01T00:00:00"),updatedAt:new Date("2024-01-01T00:00:00")},{id:"courier1",email:"<EMAIL>",name:"علي حسين",username:"courier1",phone:"07801234567",role:"courier",isActive:!0,createdAt:new Date("2024-01-02T00:00:00"),updatedAt:new Date("2024-01-02T00:00:00")},{id:"courier2",email:"<EMAIL>",name:"حسام محمد",username:"courier2",phone:"07701234567",role:"courier",isActive:!0,createdAt:new Date("2024-01-03T00:00:00"),updatedAt:new Date("2024-01-03T00:00:00")},{id:"dispatcher1",email:"<EMAIL>",name:"سارة الموزعة",username:"dispatcher",phone:"07601234567",role:"dispatcher",isActive:!0,createdAt:new Date("2024-01-04T00:00:00"),updatedAt:new Date("2024-01-04T00:00:00")}]},48340:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},51361:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86247:(e,r,t)=>{Promise.resolve().then(t.bind(t,21204))},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687);t(43210);var a=t(4780);function i({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96619:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>$});var s=t(60687),a=t(43210),i=t(44493),n=t(37730),o=t(19080),l=t(5336),d=t(48730),c=t(13943),u=t(59402),m=t(41312),p=t(88059);function x({title:e,value:r,icon:t,color:a,trend:n}){return(0,s.jsxs)(i.Zp,{className:"hover:shadow-xl transition-all duration-300 hover:scale-105 glass border-0 overflow-hidden group relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br opacity-5 group-hover:opacity-10 transition-opacity"}),(0,s.jsxs)(i.Wu,{className:"p-6 relative",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e}),(0,s.jsx)("p",{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent",children:r}),n&&(0,s.jsxs)("div",{className:`flex items-center gap-1 text-sm font-medium ${n.isPositive?"text-green-600":"text-red-600"}`,children:[(0,s.jsx)("span",{className:"text-lg",children:n.isPositive?"\uD83D\uDCC8":"\uD83D\uDCC9"}),Math.abs(n.value),"% من الأسبوع الماضي"]})]}),(0,s.jsx)("div",{className:`p-4 rounded-2xl bg-gradient-to-r ${a} shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110`,children:(0,s.jsx)(t,{className:"h-8 w-8 text-white"})})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"})]})]})}function h({stats:e}){let r=e||{totalOrders:1234,deliveredOrders:987,returnedOrders:45,pendingOrders:156,totalAmount:125e6,totalCommission:987e3,activeCouriers:12,todayOrders:89,completionRate:85.2},t=[{title:"إجمالي الطلبات",value:r.totalOrders.toLocaleString(),icon:o.A,color:"from-blue-500 to-blue-600",trend:{value:12,isPositive:!0}},{title:"الطلبات المسلمة",value:r.deliveredOrders.toLocaleString(),icon:l.A,color:"from-green-500 to-emerald-600",trend:{value:8,isPositive:!0}},{title:"الطلبات المعلقة",value:r.pendingOrders.toLocaleString(),icon:d.A,color:"from-orange-500 to-amber-600",trend:{value:3,isPositive:!1}},{title:"الرواجع",value:r.returnedOrders.toLocaleString(),icon:c.A,color:"from-red-500 to-rose-600",trend:{value:2,isPositive:!1}},{title:"المبالغ المحصلة",value:`${(r.totalAmount/1e6).toFixed(1)}M د.ع`,icon:u.A,color:"from-purple-500 to-violet-600",trend:{value:15,isPositive:!0}},{title:"المندوبين النشطين",value:(r.activeCouriers||0).toLocaleString(),icon:m.A,color:"text-indigo-600",trend:{value:1,isPositive:!0}},{title:"طلبات اليوم",value:(r.todayOrders||0).toLocaleString(),icon:p.A,color:"text-cyan-600",trend:{value:25,isPositive:!0}},{title:"معدل الإنجاز",value:`${r.completionRate||0}%`,icon:l.A,color:"text-emerald-600",trend:{value:2.1,isPositive:!0}}];return(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:t.map((e,r)=>(0,s.jsx)(x,{title:e.title,value:e.value,icon:e.icon,color:e.color,trend:e.trend},r))})}var g=t(29523),v=t(89667),f=t(99270),b=t(51361),j=t(58869),N=t(48340),y=t(85814),w=t.n(y),A=t(16189);let D=[{id:"1",trackingNumber:"MRS001",recipientName:"فاطمة علي",recipientPhone:"07801234567",status:"pending",amount:5e4},{id:"2",trackingNumber:"MRS002",recipientName:"محمد حسن",recipientPhone:"07901234567",status:"delivered",amount:75e3}],k={pending:"في الانتظار",assigned:"مسند",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي"},O={pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100"};function P(){let e=(0,A.useRouter)(),[r,t]=(0,a.useState)(""),[n,l]=(0,a.useState)([]),[d,c]=(0,a.useState)(!1),[u,m]=(0,a.useState)(!1),p=async e=>{e.preventDefault(),r.trim()&&(c(!0),m(!0),setTimeout(()=>{l(D.filter(e=>e.trackingNumber.toLowerCase().includes(r.toLowerCase())||e.recipientPhone.includes(r)||e.recipientName.includes(r))),c(!1)},500))};return(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"h-5 w-5"}),"البحث السريع"]}),(0,s.jsx)(i.BT,{children:"ابحث عن طلب باستخدام رقم الوصل أو هاتف المستلم أو اسم المستلم"})]}),(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[(0,s.jsxs)("form",{onSubmit:p,className:"flex gap-2",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[(0,s.jsx)(v.p,{placeholder:"رقم الوصل أو هاتف المستلم أو اسم المستلم...",value:r,onChange:e=>t(e.target.value),onKeyPress:t=>{if("Enter"===t.key){if(t.preventDefault(),1===n.length)e.push(`/orders/${n[0].id}`);else if(r.trim()){let s=D.find(e=>e.trackingNumber.toLowerCase()===r.trim().toLowerCase());s?e.push(`/orders/${s.id}`):p(t)}}},className:"pr-10"}),(0,s.jsx)(g.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>alert("سيتم فتح الكاميرا لقراءة الباركود قريباً"),children:(0,s.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,s.jsx)(g.$,{type:"submit",disabled:d,children:d?"جاري البحث...":"بحث"}),u&&(0,s.jsx)(g.$,{variant:"outline",onClick:()=>{t(""),l([]),m(!1)},children:"مسح"})]}),u&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"font-medium",children:"نتائج البحث"}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:[n.length," نتيجة"]})]}),0===n.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,s.jsxs)("p",{children:['لا توجد نتائج للبحث "',r,'"']})]}):(0,s.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:n.map(e=>(0,s.jsx)(w(),{href:`/orders/${e.id}`,children:(0,s.jsx)("div",{className:"p-3 border border-border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 text-primary"}),(0,s.jsx)("span",{className:"font-medium",children:e.trackingNumber}),(0,s.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${O[e.status]}`,children:k[e.status]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(j.A,{className:"h-3 w-3"}),e.recipientName]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(N.A,{className:"h-3 w-3"}),e.recipientPhone]})]})]}),(0,s.jsx)("div",{className:"text-left",children:(0,s.jsxs)("p",{className:"font-semibold",children:[e.amount.toLocaleString()," د.ع"]})})]})})},e.id))})]})]})]})}var T=t(67958),C=t(5475);!function(){var e=Error("Cannot find module 'firebase/app'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'firebase/firestore'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'firebase/auth'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'firebase/storage'");throw e.code="MODULE_NOT_FOUND",e}();let _={apiKey:C.vQ.firebase.apiKey||"AIzaSyDemoKeyForMarsalDeliveryApp123456789",authDomain:C.vQ.firebase.authDomain||"marsal-delivery.firebaseapp.com",projectId:C.vQ.firebase.projectId||"marsal-delivery-system",storageBucket:C.vQ.firebase.storageBucket||"marsal-delivery.appspot.com",messagingSenderId:C.vQ.firebase.messagingSenderId||"987654321",appId:C.vQ.firebase.appId||"1:987654321:web:abc123def456ghi789"},E=null;if(!C.vQ.demo.skipFirebase)try{E=Object(function(){var e=Error("Cannot find module 'firebase/app'");throw e.code="MODULE_NOT_FOUND",e}())(_),Object(function(){var e=Error("Cannot find module 'firebase/firestore'");throw e.code="MODULE_NOT_FOUND",e}())(E),Object(function(){var e=Error("Cannot find module 'firebase/auth'");throw e.code="MODULE_NOT_FOUND",e}())(E),Object(function(){var e=Error("Cannot find module 'firebase/storage'");throw e.code="MODULE_NOT_FOUND",e}())(E)}catch(e){console.warn("Firebase initialization failed, running in demo mode:",e)}t(36464),function(){var e=Error("Cannot find module 'firebase/firestore'");throw e.code="MODULE_NOT_FOUND",e}();var M=t(10957),S=t(53411),L=t(77026),U=t(16023),B=t(97051),q=t(84027),R=t(96882);let F={Package:o.A,TruckIcon:p.A,RotateCcw:c.A,Calculator:u.A,BarChart3:S.A,Archive:L.A,Users:m.A,Upload:U.A,Bell:B.A,Settings:q.A,Info:R.A};function $(){let{user:e}=(0,T.A)(),[r,t]=(0,a.useState)(null),[l,d]=(0,a.useState)(!0),c=e?(0,M.BC)(e.role):[];return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 p-4 md:p-6 lg:p-8 animated-bg",dir:"rtl",children:[(0,s.jsx)(n.A,{}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,s.jsxs)("div",{className:"text-center space-y-6 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl blur-3xl"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl shadow-2xl mb-6 shadow-glow animate-pulse",children:(0,s.jsx)(o.A,{className:"h-12 w-12 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-4 animate-pulse",children:"مرسال"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-xl max-w-3xl mx-auto leading-relaxed glass p-6 rounded-2xl",children:"\uD83D\uDE80 نظام إدارة التوصيل السريع والموثوق - حلول متطورة لإدارة الطلبات والتوصيل بأحدث التقنيات"})]})]})]}),(0,s.jsx)(P,{}),C.vQ.demo.enabled&&C.vQ.demo.showDemoNotice&&(0,s.jsx)("div",{className:"mt-8",children:(0,s.jsx)(i.Zp,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 shadow-lg",children:(0,s.jsx)(i.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center",children:(0,s.jsx)(R.A,{className:"h-6 w-6 text-white"})})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"\uD83C\uDFAF الوضع التجريبي مُفعل"}),(0,s.jsxs)("p",{className:"text-blue-700 text-sm leading-relaxed",children:["أنت تستخدم النسخة التجريبية من نظام مرسال. جميع البيانات تجريبية ولن يتم حفظها بشكل دائم.",(0,s.jsx)("br",{}),(0,s.jsx)("strong",{children:"بيانات الدخول:"})," manager / 123456 أو supervisor / 123456 أو courier / 123456"]})]})]})})})}),r&&(0,s.jsx)("div",{className:"mt-8",children:(0,s.jsx)(h,{stats:r})}),e&&c.length>0?(0,s.jsxs)("div",{className:"mt-12",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-800 dark:text-white mb-2",children:"أقسام النظام"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"اختر القسم الذي تريد الوصول إليه"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:c.map(e=>{let r=F[e.icon];return(0,s.jsx)(w(),{href:e.href,children:(0,s.jsxs)(i.Zp,{className:"group card-hover cursor-pointer border-2 hover:border-blue-300 glass gpu-accelerated",children:[(0,s.jsxs)(i.aR,{className:"text-center pb-4",children:[(0,s.jsx)("div",{className:`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ${e.color} shadow-lg mb-4 group-hover:scale-110 transition-transform duration-300 will-change-transform`,children:r&&(0,s.jsx)(r,{className:"h-8 w-8 text-white"})}),(0,s.jsx)(i.ZB,{className:"text-lg font-bold text-gray-800 dark:text-white group-hover:text-blue-600 transition-colors",children:e.label})]}),(0,s.jsx)(i.Wu,{className:"text-center pt-0",children:(0,s.jsx)(i.BT,{className:"text-sm text-gray-600 dark:text-gray-300 leading-relaxed",children:e.description})})]})},e.id)})})]}):null,(0,s.jsx)("div",{className:"text-center mt-12",children:(0,s.jsxs)(i.Zp,{className:"max-w-2xl mx-auto glass border-2 border-blue-200",children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsxs)(i.ZB,{className:"text-2xl text-blue-600",children:["مرحباً، ",e?.name||"مستخدم"]}),(0,s.jsx)(i.BT,{className:"text-lg",children:e?.role==="manager"?"مدير النظام":e?.role==="supervisor"?"متابع":"مندوب"})]}),(0,s.jsxs)(i.Wu,{children:[(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 leading-relaxed",children:"مرحباً بك في نظام مرسال لإدارة التوصيل. يمكنك استخدام القائمة الجانبية للتنقل بين أقسام النظام المختلفة."}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-semibold text-blue-800 dark:text-blue-200 mb-2",children:"معلومات سريعة:"}),(0,s.jsxs)("div",{className:"text-sm text-blue-700 dark:text-blue-300 space-y-1",children:[(0,s.jsxs)("div",{children:["\uD83D\uDCC5 التاريخ: ",new Date().toLocaleDateString("ar-SA")]}),(0,s.jsxs)("div",{children:["⏰ الوقت: ",new Date().toLocaleTimeString("ar-SA")]}),(0,s.jsxs)("div",{children:["\uD83D\uDC64 المستخدم: ",e?.name||"غير محدد"]}),(0,s.jsxs)("div",{children:["\uD83D\uDD11 الدور: ",e?.role==="manager"?"مدير":e?.role==="supervisor"?"متابع":"مندوب"]})]})]})]})]})})]})]})}},96882:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},99270:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,773,139,814,451,610,809],()=>t(17403));module.exports=s})();