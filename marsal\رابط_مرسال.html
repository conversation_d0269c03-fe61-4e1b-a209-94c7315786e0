<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رابط مرسال - الوصول السريع</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Tajawal', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .container {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            padding: 30px;
            text-align: center;
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        h1 {
            color: #3B82F6;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .description {
            color: #64748b;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .links {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .link-button {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 15px 20px;
            color: #334155;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .link-button:hover {
            background-color: #3B82F6;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
        }
        
        .link-button.primary {
            background-color: #3B82F6;
            color: white;
        }
        
        .link-button.primary:hover {
            background-color: #2563eb;
        }
        
        .link-icon {
            font-size: 24px;
            margin-left: 10px;
        }
        
        .link-text {
            flex: 1;
            text-align: right;
        }
        
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status.online {
            background-color: #dcfce7;
            color: #166534;
        }
        
        .footer {
            margin-top: 30px;
            color: #94a3b8;
            font-size: 14px;
        }
        
        .loading {
            display: none;
            margin-top: 20px;
            text-align: center;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(59, 130, 246, 0.1);
            border-radius: 50%;
            border-top-color: #3B82F6;
            display: inline-block;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀</div>
        <h1>رابط مرسال السريع</h1>
        <p class="description">
            اختر طريقة الوصول المناسبة لك للدخول إلى نظام مرسال لإدارة التوصيل
        </p>
        
        <div class="links">
            <a href="dist/web/index.html" class="link-button primary" onclick="showLoading()">
                <span class="link-icon">🌟</span>
                <span class="link-text">تشغيل التطبيق مباشرة</span>
                <span class="status online">متاح</span>
            </a>
            
            <a href="out/index.html" class="link-button" onclick="showLoading()">
                <span class="link-icon">📱</span>
                <span class="link-text">النسخة الأصلية</span>
                <span class="status online">متاح</span>
            </a>
            
            <a href="dist/اختبار_قاعدة_البيانات.html" class="link-button" onclick="showLoading()">
                <span class="link-icon">🔧</span>
                <span class="link-text">اختبار قاعدة البيانات</span>
                <span class="status online">متاح</span>
            </a>
            
            <a href="#" class="link-button" onclick="openAllOptions(); return false;">
                <span class="link-icon">📋</span>
                <span class="link-text">فتح جميع الخيارات</span>
                <span class="status online">متاح</span>
            </a>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p style="margin-top: 10px;">جاري تحميل التطبيق...</p>
        </div>
        
        <div class="footer">
            <p>بيانات الدخول: manager / 123456</p>
            <p>آخر تحديث: <span id="lastUpdate"></span></p>
        </div>
    </div>
    
    <script>
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            setTimeout(function() {
                document.getElementById('loading').style.display = 'none';
            }, 3000);
        }
        
        function openAllOptions() {
            showLoading();
            
            // فتح التطبيق من المجلد الرئيسي
            window.open('dist/web/index.html', '_blank');
            
            // فتح النسخة الأصلية
            setTimeout(function() {
                window.open('out/index.html', '_blank');
            }, 1000);
            
            // فتح صفحة اختبار قاعدة البيانات
            setTimeout(function() {
                window.open('dist/اختبار_قاعدة_البيانات.html', '_blank');
            }, 2000);
        }
        
        // تحديث تاريخ آخر تحديث
        document.getElementById('lastUpdate').textContent = new Date().toLocaleDateString('ar-SA');
        
        // فحص حالة الملفات عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('🚀 رابط مرسال السريع جاهز للاستخدام');
            
            // رسالة ترحيب
            setTimeout(function() {
                console.log('🎉 مرحباً بك في نظام مرسال لإدارة التوصيل!');
                console.log('📱 التطبيق جاهز للاستخدام');
                console.log('🔑 استخدم: manager/123456 للدخول كمدير');
            }, 1000);
        });
    </script>
</body>
</html>
