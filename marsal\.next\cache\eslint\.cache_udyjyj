[{"E:\\Marsal\\marsal\\src\\app\\accounting\\page.tsx": "1", "E:\\Marsal\\marsal\\src\\app\\archive\\page.tsx": "2", "E:\\Marsal\\marsal\\src\\app\\dispatch\\page.tsx": "3", "E:\\Marsal\\marsal\\src\\app\\import-export\\page.tsx": "4", "E:\\Marsal\\marsal\\src\\app\\layout.tsx": "5", "E:\\Marsal\\marsal\\src\\app\\login\\page.tsx": "6", "E:\\Marsal\\marsal\\src\\app\\notifications\\page.tsx": "7", "E:\\Marsal\\marsal\\src\\app\\orders\\new\\page.tsx": "8", "E:\\Marsal\\marsal\\src\\app\\orders\\page.tsx": "9", "E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\edit\\page.tsx": "10", "E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\edit\\static-params.ts": "11", "E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\layout.tsx": "12", "E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\page.tsx": "13", "E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\status\\page.tsx": "14", "E:\\Marsal\\marsal\\src\\app\\page.tsx": "15", "E:\\Marsal\\marsal\\src\\app\\returns\\page.tsx": "16", "E:\\Marsal\\marsal\\src\\app\\settings\\page.tsx": "17", "E:\\Marsal\\marsal\\src\\app\\statistics\\page.tsx": "18", "E:\\Marsal\\marsal\\src\\app\\users\\new\\page.tsx": "19", "E:\\Marsal\\marsal\\src\\app\\users\\page.tsx": "20", "E:\\Marsal\\marsal\\src\\components\\auth-provider.tsx": "21", "E:\\Marsal\\marsal\\src\\components\\barcode-scanner.tsx": "22", "E:\\Marsal\\marsal\\src\\components\\dashboard-stats.tsx": "23", "E:\\Marsal\\marsal\\src\\components\\header.tsx": "24", "E:\\Marsal\\marsal\\src\\components\\navigation.tsx": "25", "E:\\Marsal\\marsal\\src\\components\\notifications.tsx": "26", "E:\\Marsal\\marsal\\src\\components\\order-card.tsx": "27", "E:\\Marsal\\marsal\\src\\components\\print-receipt.tsx": "28", "E:\\Marsal\\marsal\\src\\components\\protected-route.tsx": "29", "E:\\Marsal\\marsal\\src\\components\\quick-search.tsx": "30", "E:\\Marsal\\marsal\\src\\components\\receipt-template.tsx": "31", "E:\\Marsal\\marsal\\src\\components\\role-guard.tsx": "32", "E:\\Marsal\\marsal\\src\\components\\status-update.tsx": "33", "E:\\Marsal\\marsal\\src\\components\\theme-provider.tsx": "34", "E:\\Marsal\\marsal\\src\\components\\theme-selector.tsx": "35", "E:\\Marsal\\marsal\\src\\components\\ticket-system.tsx": "36", "E:\\Marsal\\marsal\\src\\components\\ui\\alert.tsx": "37", "E:\\Marsal\\marsal\\src\\components\\ui\\badge.tsx": "38", "E:\\Marsal\\marsal\\src\\components\\ui\\button.tsx": "39", "E:\\Marsal\\marsal\\src\\components\\ui\\card.tsx": "40", "E:\\Marsal\\marsal\\src\\components\\ui\\input.tsx": "41", "E:\\Marsal\\marsal\\src\\components\\ui\\label.tsx": "42", "E:\\Marsal\\marsal\\src\\components\\ui\\textarea.tsx": "43", "E:\\Marsal\\marsal\\src\\lib\\auth.ts": "44", "E:\\Marsal\\marsal\\src\\lib\\config.ts": "45", "E:\\Marsal\\marsal\\src\\lib\\constants.ts": "46", "E:\\Marsal\\marsal\\src\\lib\\database.ts": "47", "E:\\Marsal\\marsal\\src\\lib\\firebase.ts": "48", "E:\\Marsal\\marsal\\src\\lib\\firestore.ts": "49", "E:\\Marsal\\marsal\\src\\lib\\iraq-locations.ts": "50", "E:\\Marsal\\marsal\\src\\lib\\mock-data.ts": "51", "E:\\Marsal\\marsal\\src\\lib\\permissions.ts": "52", "E:\\Marsal\\marsal\\src\\lib\\statistics.ts": "53", "E:\\Marsal\\marsal\\src\\lib\\supabase.ts": "54", "E:\\Marsal\\marsal\\src\\lib\\utils.ts": "55", "E:\\Marsal\\marsal\\src\\types\\index.ts": "56", "E:\\Marsal\\marsal\\src\\types\\locations.ts": "57", "E:\\Marsal\\marsal\\src\\types\\order.ts": "58", "E:\\Marsal\\marsal\\src\\types\\roles.ts": "59"}, {"size": 31239, "mtime": 1751497790854, "results": "60", "hashOfConfig": "61"}, {"size": 21302, "mtime": 1751676389588, "results": "62", "hashOfConfig": "61"}, {"size": 20361, "mtime": 1751497641669, "results": "63", "hashOfConfig": "61"}, {"size": 19478, "mtime": 1751497863813, "results": "64", "hashOfConfig": "61"}, {"size": 1137, "mtime": 1751187543543, "results": "65", "hashOfConfig": "61"}, {"size": 8487, "mtime": 1751675218068, "results": "66", "hashOfConfig": "61"}, {"size": 12182, "mtime": 1751676341677, "results": "67", "hashOfConfig": "61"}, {"size": 7118, "mtime": 1751156218559, "results": "68", "hashOfConfig": "61"}, {"size": 10875, "mtime": 1751691184608, "results": "69", "hashOfConfig": "61"}, {"size": 12305, "mtime": 1751238710775, "results": "70", "hashOfConfig": "61"}, {"size": 116, "mtime": 1751238568751, "results": "71", "hashOfConfig": "61"}, {"size": 227, "mtime": 1751238622001, "results": "72", "hashOfConfig": "61"}, {"size": 17895, "mtime": 1751565087902, "results": "73", "hashOfConfig": "61"}, {"size": 17333, "mtime": 1751407760598, "results": "74", "hashOfConfig": "61"}, {"size": 8736, "mtime": 1751620075455, "results": "75", "hashOfConfig": "61"}, {"size": 13820, "mtime": 1751497716439, "results": "76", "hashOfConfig": "61"}, {"size": 31160, "mtime": 1751705874317, "results": "77", "hashOfConfig": "61"}, {"size": 24353, "mtime": 1751676537821, "results": "78", "hashOfConfig": "61"}, {"size": 12413, "mtime": 1751534085630, "results": "79", "hashOfConfig": "61"}, {"size": 11797, "mtime": 1751564146430, "results": "80", "hashOfConfig": "61"}, {"size": 8043, "mtime": 1751838280793, "results": "81", "hashOfConfig": "61"}, {"size": 6955, "mtime": 1751564237930, "results": "82", "hashOfConfig": "61"}, {"size": 4930, "mtime": 1751185607705, "results": "83", "hashOfConfig": "61"}, {"size": 10209, "mtime": 1751675227489, "results": "84", "hashOfConfig": "61"}, {"size": 5187, "mtime": 1751675190894, "results": "85", "hashOfConfig": "61"}, {"size": 10460, "mtime": 1751165449734, "results": "86", "hashOfConfig": "61"}, {"size": 7429, "mtime": 1751407899022, "results": "87", "hashOfConfig": "61"}, {"size": 3987, "mtime": 1751679476716, "results": "88", "hashOfConfig": "61"}, {"size": 2005, "mtime": 1751163798349, "results": "89", "hashOfConfig": "61"}, {"size": 7125, "mtime": 1751677504840, "results": "90", "hashOfConfig": "61"}, {"size": 7382, "mtime": 1751919218455, "results": "91", "hashOfConfig": "61"}, {"size": 3926, "mtime": 1751497532860, "results": "92", "hashOfConfig": "61"}, {"size": 14546, "mtime": 1751565047702, "results": "93", "hashOfConfig": "61"}, {"size": 444, "mtime": 1751565536109, "results": "94", "hashOfConfig": "61"}, {"size": 7132, "mtime": 1751163078200, "results": "95", "hashOfConfig": "61"}, {"size": 15231, "mtime": 1751160799201, "results": "96", "hashOfConfig": "61"}, {"size": 1584, "mtime": 1751398912590, "results": "97", "hashOfConfig": "61"}, {"size": 1401, "mtime": 1751160638444, "results": "98", "hashOfConfig": "61"}, {"size": 2123, "mtime": 1751155737034, "results": "99", "hashOfConfig": "61"}, {"size": 1989, "mtime": 1751155737003, "results": "100", "hashOfConfig": "61"}, {"size": 967, "mtime": 1751155737039, "results": "101", "hashOfConfig": "61"}, {"size": 710, "mtime": 1751398945425, "results": "102", "hashOfConfig": "61"}, {"size": 772, "mtime": 1751158748532, "results": "103", "hashOfConfig": "61"}, {"size": 9944, "mtime": 1751856145977, "results": "104", "hashOfConfig": "61"}, {"size": 3576, "mtime": 1751917462057, "results": "105", "hashOfConfig": "61"}, {"size": 1601, "mtime": 1751680000990, "results": "106", "hashOfConfig": "61"}, {"size": 11603, "mtime": 1751692123851, "results": "107", "hashOfConfig": "61"}, {"size": 3250, "mtime": 1751917871953, "results": "108", "hashOfConfig": "61"}, {"size": 14727, "mtime": 1751917343059, "results": "109", "hashOfConfig": "61"}, {"size": 7206, "mtime": 1751187181674, "results": "110", "hashOfConfig": "61"}, {"size": 5246, "mtime": 1751563287066, "results": "111", "hashOfConfig": "61"}, {"size": 3738, "mtime": 1751676665563, "results": "112", "hashOfConfig": "61"}, {"size": 4803, "mtime": 1751918390421, "results": "113", "hashOfConfig": "61"}, {"size": 7678, "mtime": 1751820974938, "results": "114", "hashOfConfig": "61"}, {"size": 3707, "mtime": 1751156719894, "results": "115", "hashOfConfig": "61"}, {"size": 2103, "mtime": 1751450596327, "results": "116", "hashOfConfig": "61"}, {"size": 9646, "mtime": 1751229910949, "results": "117", "hashOfConfig": "61"}, {"size": 4304, "mtime": 1751229972339, "results": "118", "hashOfConfig": "61"}, {"size": 5756, "mtime": 1751561668667, "results": "119", "hashOfConfig": "61"}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "ttd3dc", {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 22, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 19, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Marsal\\marsal\\src\\app\\accounting\\page.tsx", ["297", "298", "299", "300", "301", "302"], [], "E:\\Marsal\\marsal\\src\\app\\archive\\page.tsx", ["303", "304", "305", "306", "307", "308"], [], "E:\\Marsal\\marsal\\src\\app\\dispatch\\page.tsx", ["309", "310", "311", "312", "313", "314", "315", "316", "317"], [], "E:\\Marsal\\marsal\\src\\app\\import-export\\page.tsx", ["318", "319", "320", "321", "322", "323", "324", "325", "326", "327", "328", "329", "330", "331", "332", "333", "334", "335", "336", "337", "338", "339"], [], "E:\\Marsal\\marsal\\src\\app\\layout.tsx", [], [], "E:\\Marsal\\marsal\\src\\app\\login\\page.tsx", [], [], "E:\\Marsal\\marsal\\src\\app\\notifications\\page.tsx", ["340"], [], "E:\\Marsal\\marsal\\src\\app\\orders\\new\\page.tsx", [], [], "E:\\Marsal\\marsal\\src\\app\\orders\\page.tsx", ["341", "342", "343", "344", "345", "346", "347", "348", "349", "350", "351", "352", "353", "354", "355", "356", "357", "358", "359"], [], "E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\edit\\page.tsx", ["360", "361"], [], "E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\edit\\static-params.ts", [], [], "E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\layout.tsx", [], [], "E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\page.tsx", ["362", "363"], [], "E:\\Marsal\\marsal\\src\\app\\orders\\[id]\\status\\page.tsx", ["364", "365"], [], "E:\\Marsal\\marsal\\src\\app\\page.tsx", ["366"], [], "E:\\Marsal\\marsal\\src\\app\\returns\\page.tsx", ["367", "368", "369"], [], "E:\\Marsal\\marsal\\src\\app\\settings\\page.tsx", ["370", "371", "372", "373", "374", "375", "376", "377", "378"], [], "E:\\Marsal\\marsal\\src\\app\\statistics\\page.tsx", ["379", "380", "381", "382", "383"], [], "E:\\Marsal\\marsal\\src\\app\\users\\new\\page.tsx", ["384", "385"], [], "E:\\Marsal\\marsal\\src\\app\\users\\page.tsx", ["386"], [], "E:\\Marsal\\marsal\\src\\components\\auth-provider.tsx", ["387", "388"], [], "E:\\Marsal\\marsal\\src\\components\\barcode-scanner.tsx", ["389", "390"], [], "E:\\Marsal\\marsal\\src\\components\\dashboard-stats.tsx", ["391"], [], "E:\\Marsal\\marsal\\src\\components\\header.tsx", ["392", "393"], [], "E:\\Marsal\\marsal\\src\\components\\navigation.tsx", [], [], "E:\\Marsal\\marsal\\src\\components\\notifications.tsx", ["394"], [], "E:\\Marsal\\marsal\\src\\components\\order-card.tsx", ["395"], [], "E:\\Marsal\\marsal\\src\\components\\print-receipt.tsx", [], [], "E:\\Marsal\\marsal\\src\\components\\protected-route.tsx", [], [], "E:\\Marsal\\marsal\\src\\components\\quick-search.tsx", ["396", "397", "398", "399"], [], "E:\\Marsal\\marsal\\src\\components\\receipt-template.tsx", [], [], "E:\\Marsal\\marsal\\src\\components\\role-guard.tsx", [], [], "E:\\Marsal\\marsal\\src\\components\\status-update.tsx", ["400", "401", "402", "403", "404", "405"], [], "E:\\Marsal\\marsal\\src\\components\\theme-provider.tsx", ["406"], [], "E:\\Marsal\\marsal\\src\\components\\theme-selector.tsx", [], [], "E:\\Marsal\\marsal\\src\\components\\ticket-system.tsx", ["407", "408", "409", "410", "411", "412", "413", "414", "415", "416", "417"], [], "E:\\Marsal\\marsal\\src\\components\\ui\\alert.tsx", [], [], "E:\\Marsal\\marsal\\src\\components\\ui\\badge.tsx", [], [], "E:\\Marsal\\marsal\\src\\components\\ui\\button.tsx", [], [], "E:\\Marsal\\marsal\\src\\components\\ui\\card.tsx", [], [], "E:\\Marsal\\marsal\\src\\components\\ui\\input.tsx", [], [], "E:\\Marsal\\marsal\\src\\components\\ui\\label.tsx", [], [], "E:\\Marsal\\marsal\\src\\components\\ui\\textarea.tsx", ["418"], [], "E:\\Marsal\\marsal\\src\\lib\\auth.ts", ["419", "420", "421", "422", "423", "424", "425", "426", "427"], [], "E:\\Marsal\\marsal\\src\\lib\\config.ts", [], [], "E:\\Marsal\\marsal\\src\\lib\\constants.ts", [], [], "E:\\Marsal\\marsal\\src\\lib\\database.ts", ["428", "429", "430", "431"], [], "E:\\Marsal\\marsal\\src\\lib\\firebase.ts", ["432", "433", "434", "435"], [], "E:\\Marsal\\marsal\\src\\lib\\firestore.ts", ["436", "437", "438", "439", "440", "441", "442", "443", "444"], [], "E:\\Marsal\\marsal\\src\\lib\\iraq-locations.ts", [], [], "E:\\Marsal\\marsal\\src\\lib\\mock-data.ts", [], [], "E:\\Marsal\\marsal\\src\\lib\\permissions.ts", ["445"], [], "E:\\Marsal\\marsal\\src\\lib\\statistics.ts", [], [], "E:\\Marsal\\marsal\\src\\lib\\supabase.ts", ["446", "447", "448"], [], "E:\\Marsal\\marsal\\src\\lib\\utils.ts", [], [], "E:\\Marsal\\marsal\\src\\types\\index.ts", [], [], "E:\\Marsal\\marsal\\src\\types\\locations.ts", [], [], "E:\\Marsal\\marsal\\src\\types\\order.ts", [], [], "E:\\Marsal\\marsal\\src\\types\\roles.ts", [], [], {"ruleId": "449", "severity": 2, "message": "450", "line": 8, "column": 10, "nodeType": null, "messageId": "451", "endLine": 8, "endColumn": 15}, {"ruleId": "449", "severity": 2, "message": "452", "line": 10, "column": 37, "nodeType": null, "messageId": "451", "endLine": 10, "endColumn": 47}, {"ruleId": "449", "severity": 2, "message": "453", "line": 10, "column": 59, "nodeType": null, "messageId": "451", "endLine": 10, "endColumn": 67}, {"ruleId": "449", "severity": 2, "message": "454", "line": 10, "column": 69, "nodeType": null, "messageId": "451", "endLine": 10, "endColumn": 72}, {"ruleId": "449", "severity": 2, "message": "455", "line": 10, "column": 143, "nodeType": null, "messageId": "451", "endLine": 10, "endColumn": 149}, {"ruleId": "449", "severity": 2, "message": "456", "line": 10, "column": 151, "nodeType": null, "messageId": "451", "endLine": 10, "endColumn": 159}, {"ruleId": "449", "severity": 2, "message": "455", "line": 7, "column": 61, "nodeType": null, "messageId": "451", "endLine": 7, "endColumn": 67}, {"ruleId": "449", "severity": 2, "message": "457", "line": 7, "column": 69, "nodeType": null, "messageId": "451", "endLine": 7, "endColumn": 73}, {"ruleId": "449", "severity": 2, "message": "458", "line": 12, "column": 10, "nodeType": null, "messageId": "451", "endLine": 12, "endColumn": 18}, {"ruleId": "459", "severity": 1, "message": "460", "line": 29, "column": 6, "nodeType": "461", "endLine": 29, "endColumn": 17, "suggestions": "462"}, {"ruleId": "449", "severity": 2, "message": "463", "line": 217, "column": 9, "nodeType": null, "messageId": "451", "endLine": 217, "endColumn": 29}, {"ruleId": "464", "severity": 2, "message": "465", "line": 405, "column": 45, "nodeType": "466", "messageId": "467", "endLine": 405, "endColumn": 48, "suggestions": "468"}, {"ruleId": "449", "severity": 2, "message": "469", "line": 16, "column": 3, "nodeType": null, "messageId": "451", "endLine": 16, "endColumn": 7}, {"ruleId": "449", "severity": 2, "message": "470", "line": 19, "column": 25, "nodeType": null, "messageId": "451", "endLine": 19, "endColumn": 37}, {"ruleId": "459", "severity": 1, "message": "471", "line": 84, "column": 6, "nodeType": "461", "endLine": 84, "endColumn": 18, "suggestions": "472"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 94, "column": 51, "nodeType": "466", "messageId": "467", "endLine": 94, "endColumn": 54, "suggestions": "473"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 104, "column": 54, "nodeType": "466", "messageId": "467", "endLine": 104, "endColumn": 57, "suggestions": "474"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 110, "column": 59, "nodeType": "466", "messageId": "467", "endLine": 110, "endColumn": 62, "suggestions": "475"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 121, "column": 37, "nodeType": "466", "messageId": "467", "endLine": 121, "endColumn": 40, "suggestions": "476"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 126, "column": 35, "nodeType": "466", "messageId": "467", "endLine": 126, "endColumn": 38, "suggestions": "477"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 144, "column": 36, "nodeType": "466", "messageId": "467", "endLine": 144, "endColumn": 39, "suggestions": "478"}, {"ruleId": "449", "severity": 2, "message": "450", "line": 8, "column": 10, "nodeType": null, "messageId": "451", "endLine": 8, "endColumn": 15}, {"ruleId": "449", "severity": 2, "message": "479", "line": 19, "column": 3, "nodeType": null, "messageId": "451", "endLine": 19, "endColumn": 10}, {"ruleId": "449", "severity": 2, "message": "480", "line": 20, "column": 3, "nodeType": null, "messageId": "451", "endLine": 20, "endColumn": 8}, {"ruleId": "449", "severity": 2, "message": "456", "line": 21, "column": 3, "nodeType": null, "messageId": "451", "endLine": 21, "endColumn": 11}, {"ruleId": "449", "severity": 2, "message": "455", "line": 22, "column": 3, "nodeType": null, "messageId": "451", "endLine": 22, "endColumn": 9}, {"ruleId": "449", "severity": 2, "message": "481", "line": 24, "column": 10, "nodeType": null, "messageId": "451", "endLine": 24, "endColumn": 23}, {"ruleId": "449", "severity": 2, "message": "482", "line": 52, "column": 7, "nodeType": null, "messageId": "451", "endLine": 52, "endColumn": 20}, {"ruleId": "449", "severity": 2, "message": "483", "line": 66, "column": 7, "nodeType": null, "messageId": "451", "endLine": 66, "endColumn": 15}, {"ruleId": "449", "severity": 2, "message": "484", "line": 76, "column": 10, "nodeType": null, "messageId": "451", "endLine": 76, "endColumn": 25}, {"ruleId": "449", "severity": 2, "message": "485", "line": 76, "column": 27, "nodeType": null, "messageId": "451", "endLine": 76, "endColumn": 45}, {"ruleId": "449", "severity": 2, "message": "486", "line": 78, "column": 10, "nodeType": null, "messageId": "451", "endLine": 78, "endColumn": 24}, {"ruleId": "449", "severity": 2, "message": "487", "line": 78, "column": 26, "nodeType": null, "messageId": "451", "endLine": 78, "endColumn": 43}, {"ruleId": "449", "severity": 2, "message": "488", "line": 79, "column": 10, "nodeType": null, "messageId": "451", "endLine": 79, "endColumn": 25}, {"ruleId": "449", "severity": 2, "message": "489", "line": 79, "column": 27, "nodeType": null, "messageId": "451", "endLine": 79, "endColumn": 45}, {"ruleId": "449", "severity": 2, "message": "490", "line": 80, "column": 10, "nodeType": null, "messageId": "451", "endLine": 80, "endColumn": 19}, {"ruleId": "449", "severity": 2, "message": "491", "line": 80, "column": 21, "nodeType": null, "messageId": "451", "endLine": 80, "endColumn": 33}, {"ruleId": "449", "severity": 2, "message": "492", "line": 81, "column": 10, "nodeType": null, "messageId": "451", "endLine": 81, "endColumn": 17}, {"ruleId": "449", "severity": 2, "message": "493", "line": 81, "column": 19, "nodeType": null, "messageId": "451", "endLine": 81, "endColumn": 29}, {"ruleId": "449", "severity": 2, "message": "494", "line": 82, "column": 10, "nodeType": null, "messageId": "451", "endLine": 82, "endColumn": 20}, {"ruleId": "449", "severity": 2, "message": "495", "line": 82, "column": 22, "nodeType": null, "messageId": "451", "endLine": 82, "endColumn": 35}, {"ruleId": "449", "severity": 2, "message": "496", "line": 83, "column": 10, "nodeType": null, "messageId": "451", "endLine": 83, "endColumn": 22}, {"ruleId": "449", "severity": 2, "message": "497", "line": 83, "column": 24, "nodeType": null, "messageId": "451", "endLine": 83, "endColumn": 39}, {"ruleId": "449", "severity": 2, "message": "498", "line": 3, "column": 20, "nodeType": null, "messageId": "451", "endLine": 3, "endColumn": 29}, {"ruleId": "449", "severity": 2, "message": "499", "line": 5, "column": 29, "nodeType": null, "messageId": "451", "endLine": 5, "endColumn": 44}, {"ruleId": "449", "severity": 2, "message": "500", "line": 9, "column": 8, "nodeType": null, "messageId": "451", "endLine": 9, "endColumn": 17}, {"ruleId": "449", "severity": 2, "message": "501", "line": 14, "column": 3, "nodeType": null, "messageId": "451", "endLine": 14, "endColumn": 9}, {"ruleId": "449", "severity": 2, "message": "454", "line": 16, "column": 3, "nodeType": null, "messageId": "451", "endLine": 16, "endColumn": 6}, {"ruleId": "449", "severity": 2, "message": "502", "line": 17, "column": 3, "nodeType": null, "messageId": "451", "endLine": 17, "endColumn": 7}, {"ruleId": "449", "severity": 2, "message": "503", "line": 18, "column": 12, "nodeType": null, "messageId": "451", "endLine": 18, "endColumn": 21}, {"ruleId": "449", "severity": 2, "message": "504", "line": 19, "column": 3, "nodeType": null, "messageId": "451", "endLine": 19, "endColumn": 14}, {"ruleId": "449", "severity": 2, "message": "505", "line": 20, "column": 3, "nodeType": null, "messageId": "451", "endLine": 20, "endColumn": 10}, {"ruleId": "449", "severity": 2, "message": "506", "line": 21, "column": 3, "nodeType": null, "messageId": "451", "endLine": 21, "endColumn": 8}, {"ruleId": "449", "severity": 2, "message": "507", "line": 22, "column": 3, "nodeType": null, "messageId": "451", "endLine": 22, "endColumn": 7}, {"ruleId": "449", "severity": 2, "message": "508", "line": 23, "column": 3, "nodeType": null, "messageId": "451", "endLine": 23, "endColumn": 8}, {"ruleId": "449", "severity": 2, "message": "509", "line": 24, "column": 3, "nodeType": null, "messageId": "451", "endLine": 24, "endColumn": 9}, {"ruleId": "449", "severity": 2, "message": "456", "line": 25, "column": 3, "nodeType": null, "messageId": "451", "endLine": 25, "endColumn": 11}, {"ruleId": "449", "severity": 2, "message": "510", "line": 75, "column": 7, "nodeType": null, "messageId": "451", "endLine": 75, "endColumn": 19}, {"ruleId": "449", "severity": 2, "message": "511", "line": 83, "column": 7, "nodeType": null, "messageId": "451", "endLine": 83, "endColumn": 19}, {"ruleId": "449", "severity": 2, "message": "512", "line": 130, "column": 9, "nodeType": null, "messageId": "451", "endLine": 130, "endColumn": 32}, {"ruleId": "464", "severity": 2, "message": "465", "line": 130, "column": 85, "nodeType": "466", "messageId": "467", "endLine": 130, "endColumn": 88, "suggestions": "513"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 163, "column": 52, "nodeType": "466", "messageId": "467", "endLine": 163, "endColumn": 55, "suggestions": "514"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 286, "column": 39, "nodeType": "466", "messageId": "467", "endLine": 286, "endColumn": 42, "suggestions": "515"}, {"ruleId": "449", "severity": 2, "message": "516", "line": 4, "column": 10, "nodeType": null, "messageId": "451", "endLine": 4, "endColumn": 19}, {"ruleId": "449", "severity": 2, "message": "517", "line": 9, "column": 37, "nodeType": null, "messageId": "451", "endLine": 9, "endColumn": 38}, {"ruleId": "449", "severity": 2, "message": "518", "line": 54, "column": 44, "nodeType": null, "messageId": "451", "endLine": 54, "endColumn": 50}, {"ruleId": "449", "severity": 2, "message": "519", "line": 60, "column": 65, "nodeType": null, "messageId": "451", "endLine": 60, "endColumn": 70}, {"ruleId": "449", "severity": 2, "message": "520", "line": 180, "column": 9, "nodeType": null, "messageId": "451", "endLine": 180, "endColumn": 23}, {"ruleId": "521", "severity": 1, "message": "522", "line": 421, "column": 21, "nodeType": "523", "endLine": 425, "endColumn": 23}, {"ruleId": "449", "severity": 2, "message": "524", "line": 46, "column": 10, "nodeType": null, "messageId": "451", "endLine": 46, "endColumn": 17}, {"ruleId": "449", "severity": 2, "message": "480", "line": 7, "column": 66, "nodeType": null, "messageId": "451", "endLine": 7, "endColumn": 71}, {"ruleId": "449", "severity": 2, "message": "457", "line": 7, "column": 80, "nodeType": null, "messageId": "451", "endLine": 7, "endColumn": 84}, {"ruleId": "459", "severity": 1, "message": "525", "line": 31, "column": 6, "nodeType": "461", "endLine": 31, "endColumn": 23, "suggestions": "526"}, {"ruleId": "449", "severity": 2, "message": "498", "line": 3, "column": 20, "nodeType": null, "messageId": "451", "endLine": 3, "endColumn": 29}, {"ruleId": "464", "severity": 2, "message": "465", "line": 66, "column": 40, "nodeType": "466", "messageId": "467", "endLine": 66, "endColumn": 43, "suggestions": "527"}, {"ruleId": "449", "severity": 2, "message": "528", "line": 75, "column": 14, "nodeType": null, "messageId": "451", "endLine": 75, "endColumn": 19}, {"ruleId": "449", "severity": 2, "message": "528", "line": 99, "column": 14, "nodeType": null, "messageId": "451", "endLine": 99, "endColumn": 19}, {"ruleId": "449", "severity": 2, "message": "528", "line": 112, "column": 14, "nodeType": null, "messageId": "451", "endLine": 112, "endColumn": 19}, {"ruleId": "449", "severity": 2, "message": "528", "line": 126, "column": 14, "nodeType": null, "messageId": "451", "endLine": 126, "endColumn": 19}, {"ruleId": "449", "severity": 2, "message": "528", "line": 155, "column": 14, "nodeType": null, "messageId": "451", "endLine": 155, "endColumn": 19}, {"ruleId": "464", "severity": 2, "message": "465", "line": 422, "column": 100, "nodeType": "466", "messageId": "467", "endLine": 422, "endColumn": 103, "suggestions": "529"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 434, "column": 103, "nodeType": "466", "messageId": "467", "endLine": 434, "endColumn": 106, "suggestions": "530"}, {"ruleId": "449", "severity": 2, "message": "505", "line": 19, "column": 3, "nodeType": null, "messageId": "451", "endLine": 19, "endColumn": 10}, {"ruleId": "449", "severity": 2, "message": "531", "line": 20, "column": 3, "nodeType": null, "messageId": "451", "endLine": 20, "endColumn": 8}, {"ruleId": "449", "severity": 2, "message": "532", "line": 21, "column": 3, "nodeType": null, "messageId": "451", "endLine": 21, "endColumn": 9}, {"ruleId": "449", "severity": 2, "message": "533", "line": 23, "column": 3, "nodeType": null, "messageId": "451", "endLine": 23, "endColumn": 12}, {"ruleId": "449", "severity": 2, "message": "534", "line": 59, "column": 11, "nodeType": null, "messageId": "451", "endLine": 59, "endColumn": 15}, {"ruleId": "449", "severity": 2, "message": "470", "line": 8, "column": 10, "nodeType": null, "messageId": "451", "endLine": 8, "endColumn": 22}, {"ruleId": "464", "severity": 2, "message": "465", "line": 140, "column": 56, "nodeType": "466", "messageId": "467", "endLine": 140, "endColumn": 59, "suggestions": "535"}, {"ruleId": "449", "severity": 2, "message": "457", "line": 7, "column": 76, "nodeType": null, "messageId": "451", "endLine": 7, "endColumn": 80}, {"ruleId": "449", "severity": 2, "message": "536", "line": 7, "column": 10, "nodeType": null, "messageId": "451", "endLine": 7, "endColumn": 32}, {"ruleId": "464", "severity": 2, "message": "465", "line": 69, "column": 34, "nodeType": "466", "messageId": "467", "endLine": 69, "endColumn": 37, "suggestions": "537"}, {"ruleId": "459", "severity": 1, "message": "538", "line": 30, "column": 6, "nodeType": "461", "endLine": 30, "endColumn": 14, "suggestions": "539"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 46, "column": 53, "nodeType": "466", "messageId": "467", "endLine": 46, "endColumn": 56, "suggestions": "540"}, {"ruleId": "449", "severity": 2, "message": "505", "line": 11, "column": 3, "nodeType": null, "messageId": "451", "endLine": 11, "endColumn": 10}, {"ruleId": "449", "severity": 2, "message": "541", "line": 28, "column": 3, "nodeType": null, "messageId": "451", "endLine": 28, "endColumn": 10}, {"ruleId": "464", "severity": 2, "message": "465", "line": 69, "column": 32, "nodeType": "466", "messageId": "467", "endLine": 69, "endColumn": 35, "suggestions": "542"}, {"ruleId": "449", "severity": 2, "message": "507", "line": 15, "column": 3, "nodeType": null, "messageId": "451", "endLine": 15, "endColumn": 7}, {"ruleId": "449", "severity": 2, "message": "543", "line": 5, "column": 10, "nodeType": null, "messageId": "451", "endLine": 5, "endColumn": 16}, {"ruleId": "464", "severity": 2, "message": "465", "line": 50, "column": 54, "nodeType": "466", "messageId": "467", "endLine": 50, "endColumn": 57, "suggestions": "544"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 93, "column": 29, "nodeType": "466", "messageId": "467", "endLine": 93, "endColumn": 32, "suggestions": "545"}, {"ruleId": "546", "severity": 2, "message": "547", "line": 153, "column": 40, "nodeType": "548", "messageId": "549", "suggestions": "550"}, {"ruleId": "546", "severity": 2, "message": "547", "line": 153, "column": 53, "nodeType": "548", "messageId": "549", "suggestions": "551"}, {"ruleId": "449", "severity": 2, "message": "505", "line": 12, "column": 3, "nodeType": null, "messageId": "451", "endLine": 12, "endColumn": 10}, {"ruleId": "449", "severity": 2, "message": "509", "line": 16, "column": 3, "nodeType": null, "messageId": "451", "endLine": 16, "endColumn": 9}, {"ruleId": "552", "severity": 2, "message": "553", "line": 87, "column": 9, "nodeType": "554", "messageId": "555", "endLine": 87, "endColumn": 25, "fix": "556"}, {"ruleId": "449", "severity": 2, "message": "528", "line": 160, "column": 14, "nodeType": null, "messageId": "451", "endLine": 160, "endColumn": 19}, {"ruleId": "449", "severity": 2, "message": "557", "line": 168, "column": 9, "nodeType": null, "messageId": "451", "endLine": 168, "endColumn": 29}, {"ruleId": "521", "severity": 1, "message": "522", "line": 337, "column": 19, "nodeType": "523", "endLine": 341, "endColumn": 21}, {"ruleId": "464", "severity": 2, "message": "465", "line": 15, "column": 43, "nodeType": "466", "messageId": "467", "endLine": 15, "endColumn": 46, "suggestions": "558"}, {"ruleId": "449", "severity": 2, "message": "499", "line": 4, "column": 29, "nodeType": null, "messageId": "451", "endLine": 4, "endColumn": 44}, {"ruleId": "449", "severity": 2, "message": "559", "line": 4, "column": 46, "nodeType": null, "messageId": "451", "endLine": 4, "endColumn": 56}, {"ruleId": "449", "severity": 2, "message": "560", "line": 4, "column": 58, "nodeType": null, "messageId": "451", "endLine": 4, "endColumn": 67}, {"ruleId": "449", "severity": 2, "message": "561", "line": 12, "column": 3, "nodeType": null, "messageId": "451", "endLine": 12, "endColumn": 12}, {"ruleId": "449", "severity": 2, "message": "562", "line": 16, "column": 3, "nodeType": null, "messageId": "451", "endLine": 16, "endColumn": 15}, {"ruleId": "449", "severity": 2, "message": "563", "line": 17, "column": 3, "nodeType": null, "messageId": "451", "endLine": 17, "endColumn": 14}, {"ruleId": "449", "severity": 2, "message": "508", "line": 18, "column": 3, "nodeType": null, "messageId": "451", "endLine": 18, "endColumn": 8}, {"ruleId": "449", "severity": 2, "message": "564", "line": 19, "column": 3, "nodeType": null, "messageId": "451", "endLine": 19, "endColumn": 7}, {"ruleId": "449", "severity": 2, "message": "456", "line": 20, "column": 3, "nodeType": null, "messageId": "451", "endLine": 20, "endColumn": 11}, {"ruleId": "459", "severity": 1, "message": "565", "line": 104, "column": 6, "nodeType": "461", "endLine": 104, "endColumn": 23, "suggestions": "566"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 295, "column": 77, "nodeType": "466", "messageId": "467", "endLine": 295, "endColumn": 80, "suggestions": "567"}, {"ruleId": "568", "severity": 2, "message": "569", "line": 5, "column": 18, "nodeType": "554", "messageId": "570", "endLine": 5, "endColumn": 31, "suggestions": "571"}, {"ruleId": "449", "severity": 2, "message": "572", "line": 6, "column": 10, "nodeType": null, "messageId": "451", "endLine": 6, "endColumn": 18}, {"ruleId": "449", "severity": 2, "message": "573", "line": 6, "column": 46, "nodeType": null, "messageId": "451", "endLine": 6, "endColumn": 58}, {"ruleId": "464", "severity": 2, "message": "465", "line": 65, "column": 28, "nodeType": "466", "messageId": "467", "endLine": 65, "endColumn": 31, "suggestions": "574"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 107, "column": 28, "nodeType": "466", "messageId": "467", "endLine": 107, "endColumn": 31, "suggestions": "575"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 126, "column": 31, "nodeType": "466", "messageId": "467", "endLine": 126, "endColumn": 34, "suggestions": "576"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 145, "column": 28, "nodeType": "466", "messageId": "467", "endLine": 145, "endColumn": 31, "suggestions": "577"}, {"ruleId": "449", "severity": 2, "message": "528", "line": 193, "column": 18, "nodeType": null, "messageId": "451", "endLine": 193, "endColumn": 23}, {"ruleId": "449", "severity": 2, "message": "578", "line": 262, "column": 49, "nodeType": null, "messageId": "451", "endLine": 262, "endColumn": 60}, {"ruleId": "449", "severity": 2, "message": "528", "line": 288, "column": 14, "nodeType": null, "messageId": "451", "endLine": 288, "endColumn": 19}, {"ruleId": "449", "severity": 2, "message": "579", "line": 4, "column": 11, "nodeType": null, "messageId": "451", "endLine": 4, "endColumn": 25}, {"ruleId": "464", "severity": 2, "message": "465", "line": 55, "column": 10, "nodeType": "466", "messageId": "467", "endLine": 55, "endColumn": 13, "suggestions": "580"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 72, "column": 9, "nodeType": "466", "messageId": "467", "endLine": 72, "endColumn": 12, "suggestions": "581"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 195, "column": 68, "nodeType": "466", "messageId": "467", "endLine": 195, "endColumn": 71, "suggestions": "582"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 18, "column": 10, "nodeType": "466", "messageId": "467", "endLine": 18, "endColumn": 13, "suggestions": "583"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 19, "column": 9, "nodeType": "466", "messageId": "467", "endLine": 19, "endColumn": 12, "suggestions": "584"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 20, "column": 11, "nodeType": "466", "messageId": "467", "endLine": 20, "endColumn": 14, "suggestions": "585"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 21, "column": 14, "nodeType": "466", "messageId": "467", "endLine": 21, "endColumn": 17, "suggestions": "586"}, {"ruleId": "449", "severity": 2, "message": "573", "line": 2, "column": 60, "nodeType": null, "messageId": "451", "endLine": 2, "endColumn": 72}, {"ruleId": "449", "severity": 2, "message": "587", "line": 2, "column": 88, "nodeType": null, "messageId": "451", "endLine": 2, "endColumn": 101}, {"ruleId": "449", "severity": 2, "message": "588", "line": 5, "column": 33, "nodeType": null, "messageId": "451", "endLine": 5, "endColumn": 48}, {"ruleId": "464", "severity": 2, "message": "465", "line": 51, "column": 41, "nodeType": "466", "messageId": "467", "endLine": 51, "endColumn": 44, "suggestions": "589"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 185, "column": 61, "nodeType": "466", "messageId": "467", "endLine": 185, "endColumn": 64, "suggestions": "590"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 203, "column": 25, "nodeType": "466", "messageId": "467", "endLine": 203, "endColumn": 28, "suggestions": "591"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 210, "column": 23, "nodeType": "466", "messageId": "467", "endLine": 210, "endColumn": 26, "suggestions": "592"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 242, "column": 61, "nodeType": "466", "messageId": "467", "endLine": 242, "endColumn": 64, "suggestions": "593"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 265, "column": 61, "nodeType": "466", "messageId": "467", "endLine": 265, "endColumn": 64, "suggestions": "594"}, {"ruleId": "449", "severity": 2, "message": "458", "line": 1, "column": 10, "nodeType": null, "messageId": "451", "endLine": 1, "endColumn": 18}, {"ruleId": "464", "severity": 2, "message": "465", "line": 54, "column": 10, "nodeType": "466", "messageId": "467", "endLine": 54, "endColumn": 13, "suggestions": "595"}, {"ruleId": "464", "severity": 2, "message": "465", "line": 71, "column": 9, "nodeType": "466", "messageId": "467", "endLine": 71, "endColumn": 12, "suggestions": "596"}, {"ruleId": "449", "severity": 2, "message": "597", "line": 79, "column": 13, "nodeType": null, "messageId": "451", "endLine": 79, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'Badge' is defined but never used.", "unusedVar", "'DollarSign' is defined but never used.", "'Download' is defined but never used.", "'Eye' is defined but never used.", "'Filter' is defined but never used.", "'Calendar' is defined but never used.", "'Home' is defined but never used.", "'UserRole' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", "ArrayExpression", ["598"], "'downloadOrderReceipt' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["599", "600"], "'Plus' is defined but never used.", "'usersService' is defined but never used.", "React Hook useEffect has a missing dependency: 'performSearch'. Either include it or remove the dependency array.", ["601"], ["602", "603"], ["604", "605"], ["606", "607"], ["608", "609"], ["610", "611"], ["612", "613"], "'Package' is defined but never used.", "'Users' is defined but never used.", "'ordersService' is defined but never used.", "'orderStatuses' is assigned a value but never used.", "'couriers' is assigned a value but never used.", "'selectedCompany' is assigned a value but never used.", "'setSelectedCompany' is assigned a value but never used.", "'selectedStatus' is assigned a value but never used.", "'setSelectedStatus' is assigned a value but never used.", "'selectedCourier' is assigned a value but never used.", "'setSelectedCourier' is assigned a value but never used.", "'startDate' is assigned a value but never used.", "'setStartDate' is assigned a value but never used.", "'endDate' is assigned a value but never used.", "'setEndDate' is assigned a value but never used.", "'deleteDate' is assigned a value but never used.", "'setDeleteDate' is assigned a value but never used.", "'isProcessing' is assigned a value but never used.", "'setIsProcessing' is assigned a value but never used.", "'useEffect' is defined but never used.", "'CardDescription' is defined but never used.", "'RoleGuard' is defined but never used.", "'Search' is defined but never used.", "'Edit' is defined but never used.", "'TruckIcon' is defined but never used.", "'CheckCircle' is defined but never used.", "'XCircle' is defined but never used.", "'Clock' is defined but never used.", "'User' is defined but never used.", "'Phone' is defined but never used.", "'MapPin' is defined but never used.", "'statusColors' is assigned a value but never used.", "'statusLabels' is assigned a value but never used.", "'handleOrderStatusUpdate' is assigned a value but never used.", ["614", "615"], ["616", "617"], ["618", "619"], "'useParams' is defined but never used.", "'X' is defined but never used.", "'params' is defined but never used.", "'image' is defined but never used.", "'getStatusLabel' is assigned a value but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadReturnedOrders'. Either include it or remove the dependency array.", ["620"], ["621", "622"], "'error' is defined but never used.", ["623", "624"], ["625", "626"], "'Truck' is defined but never used.", "'Target' is defined but never used.", "'ArrowDown' is defined but never used.", "'user' is assigned a value but never used.", ["627", "628"], "'testSupabaseConnection' is defined but never used.", ["629", "630"], "React Hook useEffect has missing dependencies: 'initializeScanner' and 'stopScanning'. Either include them or remove the dependency array.", ["631"], ["632", "633"], "'Palette' is defined but never used.", ["634", "635"], "'Button' is defined but never used.", ["636", "637"], ["638", "639"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["640", "641", "642", "643"], ["644", "645", "646", "647"], "prefer-const", "'availableOptions' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "648", "text": "649"}, "'selectedStatusOption' is assigned a value but never used.", ["650", "651"], "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Paperclip' is defined but never used.", "'CheckCircle2' is defined but never used.", "'AlertCircle' is defined but never used.", "'Mail' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockTickets'. Either include it or remove the dependency array.", ["652"], ["653", "654"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "noEmptyInterfaceWithSuper", ["655"], "'supabase' is defined but never used.", "'SupabaseUser' is defined but never used.", ["656", "657"], ["658", "659"], ["660", "661"], ["662", "663"], "'newPassword' is defined but never used.", "'DatabaseSchema' is defined but never used.", ["664", "665"], ["666", "667"], ["668", "669"], ["670", "671"], ["672", "673"], ["674", "675"], ["676", "677"], "'SupabaseOrder' is defined but never used.", "'mockSettlements' is defined but never used.", ["678", "679"], ["680", "681"], ["682", "683"], ["684", "685"], ["686", "687"], ["688", "689"], ["690", "691"], ["692", "693"], "'data' is assigned a value but never used.", {"desc": "694", "fix": "695"}, {"messageId": "696", "fix": "697", "desc": "698"}, {"messageId": "699", "fix": "700", "desc": "701"}, {"desc": "702", "fix": "703"}, {"messageId": "696", "fix": "704", "desc": "698"}, {"messageId": "699", "fix": "705", "desc": "701"}, {"messageId": "696", "fix": "706", "desc": "698"}, {"messageId": "699", "fix": "707", "desc": "701"}, {"messageId": "696", "fix": "708", "desc": "698"}, {"messageId": "699", "fix": "709", "desc": "701"}, {"messageId": "696", "fix": "710", "desc": "698"}, {"messageId": "699", "fix": "711", "desc": "701"}, {"messageId": "696", "fix": "712", "desc": "698"}, {"messageId": "699", "fix": "713", "desc": "701"}, {"messageId": "696", "fix": "714", "desc": "698"}, {"messageId": "699", "fix": "715", "desc": "701"}, {"messageId": "696", "fix": "716", "desc": "698"}, {"messageId": "699", "fix": "717", "desc": "701"}, {"messageId": "696", "fix": "718", "desc": "698"}, {"messageId": "699", "fix": "719", "desc": "701"}, {"messageId": "696", "fix": "720", "desc": "698"}, {"messageId": "699", "fix": "721", "desc": "701"}, {"desc": "722", "fix": "723"}, {"messageId": "696", "fix": "724", "desc": "698"}, {"messageId": "699", "fix": "725", "desc": "701"}, {"messageId": "696", "fix": "726", "desc": "698"}, {"messageId": "699", "fix": "727", "desc": "701"}, {"messageId": "696", "fix": "728", "desc": "698"}, {"messageId": "699", "fix": "729", "desc": "701"}, {"messageId": "696", "fix": "730", "desc": "698"}, {"messageId": "699", "fix": "731", "desc": "701"}, {"messageId": "696", "fix": "732", "desc": "698"}, {"messageId": "699", "fix": "733", "desc": "701"}, {"desc": "734", "fix": "735"}, {"messageId": "696", "fix": "736", "desc": "698"}, {"messageId": "699", "fix": "737", "desc": "701"}, {"messageId": "696", "fix": "738", "desc": "698"}, {"messageId": "699", "fix": "739", "desc": "701"}, {"messageId": "696", "fix": "740", "desc": "698"}, {"messageId": "699", "fix": "741", "desc": "701"}, {"messageId": "696", "fix": "742", "desc": "698"}, {"messageId": "699", "fix": "743", "desc": "701"}, {"messageId": "744", "data": "745", "fix": "746", "desc": "747"}, {"messageId": "744", "data": "748", "fix": "749", "desc": "750"}, {"messageId": "744", "data": "751", "fix": "752", "desc": "753"}, {"messageId": "744", "data": "754", "fix": "755", "desc": "756"}, {"messageId": "744", "data": "757", "fix": "758", "desc": "747"}, {"messageId": "744", "data": "759", "fix": "760", "desc": "750"}, {"messageId": "744", "data": "761", "fix": "762", "desc": "753"}, {"messageId": "744", "data": "763", "fix": "764", "desc": "756"}, [2414, 2456], "const availableOptions = [...statusOptions];", {"messageId": "696", "fix": "765", "desc": "698"}, {"messageId": "699", "fix": "766", "desc": "701"}, {"desc": "767", "fix": "768"}, {"messageId": "696", "fix": "769", "desc": "698"}, {"messageId": "699", "fix": "770", "desc": "701"}, {"messageId": "771", "fix": "772", "desc": "773"}, {"messageId": "696", "fix": "774", "desc": "698"}, {"messageId": "699", "fix": "775", "desc": "701"}, {"messageId": "696", "fix": "776", "desc": "698"}, {"messageId": "699", "fix": "777", "desc": "701"}, {"messageId": "696", "fix": "778", "desc": "698"}, {"messageId": "699", "fix": "779", "desc": "701"}, {"messageId": "696", "fix": "780", "desc": "698"}, {"messageId": "699", "fix": "781", "desc": "701"}, {"messageId": "696", "fix": "782", "desc": "698"}, {"messageId": "699", "fix": "783", "desc": "701"}, {"messageId": "696", "fix": "784", "desc": "698"}, {"messageId": "699", "fix": "785", "desc": "701"}, {"messageId": "696", "fix": "786", "desc": "698"}, {"messageId": "699", "fix": "787", "desc": "701"}, {"messageId": "696", "fix": "788", "desc": "698"}, {"messageId": "699", "fix": "789", "desc": "701"}, {"messageId": "696", "fix": "790", "desc": "698"}, {"messageId": "699", "fix": "791", "desc": "701"}, {"messageId": "696", "fix": "792", "desc": "698"}, {"messageId": "699", "fix": "793", "desc": "701"}, {"messageId": "696", "fix": "794", "desc": "698"}, {"messageId": "699", "fix": "795", "desc": "701"}, {"messageId": "696", "fix": "796", "desc": "698"}, {"messageId": "699", "fix": "797", "desc": "701"}, {"messageId": "696", "fix": "798", "desc": "698"}, {"messageId": "699", "fix": "799", "desc": "701"}, {"messageId": "696", "fix": "800", "desc": "698"}, {"messageId": "699", "fix": "801", "desc": "701"}, {"messageId": "696", "fix": "802", "desc": "698"}, {"messageId": "699", "fix": "803", "desc": "701"}, {"messageId": "696", "fix": "804", "desc": "698"}, {"messageId": "699", "fix": "805", "desc": "701"}, {"messageId": "696", "fix": "806", "desc": "698"}, {"messageId": "699", "fix": "807", "desc": "701"}, {"messageId": "696", "fix": "808", "desc": "698"}, {"messageId": "699", "fix": "809", "desc": "701"}, {"messageId": "696", "fix": "810", "desc": "698"}, {"messageId": "699", "fix": "811", "desc": "701"}, "Update the dependencies array to be: [activeTab, loadData]", {"range": "812", "text": "813"}, "suggestUnknown", {"range": "814", "text": "815"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "816", "text": "817"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [performSearch, searchTerm]", {"range": "818", "text": "819"}, {"range": "820", "text": "815"}, {"range": "821", "text": "817"}, {"range": "822", "text": "815"}, {"range": "823", "text": "817"}, {"range": "824", "text": "815"}, {"range": "825", "text": "817"}, {"range": "826", "text": "815"}, {"range": "827", "text": "817"}, {"range": "828", "text": "815"}, {"range": "829", "text": "817"}, {"range": "830", "text": "815"}, {"range": "831", "text": "817"}, {"range": "832", "text": "815"}, {"range": "833", "text": "817"}, {"range": "834", "text": "815"}, {"range": "835", "text": "817"}, {"range": "836", "text": "815"}, {"range": "837", "text": "817"}, "Update the dependencies array to be: [loadReturnedOrders, selectedCourier]", {"range": "838", "text": "839"}, {"range": "840", "text": "815"}, {"range": "841", "text": "817"}, {"range": "842", "text": "815"}, {"range": "843", "text": "817"}, {"range": "844", "text": "815"}, {"range": "845", "text": "817"}, {"range": "846", "text": "815"}, {"range": "847", "text": "817"}, {"range": "848", "text": "815"}, {"range": "849", "text": "817"}, "Update the dependencies array to be: [initializeScanner, isOpen, stopScanning]", {"range": "850", "text": "851"}, {"range": "852", "text": "815"}, {"range": "853", "text": "817"}, {"range": "854", "text": "815"}, {"range": "855", "text": "817"}, {"range": "856", "text": "815"}, {"range": "857", "text": "817"}, {"range": "858", "text": "815"}, {"range": "859", "text": "817"}, "replaceWithAlt", {"alt": "860"}, {"range": "861", "text": "862"}, "Replace with `&quot;`.", {"alt": "863"}, {"range": "864", "text": "865"}, "Replace with `&ldquo;`.", {"alt": "866"}, {"range": "867", "text": "868"}, "Replace with `&#34;`.", {"alt": "869"}, {"range": "870", "text": "871"}, "Replace with `&rdquo;`.", {"alt": "860"}, {"range": "872", "text": "860"}, {"alt": "863"}, {"range": "873", "text": "863"}, {"alt": "866"}, {"range": "874", "text": "866"}, {"alt": "869"}, {"range": "875", "text": "869"}, {"range": "876", "text": "815"}, {"range": "877", "text": "817"}, "Update the dependencies array to be: [isOpen, mockTickets, orderId]", {"range": "878", "text": "879"}, {"range": "880", "text": "815"}, {"range": "881", "text": "817"}, "replaceEmptyInterfaceWithSuper", {"range": "882", "text": "883"}, "Replace empty interface with a type alias.", {"range": "884", "text": "815"}, {"range": "885", "text": "817"}, {"range": "886", "text": "815"}, {"range": "887", "text": "817"}, {"range": "888", "text": "815"}, {"range": "889", "text": "817"}, {"range": "890", "text": "815"}, {"range": "891", "text": "817"}, {"range": "892", "text": "815"}, {"range": "893", "text": "817"}, {"range": "894", "text": "815"}, {"range": "895", "text": "817"}, {"range": "896", "text": "815"}, {"range": "897", "text": "817"}, {"range": "898", "text": "815"}, {"range": "899", "text": "817"}, {"range": "900", "text": "815"}, {"range": "901", "text": "817"}, {"range": "902", "text": "815"}, {"range": "903", "text": "817"}, {"range": "904", "text": "815"}, {"range": "905", "text": "817"}, {"range": "906", "text": "815"}, {"range": "907", "text": "817"}, {"range": "908", "text": "815"}, {"range": "909", "text": "817"}, {"range": "910", "text": "815"}, {"range": "911", "text": "817"}, {"range": "912", "text": "815"}, {"range": "913", "text": "817"}, {"range": "914", "text": "815"}, {"range": "915", "text": "817"}, {"range": "916", "text": "815"}, {"range": "917", "text": "817"}, {"range": "918", "text": "815"}, {"range": "919", "text": "817"}, {"range": "920", "text": "815"}, {"range": "921", "text": "817"}, [1384, 1395], "[activeTab, loadData]", [16181, 16184], "unknown", [16181, 16184], "never", [2422, 2434], "[performSearch, searchTerm]", [2782, 2785], [2782, 2785], [3056, 3059], [3056, 3059], [3262, 3265], [3262, 3265], [3626, 3629], [3626, 3629], [3782, 3785], [3782, 3785], [4467, 4470], [4467, 4470], [3348, 3351], [3348, 3351], [4283, 4286], [4283, 4286], [9462, 9465], [9462, 9465], [1284, 1301], "[loadReturned<PERSON><PERSON><PERSON>, selectedCourier]", [2033, 2036], [2033, 2036], [16177, 16180], [16177, 16180], [16817, 16820], [16817, 16820], [4208, 4211], [4208, 4211], [2613, 2616], [2613, 2616], [838, 846], "[initialize<PERSON><PERSON><PERSON>, is<PERSON><PERSON>, stopScanning]", [1339, 1342], [1339, 1342], [2409, 2412], [2409, 2412], [1314, 1317], [1314, 1317], [2741, 2744], [2741, 2744], "&quot;", [4759, 4780], "لا توجد نتائج للبحث &quot;", "&ldquo;", [4759, 4780], "لا توجد نتائج للبحث &ldquo;", "&#34;", [4759, 4780], "لا توجد نتائج للبحث &#34;", "&rdquo;", [4759, 4780], "لا توجد نتائج للبحث &rdquo;", [4792, 4793], [4792, 4793], [4792, 4793], [4792, 4793], [405, 408], [405, 408], [2868, 2885], "[isOpen, mockTickets, orderId]", [9875, 9878], [9875, 9878], [73, 159], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [1888, 1891], [1888, 1891], [3140, 3143], [3140, 3143], [3681, 3684], [3681, 3684], [4236, 4239], [4236, 4239], [1018, 1021], [1018, 1021], [1288, 1291], [1288, 1291], [5802, 5805], [5802, 5805], [877, 880], [877, 880], [897, 900], [897, 900], [919, 922], [919, 922], [944, 947], [944, 947], [1804, 1807], [1804, 1807], [6151, 6154], [6151, 6154], [6648, 6651], [6648, 6651], [6756, 6759], [6756, 6759], [7712, 7715], [7712, 7715], [8426, 8429], [8426, 8429], [1216, 1219], [1216, 1219], [1496, 1499], [1496, 1499]]