(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[586],{160:(e,s,t)=>{"use strict";t.d(s,{E8:()=>n,Rm:()=>o,tv:()=>l});var r=t(9345),a=t(5317),c=t(6104);let n=async()=>{try{return console.log("\uD83D\uDD25 بدء إعداد قاعدة البيانات Firebase..."),console.log("\uD83D\uDC65 إنشاء المستخدمين الافتراضيين..."),await r.firebaseAuthService.initializeDefaultUsers(),console.log("\uD83D\uDCE6 إنشاء طلبات تجريبية..."),await i(),console.log("\uD83D\uDD14 إنشاء إشعارات تجريبية..."),await d(),console.log("✅ تم إعداد قاعدة البيانات Firebase بنجاح!"),{success:!0,message:"تم إعداد قاعدة البيانات بنجاح مع جميع البيانات المطلوبة"}}catch(e){return console.error("❌ خطأ في إعداد قاعدة البيانات:",e),{success:!1,message:"فشل في إعداد قاعدة البيانات: ".concat(e.message)}}},i=async()=>{if(!c.db)throw Error("Firebase غير متصل");let e=[{trackingNumber:"ORDER_001",customerName:"أحمد محمد علي",customerPhone:"07701234567",address:"بغداد - الكرادة - شارع الرشيد",amount:25e3,status:"pending",courierName:"",assignedTo:"",notes:"طلب تجريبي - يرجى التوصيل صباحاً",createdBy:"system"},{trackingNumber:"ORDER_002",customerName:"فاطمة علي حسن",customerPhone:"07701234568",address:"البصرة - المعقل - حي الجمهورية",amount:35e3,status:"assigned",courierName:"مندوب التوصيل",assignedTo:"courier_1",notes:"طلب عاجل - يرجى التوصيل اليوم",createdBy:"manager"},{trackingNumber:"ORDER_003",customerName:"محمد حسين كريم",customerPhone:"07701234569",address:"أربيل - عنكاوا - شارع الكنائس",amount:45e3,status:"delivered",courierName:"مندوب التوصيل",assignedTo:"courier_1",notes:"تم التسليم بنجاح",createdBy:"supervisor",deliveredAt:new Date},{trackingNumber:"ORDER_004",customerName:"زينب أحمد محمود",customerPhone:"07701234570",address:"النجف - المدينة القديمة - قرب الحرم",amount:3e4,status:"returned",courierName:"مندوب التوصيل",assignedTo:"courier_1",notes:"تم الإرجاع - العنوان غير صحيح",createdBy:"supervisor"},{trackingNumber:"ORDER_005",customerName:"علي حسن جعفر",customerPhone:"07701234571",address:"كربلاء - حي الحسين - شارع الإمام علي",amount:55e3,status:"in_transit",courierName:"مندوب التوصيل",assignedTo:"courier_1",notes:"في الطريق للتسليم",createdBy:"manager"}],s=(0,a.collection)(c.db,"orders");for(let t of e)try{await (0,a.gS)(s,{...t,createdAt:(0,a.O5)(),updatedAt:(0,a.O5)()}),console.log("✅ تم إنشاء الطلب: ".concat(t.trackingNumber))}catch(e){console.error("❌ خطأ في إنشاء الطلب ".concat(t.trackingNumber,":"),e)}},d=async()=>{if(!c.db)throw Error("Firebase غير متصل");let e=(0,a.collection)(c.db,"notifications");for(let s of[{title:"طلب جديد",message:"تم إسناد طلب ORDER_002 إليك",type:"order_assigned",userId:"courier_1",orderId:"ORDER_002",isRead:!1},{title:"تحديث حالة الطلب",message:"تم تسليم الطلب ORDER_003 بنجاح",type:"status_changed",userId:"manager",orderId:"ORDER_003",isRead:!1},{title:"طلب مرتجع",message:"تم إرجاع الطلب ORDER_004 - العنوان غير صحيح",type:"order_returned",userId:"supervisor",orderId:"ORDER_004",isRead:!1},{title:"مستخدم جديد",message:"تم إنشاء حساب مندوب جديد",type:"user_created",userId:"manager",isRead:!0},{title:"تقرير يومي",message:"تم تسليم 5 طلبات اليوم بنجاح",type:"daily_report",userId:"manager",isRead:!1}])try{await (0,a.gS)(e,{...s,createdAt:(0,a.O5)()}),console.log("✅ تم إنشاء الإشعار: ".concat(s.title))}catch(e){console.error("❌ خطأ في إنشاء الإشعار:",e)}},l=async()=>{try{let{testFirebaseConnection:e}=await Promise.resolve().then(t.bind(t,6104)),s=await e();if(!s.success)return{success:!1,message:"فشل الاتصال بـ Firebase: ".concat(s.message)};let a=await r.firebaseAuthService.checkConnection();if(!a.connected)return{success:!1,message:"فشل في نظام المصادقة: ".concat(a.message)};return{success:!0,message:"Firebase مُعد بشكل صحيح ومتصل"}}catch(e){return{success:!1,message:"خطأ في فحص Firebase: ".concat(e.message)}}},o=async()=>{try{if(!c.db)throw Error("Firebase غير متصل");let{getDocs:e,collection:s}=await Promise.resolve().then(t.bind(t,5317)),r=(await e(s(c.db,"users"))).size,a=(await e(s(c.db,"orders"))).size,n=(await e(s(c.db,"notifications"))).size;return{users:r,orders:a,notifications:n,total:r+a+n}}catch(e){return console.error("خطأ في جلب إحصائيات قاعدة البيانات:",e),{users:0,orders:0,notifications:0,total:0}}}},285:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var r=t(5155);t(2115);var a=t(9708),c=t(2085),n=t(9434);let i=(0,c.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:t,size:c,asChild:d=!1,...l}=e,o=d?a.DX:"button";return(0,r.jsx)(o,{"data-slot":"button",className:(0,n.cn)(i({variant:t,size:c,className:s})),...l})}},646:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1154:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},3861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},3904:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4213:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},4572:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(5155),a=t(2115),c=t(6695),n=t(285),i=t(5365),d=t(1154),l=t(646),o=t(4861),u=t(4213),m=t(3904),g=t(7580),x=t(7108),h=t(3861),b=t(160);function p(){let[e,s]=(0,a.useState)(!1),[t,p]=(0,a.useState)(!0),[v,f]=(0,a.useState)(!1),[y,j]=(0,a.useState)(""),[N,w]=(0,a.useState)(""),[k,A]=(0,a.useState)("checking"),[_,R]=(0,a.useState)({users:0,orders:0,notifications:0,total:0});(0,a.useEffect)(()=>{F()},[]);let F=async()=>{try{p(!0),j("");let e=await (0,b.tv)();if(e.success){A("connected"),f(!0);let e=await (0,b.Rm)();R(e),e.total>0?w("Firebase مُعد بشكل صحيح ويحتوي على بيانات"):(w("Firebase متصل ولكن يحتاج إلى إعداد البيانات"),f(!1))}else A("disconnected"),f(!1),j(e.message)}catch(e){A("disconnected"),f(!1),j("خطأ في فحص Firebase: ".concat(e.message))}finally{p(!1)}},E=async()=>{s(!0),j(""),w("");try{console.log("\uD83D\uDD25 بدء إعداد Firebase...");let e=await (0,b.E8)();if(e.success){w(e.message),f(!0);let s=await (0,b.Rm)();R(s),console.log("✅ تم إعداد Firebase بنجاح")}else j(e.message),console.error("❌ فشل إعداد Firebase:",e.message)}catch(e){j("حدث خطأ غير متوقع: ".concat(e.message)),console.error("❌ خطأ في إعداد Firebase:",e)}finally{s(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-4xl",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(u.A,{className:"h-8 w-8 text-white"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إعداد قاعدة البيانات Firebase"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"إعداد وتهيئة قاعدة البيانات السحابية لتطبيق مرسال"})]}),(0,r.jsx)(c.Zp,{className:"mb-6 ".concat((()=>{switch(k){case"checking":return"border-yellow-200 bg-yellow-50";case"connected":return"border-green-200 bg-green-50";case"disconnected":return"border-red-200 bg-red-50"}})()),children:(0,r.jsx)(c.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(()=>{switch(k){case"checking":return(0,r.jsx)(d.A,{className:"h-5 w-5 animate-spin text-yellow-600"});case"connected":return(0,r.jsx)(l.A,{className:"h-5 w-5 text-green-600"});case"disconnected":return(0,r.jsx)(o.A,{className:"h-5 w-5 text-red-600"})}})(),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:(()=>{switch(k){case"checking":return"جاري فحص الاتصال...";case"connected":return"متصل بـ Firebase";case"disconnected":return"غير متصل بـ Firebase"}})()}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"connected"===k?"Firebase جاهز للاستخدام":"disconnected"===k?"يرجى التحقق من إعدادات Firebase":"جاري التحقق من الاتصال..."})]})]}),(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:F,disabled:t,children:[t?(0,r.jsx)(d.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"إعادة الفحص"]})]})})}),"connected"===k&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsx)(c.Zp,{children:(0,r.jsx)(c.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(g.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold",children:_.users}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"المستخدمين"})]})]})})}),(0,r.jsx)(c.Zp,{children:(0,r.jsx)(c.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-green-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold",children:_.orders}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"الطلبات"})]})]})})}),(0,r.jsx)(c.Zp,{children:(0,r.jsx)(c.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(h.A,{className:"h-8 w-8 text-yellow-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold",children:_.notifications}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"الإشعارات"})]})]})})}),(0,r.jsx)(c.Zp,{children:(0,r.jsx)(c.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-purple-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold",children:_.total}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"إجمالي البيانات"})]})]})})})]}),y&&(0,r.jsxs)(i.Fc,{variant:"destructive",className:"mb-6",children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsx)(i.TN,{children:y})]}),N&&(0,r.jsxs)(i.Fc,{className:"mb-6 border-green-200 bg-green-50",children:[(0,r.jsx)(l.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)(i.TN,{className:"text-green-800",children:N})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"h-5 w-5"}),"إعداد قاعدة البيانات"]}),(0,r.jsx)(c.BT,{children:"إعداد المستخدمين والطلبات والإشعارات الافتراضية في Firebase"})]}),(0,r.jsx)(c.Wu,{className:"space-y-4",children:v?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(l.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-green-800 mb-2",children:"تم إعداد قاعدة البيانات بنجاح!"}),(0,r.jsx)("p",{className:"text-green-600 mb-4",children:"جميع البيانات المطلوبة متوفرة ويمكنك الآن استخدام التطبيق"}),(0,r.jsxs)("div",{className:"flex gap-2 justify-center",children:[(0,r.jsx)(n.$,{onClick:()=>window.open("/firebase-login","_blank"),className:"bg-green-600 hover:bg-green-700",children:"تسجيل الدخول للتطبيق"}),(0,r.jsx)(n.$,{variant:"outline",onClick:E,disabled:e,children:"إعادة الإعداد"})]})]}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(u.A,{className:"h-16 w-16 text-blue-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"إعداد قاعدة البيانات مطلوب"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"انقر على الزر أدناه لإنشاء المستخدمين والطلبات الافتراضية"}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6 text-right",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"سيتم إنشاء:"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• 4 مستخدمين افتراضيين (azad95, manager, supervisor, courier)"}),(0,r.jsx)("li",{children:"• 5 طلبات تجريبية بحالات مختلفة"}),(0,r.jsx)("li",{children:"• 5 إشعارات تجريبية"}),(0,r.jsx)("li",{children:"• إعدادات النظام الأساسية"})]})]}),(0,r.jsx)(n.$,{onClick:E,disabled:e||"connected"!==k,size:"lg",className:"bg-blue-600 hover:bg-blue-700",children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A,{className:"mr-2 h-5 w-5 animate-spin"}),"جاري الإعداد..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"mr-2 h-5 w-5"}),"بدء إعداد قاعدة البيانات"]})})]})})]}),(0,r.jsxs)("div",{className:"text-center mt-8 text-sm text-gray-500",children:[(0,r.jsx)("p",{children:"تطبيق مرسال - نظام إدارة التوصيل"}),(0,r.jsx)("p",{children:"مدعوم بـ Firebase"})]})]})})}},4861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5365:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>d,TN:()=>l});var r=t(5155),a=t(2115),c=t(2085),n=t(9434);let i=(0,c.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=a.forwardRef((e,s)=>{let{className:t,variant:a,...c}=e;return(0,r.jsx)("div",{ref:s,role:"alert",className:(0,n.cn)(i({variant:a}),t),...c})});d.displayName="Alert",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h5",{ref:s,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",t),...a})}).displayName="AlertTitle";let l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",t),...a})});l.displayName="AlertDescription"},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>l,ZB:()=>i,Zp:()=>c,aR:()=>n});var r=t(5155);t(2115);var a=t(9434);function c(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function n(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function i(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...t})}function d(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...t})}function l(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...t})}},7108:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7580:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},8694:(e,s,t)=>{Promise.resolve().then(t.bind(t,4572))},9434:(e,s,t)=>{"use strict";t.d(s,{Yq:()=>i,cn:()=>c,ps:()=>m,qY:()=>u,r6:()=>d,vv:()=>n,y7:()=>l,zC:()=>o});var r=t(2596),a=t(9688);function c(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,r.$)(s))}function n(e){return"".concat(e.toLocaleString("ar-IQ")," د.ع")}function i(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function d(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function l(){let e=Date.now().toString().slice(-6),s=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"".concat("MRS").concat(e).concat(s)}function o(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function u(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function m(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}}},e=>{var s=s=>e(e.s=s);e.O(0,[992,811,455,134,568,345,441,684,358],()=>s(8694)),_N_E=e.O()}]);