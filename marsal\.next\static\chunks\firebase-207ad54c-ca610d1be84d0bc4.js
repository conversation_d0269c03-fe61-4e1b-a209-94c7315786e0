"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8541],{16203:(e,t,a)=>{a.d(t,{eJ:()=>r.ab,xI:()=>r.p,hg:()=>r.z,x9:()=>r.ac,CI:()=>r.D});var r=a(4375);a(46235),a(49887),a(10796),a(56391)},23915:(e,t,a)=>{a.d(t,{Wp:()=>r.Wp});var r=a(46235);(0,r.KO)("firebase","11.10.0","app")},46235:(e,t,a)=>{a.d(t,{KO:()=>$,MF:()=>y,Sx:()=>E,Wp:()=>S,j6:()=>w,om:()=>m,xZ:()=>v});var r=a(56391),i=a(10796),n=a(49887),s=a(46984);class o{constructor(e){this.container=e}getPlatformInfoString(){return this.container.getProviders().map(e=>{if(!function(e){let t=e.getComponent();return(null==t?void 0:t.type)==="VERSION"}(e))return null;{let t=e.getImmediate();return`${t.library}/${t.version}`}}).filter(e=>e).join(" ")}}let c="@firebase/app",l="0.13.2",h=new i.Vy("@firebase/app"),p="[DEFAULT]",f={[c]:"fire-core","@firebase/app-compat":"fire-core-compat","@firebase/analytics":"fire-analytics","@firebase/analytics-compat":"fire-analytics-compat","@firebase/app-check":"fire-app-check","@firebase/app-check-compat":"fire-app-check-compat","@firebase/auth":"fire-auth","@firebase/auth-compat":"fire-auth-compat","@firebase/database":"fire-rtdb","@firebase/data-connect":"fire-data-connect","@firebase/database-compat":"fire-rtdb-compat","@firebase/functions":"fire-fn","@firebase/functions-compat":"fire-fn-compat","@firebase/installations":"fire-iid","@firebase/installations-compat":"fire-iid-compat","@firebase/messaging":"fire-fcm","@firebase/messaging-compat":"fire-fcm-compat","@firebase/performance":"fire-perf","@firebase/performance-compat":"fire-perf-compat","@firebase/remote-config":"fire-rc","@firebase/remote-config-compat":"fire-rc-compat","@firebase/storage":"fire-gcs","@firebase/storage-compat":"fire-gcs-compat","@firebase/firestore":"fire-fst","@firebase/firestore-compat":"fire-fst-compat","@firebase/ai":"fire-vertex","fire-js":"fire-js",firebase:"fire-js-all"},d=new Map,b=new Map,u=new Map;function g(e,t){try{e.container.addComponent(t)}catch(a){h.debug(`Component ${t.name} failed to register with FirebaseApp ${e.name}`,a)}}function m(e){let t=e.name;if(u.has(t))return h.debug(`There were multiple attempts to register component ${t}.`),!1;for(let a of(u.set(t,e),d.values()))g(a,e);for(let t of b.values())g(t,e);return!0}function w(e,t){let a=e.container.getProvider("heartbeat").getImmediate({optional:!0});return a&&a.triggerHeartbeat(),e.container.getProvider(t)}function v(e){return null!=e&&void 0!==e.settings}let _=new n.FA("app","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."});class D{constructor(e,t,a){this._isDeleted=!1,this._options=Object.assign({},e),this._config=Object.assign({},t),this._name=t.name,this._automaticDataCollectionEnabled=t.automaticDataCollectionEnabled,this._container=a,this.container.addComponent(new r.uA("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(e){this.checkDestroyed(),this._automaticDataCollectionEnabled=e}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(e){this._isDeleted=e}checkDestroyed(){if(this.isDeleted)throw _.create("app-deleted",{appName:this._name})}}function C(e,t){let a=(0,n.u)(e.split(".")[1]);if(null===a)return void console.error(`FirebaseServerApp ${t} is invalid: second part could not be parsed.`);if(void 0===JSON.parse(a).exp)return void console.error(`FirebaseServerApp ${t} is invalid: expiration claim could not be parsed`);let r=1e3*JSON.parse(a).exp;r-new Date().getTime()<=0&&console.error(`FirebaseServerApp ${t} is invalid: the token has expired.`)}let y="11.10.0";function S(e,t={}){let a=e;"object"!=typeof t&&(t={name:t});let i=Object.assign({name:p,automaticDataCollectionEnabled:!0},t),s=i.name;if("string"!=typeof s||!s)throw _.create("bad-app-name",{appName:String(s)});if(a||(a=(0,n.T9)()),!a)throw _.create("no-options");let o=d.get(s);if(o)if((0,n.bD)(a,o.options)&&(0,n.bD)(i,o.config))return o;else throw _.create("duplicate-app",{appName:s});let c=new r.h1(s);for(let e of u.values())c.addComponent(e);let l=new D(a,i,c);return d.set(s,l),l}function E(e=p){let t=d.get(e);if(!t&&e===p&&(0,n.T9)())return S();if(!t)throw _.create("no-app",{appName:e});return t}async function I(e){let t=!1,a=e.name;d.has(a)?(t=!0,d.delete(a)):b.has(a)&&0>=e.decRefCount()&&(b.delete(a),t=!0),t&&(await Promise.all(e.container.getProviders().map(e=>e.delete())),e.isDeleted=!0)}function $(e,t,a){var i;let n=null!=(i=f[e])?i:e;a&&(n+=`-${a}`);let s=n.match(/\s|\//),o=t.match(/\s|\//);if(s||o){let e=[`Unable to register library "${n}" with version "${t}":`];s&&e.push(`library name "${n}" contains illegal characters (whitespace or "/")`),s&&o&&e.push("and"),o&&e.push(`version name "${t}" contains illegal characters (whitespace or "/")`),h.warn(e.join(" "));return}m(new r.uA(`${n}-version`,()=>({library:n,version:t}),"VERSION"))}let A="firebase-heartbeat-store",k=null;function x(){return k||(k=(0,s.P2)("firebase-heartbeat-database",1,{upgrade:(e,t)=>{if(0===t)try{e.createObjectStore(A)}catch(e){console.warn(e)}}}).catch(e=>{throw _.create("idb-open",{originalErrorMessage:e.message})})),k}async function P(e){try{let t=(await x()).transaction(A),a=await t.objectStore(A).get(O(e));return await t.done,a}catch(e){if(e instanceof n.g)h.warn(e.message);else{let t=_.create("idb-get",{originalErrorMessage:null==e?void 0:e.message});h.warn(t.message)}}}async function N(e,t){try{let a=(await x()).transaction(A,"readwrite"),r=a.objectStore(A);await r.put(t,O(e)),await a.done}catch(e){if(e instanceof n.g)h.warn(e.message);else{let t=_.create("idb-set",{originalErrorMessage:null==e?void 0:e.message});h.warn(t.message)}}}function O(e){return`${e.name}!${e.options.appId}`}class F{constructor(e){this.container=e,this._heartbeatsCache=null;let t=this.container.getProvider("app").getImmediate();this._storage=new H(t),this._heartbeatsCachePromise=this._storage.read().then(e=>(this._heartbeatsCache=e,e))}async triggerHeartbeat(){var e,t;try{let a=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),r=j();if((null==(e=this._heartbeatsCache)?void 0:e.heartbeats)==null&&(this._heartbeatsCache=await this._heartbeatsCachePromise,(null==(t=this._heartbeatsCache)?void 0:t.heartbeats)==null)||this._heartbeatsCache.lastSentHeartbeatDate===r||this._heartbeatsCache.heartbeats.some(e=>e.date===r))return;if(this._heartbeatsCache.heartbeats.push({date:r,agent:a}),this._heartbeatsCache.heartbeats.length>30){let e=function(e){if(0===e.length)return -1;let t=0,a=e[0].date;for(let r=1;r<e.length;r++)e[r].date<a&&(a=e[r].date,t=r);return t}(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(e,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(e){h.warn(e)}}async getHeartbeatsHeader(){var e;try{if(null===this._heartbeatsCache&&await this._heartbeatsCachePromise,(null==(e=this._heartbeatsCache)?void 0:e.heartbeats)==null||0===this._heartbeatsCache.heartbeats.length)return"";let t=j(),{heartbeatsToSend:a,unsentEntries:r}=function(e,t=1024){let a=[],r=e.slice();for(let i of e){let e=a.find(e=>e.agent===i.agent);if(e){if(e.dates.push(i.date),B(a)>t){e.dates.pop();break}}else if(a.push({agent:i.agent,dates:[i.date]}),B(a)>t){a.pop();break}r=r.slice(1)}return{heartbeatsToSend:a,unsentEntries:r}}(this._heartbeatsCache.heartbeats),i=(0,n.Uj)(JSON.stringify({version:2,heartbeats:a}));return this._heartbeatsCache.lastSentHeartbeatDate=t,r.length>0?(this._heartbeatsCache.heartbeats=r,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),i}catch(e){return h.warn(e),""}}}function j(){return new Date().toISOString().substring(0,10)}class H{constructor(e){this.app=e,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return!!(0,n.zW)()&&(0,n.eX)().then(()=>!0).catch(()=>!1)}async read(){if(!await this._canUseIndexedDBPromise)return{heartbeats:[]};{let e=await P(this.app);return(null==e?void 0:e.heartbeats)?e:{heartbeats:[]}}}async overwrite(e){var t;if(await this._canUseIndexedDBPromise){let a=await this.read();return N(this.app,{lastSentHeartbeatDate:null!=(t=e.lastSentHeartbeatDate)?t:a.lastSentHeartbeatDate,heartbeats:e.heartbeats})}}async add(e){var t;if(await this._canUseIndexedDBPromise){let a=await this.read();return N(this.app,{lastSentHeartbeatDate:null!=(t=e.lastSentHeartbeatDate)?t:a.lastSentHeartbeatDate,heartbeats:[...a.heartbeats,...e.heartbeats]})}}}function B(e){return(0,n.Uj)(JSON.stringify({version:2,heartbeats:e})).length}m(new r.uA("platform-logger",e=>new o(e),"PRIVATE")),m(new r.uA("heartbeat",e=>new F(e),"PRIVATE")),$(c,l,""),$(c,l,"esm2017"),$("fire-js","")}}]);