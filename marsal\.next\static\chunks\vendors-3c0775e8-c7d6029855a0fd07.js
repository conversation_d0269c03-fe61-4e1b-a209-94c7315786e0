"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4927],{3451:(t,e,r)=>{r.d(e,{A:()=>o});var n=r(75511),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.kind="UnsupportedOperationException",e}(n.A)},10646:(t,e,r)=>{r.d(e,{A:()=>o});var n=r(75511),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.kind="ReaderException",e}(n.A)},14842:(t,e,r)=>{var n=r(25969),i=r(27217),o=r(23583),a=r(10782),s=r(2257),u=r(91375),f=r(63479),h=r(39778),c=r(38988),p=r(23510);!function(){function t(){}t.prototype.encode=function(t,e,r,n){return this.encodeWithHints(t,e,r,n,null)},t.prototype.encodeWithHints=function(e,r,n,a,h){var c=u.A.ISO_8859_1,p=o.A.DEFAULT_EC_PERCENT,y=o.A.DEFAULT_AZTEC_LAYERS;return null!=h&&(h.has(i.A.CHARACTER_SET)&&(c=s.A.forName(h.get(i.A.CHARACTER_SET).toString())),h.has(i.A.ERROR_CORRECTION)&&(p=f.A.parseInt(h.get(i.A.ERROR_CORRECTION).toString())),h.has(i.A.AZTEC_LAYERS)&&(y=f.A.parseInt(h.get(i.A.AZTEC_LAYERS).toString()))),t.encodeLayers(e,r,n,a,c,p,y)},t.encodeLayers=function(e,r,i,a,s,u,f){if(r!==n.A.AZTEC)throw new c.A("Can only encode AZTEC, but got "+r);var h=o.A.encode(p.A.getBytes(e,s),u,f);return t.renderResult(h,i,a)},t.renderResult=function(t,e,r){var n=t.getMatrix();if(null==n)throw new h.A;for(var i=n.getWidth(),o=n.getHeight(),s=Math.max(e,i),u=Math.max(r,o),f=Math.min(s/i,u/o),c=(s-i*f)/2,p=(u-o*f)/2,y=new a.A(s,u),l=0,d=p;l<o;l++,d+=f)for(var A=0,g=c;A<i;A++,g+=f)n.get(A,l)&&y.setRegion(g,d,f,f);return y}}()},20738:(t,e,r)=>{r.d(e,{A:()=>o});var n=r(75511),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.kind="ReedSolomonException",e}(n.A)},23583:(t,e,r)=>{r.d(e,{A:()=>y});var n=r(22868),i=r(38988),o=r(23510),a=r(10782),s=r(61767),u=r(29477),f=r(55192),h=r(61995),c=r(63479),p=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let y=function(){function t(){}return t.encodeBytes=function(e){return t.encode(e,t.DEFAULT_EC_PERCENT,t.DEFAULT_AZTEC_LAYERS)},t.encode=function(e,r,n){var u,f,p,y,l,d,A=new h.A(e).encode(),g=c.A.truncDivision(A.getSize()*r,100)+11,C=A.getSize()+g;if(n!==t.DEFAULT_AZTEC_LAYERS){if(u=n<0,(f=Math.abs(n))>(u?t.MAX_NB_BITS_COMPACT:t.MAX_NB_BITS))throw new i.A(o.A.format("Illegal value %s for layers",n));var v=(p=t.totalBitsInLayer(f,u))-p%(y=t.WORD_SIZE[f]);if((l=t.stuffBits(A,y)).getSize()+g>v||u&&l.getSize()>64*y)throw new i.A("Data to large for user specified layer")}else{y=0,l=null;for(var _=0;;_++){if(_>t.MAX_NB_BITS)throw new i.A("Data too large for an Aztec code");if(f=(u=_<=3)?_+1:_,!(C>(p=t.totalBitsInLayer(f,u)))){(null==l||y!==t.WORD_SIZE[f])&&(y=t.WORD_SIZE[f],l=t.stuffBits(A,y));var v=p-p%y;if(!(u&&l.getSize()>64*y)&&l.getSize()+g<=v)break}}}var E=t.generateCheckWords(l,p,y),T=l.getSize()/y,S=t.generateModeMessage(u,f,T),B=(u?11:14)+4*f,b=new Int32Array(B);if(u){d=B;for(var _=0;_<b.length;_++)b[_]=_}else{d=B+1+2*c.A.truncDivision(c.A.truncDivision(B,2)-1,15);for(var R=c.A.truncDivision(B,2),w=c.A.truncDivision(d,2),_=0;_<R;_++){var L=_+c.A.truncDivision(_,15);b[R-_-1]=w-L-1,b[R+_]=w+L+1}}for(var P=new a.A(d),_=0,m=0;_<f;_++){for(var D=(f-_)*4+(u?9:12),x=0;x<D;x++)for(var I=2*x,O=0;O<2;O++)E.get(m+I+O)&&P.set(b[2*_+O],b[2*_+x]),E.get(m+2*D+I+O)&&P.set(b[2*_+x],b[B-1-2*_-O]),E.get(m+4*D+I+O)&&P.set(b[B-1-2*_-O],b[B-1-2*_-x]),E.get(m+6*D+I+O)&&P.set(b[B-1-2*_-x],b[2*_+O]);m+=8*D}if(t.drawModeMessage(P,u,d,S),u)t.drawBullsEye(P,c.A.truncDivision(d,2),5);else{t.drawBullsEye(P,c.A.truncDivision(d,2),7);for(var _=0,x=0;_<c.A.truncDivision(B,2)-1;_+=15,x+=16)for(var O=1&c.A.truncDivision(d,2);O<d;O+=2)P.set(c.A.truncDivision(d,2)-x,O),P.set(c.A.truncDivision(d,2)+x,O),P.set(O,c.A.truncDivision(d,2)-x),P.set(O,c.A.truncDivision(d,2)+x)}var M=new s.A;return M.setCompact(u),M.setSize(d),M.setLayers(f),M.setCodeWords(T),M.setMatrix(P),M},t.drawBullsEye=function(t,e,r){for(var n=0;n<r;n+=2)for(var i=e-n;i<=e+n;i++)t.set(i,e-n),t.set(i,e+n),t.set(e-n,i),t.set(e+n,i);t.set(e-r,e-r),t.set(e-r+1,e-r),t.set(e-r,e-r+1),t.set(e+r,e-r),t.set(e+r,e-r+1),t.set(e+r,e+r-1)},t.generateModeMessage=function(e,r,i){var o=new n.A;return e?(o.appendBits(r-1,2),o.appendBits(i-1,6),o=t.generateCheckWords(o,28,4)):(o.appendBits(r-1,5),o.appendBits(i-1,11),o=t.generateCheckWords(o,40,4)),o},t.drawModeMessage=function(t,e,r,n){var i=c.A.truncDivision(r,2);if(e)for(var o=0;o<7;o++){var a=i-3+o;n.get(o)&&t.set(a,i-5),n.get(o+7)&&t.set(i+5,a),n.get(20-o)&&t.set(a,i+5),n.get(27-o)&&t.set(i-5,a)}else for(var o=0;o<10;o++){var a=i-5+o+c.A.truncDivision(o,5);n.get(o)&&t.set(a,i-7),n.get(o+10)&&t.set(i+7,a),n.get(29-o)&&t.set(a,i+7),n.get(39-o)&&t.set(i-7,a)}},t.generateCheckWords=function(e,r,i){var o,a,s=e.getSize()/i,f=new u.A(t.getGF(i)),h=c.A.truncDivision(r,i),y=t.bitsToWords(e,i,h);f.encode(y,h-s);var l=r%i,d=new n.A;d.appendBits(0,l);try{for(var A=p(Array.from(y)),g=A.next();!g.done;g=A.next()){var C=g.value;d.appendBits(C,i)}}catch(t){o={error:t}}finally{try{g&&!g.done&&(a=A.return)&&a.call(A)}finally{if(o)throw o.error}}return d},t.bitsToWords=function(t,e,r){var n,i,o=new Int32Array(r);for(n=0,i=t.getSize()/e;n<i;n++){for(var a=0,s=0;s<e;s++)a|=t.get(n*e+s)?1<<e-s-1:0;o[n]=a}return o},t.getGF=function(t){switch(t){case 4:return f.A.AZTEC_PARAM;case 6:return f.A.AZTEC_DATA_6;case 8:return f.A.AZTEC_DATA_8;case 10:return f.A.AZTEC_DATA_10;case 12:return f.A.AZTEC_DATA_12;default:throw new i.A("Unsupported word size "+t)}},t.stuffBits=function(t,e){for(var r=new n.A,i=t.getSize(),o=(1<<e)-2,a=0;a<i;a+=e){for(var s=0,u=0;u<e;u++)(a+u>=i||t.get(a+u))&&(s|=1<<e-1-u);(s&o)===o?(r.appendBits(s&o,e),a--):(s&o)==0?(r.appendBits(1|s,e),a--):r.appendBits(s,e)}return r},t.totalBitsInLayer=function(t,e){return((e?88:112)+16*t)*t},t.DEFAULT_EC_PERCENT=33,t.DEFAULT_AZTEC_LAYERS=0,t.MAX_NB_BITS=32,t.MAX_NB_BITS_COMPACT=4,t.WORD_SIZE=Int32Array.from([4,6,6,8,8,8,8,8,8,10,10,10,10,10,10,10,10,10,10,10,10,10,10,12,12,12,12,12,12,12,12,12,12]),t}()},43197:(t,e,r)=>{r.d(e,{A:()=>y});var n=r(56595),i=r(71614),o=r(79886),a=r(17391),s=r(55192),u=r(60109),f=r(438),h=r(20367),c=r(63479),p=function(){function t(t,e){this.x=t,this.y=e}return t.prototype.toResultPoint=function(){return new n.A(this.getX(),this.getY())},t.prototype.getX=function(){return this.x},t.prototype.getY=function(){return this.y},t}();let y=function(){function t(t){this.EXPECTED_CORNER_BITS=new Int32Array([3808,476,2107,1799]),this.image=t}return t.prototype.detect=function(){return this.detectMirror(!1)},t.prototype.detectMirror=function(t){var e=this.getMatrixCenter(),r=this.getBullsEyeCorners(e);if(t){var n=r[0];r[0]=r[2],r[2]=n}this.extractParameters(r);var o=this.sampleGrid(this.image,r[this.shift%4],r[(this.shift+1)%4],r[(this.shift+2)%4],r[(this.shift+3)%4]),a=this.getMatrixCornerPoints(r);return new i.A(o,a,this.compact,this.nbDataBlocks,this.nbLayers)},t.prototype.extractParameters=function(t){if(!this.isValidPoint(t[0])||!this.isValidPoint(t[1])||!this.isValidPoint(t[2])||!this.isValidPoint(t[3]))throw new f.A;var e=2*this.nbCenterLayers,r=new Int32Array([this.sampleLine(t[0],t[1],e),this.sampleLine(t[1],t[2],e),this.sampleLine(t[2],t[3],e),this.sampleLine(t[3],t[0],e)]);this.shift=this.getRotation(r,e);for(var n=0,i=0;i<4;i++){var o=r[(this.shift+i)%4];this.compact?(n<<=7,n+=o>>1&127):(n<<=10,n+=(o>>2&992)+(o>>1&31))}var a=this.getCorrectedParameterData(n,this.compact);this.compact?(this.nbLayers=(a>>6)+1,this.nbDataBlocks=(63&a)+1):(this.nbLayers=(a>>11)+1,this.nbDataBlocks=(2047&a)+1)},t.prototype.getRotation=function(t,e){var r=0;t.forEach(function(t,n,i){r=(r<<3)+((t>>e-2<<1)+(1&t))}),r=((1&r)<<11)+(r>>1);for(var n=0;n<4;n++)if(2>=c.A.bitCount(r^this.EXPECTED_CORNER_BITS[n]))return n;throw new f.A},t.prototype.getCorrectedParameterData=function(t,e){e?(r=7,n=2):(r=10,n=4);for(var r,n,i=r-n,o=new Int32Array(r),a=r-1;a>=0;--a)o[a]=15&t,t>>=4;try{new u.A(s.A.AZTEC_PARAM).decode(o,i)}catch(t){throw new f.A}for(var h=0,a=0;a<n;a++)h=(h<<4)+o[a];return h},t.prototype.getBullsEyeCorners=function(t){var e=t,r=t,i=t,o=t,a=!0;for(this.nbCenterLayers=1;this.nbCenterLayers<9;this.nbCenterLayers++){var s=this.getFirstDifferent(e,a,1,-1),u=this.getFirstDifferent(r,a,1,1),h=this.getFirstDifferent(i,a,-1,1),c=this.getFirstDifferent(o,a,-1,-1);if(this.nbCenterLayers>2){var p=this.distancePoint(c,s)*this.nbCenterLayers/(this.distancePoint(o,e)*(this.nbCenterLayers+2));if(p<.75||p>1.25||!this.isWhiteOrBlackRectangle(s,u,h,c))break}e=s,r=u,i=h,o=c,a=!a}if(5!==this.nbCenterLayers&&7!==this.nbCenterLayers)throw new f.A;this.compact=5===this.nbCenterLayers;var y=new n.A(e.getX()+.5,e.getY()-.5),l=new n.A(r.getX()+.5,r.getY()+.5),d=new n.A(i.getX()-.5,i.getY()+.5),A=new n.A(o.getX()-.5,o.getY()-.5);return this.expandSquare([y,l,d,A],2*this.nbCenterLayers-3,2*this.nbCenterLayers)},t.prototype.getMatrixCenter=function(){try{var t,e,r,n,i=new a.A(this.image).detect();t=i[0],e=i[1],r=i[2],n=i[3]}catch(i){var s=this.image.getWidth()/2,u=this.image.getHeight()/2;t=this.getFirstDifferent(new p(s+7,u-7),!1,1,-1).toResultPoint(),e=this.getFirstDifferent(new p(s+7,u+7),!1,1,1).toResultPoint(),r=this.getFirstDifferent(new p(s-7,u+7),!1,-1,1).toResultPoint(),n=this.getFirstDifferent(new p(s-7,u-7),!1,-1,-1).toResultPoint()}var f=o.A.round((t.getX()+n.getX()+e.getX()+r.getX())/4),h=o.A.round((t.getY()+n.getY()+e.getY()+r.getY())/4);try{var i=new a.A(this.image,15,f,h).detect();t=i[0],e=i[1],r=i[2],n=i[3]}catch(i){t=this.getFirstDifferent(new p(f+7,h-7),!1,1,-1).toResultPoint(),e=this.getFirstDifferent(new p(f+7,h+7),!1,1,1).toResultPoint(),r=this.getFirstDifferent(new p(f-7,h+7),!1,-1,1).toResultPoint(),n=this.getFirstDifferent(new p(f-7,h-7),!1,-1,-1).toResultPoint()}return new p(f=o.A.round((t.getX()+n.getX()+e.getX()+r.getX())/4),h=o.A.round((t.getY()+n.getY()+e.getY()+r.getY())/4))},t.prototype.getMatrixCornerPoints=function(t){return this.expandSquare(t,2*this.nbCenterLayers,this.getDimension())},t.prototype.sampleGrid=function(t,e,r,n,i){var o=h.A.getInstance(),a=this.getDimension(),s=a/2-this.nbCenterLayers,u=a/2+this.nbCenterLayers;return o.sampleGrid(t,a,a,s,s,u,s,u,u,s,u,e.getX(),e.getY(),r.getX(),r.getY(),n.getX(),n.getY(),i.getX(),i.getY())},t.prototype.sampleLine=function(t,e,r){for(var n=0,i=this.distanceResultPoint(t,e),a=i/r,s=t.getX(),u=t.getY(),f=a*(e.getX()-t.getX())/i,h=a*(e.getY()-t.getY())/i,c=0;c<r;c++)this.image.get(o.A.round(s+c*f),o.A.round(u+c*h))&&(n|=1<<r-c-1);return n},t.prototype.isWhiteOrBlackRectangle=function(t,e,r,n){t=new p(t.getX()-3,t.getY()+3),e=new p(e.getX()-3,e.getY()-3),r=new p(r.getX()+3,r.getY()-3),n=new p(n.getX()+3,n.getY()+3);var i=this.getColor(n,t);if(0===i)return!1;var o=this.getColor(t,e);return o===i&&(o=this.getColor(e,r))===i&&(o=this.getColor(r,n))===i},t.prototype.getColor=function(t,e){for(var r=this.distancePoint(t,e),n=(e.getX()-t.getX())/r,i=(e.getY()-t.getY())/r,a=0,s=t.getX(),u=t.getY(),f=this.image.get(t.getX(),t.getY()),h=Math.ceil(r),c=0;c<h;c++)s+=n,u+=i,this.image.get(o.A.round(s),o.A.round(u))!==f&&a++;var p=a/r;return p>.1&&p<.9?0:p<=.1===f?1:-1},t.prototype.getFirstDifferent=function(t,e,r,n){for(var i=t.getX()+r,o=t.getY()+n;this.isValid(i,o)&&this.image.get(i,o)===e;)i+=r,o+=n;for(i-=r,o-=n;this.isValid(i,o)&&this.image.get(i,o)===e;)i+=r;for(i-=r;this.isValid(i,o)&&this.image.get(i,o)===e;)o+=n;return new p(i,o-=n)},t.prototype.expandSquare=function(t,e,r){var i=r/(2*e),o=t[0].getX()-t[2].getX(),a=t[0].getY()-t[2].getY(),s=(t[0].getX()+t[2].getX())/2,u=(t[0].getY()+t[2].getY())/2,f=new n.A(s+i*o,u+i*a),h=new n.A(s-i*o,u-i*a);return o=t[1].getX()-t[3].getX(),a=t[1].getY()-t[3].getY(),s=(t[1].getX()+t[3].getX())/2,u=(t[1].getY()+t[3].getY())/2,[f,new n.A(s+i*o,u+i*a),h,new n.A(s-i*o,u-i*a)]},t.prototype.isValid=function(t,e){return t>=0&&t<this.image.getWidth()&&e>0&&e<this.image.getHeight()},t.prototype.isValidPoint=function(t){var e=o.A.round(t.getX()),r=o.A.round(t.getY());return this.isValid(e,r)},t.prototype.distancePoint=function(t,e){return o.A.distance(t.getX(),t.getY(),e.getX(),e.getY())},t.prototype.distanceResultPoint=function(t,e){return o.A.distance(t.getX(),t.getY(),e.getX(),e.getY())},t.prototype.getDimension=function(){return this.compact?4*this.nbLayers+11:this.nbLayers<=4?4*this.nbLayers+15:4*this.nbLayers+2*(c.A.truncDivision(this.nbLayers-4,8)+1)+15},t}()},43358:(t,e,r)=>{var n;r.d(e,{A:()=>i}),function(t){t[t.OTHER=0]="OTHER",t[t.ORIENTATION=1]="ORIENTATION",t[t.BYTE_SEGMENTS=2]="BYTE_SEGMENTS",t[t.ERROR_CORRECTION_LEVEL=3]="ERROR_CORRECTION_LEVEL",t[t.ISSUE_NUMBER=4]="ISSUE_NUMBER",t[t.SUGGESTED_PRICE=5]="SUGGESTED_PRICE",t[t.POSSIBLE_COUNTRY=6]="POSSIBLE_COUNTRY",t[t.UPC_EAN_EXTENSION=7]="UPC_EAN_EXTENSION",t[t.PDF417_EXTRA_METADATA=8]="PDF417_EXTRA_METADATA",t[t.STRUCTURED_APPEND_SEQUENCE=9]="STRUCTURED_APPEND_SEQUENCE",t[t.STRUCTURED_APPEND_PARITY=10]="STRUCTURED_APPEND_PARITY"}(n||(n={}));let i=n},56595:(t,e,r)=>{r.d(e,{A:()=>o});var n=r(79886),i=r(46263);let o=function(){function t(t,e){this.x=t,this.y=e}return t.prototype.getX=function(){return this.x},t.prototype.getY=function(){return this.y},t.prototype.equals=function(e){return e instanceof t&&this.x===e.x&&this.y===e.y},t.prototype.hashCode=function(){return 31*i.A.floatToIntBits(this.x)+i.A.floatToIntBits(this.y)},t.prototype.toString=function(){return"("+this.x+","+this.y+")"},t.orderBestPatterns=function(t){var e,r,n,i=this.distance(t[0],t[1]),o=this.distance(t[1],t[2]),a=this.distance(t[0],t[2]);if(o>=i&&o>=a?(r=t[0],e=t[1],n=t[2]):a>=o&&a>=i?(r=t[1],e=t[0],n=t[2]):(r=t[2],e=t[0],n=t[1]),0>this.crossProductZ(e,r,n)){var s=e;e=n,n=s}t[0]=e,t[1]=r,t[2]=n},t.distance=function(t,e){return n.A.distance(t.x,t.y,e.x,e.y)},t.crossProductZ=function(t,e,r){var n=e.x,i=e.y;return(r.x-n)*(t.y-i)-(r.y-i)*(t.x-n)},t}()},61767:(t,e,r)=>{r.d(e,{A:()=>n});let n=function(){function t(){}return t.prototype.isCompact=function(){return this.compact},t.prototype.setCompact=function(t){this.compact=t},t.prototype.getSize=function(){return this.size},t.prototype.setSize=function(t){this.size=t},t.prototype.getLayers=function(){return this.layers},t.prototype.setLayers=function(t){this.layers=t},t.prototype.getCodeWords=function(){return this.codeWords},t.prototype.setCodeWords=function(t){this.codeWords=t},t.prototype.getMatrix=function(){return this.matrix},t.prototype.setMatrix=function(t){this.matrix=t},t}()},61995:(t,e,r)=>{r.d(e,{A:()=>S});var n=r(73216),i=r(22868),o=function(){function t(t){this.previous=t}return t.prototype.getPrevious=function(){return this.previous},t}(),a=r(63479),s=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),u=function(t){function e(e,r,n){var i=t.call(this,e)||this;return i.value=r,i.bitCount=n,i}return s(e,t),e.prototype.appendTo=function(t,e){t.appendBits(this.value,this.bitCount)},e.prototype.add=function(t,r){return new e(this,t,r)},e.prototype.addBinaryShift=function(t,r){return console.warn("addBinaryShift on SimpleToken, this simply returns a copy of this token"),new e(this,t,r)},e.prototype.toString=function(){var t=this.value&(1<<this.bitCount)-1;return t|=1<<this.bitCount,"<"+a.A.toBinaryString(t|1<<this.bitCount).substring(1)+">"},e}(o),f=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),h=function(t){function e(e,r,n){var i=t.call(this,e,0,0)||this;return i.binaryShiftStart=r,i.binaryShiftByteCount=n,i}return f(e,t),e.prototype.appendTo=function(t,e){for(var r=0;r<this.binaryShiftByteCount;r++)(0===r||31===r&&this.binaryShiftByteCount<=62)&&(t.appendBits(31,5),this.binaryShiftByteCount>62?t.appendBits(this.binaryShiftByteCount-31,16):0===r?t.appendBits(Math.min(this.binaryShiftByteCount,31),5):t.appendBits(this.binaryShiftByteCount-31,5)),t.appendBits(e[this.binaryShiftStart+r],8)},e.prototype.addBinaryShift=function(t,r){return new e(this,t,r)},e.prototype.toString=function(){return"<"+this.binaryShiftStart+"::"+(this.binaryShiftStart+this.binaryShiftByteCount-1)+">"},e}(u);function c(t,e,r){return new u(t,e,r)}var p=["UPPER","LOWER","DIGIT","MIXED","PUNCT"],y=new u(null,0,0),l=[Int32Array.from([0,327708,327710,327709,656318]),Int32Array.from([590318,0,327710,327709,656318]),Int32Array.from([262158,590300,0,590301,932798]),Int32Array.from([327709,327708,656318,0,327710]),Int32Array.from([327711,656380,656382,656381,0])],d=r(10077),A=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},g=function(t){var e,r;try{for(var n=A(t),i=n.next();!i.done;i=n.next()){var o=i.value;d.A.fill(o,-1)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return t[0][4]=0,t[1][4]=0,t[1][0]=28,t[3][4]=0,t[2][4]=0,t[2][0]=15,t}(d.A.createInt32Array(6,6)),C=r(23510),v=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},_=function(){function t(t,e,r,n){this.token=t,this.mode=e,this.binaryShiftByteCount=r,this.bitCount=n}return t.prototype.getMode=function(){return this.mode},t.prototype.getToken=function(){return this.token},t.prototype.getBinaryShiftByteCount=function(){return this.binaryShiftByteCount},t.prototype.getBitCount=function(){return this.bitCount},t.prototype.latchAndAppend=function(e,r){var n=this.bitCount,i=this.token;if(e!==this.mode){var o=l[this.mode][e];i=c(i,65535&o,o>>16),n+=o>>16}var a=2===e?4:5;return new t(i=c(i,r,a),e,0,n+a)},t.prototype.shiftAndAppend=function(e,r){var n=this.token,i=2===this.mode?4:5;return n=c(n,g[this.mode][e],i),new t(n=c(n,r,5),this.mode,0,this.bitCount+i+5)},t.prototype.addBinaryShiftChar=function(e){var r=this.token,n=this.mode,i=this.bitCount;if(4===this.mode||2===this.mode){var o=l[n][0];r=c(r,65535&o,o>>16),i+=o>>16,n=0}var a=0===this.binaryShiftByteCount||31===this.binaryShiftByteCount?18:62===this.binaryShiftByteCount?9:8,s=new t(r,n,this.binaryShiftByteCount+1,i+a);return 2078===s.binaryShiftByteCount&&(s=s.endBinaryShift(e+1)),s},t.prototype.endBinaryShift=function(e){if(0===this.binaryShiftByteCount)return this;var r=this.token;return new t(r=new h(r,e-this.binaryShiftByteCount,this.binaryShiftByteCount),this.mode,0,this.bitCount)},t.prototype.isBetterThanOrEqualTo=function(e){var r=this.bitCount+(l[this.mode][e.mode]>>16);return this.binaryShiftByteCount<e.binaryShiftByteCount?r+=t.calculateBinaryShiftCost(e)-t.calculateBinaryShiftCost(this):this.binaryShiftByteCount>e.binaryShiftByteCount&&e.binaryShiftByteCount>0&&(r+=10),r<=e.bitCount},t.prototype.toBitArray=function(t){for(var e,r,n=[],o=this.endBinaryShift(t.length).token;null!==o;o=o.getPrevious())n.unshift(o);var a=new i.A;try{for(var s=v(n),u=s.next();!u.done;u=s.next())u.value.appendTo(a,t)}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}return a},t.prototype.toString=function(){return C.A.format("%s bits=%d bytes=%d",p[this.mode],this.bitCount,this.binaryShiftByteCount)},t.calculateBinaryShiftCost=function(t){return t.binaryShiftByteCount>62?21:t.binaryShiftByteCount>31?20:10*(t.binaryShiftByteCount>0)},t.INITIAL_STATE=new t(y,0,0,0),t}(),E=function(t){var e=C.A.getCharCode(" "),r=C.A.getCharCode("."),n=C.A.getCharCode(",");t[0][e]=1;for(var i=C.A.getCharCode("Z"),o=C.A.getCharCode("A"),a=o;a<=i;a++)t[0][a]=a-o+2;t[1][e]=1;for(var s=C.A.getCharCode("z"),u=C.A.getCharCode("a"),a=u;a<=s;a++)t[1][a]=a-u+2;t[2][e]=1;for(var f=C.A.getCharCode("9"),h=C.A.getCharCode("0"),a=h;a<=f;a++)t[2][a]=a-h+2;t[2][n]=12,t[2][r]=13;for(var c=["\0"," ","\x01","\x02","\x03","\x04","\x05","\x06","\x07","\b","	","\n","\v","\f","\r","\x1b","\x1c","\x1d","\x1e","\x1f","@","\\","^","_","`","|","~",""],p=0;p<c.length;p++)t[3][C.A.getCharCode(c[p])]=p;for(var y=["\0","\r","\0","\0","\0","\0","!","'","#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}"],p=0;p<y.length;p++)C.A.getCharCode(y[p])>0&&(t[4][C.A.getCharCode(y[p])]=p);return t}(d.A.createInt32Array(5,256)),T=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let S=function(){function t(t){this.text=t}return t.prototype.encode=function(){for(var e=C.A.getCharCode(" "),r=C.A.getCharCode("\n"),i=n.A.singletonList(_.INITIAL_STATE),o=0;o<this.text.length;o++){var a=void 0,s=o+1<this.text.length?this.text[o+1]:0;switch(this.text[o]){case C.A.getCharCode("\r"):a=2*(s===r);break;case C.A.getCharCode("."):a=3*(s===e);break;case C.A.getCharCode(","):a=4*(s===e);break;case C.A.getCharCode(":"):a=5*(s===e);break;default:a=0}a>0?(i=t.updateStateListForPair(i,o,a),o++):i=this.updateStateListForChar(i,o)}return n.A.min(i,function(t,e){return t.getBitCount()-e.getBitCount()}).toBitArray(this.text)},t.prototype.updateStateListForChar=function(e,r){var n,i,o=[];try{for(var a=T(e),s=a.next();!s.done;s=a.next()){var u=s.value;this.updateStateForChar(u,r,o)}}catch(t){n={error:t}}finally{try{s&&!s.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}return t.simplifyStates(o)},t.prototype.updateStateForChar=function(t,e,r){for(var n=255&this.text[e],i=E[t.getMode()][n]>0,o=null,a=0;a<=4;a++){var s=E[a][n];if(s>0){if(null==o&&(o=t.endBinaryShift(e)),!i||a===t.getMode()||2===a){var u=o.latchAndAppend(a,s);r.push(u)}if(!i&&g[t.getMode()][a]>=0){var f=o.shiftAndAppend(a,s);r.push(f)}}}if(t.getBinaryShiftByteCount()>0||0===E[t.getMode()][n]){var h=t.addBinaryShiftChar(e);r.push(h)}},t.updateStateListForPair=function(t,e,r){var n,i,o=[];try{for(var a=T(t),s=a.next();!s.done;s=a.next()){var u=s.value;this.updateStateForPair(u,e,r,o)}}catch(t){n={error:t}}finally{try{s&&!s.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}return this.simplifyStates(o)},t.updateStateForPair=function(t,e,r,n){var i=t.endBinaryShift(e);if(n.push(i.latchAndAppend(4,r)),4!==t.getMode()&&n.push(i.shiftAndAppend(4,r)),3===r||4===r){var o=i.latchAndAppend(2,16-r).latchAndAppend(2,1);n.push(o)}if(t.getBinaryShiftByteCount()>0){var a=t.addBinaryShiftChar(e).addBinaryShiftChar(e+1);n.push(a)}},t.simplifyStates=function(t){var e,r,n,i,o=[];try{for(var a=T(t),s=a.next();!s.done;s=a.next()){var u=s.value,f=!0,h=function(t){if(t.isBetterThanOrEqualTo(u))return f=!1,"break";u.isBetterThanOrEqualTo(t)&&(o=o.filter(function(e){return e!==t}))};try{for(var c=(n=void 0,T(o)),p=c.next();!p.done;p=c.next()){var y=p.value,l=h(y);if("break"===l)break}}catch(t){n={error:t}}finally{try{p&&!p.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}f&&o.push(u)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return o},t}()},64191:(t,e,r)=>{r.d(e,{A:()=>c});var n,i=r(55701),o=r(55192),a=r(60109),s=r(39778),u=r(71534),f=r(23510),h=r(63479);!function(t){t[t.UPPER=0]="UPPER",t[t.LOWER=1]="LOWER",t[t.MIXED=2]="MIXED",t[t.DIGIT=3]="DIGIT",t[t.PUNCT=4]="PUNCT",t[t.BINARY=5]="BINARY"}(n||(n={}));let c=function(){function t(){}return t.prototype.decode=function(e){this.ddata=e;var r=e.getBits(),n=this.extractBits(r),o=this.correctBits(n),a=t.convertBoolArrayToByteArray(o),s=t.getEncodedData(o),u=new i.A(a,s,null,null);return u.setNumBits(o.length),u},t.highLevelDecode=function(t){return this.getEncodedData(t)},t.getEncodedData=function(e){for(var r=e.length,i=n.UPPER,o=n.UPPER,a="",s=0;s<r;)if(o===n.BINARY){if(r-s<5)break;var u=t.readCode(e,s,5);if(s+=5,0===u){if(r-s<11)break;u=t.readCode(e,s,11)+31,s+=11}for(var h=0;h<u;h++){if(r-s<8){s=r;break}var c=t.readCode(e,s,8);a+=f.A.castAsNonUtf8Char(c),s+=8}o=i}else{var p=o===n.DIGIT?4:5;if(r-s<p)break;var c=t.readCode(e,s,p);s+=p;var y=t.getCharacter(o,c);y.startsWith("CTRL_")?(i=o,o=t.getTable(y.charAt(5)),"L"===y.charAt(6)&&(i=o)):(a+=y,o=i)}return a},t.getTable=function(t){switch(t){case"L":return n.LOWER;case"P":return n.PUNCT;case"M":return n.MIXED;case"D":return n.DIGIT;case"B":return n.BINARY;default:return n.UPPER}},t.getCharacter=function(e,r){switch(e){case n.UPPER:return t.UPPER_TABLE[r];case n.LOWER:return t.LOWER_TABLE[r];case n.MIXED:return t.MIXED_TABLE[r];case n.PUNCT:return t.PUNCT_TABLE[r];case n.DIGIT:return t.DIGIT_TABLE[r];default:throw new s.A("Bad table")}},t.prototype.correctBits=function(e){2>=this.ddata.getNbLayers()?(n=6,r=o.A.AZTEC_DATA_6):8>=this.ddata.getNbLayers()?(n=8,r=o.A.AZTEC_DATA_8):22>=this.ddata.getNbLayers()?(n=10,r=o.A.AZTEC_DATA_10):(n=12,r=o.A.AZTEC_DATA_12);var r,n,i=this.ddata.getNbDatablocks(),s=e.length/n;if(s<i)throw new u.A;for(var f=e.length%n,h=new Int32Array(s),c=0;c<s;c++,f+=n)h[c]=t.readCode(e,f,n);try{new a.A(r).decode(h,s-i)}catch(t){throw new u.A(t)}for(var p=(1<<n)-1,y=0,c=0;c<i;c++){var l=h[c];if(0===l||l===p)throw new u.A;(1===l||l===p-1)&&y++}for(var d=Array(i*n-y),A=0,c=0;c<i;c++){var l=h[c];if(1===l||l===p-1)d.fill(l>1,A,A+n-1),A+=n-1;else for(var g=n-1;g>=0;--g)d[A++]=(l&1<<g)!=0}return d},t.prototype.extractBits=function(t){var e=this.ddata.isCompact(),r=this.ddata.getNbLayers(),n=(e?11:14)+4*r,i=new Int32Array(n),o=Array(this.totalBitsInLayer(r,e));if(e)for(var a=0;a<i.length;a++)i[a]=a;else for(var s=n+1+2*h.A.truncDivision(h.A.truncDivision(n,2)-1,15),u=n/2,f=h.A.truncDivision(s,2),a=0;a<u;a++){var c=a+h.A.truncDivision(a,15);i[u-a-1]=f-c-1,i[u+a]=f+c+1}for(var a=0,p=0;a<r;a++){for(var y=(r-a)*4+(e?9:12),l=2*a,d=n-1-l,A=0;A<y;A++)for(var g=2*A,C=0;C<2;C++)o[p+g+C]=t.get(i[l+C],i[l+A]),o[p+2*y+g+C]=t.get(i[l+A],i[d-C]),o[p+4*y+g+C]=t.get(i[d-C],i[d-A]),o[p+6*y+g+C]=t.get(i[d-A],i[l+C]);p+=8*y}return o},t.readCode=function(t,e,r){for(var n=0,i=e;i<e+r;i++)n<<=1,t[i]&&(n|=1);return n},t.readByte=function(e,r){var n=e.length-r;return n>=8?t.readCode(e,r,8):t.readCode(e,r,n)<<8-n},t.convertBoolArrayToByteArray=function(e){for(var r=new Uint8Array((e.length+7)/8),n=0;n<r.length;n++)r[n]=t.readByte(e,8*n);return r},t.prototype.totalBitsInLayer=function(t,e){return((e?88:112)+16*t)*t},t.UPPER_TABLE=["CTRL_PS"," ","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","CTRL_LL","CTRL_ML","CTRL_DL","CTRL_BS"],t.LOWER_TABLE=["CTRL_PS"," ","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","CTRL_US","CTRL_ML","CTRL_DL","CTRL_BS"],t.MIXED_TABLE=["CTRL_PS"," ","\x01","\x02","\x03","\x04","\x05","\x06","\x07","\b","	","\n","\v","\f","\r","\x1b","\x1c","\x1d","\x1e","\x1f","@","\\","^","_","`","|","~","","CTRL_LL","CTRL_UL","CTRL_PL","CTRL_BS"],t.PUNCT_TABLE=["","\r","\r\n",". ",", ",": ","!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}","CTRL_UL"],t.DIGIT_TABLE=["CTRL_PS"," ","0","1","2","3","4","5","6","7","8","9",",",".","CTRL_UL","CTRL_US"],t}()},69071:(t,e,r)=>{r.d(e,{A:()=>i});var n=r(322);let i=function(){function t(t,e,r,i,o,a){void 0===r&&(r=null==e?0:8*e.length),void 0===a&&(a=n.A.currentTimeMillis()),this.text=t,this.rawBytes=e,this.numBits=r,this.resultPoints=i,this.format=o,this.timestamp=a,this.text=t,this.rawBytes=e,null==r?this.numBits=null==e?0:8*e.length:this.numBits=r,this.resultPoints=i,this.format=o,this.resultMetadata=null,null==a?this.timestamp=n.A.currentTimeMillis():this.timestamp=a}return t.prototype.getText=function(){return this.text},t.prototype.getRawBytes=function(){return this.rawBytes},t.prototype.getNumBits=function(){return this.numBits},t.prototype.getResultPoints=function(){return this.resultPoints},t.prototype.getBarcodeFormat=function(){return this.format},t.prototype.getResultMetadata=function(){return this.resultMetadata},t.prototype.putMetadata=function(t,e){null===this.resultMetadata&&(this.resultMetadata=new Map),this.resultMetadata.set(t,e)},t.prototype.putAllMetadata=function(t){null!==t&&(null===this.resultMetadata?this.resultMetadata=t:this.resultMetadata=new Map(t))},t.prototype.addResultPoints=function(t){var e=this.resultPoints;if(null===e)this.resultPoints=t;else if(null!==t&&t.length>0){var r=Array(e.length+t.length);n.A.arraycopy(e,0,r,0,e.length),n.A.arraycopy(t,0,r,e.length,t.length),this.resultPoints=r}},t.prototype.getTimestamp=function(){return this.timestamp},t.prototype.toString=function(){return this.text},t}()},71614:(t,e,r)=>{r.d(e,{A:()=>o});var n=r(56451),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let o=function(t){function e(e,r,n,i,o){var a=t.call(this,e,r)||this;return a.compact=n,a.nbDatablocks=i,a.nbLayers=o,a}return i(e,t),e.prototype.getNbLayers=function(){return this.nbLayers},e.prototype.getNbDatablocks=function(){return this.nbDatablocks},e.prototype.isCompact=function(){return this.compact},e}(n.A)},72106:(t,e,r)=>{r.d(e,{A:()=>h});var n=r(69071),i=r(25969),o=r(79417),a=r(43358),s=r(322),u=r(64191),f=r(43197);let h=function(){function t(){}return t.prototype.decode=function(t,e){void 0===e&&(e=null);var r=null,o=new f.A(t.getBlackMatrix()),h=null,c=null;try{var p=o.detectMirror(!1);h=p.getPoints(),this.reportFoundResultPoints(e,h),c=new u.A().decode(p)}catch(t){r=t}if(null==c)try{var p=o.detectMirror(!0);h=p.getPoints(),this.reportFoundResultPoints(e,h),c=new u.A().decode(p)}catch(t){if(null!=r)throw r;throw t}var y=new n.A(c.getText(),c.getRawBytes(),c.getNumBits(),h,i.A.AZTEC,s.A.currentTimeMillis()),l=c.getByteSegments();null!=l&&y.putMetadata(a.A.BYTE_SEGMENTS,l);var d=c.getECLevel();return null!=d&&y.putMetadata(a.A.ERROR_CORRECTION_LEVEL,d),y},t.prototype.reportFoundResultPoints=function(t,e){if(null!=t){var r=t.get(o.A.NEED_RESULT_POINT_CALLBACK);null!=r&&e.forEach(function(t,e,n){r.foundPossibleResultPoint(t)})}},t.prototype.reset=function(){},t}()},93682:(t,e,r)=>{r.d(e,{A:()=>o});var n=r(75511),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.kind="WriterException",e}(n.A)}}]);