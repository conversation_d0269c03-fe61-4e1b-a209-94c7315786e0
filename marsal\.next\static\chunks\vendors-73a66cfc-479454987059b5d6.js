"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3610],{27051:(t,e,r)=>{r.d(e,{A:()=>n});let n=function(){function t(){this.segmentCount=-1,this.fileSize=-1,this.timestamp=-1,this.checksum=-1}return t.prototype.getSegmentIndex=function(){return this.segmentIndex},t.prototype.setSegmentIndex=function(t){this.segmentIndex=t},t.prototype.getFileId=function(){return this.fileId},t.prototype.setFileId=function(t){this.fileId=t},t.prototype.getOptionalData=function(){return this.optionalData},t.prototype.setOptionalData=function(t){this.optionalData=t},t.prototype.isLastSegment=function(){return this.lastSegment},t.prototype.setLastSegment=function(t){this.lastSegment=t},t.prototype.getSegmentCount=function(){return this.segmentCount},t.prototype.setSegmentCount=function(t){this.segmentCount=t},t.prototype.getSender=function(){return this.sender||null},t.prototype.setSender=function(t){this.sender=t},t.prototype.getAddressee=function(){return this.addressee||null},t.prototype.setAddressee=function(t){this.addressee=t},t.prototype.getFileName=function(){return this.fileName},t.prototype.setFileName=function(t){this.fileName=t},t.prototype.getFileSize=function(){return this.fileSize},t.prototype.setFileSize=function(t){this.fileSize=t},t.prototype.getChecksum=function(){return this.checksum},t.prototype.setChecksum=function(t){this.checksum=t},t.prototype.getTimestamp=function(){return this.timestamp},t.prototype.setTimestamp=function(t){this.timestamp=t},t}()},39188:(t,e,r)=>{r.d(e,{A:()=>k});var n=r(25969),o=r(66950),i=r(71534),u=r(438),a=r(69071),l=r(93770),s=r(63479),c=r(43358),f=r(56595),d=r(322),h=r(10077),g=function(){function t(t,e){this.bits=t,this.points=e}return t.prototype.getBits=function(){return this.bits},t.prototype.getPoints=function(){return this.points},t}(),C=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},_=function(){function t(){}return t.detectMultiple=function(e,r,n){var o=e.getBlackMatrix(),i=t.detect(n,o);return i.length||((o=o.clone()).rotate180(),i=t.detect(n,o)),new g(o,i)},t.detect=function(e,r){for(var n,o,i=[],u=0,a=0,l=!1;u<r.getHeight();){var s=t.findVertices(r,u,a);if(null==s[0]&&null==s[3]){if(!l)break;l=!1,a=0;try{for(var c=(n=void 0,C(i)),f=c.next();!f.done;f=c.next()){var d=f.value;null!=d[1]&&(u=Math.trunc(Math.max(u,d[1].getY()))),null!=d[3]&&(u=Math.max(u,Math.trunc(d[3].getY())))}}catch(t){n={error:t}}finally{try{f&&!f.done&&(o=c.return)&&o.call(c)}finally{if(n)throw n.error}}u+=t.ROW_STEP;continue}if(l=!0,i.push(s),!e)break;null!=s[2]?(a=Math.trunc(s[2].getX()),u=Math.trunc(s[2].getY())):(a=Math.trunc(s[4].getX()),u=Math.trunc(s[4].getY()))}return i},t.findVertices=function(e,r,n){var o=e.getHeight(),i=e.getWidth(),u=Array(8);return t.copyToResult(u,t.findRowsWithPattern(e,o,i,r,n,t.START_PATTERN),t.INDEXES_START_PATTERN),null!=u[4]&&(n=Math.trunc(u[4].getX()),r=Math.trunc(u[4].getY())),t.copyToResult(u,t.findRowsWithPattern(e,o,i,r,n,t.STOP_PATTERN),t.INDEXES_STOP_PATTERN),u},t.copyToResult=function(t,e,r){for(var n=0;n<r.length;n++)t[r[n]]=e[n]},t.findRowsWithPattern=function(e,r,n,o,i,u){for(var a=[,,,,],l=!1,s=new Int32Array(u.length);o<r;o+=t.ROW_STEP){var c=t.findGuardPattern(e,i,o,n,!1,u,s);if(null!=c){for(;o>0;){var d=t.findGuardPattern(e,i,--o,n,!1,u,s);if(null!=d)c=d;else{o++;break}}a[0]=new f.A(c[0],o),a[1]=new f.A(c[1],o),l=!0;break}}var g=o+1;if(l){for(var C=0,d=Int32Array.from([Math.trunc(a[0].getX()),Math.trunc(a[1].getX())]);g<r;g++){var c=t.findGuardPattern(e,d[0],g,n,!1,u,s);if(null!=c&&Math.abs(d[0]-c[0])<t.MAX_PATTERN_DRIFT&&Math.abs(d[1]-c[1])<t.MAX_PATTERN_DRIFT)d=c,C=0;else if(C>t.SKIPPED_ROW_COUNT_MAX)break;else C++}g-=C+1,a[2]=new f.A(d[0],g),a[3]=new f.A(d[1],g)}return g-o<t.BARCODE_MIN_HEIGHT&&h.A.fill(a,null),a},t.findGuardPattern=function(e,r,n,o,i,u,a){h.A.fillWithin(a,0,a.length,0);for(var l=r,s=0;e.get(l,n)&&l>0&&s++<t.MAX_PIXEL_DRIFT;)l--;for(var c=l,f=0,g=u.length,C=i;c<o;c++)if(e.get(c,n)!==C)a[f]++;else{if(f===g-1){if(t.patternMatchVariance(a,u,t.MAX_INDIVIDUAL_VARIANCE)<t.MAX_AVG_VARIANCE)return new Int32Array([l,c]);l+=a[0]+a[1],d.A.arraycopy(a,2,a,0,f-1),a[f-1]=0,a[f]=0,f--}else f++;a[f]=1,C=!C}return f===g-1&&t.patternMatchVariance(a,u,t.MAX_INDIVIDUAL_VARIANCE)<t.MAX_AVG_VARIANCE?new Int32Array([l,c-1]):null},t.patternMatchVariance=function(t,e,r){for(var n=t.length,o=0,i=0,u=0;u<n;u++)o+=t[u],i+=e[u];if(o<i)return 1/0;var a=o/i;r*=a;for(var l=0,s=0;s<n;s++){var c=t[s],f=e[s]*a,d=c>f?c-f:f-c;if(d>r)return 1/0;l+=d}return l/o},t.INDEXES_START_PATTERN=Int32Array.from([0,4,1,5]),t.INDEXES_STOP_PATTERN=Int32Array.from([6,2,7,3]),t.MAX_AVG_VARIANCE=.42,t.MAX_INDIVIDUAL_VARIANCE=.8,t.START_PATTERN=Int32Array.from([8,1,1,1,1,1,1,3]),t.STOP_PATTERN=Int32Array.from([7,1,1,3,1,1,1,2,1]),t.MAX_PIXEL_DRIFT=3,t.MAX_PATTERN_DRIFT=5,t.SKIPPED_ROW_COUNT_MAX=25,t.ROW_STEP=5,t.BARCODE_MIN_HEIGHT=10,t}(),A=r(79886),O=r(74150),m=function(){function t(e,r,n,o,i){e instanceof t?this.constructor_2(e):this.constructor_1(e,r,n,o,i)}return t.prototype.constructor_1=function(t,e,r,n,o){var i=null==e||null==r,a=null==n||null==o;if(i&&a)throw new u.A;i?(e=new f.A(0,n.getY()),r=new f.A(0,o.getY())):a&&(n=new f.A(t.getWidth()-1,e.getY()),o=new f.A(t.getWidth()-1,r.getY())),this.image=t,this.topLeft=e,this.bottomLeft=r,this.topRight=n,this.bottomRight=o,this.minX=Math.trunc(Math.min(e.getX(),r.getX())),this.maxX=Math.trunc(Math.max(n.getX(),o.getX())),this.minY=Math.trunc(Math.min(e.getY(),n.getY())),this.maxY=Math.trunc(Math.max(r.getY(),o.getY()))},t.prototype.constructor_2=function(t){this.image=t.image,this.topLeft=t.getTopLeft(),this.bottomLeft=t.getBottomLeft(),this.topRight=t.getTopRight(),this.bottomRight=t.getBottomRight(),this.minX=t.getMinX(),this.maxX=t.getMaxX(),this.minY=t.getMinY(),this.maxY=t.getMaxY()},t.merge=function(e,r){return null==e?r:null==r?e:new t(e.image,e.topLeft,e.bottomLeft,r.topRight,r.bottomRight)},t.prototype.addMissingRows=function(e,r,n){var o=this.topLeft,i=this.bottomLeft,u=this.topRight,a=this.bottomRight;if(e>0){var l=n?this.topLeft:this.topRight,s=Math.trunc(l.getY()-e);s<0&&(s=0);var c=new f.A(l.getX(),s);n?o=c:u=c}if(r>0){var d=n?this.bottomLeft:this.bottomRight,h=Math.trunc(d.getY()+r);h>=this.image.getHeight()&&(h=this.image.getHeight()-1);var g=new f.A(d.getX(),h);n?i=g:a=g}return new t(this.image,o,i,u,a)},t.prototype.getMinX=function(){return this.minX},t.prototype.getMaxX=function(){return this.maxX},t.prototype.getMinY=function(){return this.minY},t.prototype.getMaxY=function(){return this.maxY},t.prototype.getTopLeft=function(){return this.topLeft},t.prototype.getTopRight=function(){return this.topRight},t.prototype.getBottomLeft=function(){return this.bottomLeft},t.prototype.getBottomRight=function(){return this.bottomRight},t}(),p=function(){function t(t,e,r,n){this.columnCount=t,this.errorCorrectionLevel=n,this.rowCountUpperPart=e,this.rowCountLowerPart=r,this.rowCount=e+r}return t.prototype.getColumnCount=function(){return this.columnCount},t.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},t.prototype.getRowCount=function(){return this.rowCount},t.prototype.getRowCountUpperPart=function(){return this.rowCountUpperPart},t.prototype.getRowCountLowerPart=function(){return this.rowCountLowerPart},t}(),E=r(3447),w=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},R=function(){function t(t){this.boundingBox=new m(t),this.codewords=Array(t.getMaxY()-t.getMinY()+1)}return t.prototype.getCodewordNearby=function(e){var r=this.getCodeword(e);if(null!=r)return r;for(var n=1;n<t.MAX_NEARBY_DISTANCE;n++){var o=this.imageRowToCodewordIndex(e)-n;if(o>=0&&null!=(r=this.codewords[o])||(o=this.imageRowToCodewordIndex(e)+n)<this.codewords.length&&null!=(r=this.codewords[o]))return r}return null},t.prototype.imageRowToCodewordIndex=function(t){return t-this.boundingBox.getMinY()},t.prototype.setCodeword=function(t,e){this.codewords[this.imageRowToCodewordIndex(t)]=e},t.prototype.getCodeword=function(t){return this.codewords[this.imageRowToCodewordIndex(t)]},t.prototype.getBoundingBox=function(){return this.boundingBox},t.prototype.getCodewords=function(){return this.codewords},t.prototype.toString=function(){var t,e,r=new E.A,n=0;try{for(var o=w(this.codewords),i=o.next();!i.done;i=o.next()){var u=i.value;if(null==u){r.format("%3d:    |   %n",n++);continue}r.format("%3d: %3d|%3d%n",n++,u.getRowNumber(),u.getValue())}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return r.toString()},t.MAX_NEARBY_DISTANCE=5,t}(),M=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},y=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},T=function(){function t(){this.values=new Map}return t.prototype.setValue=function(t){t=Math.trunc(t);var e=this.values.get(t);null==e&&(e=0),e++,this.values.set(t,e)},t.prototype.getValue=function(){var t,e,r=-1,n=[],o=function(t,e){var o={getKey:function(){return t},getValue:function(){return e}};o.getValue()>r?(r=o.getValue(),(n=[]).push(o.getKey())):o.getValue()===r&&n.push(o.getKey())};try{for(var i=M(this.values.entries()),u=i.next();!u.done;u=i.next()){var a=y(u.value,2),s=a[0],c=a[1];o(s,c)}}catch(e){t={error:e}}finally{try{u&&!u.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}return l.A.toIntArray(n)},t.prototype.getConfidence=function(t){return this.values.get(t)},t}(),I=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),v=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},N=function(t){function e(e,r){var n=t.call(this,e)||this;return n._isLeft=r,n}return I(e,t),e.prototype.setRowNumbers=function(){var t,e;try{for(var r=v(this.getCodewords()),n=r.next();!n.done;n=r.next()){var o=n.value;null!=o&&o.setRowNumberAsRowIndicatorColumn()}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e.prototype.adjustCompleteIndicatorColumnRowNumbers=function(t){var e=this.getCodewords();this.setRowNumbers(),this.removeIncorrectCodewords(e,t);for(var r=this.getBoundingBox(),n=this._isLeft?r.getTopLeft():r.getTopRight(),o=this._isLeft?r.getBottomLeft():r.getBottomRight(),i=this.imageRowToCodewordIndex(Math.trunc(n.getY())),u=this.imageRowToCodewordIndex(Math.trunc(o.getY())),a=-1,l=1,s=0,c=i;c<u;c++)if(null!=e[c]){var f=e[c],d=f.getRowNumber()-a;if(0===d)s++;else if(1===d)l=Math.max(l,s),s=1,a=f.getRowNumber();else if(d<0||f.getRowNumber()>=t.getRowCount()||d>c)e[c]=null;else{for(var h=void 0,g=(h=l>2?(l-2)*d:d)>=c,C=1;C<=h&&!g;C++)g=null!=e[c-C];g?e[c]=null:(a=f.getRowNumber(),s=1)}}},e.prototype.getRowHeights=function(){var t,e,r=this.getBarcodeMetadata();if(null==r)return null;this.adjustIncompleteIndicatorColumnRowNumbers(r);var n=new Int32Array(r.getRowCount());try{for(var o=v(this.getCodewords()),i=o.next();!i.done;i=o.next()){var u=i.value;if(null!=u){var a=u.getRowNumber();if(a>=n.length)continue;n[a]++}}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return n},e.prototype.adjustIncompleteIndicatorColumnRowNumbers=function(t){for(var e=this.getBoundingBox(),r=this._isLeft?e.getTopLeft():e.getTopRight(),n=this._isLeft?e.getBottomLeft():e.getBottomRight(),o=this.imageRowToCodewordIndex(Math.trunc(r.getY())),i=this.imageRowToCodewordIndex(Math.trunc(n.getY())),u=this.getCodewords(),a=-1,l=1,s=0,c=o;c<i;c++)if(null!=u[c]){var f=u[c];f.setRowNumberAsRowIndicatorColumn();var d=f.getRowNumber()-a;0===d?s++:1===d?(l=Math.max(l,s),s=1,a=f.getRowNumber()):f.getRowNumber()>=t.getRowCount()?u[c]=null:(a=f.getRowNumber(),s=1)}},e.prototype.getBarcodeMetadata=function(){var t,e,r=this.getCodewords(),n=new T,o=new T,i=new T,u=new T;try{for(var a=v(r),s=a.next();!s.done;s=a.next()){var c=s.value;if(null!=c){c.setRowNumberAsRowIndicatorColumn();var f=c.getValue()%30,d=c.getRowNumber();switch(!this._isLeft&&(d+=2),d%3){case 0:o.setValue(3*f+1);break;case 1:u.setValue(f/3),i.setValue(f%3);break;case 2:n.setValue(f+1)}}}}catch(e){t={error:e}}finally{try{s&&!s.done&&(e=a.return)&&e.call(a)}finally{if(t)throw t.error}}if(0===n.getValue().length||0===o.getValue().length||0===i.getValue().length||0===u.getValue().length||n.getValue()[0]<1||o.getValue()[0]+i.getValue()[0]<l.A.MIN_ROWS_IN_BARCODE||o.getValue()[0]+i.getValue()[0]>l.A.MAX_ROWS_IN_BARCODE)return null;var h=new p(n.getValue()[0],o.getValue()[0],i.getValue()[0],u.getValue()[0]);return this.removeIncorrectCodewords(r,h),h},e.prototype.removeIncorrectCodewords=function(t,e){for(var r=0;r<t.length;r++){var n=t[r];if(null!=t[r]){var o=n.getValue()%30,i=n.getRowNumber();if(i>e.getRowCount()){t[r]=null;continue}switch(!this._isLeft&&(i+=2),i%3){case 0:3*o+1!==e.getRowCountUpperPart()&&(t[r]=null);break;case 1:(Math.trunc(o/3)!==e.getErrorCorrectionLevel()||o%3!==e.getRowCountLowerPart())&&(t[r]=null);break;case 2:o+1!==e.getColumnCount()&&(t[r]=null)}}}},e.prototype.isLeft=function(){return this._isLeft},e.prototype.toString=function(){return"IsLeft: "+this._isLeft+"\n"+t.prototype.toString.call(this)},e}(R),b=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},D=function(){function t(t,e){this.ADJUST_ROW_NUMBER_SKIP=2,this.barcodeMetadata=t,this.barcodeColumnCount=t.getColumnCount(),this.boundingBox=e,this.detectionResultColumns=Array(this.barcodeColumnCount+2)}return t.prototype.getDetectionResultColumns=function(){this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[0]),this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[this.barcodeColumnCount+1]);var t,e=l.A.MAX_CODEWORDS_IN_BARCODE;do t=e,e=this.adjustRowNumbersAndGetCount();while(e>0&&e<t);return this.detectionResultColumns},t.prototype.adjustIndicatorColumnRowNumbers=function(t){null!=t&&t.adjustCompleteIndicatorColumnRowNumbers(this.barcodeMetadata)},t.prototype.adjustRowNumbersAndGetCount=function(){var t=this.adjustRowNumbersByRow();if(0===t)return 0;for(var e=1;e<this.barcodeColumnCount+1;e++)for(var r=this.detectionResultColumns[e].getCodewords(),n=0;n<r.length;n++)null!=r[n]&&(r[n].hasValidRowNumber()||this.adjustRowNumbers(e,n,r));return t},t.prototype.adjustRowNumbersByRow=function(){return this.adjustRowNumbersFromBothRI(),this.adjustRowNumbersFromLRI()+this.adjustRowNumbersFromRRI()},t.prototype.adjustRowNumbersFromBothRI=function(){if(null!=this.detectionResultColumns[0]&&null!=this.detectionResultColumns[this.barcodeColumnCount+1]){for(var t=this.detectionResultColumns[0].getCodewords(),e=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),r=0;r<t.length;r++)if(null!=t[r]&&null!=e[r]&&t[r].getRowNumber()===e[r].getRowNumber())for(var n=1;n<=this.barcodeColumnCount;n++){var o=this.detectionResultColumns[n].getCodewords()[r];null!=o&&(o.setRowNumber(t[r].getRowNumber()),o.hasValidRowNumber()||(this.detectionResultColumns[n].getCodewords()[r]=null))}}},t.prototype.adjustRowNumbersFromRRI=function(){if(null==this.detectionResultColumns[this.barcodeColumnCount+1])return 0;for(var e=0,r=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),n=0;n<r.length;n++)if(null!=r[n])for(var o=r[n].getRowNumber(),i=0,u=this.barcodeColumnCount+1;u>0&&i<this.ADJUST_ROW_NUMBER_SKIP;u--){var a=this.detectionResultColumns[u].getCodewords()[n];null!=a&&(i=t.adjustRowNumberIfValid(o,i,a),!a.hasValidRowNumber()&&e++)}return e},t.prototype.adjustRowNumbersFromLRI=function(){if(null==this.detectionResultColumns[0])return 0;for(var e=0,r=this.detectionResultColumns[0].getCodewords(),n=0;n<r.length;n++)if(null!=r[n])for(var o=r[n].getRowNumber(),i=0,u=1;u<this.barcodeColumnCount+1&&i<this.ADJUST_ROW_NUMBER_SKIP;u++){var a=this.detectionResultColumns[u].getCodewords()[n];null!=a&&(i=t.adjustRowNumberIfValid(o,i,a),!a.hasValidRowNumber()&&e++)}return e},t.adjustRowNumberIfValid=function(t,e,r){return null==r||!r.hasValidRowNumber()&&(r.isValidRowNumber(t)?(r.setRowNumber(t),e=0):++e),e},t.prototype.adjustRowNumbers=function(e,r,n){if(null!=this.detectionResultColumns[e-1]){var o,i,u=n[r],a=this.detectionResultColumns[e-1].getCodewords(),l=a;null!=this.detectionResultColumns[e+1]&&(l=this.detectionResultColumns[e+1].getCodewords());var s=Array(14);s[2]=a[r],s[3]=l[r],r>0&&(s[0]=n[r-1],s[4]=a[r-1],s[5]=l[r-1]),r>1&&(s[8]=n[r-2],s[10]=a[r-2],s[11]=l[r-2]),r<n.length-1&&(s[1]=n[r+1],s[6]=a[r+1],s[7]=l[r+1]),r<n.length-2&&(s[9]=n[r+2],s[12]=a[r+2],s[13]=l[r+2]);try{for(var c=b(s),f=c.next();!f.done;f=c.next()){var d=f.value;if(t.adjustRowNumber(u,d))return}}catch(t){o={error:t}}finally{try{f&&!f.done&&(i=c.return)&&i.call(c)}finally{if(o)throw o.error}}}},t.adjustRowNumber=function(t,e){return null!=e&&!!e.hasValidRowNumber()&&e.getBucket()===t.getBucket()&&(t.setRowNumber(e.getRowNumber()),!0)},t.prototype.getBarcodeColumnCount=function(){return this.barcodeColumnCount},t.prototype.getBarcodeRowCount=function(){return this.barcodeMetadata.getRowCount()},t.prototype.getBarcodeECLevel=function(){return this.barcodeMetadata.getErrorCorrectionLevel()},t.prototype.setBoundingBox=function(t){this.boundingBox=t},t.prototype.getBoundingBox=function(){return this.boundingBox},t.prototype.setDetectionResultColumn=function(t,e){this.detectionResultColumns[t]=e},t.prototype.getDetectionResultColumn=function(t){return this.detectionResultColumns[t]},t.prototype.toString=function(){var t=this.detectionResultColumns[0];null==t&&(t=this.detectionResultColumns[this.barcodeColumnCount+1]);for(var e=new E.A,r=0;r<t.getCodewords().length;r++){e.format("CW %3d:",r);for(var n=0;n<this.barcodeColumnCount+2;n++){if(null==this.detectionResultColumns[n]){e.format("    |   ");continue}var o=this.detectionResultColumns[n].getCodewords()[r];if(null==o){e.format("    |   ");continue}e.format(" %3d|%3d",o.getRowNumber(),o.getValue())}e.format("%n")}return e.toString()},t}(),S=function(){function t(e,r,n,o){this.rowNumber=t.BARCODE_ROW_UNKNOWN,this.startX=Math.trunc(e),this.endX=Math.trunc(r),this.bucket=Math.trunc(n),this.value=Math.trunc(o)}return t.prototype.hasValidRowNumber=function(){return this.isValidRowNumber(this.rowNumber)},t.prototype.isValidRowNumber=function(e){return e!==t.BARCODE_ROW_UNKNOWN&&this.bucket===e%3*3},t.prototype.setRowNumberAsRowIndicatorColumn=function(){this.rowNumber=Math.trunc(3*Math.trunc(this.value/30)+Math.trunc(this.bucket/3))},t.prototype.getWidth=function(){return this.endX-this.startX},t.prototype.getStartX=function(){return this.startX},t.prototype.getEndX=function(){return this.endX},t.prototype.getBucket=function(){return this.bucket},t.prototype.getValue=function(){return this.value},t.prototype.getRowNumber=function(){return this.rowNumber},t.prototype.setRowNumber=function(t){this.rowNumber=t},t.prototype.toString=function(){return this.rowNumber+"|"+this.value},t.BARCODE_ROW_UNKNOWN=-1,t}(),L=r(46263),P=function(){function t(){}return t.initialize=function(){for(var e=0;e<l.A.SYMBOL_TABLE.length;e++)for(var r=l.A.SYMBOL_TABLE[e],n=1&r,o=0;o<l.A.BARS_IN_MODULE;o++){for(var i=0;(1&r)===n;)i+=1,r>>=1;n=1&r,t.RATIOS_TABLE[e]||(t.RATIOS_TABLE[e]=Array(l.A.BARS_IN_MODULE)),t.RATIOS_TABLE[e][l.A.BARS_IN_MODULE-o-1]=Math.fround(i/l.A.MODULES_IN_CODEWORD)}this.bSymbolTableReady=!0},t.getDecodedValue=function(e){var r=t.getDecodedCodewordValue(t.sampleBitCounts(e));return -1!==r?r:t.getClosestDecodedValue(e)},t.sampleBitCounts=function(t){for(var e=A.A.sum(t),r=new Int32Array(l.A.BARS_IN_MODULE),n=0,o=0,i=0;i<l.A.MODULES_IN_CODEWORD;i++){var u=e/(2*l.A.MODULES_IN_CODEWORD)+i*e/l.A.MODULES_IN_CODEWORD;o+t[n]<=u&&(o+=t[n],n++),r[n]++}return r},t.getDecodedCodewordValue=function(e){var r=t.getBitValue(e);return -1===l.A.getCodeword(r)?-1:r},t.getBitValue=function(t){for(var e=0,r=0;r<t.length;r++)for(var n=0;n<t[r];n++)e=e<<1|r%2==0;return Math.trunc(e)},t.getClosestDecodedValue=function(e){var r=A.A.sum(e),n=Array(l.A.BARS_IN_MODULE);if(r>1)for(var o=0;o<n.length;o++)n[o]=Math.fround(e[o]/r);var i=L.A.MAX_VALUE,u=-1;this.bSymbolTableReady||t.initialize();for(var a=0;a<t.RATIOS_TABLE.length;a++){for(var s=0,c=t.RATIOS_TABLE[a],f=0;f<l.A.BARS_IN_MODULE;f++){var d=Math.fround(c[f]-n[f]);if((s+=Math.fround(d*d))>=i)break}s<i&&(i=s,u=l.A.SYMBOL_TABLE[a])}return u},t.bSymbolTableReady=!1,t.RATIOS_TABLE=Array(l.A.SYMBOL_TABLE.length).map(function(t){return Array(l.A.BARS_IN_MODULE)}),t}(),B=r(45442),x=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},F=function(){function t(){}return t.decode=function(e,r,n,o,i,a,l){for(var s,c=new m(e,r,n,o,i),f=null,d=null,h=!0;;h=!1){if(null!=r&&(f=t.getRowIndicatorColumn(e,c,r,!0,a,l)),null!=o&&(d=t.getRowIndicatorColumn(e,c,o,!1,a,l)),null==(s=t.merge(f,d)))throw u.A.getNotFoundInstance();var g=s.getBoundingBox();if(h&&null!=g&&(g.getMinY()<c.getMinY()||g.getMaxY()>c.getMaxY()))c=g;else break}s.setBoundingBox(c);var C=s.getBarcodeColumnCount()+1;s.setDetectionResultColumn(0,f),s.setDetectionResultColumn(C,d);for(var _=null!=f,A=1;A<=C;A++){var O=_?A:C-A;if(void 0===s.getDetectionResultColumn(O)){var p=void 0;p=0===O||O===C?new N(c,0===O):new R(c),s.setDetectionResultColumn(O,p);for(var E=-1,w=-1,M=c.getMinY();M<=c.getMaxY();M++){if((E=t.getStartColumn(s,O,M,_))<0||E>c.getMaxX()){if(-1===w)continue;E=w}var y=t.detectCodeword(e,c.getMinX(),c.getMaxX(),_,E,M,a,l);null!=y&&(p.setCodeword(M,y),w=E,a=Math.min(a,y.getWidth()),l=Math.max(l,y.getWidth()))}}}return t.createDecoderResult(s)},t.merge=function(e,r){if(null==e&&null==r)return null;var n=t.getBarcodeMetadata(e,r);return null==n?null:new D(n,m.merge(t.adjustBoundingBox(e),t.adjustBoundingBox(r)))},t.adjustBoundingBox=function(e){if(null==e)return null;var r,n,o=e.getRowHeights();if(null==o)return null;var i=t.getMax(o),u=0;try{for(var a=x(o),l=a.next();!l.done;l=a.next()){var s=l.value;if(u+=i-s,s>0)break}}catch(t){r={error:t}}finally{try{l&&!l.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}for(var c=e.getCodewords(),f=0;u>0&&null==c[f];f++)u--;for(var d=0,f=o.length-1;f>=0&&(d+=i-o[f],!(o[f]>0));f--);for(var f=c.length-1;d>0&&null==c[f];f--)d--;return e.getBoundingBox().addMissingRows(u,d,e.isLeft())},t.getMax=function(t){var e,r,n=-1;try{for(var o=x(t),i=o.next();!i.done;i=o.next()){var u=i.value;n=Math.max(n,u)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n},t.getBarcodeMetadata=function(t,e){var r,n;return null==t||null==(r=t.getBarcodeMetadata())?null==e?null:e.getBarcodeMetadata():null==e||null==(n=e.getBarcodeMetadata())?r:r.getColumnCount()!==n.getColumnCount()&&r.getErrorCorrectionLevel()!==n.getErrorCorrectionLevel()&&r.getRowCount()!==n.getRowCount()?null:r},t.getRowIndicatorColumn=function(e,r,n,o,i,u){for(var a=new N(r,o),l=0;l<2;l++)for(var s=0===l?1:-1,c=Math.trunc(Math.trunc(n.getX())),f=Math.trunc(Math.trunc(n.getY()));f<=r.getMaxY()&&f>=r.getMinY();f+=s){var d=t.detectCodeword(e,0,e.getWidth(),o,c,f,i,u);null!=d&&(a.setCodeword(f,d),c=o?d.getStartX():d.getEndX())}return a},t.adjustCodewordCount=function(e,r){var n=r[0][1],o=n.getValue(),i=e.getBarcodeColumnCount()*e.getBarcodeRowCount()-t.getNumberOfECCodeWords(e.getBarcodeECLevel());if(0===o.length){if(i<1||i>l.A.MAX_CODEWORDS_IN_BARCODE)throw u.A.getNotFoundInstance();n.setValue(i)}else o[0]!==i&&n.setValue(i)},t.createDecoderResult=function(e){var r=t.createBarcodeMatrix(e);t.adjustCodewordCount(e,r);for(var n=[],o=new Int32Array(e.getBarcodeRowCount()*e.getBarcodeColumnCount()),i=[],u=[],a=0;a<e.getBarcodeRowCount();a++)for(var s=0;s<e.getBarcodeColumnCount();s++){var c=r[a][s+1].getValue(),f=a*e.getBarcodeColumnCount()+s;0===c.length?n.push(f):1===c.length?o[f]=c[0]:(u.push(f),i.push(c))}for(var d=Array(i.length),h=0;h<d.length;h++)d[h]=i[h];return t.createDecoderResultFromAmbiguousValues(e.getBarcodeECLevel(),o,l.A.toIntArray(n),l.A.toIntArray(u),d)},t.createDecoderResultFromAmbiguousValues=function(e,r,n,i,u){for(var a=new Int32Array(i.length),l=100;l-- >0;){for(var s=0;s<a.length;s++)r[i[s]]=u[s][a[s]];try{return t.decodeCodewords(r,e,n)}catch(t){if(!(t instanceof o.A))throw t}if(0===a.length)break;for(var s=0;s<a.length;s++)if(a[s]<u[s].length-1){a[s]++;break}else if(a[s]=0,s===a.length-1)throw o.A.getChecksumInstance()}throw o.A.getChecksumInstance()},t.createBarcodeMatrix=function(t){for(var e,r,n,o,i=Array.from({length:t.getBarcodeRowCount()},function(){return Array(t.getBarcodeColumnCount()+2)}),u=0;u<i.length;u++)for(var a=0;a<i[u].length;a++)i[u][a]=new T;var l=0;try{for(var s=x(t.getDetectionResultColumns()),c=s.next();!c.done;c=s.next()){var f=c.value;if(null!=f)try{for(var d=(n=void 0,x(f.getCodewords())),h=d.next();!h.done;h=d.next()){var g=h.value;if(null!=g){var C=g.getRowNumber();if(C>=0){if(C>=i.length)continue;i[C][l].setValue(g.getValue())}}}}catch(t){n={error:t}}finally{try{h&&!h.done&&(o=d.return)&&o.call(d)}finally{if(n)throw n.error}}l++}}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}return i},t.isValidBarcodeColumn=function(t,e){return e>=0&&e<=t.getBarcodeColumnCount()+1},t.getStartColumn=function(e,r,n,o){var i,u,a=o?1:-1,l=null;if(t.isValidBarcodeColumn(e,r-a)&&(l=e.getDetectionResultColumn(r-a).getCodeword(n)),null!=l)return o?l.getEndX():l.getStartX();if(null!=(l=e.getDetectionResultColumn(r).getCodewordNearby(n)))return o?l.getStartX():l.getEndX();if(t.isValidBarcodeColumn(e,r-a)&&(l=e.getDetectionResultColumn(r-a).getCodewordNearby(n)),null!=l)return o?l.getEndX():l.getStartX();for(var s=0;t.isValidBarcodeColumn(e,r-a);){r-=a;try{for(var c=(i=void 0,x(e.getDetectionResultColumn(r).getCodewords())),f=c.next();!f.done;f=c.next()){var d=f.value;if(null!=d)return(o?d.getEndX():d.getStartX())+a*s*(d.getEndX()-d.getStartX())}}catch(t){i={error:t}}finally{try{f&&!f.done&&(u=c.return)&&u.call(c)}finally{if(i)throw i.error}}s++}return o?e.getBoundingBox().getMinX():e.getBoundingBox().getMaxX()},t.detectCodeword=function(e,r,n,o,i,u,a,s){i=t.adjustCodewordStartColumn(e,r,n,o,i,u);var c,f=t.getModuleBitCount(e,r,n,o,i,u);if(null==f)return null;var d=A.A.sum(f);if(o)c=i+d;else{for(var h=0;h<f.length/2;h++){var g=f[h];f[h]=f[f.length-1-h],f[f.length-1-h]=g}i=(c=i)-d}if(!t.checkCodewordSkew(d,a,s))return null;var C=P.getDecodedValue(f),_=l.A.getCodeword(C);return -1===_?null:new S(i,c,t.getCodewordBucketNumber(C),_)},t.getModuleBitCount=function(t,e,r,n,o,i){for(var u=o,a=new Int32Array(8),l=0,s=n?1:-1,c=n;(n?u<r:u>=e)&&l<a.length;)t.get(u,i)===c?(a[l]++,u+=s):(l++,c=!c);return l===a.length||u===(n?r:e)&&l===a.length-1?a:null},t.getNumberOfECCodeWords=function(t){return 2<<t},t.adjustCodewordStartColumn=function(e,r,n,o,i,u){for(var a=i,l=o?-1:1,s=0;s<2;s++){for(;(o?a>=r:a<n)&&o===e.get(a,u);){if(Math.abs(i-a)>t.CODEWORD_SKEW_SIZE)return i;a+=l}l=-l,o=!o}return a},t.checkCodewordSkew=function(e,r,n){return r-t.CODEWORD_SKEW_SIZE<=e&&e<=n+t.CODEWORD_SKEW_SIZE},t.decodeCodewords=function(e,r,n){if(0===e.length)throw i.A.getFormatInstance();var o=1<<r+1,u=t.correctErrors(e,n,o);t.verifyCodewordCount(e,o);var a=B.A.decode(e,""+r);return a.setErrorsCorrected(u),a.setErasures(n.length),a},t.correctErrors=function(e,r,n){if(null!=r&&r.length>n/2+t.MAX_ERRORS||n<0||n>t.MAX_EC_CODEWORDS)throw o.A.getChecksumInstance();return t.errorCorrection.decode(e,n,r)},t.verifyCodewordCount=function(t,e){if(t.length<4)throw i.A.getFormatInstance();var r=t[0];if(r>t.length)throw i.A.getFormatInstance();if(0===r)if(e<t.length)t[0]=t.length-e;else throw i.A.getFormatInstance()},t.getBitCountForCodeword=function(t){for(var e=new Int32Array(8),r=0,n=e.length-1;!((1&t)!==r&&(r=1&t,--n<0));)e[n]++,t>>=1;return e},t.getCodewordBucketNumber=function(t){return t instanceof Int32Array?this.getCodewordBucketNumber_Int32Array(t):this.getCodewordBucketNumber_number(t)},t.getCodewordBucketNumber_number=function(e){return t.getCodewordBucketNumber(t.getBitCountForCodeword(e))},t.getCodewordBucketNumber_Int32Array=function(t){return(t[0]-t[2]+t[4]-t[6]+9)%9},t.toString=function(t){for(var e=new E.A,r=0;r<t.length;r++){e.format("Row %2d: ",r);for(var n=0;n<t[r].length;n++){var o=t[r][n];0===o.getValue().length?e.format("        ",null):e.format("%4d(%2d)",o.getValue()[0],o.getConfidence(o.getValue()[0]))}e.format("%n")}return e.toString()},t.CODEWORD_SKEW_SIZE=2,t.MAX_ERRORS=3,t.MAX_EC_CODEWORDS=512,t.errorCorrection=new O.A,t}(),X=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let k=function(){function t(){}return t.prototype.decode=function(e,r){void 0===r&&(r=null);var n=t.decode(e,r,!1);if(null==n||0===n.length||null==n[0])throw u.A.getNotFoundInstance();return n[0]},t.prototype.decodeMultiple=function(e,r){void 0===r&&(r=null);try{return t.decode(e,r,!0)}catch(t){if(t instanceof i.A||t instanceof o.A)throw u.A.getNotFoundInstance();throw t}},t.decode=function(e,r,o){var i,u,l=[],s=_.detectMultiple(e,r,o);try{for(var f=X(s.getPoints()),d=f.next();!d.done;d=f.next()){var h=d.value,g=F.decode(s.getBits(),h[4],h[5],h[6],h[7],t.getMinCodewordWidth(h),t.getMaxCodewordWidth(h)),C=new a.A(g.getText(),g.getRawBytes(),void 0,h,n.A.PDF_417);C.putMetadata(c.A.ERROR_CORRECTION_LEVEL,g.getECLevel());var A=g.getOther();null!=A&&C.putMetadata(c.A.PDF417_EXTRA_METADATA,A),l.push(C)}}catch(t){i={error:t}}finally{try{d&&!d.done&&(u=f.return)&&u.call(f)}finally{if(i)throw i.error}}return l.map(function(t){return t})},t.getMaxWidth=function(t,e){return null==t||null==e?0:Math.trunc(Math.abs(t.getX()-e.getX()))},t.getMinWidth=function(t,e){return null==t||null==e?s.A.MAX_VALUE:Math.trunc(Math.abs(t.getX()-e.getX()))},t.getMaxCodewordWidth=function(e){return Math.floor(Math.max(Math.max(t.getMaxWidth(e[0],e[4]),t.getMaxWidth(e[6],e[2])*l.A.MODULES_IN_CODEWORD/l.A.MODULES_IN_STOP_PATTERN),Math.max(t.getMaxWidth(e[1],e[5]),t.getMaxWidth(e[7],e[3])*l.A.MODULES_IN_CODEWORD/l.A.MODULES_IN_STOP_PATTERN)))},t.getMinCodewordWidth=function(e){return Math.floor(Math.min(Math.min(t.getMinWidth(e[0],e[4]),t.getMinWidth(e[6],e[2])*l.A.MODULES_IN_CODEWORD/l.A.MODULES_IN_STOP_PATTERN),Math.min(t.getMinWidth(e[1],e[5]),t.getMinWidth(e[7],e[3])*l.A.MODULES_IN_CODEWORD/l.A.MODULES_IN_STOP_PATTERN)))},t.prototype.reset=function(){},t}()},45442:(t,e,r)=>{r.d(e,{A:()=>A});var n,o,i=r(71534),u=r(59612),a=r(55701),l=r(27051),s=r(10077),c=r(1933),f=r(63479),d=r(55161),h=r(30260),g=r(63623);function C(){if("undefined"!=typeof window)return window.BigInt||null;if(void 0!==r.g)return r.g.BigInt||null;if("undefined"!=typeof self)return self.BigInt||null;throw Error("Can't search globals for BigInt!")}function _(t){if(void 0===o&&(o=C()),null===o)throw Error("BigInt is not supported!");return o(t)}!function(t){t[t.ALPHA=0]="ALPHA",t[t.LOWER=1]="LOWER",t[t.MIXED=2]="MIXED",t[t.PUNCT=3]="PUNCT",t[t.ALPHA_SHIFT=4]="ALPHA_SHIFT",t[t.PUNCT_SHIFT=5]="PUNCT_SHIFT"}(n||(n={}));let A=function(){function t(){}return t.decode=function(e,r){var n=new c.A(""),o=u.A.ISO8859_1;n.enableDecoding(o);for(var s=1,f=e[s++],d=new l.A;s<e[0];){switch(f){case t.TEXT_COMPACTION_MODE_LATCH:s=t.textCompaction(e,s,n);break;case t.BYTE_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:s=t.byteCompaction(f,e,o,s,n);break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(e[s++]);break;case t.NUMERIC_COMPACTION_MODE_LATCH:s=t.numericCompaction(e,s,n);break;case t.ECI_CHARSET:u.A.getCharacterSetECIByValue(e[s++]);break;case t.ECI_GENERAL_PURPOSE:s+=2;break;case t.ECI_USER_DEFINED:s++;break;case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:s=t.decodeMacroBlock(e,s,d);break;case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:throw new i.A;default:s--,s=t.textCompaction(e,s,n)}if(s<e.length)f=e[s++];else throw i.A.getFormatInstance()}if(0===n.length())throw i.A.getFormatInstance();var h=new a.A(null,n.toString(),null,r);return h.setOther(d),h},t.decodeMacroBlock=function(e,r,n){if(r+t.NUMBER_OF_SEQUENCE_CODEWORDS>e[0])throw i.A.getFormatInstance();for(var o=new Int32Array(t.NUMBER_OF_SEQUENCE_CODEWORDS),u=0;u<t.NUMBER_OF_SEQUENCE_CODEWORDS;u++,r++)o[u]=e[r];n.setSegmentIndex(f.A.parseInt(t.decodeBase900toBase10(o,t.NUMBER_OF_SEQUENCE_CODEWORDS)));var a=new c.A;r=t.textCompaction(e,r,a),n.setFileId(a.toString());var l=-1;for(e[r]===t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD&&(l=r+1);r<e[0];)switch(e[r]){case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:switch(e[++r]){case t.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME:var h=new c.A;r=t.textCompaction(e,r+1,h),n.setFileName(h.toString());break;case t.MACRO_PDF417_OPTIONAL_FIELD_SENDER:var g=new c.A;r=t.textCompaction(e,r+1,g),n.setSender(g.toString());break;case t.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE:var C=new c.A;r=t.textCompaction(e,r+1,C),n.setAddressee(C.toString());break;case t.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT:var _=new c.A;r=t.numericCompaction(e,r+1,_),n.setSegmentCount(f.A.parseInt(_.toString()));break;case t.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP:var A=new c.A;r=t.numericCompaction(e,r+1,A),n.setTimestamp(d.A.parseLong(A.toString()));break;case t.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM:var O=new c.A;r=t.numericCompaction(e,r+1,O),n.setChecksum(f.A.parseInt(O.toString()));break;case t.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE:var m=new c.A;r=t.numericCompaction(e,r+1,m),n.setFileSize(d.A.parseLong(m.toString()));break;default:throw i.A.getFormatInstance()}break;case t.MACRO_PDF417_TERMINATOR:r++,n.setLastSegment(!0);break;default:throw i.A.getFormatInstance()}if(-1!==l){var p=r-l;n.isLastSegment()&&p--,n.setOptionalData(s.A.copyOfRange(e,l,l+p))}return r},t.textCompaction=function(e,r,n){for(var o=new Int32Array((e[0]-r)*2),i=new Int32Array((e[0]-r)*2),u=0,a=!1;r<e[0]&&!a;){var l=e[r++];if(l<t.TEXT_COMPACTION_MODE_LATCH)o[u]=l/30,o[u+1]=l%30,u+=2;else switch(l){case t.TEXT_COMPACTION_MODE_LATCH:o[u++]=t.TEXT_COMPACTION_MODE_LATCH;break;case t.BYTE_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.NUMERIC_COMPACTION_MODE_LATCH:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:r--,a=!0;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:o[u]=t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE,l=e[r++],i[u]=l,u++}}return t.decodeTextCompaction(o,i,u,n),r},t.decodeTextCompaction=function(e,r,o,i){for(var u=n.ALPHA,a=n.ALPHA,l=0;l<o;){var s=e[l],c="";switch(u){case n.ALPHA:if(s<26)c=String.fromCharCode(65+s);else switch(s){case 26:c=" ";break;case t.LL:u=n.LOWER;break;case t.ML:u=n.MIXED;break;case t.PS:a=u,u=n.PUNCT_SHIFT;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[l]);break;case t.TEXT_COMPACTION_MODE_LATCH:u=n.ALPHA}break;case n.LOWER:if(s<26)c=String.fromCharCode(97+s);else switch(s){case 26:c=" ";break;case t.AS:a=u,u=n.ALPHA_SHIFT;break;case t.ML:u=n.MIXED;break;case t.PS:a=u,u=n.PUNCT_SHIFT;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[l]);break;case t.TEXT_COMPACTION_MODE_LATCH:u=n.ALPHA}break;case n.MIXED:if(s<t.PL)c=t.MIXED_CHARS[s];else switch(s){case t.PL:u=n.PUNCT;break;case 26:c=" ";break;case t.LL:u=n.LOWER;break;case t.AL:u=n.ALPHA;break;case t.PS:a=u,u=n.PUNCT_SHIFT;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[l]);break;case t.TEXT_COMPACTION_MODE_LATCH:u=n.ALPHA}break;case n.PUNCT:if(s<t.PAL)c=t.PUNCT_CHARS[s];else switch(s){case t.PAL:u=n.ALPHA;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[l]);break;case t.TEXT_COMPACTION_MODE_LATCH:u=n.ALPHA}break;case n.ALPHA_SHIFT:if(u=a,s<26)c=String.fromCharCode(65+s);else switch(s){case 26:c=" ";break;case t.TEXT_COMPACTION_MODE_LATCH:u=n.ALPHA}break;case n.PUNCT_SHIFT:if(u=a,s<t.PAL)c=t.PUNCT_CHARS[s];else switch(s){case t.PAL:u=n.ALPHA;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[l]);break;case t.TEXT_COMPACTION_MODE_LATCH:u=n.ALPHA}}""!==c&&i.append(c),l++}},t.byteCompaction=function(e,r,n,o,i){var u=new h.A,a=0,l=0,s=!1;switch(e){case t.BYTE_COMPACTION_MODE_LATCH:for(var c=new Int32Array(6),f=r[o++];o<r[0]&&!s;)switch(c[a++]=f,l=900*l+f,f=r[o++]){case t.TEXT_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH:case t.NUMERIC_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:o--,s=!0;break;default:if(a%5==0&&a>0){for(var d=0;d<6;++d)u.write(Number(_(l)>>_(8*(5-d))));l=0,a=0}}o===r[0]&&f<t.TEXT_COMPACTION_MODE_LATCH&&(c[a++]=f);for(var C=0;C<a;C++)u.write(c[C]);break;case t.BYTE_COMPACTION_MODE_LATCH_6:for(;o<r[0]&&!s;){var A=r[o++];if(A<t.TEXT_COMPACTION_MODE_LATCH)a++,l=900*l+A;else switch(A){case t.TEXT_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH:case t.NUMERIC_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:o--,s=!0}if(a%5==0&&a>0){for(var d=0;d<6;++d)u.write(Number(_(l)>>_(8*(5-d))));l=0,a=0}}}return i.append(g.A.decode(u.toByteArray(),n)),o},t.numericCompaction=function(e,r,n){for(var o=0,i=!1,u=new Int32Array(t.MAX_NUMERIC_CODEWORDS);r<e[0]&&!i;){var a=e[r++];if(r===e[0]&&(i=!0),a<t.TEXT_COMPACTION_MODE_LATCH)u[o]=a,o++;else switch(a){case t.TEXT_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:r--,i=!0}(o%t.MAX_NUMERIC_CODEWORDS==0||a===t.NUMERIC_COMPACTION_MODE_LATCH||i)&&o>0&&(n.append(t.decodeBase900toBase10(u,o)),o=0)}return r},t.decodeBase900toBase10=function(e,r){for(var n=_(0),o=0;o<r;o++)n+=t.EXP900[r-o-1]*_(e[o]);var u=n.toString();if("1"!==u.charAt(0))throw new i.A;return u.substring(1)},t.TEXT_COMPACTION_MODE_LATCH=900,t.BYTE_COMPACTION_MODE_LATCH=901,t.NUMERIC_COMPACTION_MODE_LATCH=902,t.BYTE_COMPACTION_MODE_LATCH_6=924,t.ECI_USER_DEFINED=925,t.ECI_GENERAL_PURPOSE=926,t.ECI_CHARSET=927,t.BEGIN_MACRO_PDF417_CONTROL_BLOCK=928,t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD=923,t.MACRO_PDF417_TERMINATOR=922,t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE=913,t.MAX_NUMERIC_CODEWORDS=15,t.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME=0,t.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT=1,t.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP=2,t.MACRO_PDF417_OPTIONAL_FIELD_SENDER=3,t.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE=4,t.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE=5,t.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM=6,t.PL=25,t.LL=27,t.AS=27,t.ML=28,t.AL=28,t.PS=29,t.PAL=29,t.PUNCT_CHARS=";<>@[\\]_`~!\r	,:\n-.$/\"|*()?{}'",t.MIXED_CHARS="0123456789&\r	,:#-.$/+%*=^",t.EXP900=C()?function(){var t=[];t[0]=_(1);var e=_(900);t[1]=e;for(var r=2;r<16;r++)t[r]=t[r-1]*e;return t}():[],t.NUMBER_OF_SEQUENCE_CODEWORDS=2,t}()},74150:(t,e,r)=>{r.d(e,{A:()=>C});var n=r(66950),o=r(38988),i=r(322),u=r(1933),a=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},l=function(){function t(t,e){if(0===e.length)throw new o.A;this.field=t;var r=e.length;if(r>1&&0===e[0]){for(var n=1;n<r&&0===e[n];)n++;n===r?this.coefficients=new Int32Array([0]):(this.coefficients=new Int32Array(r-n),i.A.arraycopy(e,n,this.coefficients,0,this.coefficients.length))}else this.coefficients=e}return t.prototype.getCoefficients=function(){return this.coefficients},t.prototype.getDegree=function(){return this.coefficients.length-1},t.prototype.isZero=function(){return 0===this.coefficients[0]},t.prototype.getCoefficient=function(t){return this.coefficients[this.coefficients.length-1-t]},t.prototype.evaluateAt=function(t){if(0===t)return this.getCoefficient(0);if(1===t){var e,r,n=0;try{for(var o=a(this.coefficients),i=o.next();!i.done;i=o.next()){var u=i.value;n=this.field.add(n,u)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n}for(var l=this.coefficients[0],s=this.coefficients.length,c=1;c<s;c++)l=this.field.add(this.field.multiply(t,l),this.coefficients[c]);return l},t.prototype.add=function(e){if(!this.field.equals(e.field))throw new o.A("ModulusPolys do not have same ModulusGF field");if(this.isZero())return e;if(e.isZero())return this;var r=this.coefficients,n=e.coefficients;if(r.length>n.length){var u=r;r=n,n=u}var a=new Int32Array(n.length),l=n.length-r.length;i.A.arraycopy(n,0,a,0,l);for(var s=l;s<n.length;s++)a[s]=this.field.add(r[s-l],n[s]);return new t(this.field,a)},t.prototype.subtract=function(t){if(!this.field.equals(t.field))throw new o.A("ModulusPolys do not have same ModulusGF field");return t.isZero()?this:this.add(t.negative())},t.prototype.multiply=function(e){return e instanceof t?this.multiplyOther(e):this.multiplyScalar(e)},t.prototype.multiplyOther=function(e){if(!this.field.equals(e.field))throw new o.A("ModulusPolys do not have same ModulusGF field");if(this.isZero()||e.isZero())return new t(this.field,new Int32Array([0]));for(var r=this.coefficients,n=r.length,i=e.coefficients,u=i.length,a=new Int32Array(n+u-1),l=0;l<n;l++)for(var s=r[l],c=0;c<u;c++)a[l+c]=this.field.add(a[l+c],this.field.multiply(s,i[c]));return new t(this.field,a)},t.prototype.negative=function(){for(var e=this.coefficients.length,r=new Int32Array(e),n=0;n<e;n++)r[n]=this.field.subtract(0,this.coefficients[n]);return new t(this.field,r)},t.prototype.multiplyScalar=function(e){if(0===e)return new t(this.field,new Int32Array([0]));if(1===e)return this;for(var r=this.coefficients.length,n=new Int32Array(r),o=0;o<r;o++)n[o]=this.field.multiply(this.coefficients[o],e);return new t(this.field,n)},t.prototype.multiplyByMonomial=function(e,r){if(e<0)throw new o.A;if(0===r)return new t(this.field,new Int32Array([0]));for(var n=this.coefficients.length,i=new Int32Array(n+e),u=0;u<n;u++)i[u]=this.field.multiply(this.coefficients[u],r);return new t(this.field,i)},t.prototype.toString=function(){for(var t=new u.A,e=this.getDegree();e>=0;e--){var r=this.getCoefficient(e);0!==r&&(r<0?(t.append(" - "),r=-r):t.length()>0&&t.append(" + "),(0===e||1!==r)&&t.append(r),0!==e&&(1===e?t.append("x"):(t.append("x^"),t.append(e))))}return t.toString()},t}(),s=r(93770),c=r(73179),f=function(){function t(){}return t.prototype.add=function(t,e){return(t+e)%this.modulus},t.prototype.subtract=function(t,e){return(this.modulus+t-e)%this.modulus},t.prototype.exp=function(t){return this.expTable[t]},t.prototype.log=function(t){if(0===t)throw new o.A;return this.logTable[t]},t.prototype.inverse=function(t){if(0===t)throw new c.A;return this.expTable[this.modulus-this.logTable[t]-1]},t.prototype.multiply=function(t,e){return 0===t||0===e?0:this.expTable[(this.logTable[t]+this.logTable[e])%(this.modulus-1)]},t.prototype.getSize=function(){return this.modulus},t.prototype.equals=function(t){return t===this},t}(),d=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),h=function(t){function e(e,r){var n=t.call(this)||this;n.modulus=e,n.expTable=new Int32Array(e),n.logTable=new Int32Array(e);for(var o=1,i=0;i<e;i++)n.expTable[i]=o,o=o*r%e;for(var i=0;i<e-1;i++)n.logTable[n.expTable[i]]=i;return n.zero=new l(n,new Int32Array([0])),n.one=new l(n,new Int32Array([1])),n}return d(e,t),e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,e){if(t<0)throw new o.A;if(0===e)return this.zero;var r=new Int32Array(t+1);return r[0]=e,new l(this,r)},e.PDF417_GF=new e(s.A.NUMBER_OF_CODEWORDS,3),e}(f),g=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let C=function(){function t(){this.field=h.PDF417_GF}return t.prototype.decode=function(t,e,r){for(var o,i,u=new l(this.field,t),a=new Int32Array(e),s=!1,c=e;c>0;c--){var f=u.evaluateAt(this.field.exp(c));a[e-c]=f,0!==f&&(s=!0)}if(!s)return 0;var d=this.field.getOne();if(null!=r)try{for(var h=g(r),C=h.next();!C.done;C=h.next()){var _=C.value,A=this.field.exp(t.length-1-_),O=new l(this.field,new Int32Array([this.field.subtract(0,A),1]));d=d.multiply(O)}}catch(t){o={error:t}}finally{try{C&&!C.done&&(i=h.return)&&i.call(h)}finally{if(o)throw o.error}}for(var m=new l(this.field,a),p=this.runEuclideanAlgorithm(this.field.buildMonomial(e,1),m,e),E=p[0],w=p[1],R=this.findErrorLocations(E),M=this.findErrorMagnitudes(w,E,R),c=0;c<R.length;c++){var y=t.length-1-this.field.log(R[c]);if(y<0)throw n.A.getChecksumInstance();t[y]=this.field.subtract(t[y],M[c])}return R.length},t.prototype.runEuclideanAlgorithm=function(t,e,r){if(t.getDegree()<e.getDegree()){var o=t;t=e,e=o}for(var i=t,u=e,a=this.field.getZero(),l=this.field.getOne();u.getDegree()>=Math.round(r/2);){var s=i,c=a;if(i=u,a=l,i.isZero())throw n.A.getChecksumInstance();u=s;for(var f=this.field.getZero(),d=i.getCoefficient(i.getDegree()),h=this.field.inverse(d);u.getDegree()>=i.getDegree()&&!u.isZero();){var g=u.getDegree()-i.getDegree(),C=this.field.multiply(u.getCoefficient(u.getDegree()),h);f=f.add(this.field.buildMonomial(g,C)),u=u.subtract(i.multiplyByMonomial(g,C))}l=f.multiply(a).subtract(c).negative()}var _=l.getCoefficient(0);if(0===_)throw n.A.getChecksumInstance();var A=this.field.inverse(_);return[l.multiply(A),u.multiply(A)]},t.prototype.findErrorLocations=function(t){for(var e=t.getDegree(),r=new Int32Array(e),o=0,i=1;i<this.field.getSize()&&o<e;i++)0===t.evaluateAt(i)&&(r[o]=this.field.inverse(i),o++);if(o!==e)throw n.A.getChecksumInstance();return r},t.prototype.findErrorMagnitudes=function(t,e,r){for(var n=e.getDegree(),o=new Int32Array(n),i=1;i<=n;i++)o[n-i]=this.field.multiply(i,e.getCoefficient(i));for(var u=new l(this.field,o),a=r.length,s=new Int32Array(a),i=0;i<a;i++){var c=this.field.inverse(r[i]),f=this.field.subtract(0,t.evaluateAt(c)),d=this.field.inverse(u.evaluateAt(c));s[i]=this.field.multiply(f,d)}return s},t}()},93770:(t,e,r)=>{r.d(e,{A:()=>u});var n=r(10077),o=r(79886),i=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let u=function(){function t(){}return t.prototype.PDF417Common=function(){},t.getBitCountSum=function(t){return o.A.sum(t)},t.toIntArray=function(e){if(null==e||!e.length)return t.EMPTY_INT_ARRAY;var r,n,o=new Int32Array(e.length),u=0;try{for(var a=i(e),l=a.next();!l.done;l=a.next()){var s=l.value;o[u++]=s}}catch(t){r={error:t}}finally{try{l&&!l.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}return o},t.getCodeword=function(e){var r=n.A.binarySearch(t.SYMBOL_TABLE,262143&e);return r<0?-1:(t.CODEWORD_TABLE[r]-1)%t.NUMBER_OF_CODEWORDS},t.NUMBER_OF_CODEWORDS=929,t.MAX_CODEWORDS_IN_BARCODE=t.NUMBER_OF_CODEWORDS-1,t.MIN_ROWS_IN_BARCODE=3,t.MAX_ROWS_IN_BARCODE=90,t.MODULES_IN_CODEWORD=17,t.MODULES_IN_STOP_PATTERN=18,t.BARS_IN_MODULE=8,t.EMPTY_INT_ARRAY=new Int32Array([]),t.SYMBOL_TABLE=Int32Array.from([66142,66170,66206,66236,66290,66292,66350,66382,66396,66454,66470,66476,66594,66600,66614,66626,66628,66632,66640,66654,66662,66668,66682,66690,66718,66720,66748,66758,66776,66798,66802,66804,66820,66824,66832,66846,66848,66876,66880,66936,66950,66956,66968,66992,67006,67022,67036,67042,67044,67048,67062,67118,67150,67164,67214,67228,67256,67294,67322,67350,67366,67372,67398,67404,67416,67438,67474,67476,67490,67492,67496,67510,67618,67624,67650,67656,67664,67678,67686,67692,67706,67714,67716,67728,67742,67744,67772,67782,67788,67800,67822,67826,67828,67842,67848,67870,67872,67900,67904,67960,67974,67992,68016,68030,68046,68060,68066,68068,68072,68086,68104,68112,68126,68128,68156,68160,68216,68336,68358,68364,68376,68400,68414,68448,68476,68494,68508,68536,68546,68548,68552,68560,68574,68582,68588,68654,68686,68700,68706,68708,68712,68726,68750,68764,68792,68802,68804,68808,68816,68830,68838,68844,68858,68878,68892,68920,68976,68990,68994,68996,69e3,69008,69022,69024,69052,69062,69068,69080,69102,69106,69108,69142,69158,69164,69190,69208,69230,69254,69260,69272,69296,69310,69326,69340,69386,69394,69396,69410,69416,69430,69442,69444,69448,69456,69470,69478,69484,69554,69556,69666,69672,69698,69704,69712,69726,69754,69762,69764,69776,69790,69792,69820,69830,69836,69848,69870,69874,69876,69890,69918,69920,69948,69952,70008,70022,70040,70064,70078,70094,70108,70114,70116,70120,70134,70152,70174,70176,70264,70384,70412,70448,70462,70496,70524,70542,70556,70584,70594,70600,70608,70622,70630,70636,70664,70672,70686,70688,70716,70720,70776,70896,71136,71180,71192,71216,71230,71264,71292,71360,71416,71452,71480,71536,71550,71554,71556,71560,71568,71582,71584,71612,71622,71628,71640,71662,71726,71732,71758,71772,71778,71780,71784,71798,71822,71836,71864,71874,71880,71888,71902,71910,71916,71930,71950,71964,71992,72048,72062,72066,72068,72080,72094,72096,72124,72134,72140,72152,72174,72178,72180,72206,72220,72248,72304,72318,72416,72444,72456,72464,72478,72480,72508,72512,72568,72588,72600,72624,72638,72654,72668,72674,72676,72680,72694,72726,72742,72748,72774,72780,72792,72814,72838,72856,72880,72894,72910,72924,72930,72932,72936,72950,72966,72972,72984,73008,73022,73056,73084,73102,73116,73144,73156,73160,73168,73182,73190,73196,73210,73226,73234,73236,73250,73252,73256,73270,73282,73284,73296,73310,73318,73324,73346,73348,73352,73360,73374,73376,73404,73414,73420,73432,73454,73498,73518,73522,73524,73550,73564,73570,73572,73576,73590,73800,73822,73858,73860,73872,73886,73888,73916,73944,73970,73972,73992,74014,74016,74044,74048,74104,74118,74136,74160,74174,74210,74212,74216,74230,74244,74256,74270,74272,74360,74480,74502,74508,74544,74558,74592,74620,74638,74652,74680,74690,74696,74704,74726,74732,74782,74784,74812,74992,75232,75288,75326,75360,75388,75456,75512,75576,75632,75646,75650,75652,75664,75678,75680,75708,75718,75724,75736,75758,75808,75836,75840,75896,76016,76256,76736,76824,76848,76862,76896,76924,76992,77048,77296,77340,77368,77424,77438,77536,77564,77572,77576,77584,77600,77628,77632,77688,77702,77708,77720,77744,77758,77774,77788,77870,77902,77916,77922,77928,77966,77980,78008,78018,78024,78032,78046,78060,78074,78094,78136,78192,78206,78210,78212,78224,78238,78240,78268,78278,78284,78296,78322,78324,78350,78364,78448,78462,78560,78588,78600,78622,78624,78652,78656,78712,78726,78744,78768,78782,78798,78812,78818,78820,78824,78838,78862,78876,78904,78960,78974,79072,79100,79296,79352,79368,79376,79390,79392,79420,79424,79480,79600,79628,79640,79664,79678,79712,79740,79772,79800,79810,79812,79816,79824,79838,79846,79852,79894,79910,79916,79942,79948,79960,79982,79988,80006,80024,80048,80062,80078,80092,80098,80100,80104,80134,80140,80176,80190,80224,80252,80270,80284,80312,80328,80336,80350,80358,80364,80378,80390,80396,80408,80432,80446,80480,80508,80576,80632,80654,80668,80696,80752,80766,80776,80784,80798,80800,80828,80844,80856,80878,80882,80884,80914,80916,80930,80932,80936,80950,80962,80968,80976,80990,80998,81004,81026,81028,81040,81054,81056,81084,81094,81100,81112,81134,81154,81156,81160,81168,81182,81184,81212,81216,81272,81286,81292,81304,81328,81342,81358,81372,81380,81384,81398,81434,81454,81458,81460,81486,81500,81506,81508,81512,81526,81550,81564,81592,81602,81604,81608,81616,81630,81638,81644,81702,81708,81722,81734,81740,81752,81774,81778,81780,82050,82078,82080,82108,82180,82184,82192,82206,82208,82236,82240,82296,82316,82328,82352,82366,82402,82404,82408,82440,82448,82462,82464,82492,82496,82552,82672,82694,82700,82712,82736,82750,82784,82812,82830,82882,82884,82888,82896,82918,82924,82952,82960,82974,82976,83004,83008,83064,83184,83424,83468,83480,83504,83518,83552,83580,83648,83704,83740,83768,83824,83838,83842,83844,83848,83856,83872,83900,83910,83916,83928,83950,83984,84e3,84028,84032,84088,84208,84448,84928,85040,85054,85088,85116,85184,85240,85488,85560,85616,85630,85728,85756,85764,85768,85776,85790,85792,85820,85824,85880,85894,85900,85912,85936,85966,85980,86048,86080,86136,86256,86496,86976,88160,88188,88256,88312,88560,89056,89200,89214,89312,89340,89536,89592,89608,89616,89632,89664,89720,89840,89868,89880,89904,89952,89980,89998,90012,90040,90190,90204,90254,90268,90296,90306,90308,90312,90334,90382,90396,90424,90480,90494,90500,90504,90512,90526,90528,90556,90566,90572,90584,90610,90612,90638,90652,90680,90736,90750,90848,90876,90884,90888,90896,90910,90912,90940,90944,91e3,91014,91020,91032,91056,91070,91086,91100,91106,91108,91112,91126,91150,91164,91192,91248,91262,91360,91388,91584,91640,91664,91678,91680,91708,91712,91768,91888,91928,91952,91966,92e3,92028,92046,92060,92088,92098,92100,92104,92112,92126,92134,92140,92188,92216,92272,92384,92412,92608,92664,93168,93200,93214,93216,93244,93248,93304,93424,93664,93720,93744,93758,93792,93820,93888,93944,93980,94008,94064,94078,94084,94088,94096,94110,94112,94140,94150,94156,94168,94246,94252,94278,94284,94296,94318,94342,94348,94360,94384,94398,94414,94428,94440,94470,94476,94488,94512,94526,94560,94588,94606,94620,94648,94658,94660,94664,94672,94686,94694,94700,94714,94726,94732,94744,94768,94782,94816,94844,94912,94968,94990,95004,95032,95088,95102,95112,95120,95134,95136,95164,95180,95192,95214,95218,95220,95244,95256,95280,95294,95328,95356,95424,95480,95728,95758,95772,95800,95856,95870,95968,95996,96008,96016,96030,96032,96060,96064,96120,96152,96176,96190,96220,96226,96228,96232,96290,96292,96296,96310,96322,96324,96328,96336,96350,96358,96364,96386,96388,96392,96400,96414,96416,96444,96454,96460,96472,96494,96498,96500,96514,96516,96520,96528,96542,96544,96572,96576,96632,96646,96652,96664,96688,96702,96718,96732,96738,96740,96744,96758,96772,96776,96784,96798,96800,96828,96832,96888,97008,97030,97036,97048,97072,97086,97120,97148,97166,97180,97208,97220,97224,97232,97246,97254,97260,97326,97330,97332,97358,97372,97378,97380,97384,97398,97422,97436,97464,97474,97476,97480,97488,97502,97510,97516,97550,97564,97592,97648,97666,97668,97672,97680,97694,97696,97724,97734,97740,97752,97774,97830,97836,97850,97862,97868,97880,97902,97906,97908,97926,97932,97944,97968,97998,98012,98018,98020,98024,98038,98618,98674,98676,98838,98854,98874,98892,98904,98926,98930,98932,98968,99006,99042,99044,99048,99062,99166,99194,99246,99286,99350,99366,99372,99386,99398,99416,99438,99442,99444,99462,99504,99518,99534,99548,99554,99556,99560,99574,99590,99596,99608,99632,99646,99680,99708,99726,99740,99768,99778,99780,99784,99792,99806,99814,99820,99834,99858,99860,99874,99880,99894,99906,99920,99934,99962,99970,99972,99976,99984,99998,1e5,100028,100038,100044,100056,100078,100082,100084,100142,100174,100188,100246,100262,100268,100306,100308,100390,100396,100410,100422,100428,100440,100462,100466,100468,100486,100504,100528,100542,100558,100572,100578,100580,100584,100598,100620,100656,100670,100704,100732,100750,100792,100802,100808,100816,100830,100838,100844,100858,100888,100912,100926,100960,100988,101056,101112,101148,101176,101232,101246,101250,101252,101256,101264,101278,101280,101308,101318,101324,101336,101358,101362,101364,101410,101412,101416,101430,101442,101448,101456,101470,101478,101498,101506,101508,101520,101534,101536,101564,101580,101618,101620,101636,101640,101648,101662,101664,101692,101696,101752,101766,101784,101838,101858,101860,101864,101934,101938,101940,101966,101980,101986,101988,101992,102030,102044,102072,102082,102084,102088,102096,102138,102166,102182,102188,102214,102220,102232,102254,102282,102290,102292,102306,102308,102312,102326,102444,102458,102470,102476,102488,102514,102516,102534,102552,102576,102590,102606,102620,102626,102632,102646,102662,102668,102704,102718,102752,102780,102798,102812,102840,102850,102856,102864,102878,102886,102892,102906,102936,102974,103008,103036,103104,103160,103224,103280,103294,103298,103300,103312,103326,103328,103356,103366,103372,103384,103406,103410,103412,103472,103486,103520,103548,103616,103672,103920,103992,104048,104062,104160,104188,104194,104196,104200,104208,104224,104252,104256,104312,104326,104332,104344,104368,104382,104398,104412,104418,104420,104424,104482,104484,104514,104520,104528,104542,104550,104570,104578,104580,104592,104606,104608,104636,104652,104690,104692,104706,104712,104734,104736,104764,104768,104824,104838,104856,104910,104930,104932,104936,104968,104976,104990,104992,105020,105024,105080,105200,105240,105278,105312,105372,105410,105412,105416,105424,105446,105518,105524,105550,105564,105570,105572,105576,105614,105628,105656,105666,105672,105680,105702,105722,105742,105756,105784,105840,105854,105858,105860,105864,105872,105888,105932,105970,105972,106006,106022,106028,106054,106060,106072,106100,106118,106124,106136,106160,106174,106190,106210,106212,106216,106250,106258,106260,106274,106276,106280,106306,106308,106312,106320,106334,106348,106394,106414,106418,106420,106566,106572,106610,106612,106630,106636,106648,106672,106686,106722,106724,106728,106742,106758,106764,106776,106800,106814,106848,106876,106894,106908,106936,106946,106948,106952,106960,106974,106982,106988,107032,107056,107070,107104,107132,107200,107256,107292,107320,107376,107390,107394,107396,107400,107408,107422,107424,107452,107462,107468,107480,107502,107506,107508,107544,107568,107582,107616,107644,107712,107768,108016,108060,108088,108144,108158,108256,108284,108290,108292,108296,108304,108318,108320,108348,108352,108408,108422,108428,108440,108464,108478,108494,108508,108514,108516,108520,108592,108640,108668,108736,108792,109040,109536,109680,109694,109792,109820,110016,110072,110084,110088,110096,110112,110140,110144,110200,110320,110342,110348,110360,110384,110398,110432,110460,110478,110492,110520,110532,110536,110544,110558,110658,110686,110714,110722,110724,110728,110736,110750,110752,110780,110796,110834,110836,110850,110852,110856,110864,110878,110880,110908,110912,110968,110982,111e3,111054,111074,111076,111080,111108,111112,111120,111134,111136,111164,111168,111224,111344,111372,111422,111456,111516,111554,111556,111560,111568,111590,111632,111646,111648,111676,111680,111736,111856,112096,112152,112224,112252,112320,112440,112514,112516,112520,112528,112542,112544,112588,112686,112718,112732,112782,112796,112824,112834,112836,112840,112848,112870,112890,112910,112924,112952,113008,113022,113026,113028,113032,113040,113054,113056,113100,113138,113140,113166,113180,113208,113264,113278,113376,113404,113416,113424,113440,113468,113472,113560,113614,113634,113636,113640,113686,113702,113708,113734,113740,113752,113778,113780,113798,113804,113816,113840,113854,113870,113890,113892,113896,113926,113932,113944,113968,113982,114016,114044,114076,114114,114116,114120,114128,114150,114170,114194,114196,114210,114212,114216,114242,114244,114248,114256,114270,114278,114306,114308,114312,114320,114334,114336,114364,114380,114420,114458,114478,114482,114484,114510,114524,114530,114532,114536,114842,114866,114868,114970,114994,114996,115042,115044,115048,115062,115130,115226,115250,115252,115278,115292,115298,115300,115304,115318,115342,115394,115396,115400,115408,115422,115430,115436,115450,115478,115494,115514,115526,115532,115570,115572,115738,115758,115762,115764,115790,115804,115810,115812,115816,115830,115854,115868,115896,115906,115912,115920,115934,115942,115948,115962,115996,116024,116080,116094,116098,116100,116104,116112,116126,116128,116156,116166,116172,116184,116206,116210,116212,116246,116262,116268,116282,116294,116300,116312,116334,116338,116340,116358,116364,116376,116400,116414,116430,116444,116450,116452,116456,116498,116500,116514,116520,116534,116546,116548,116552,116560,116574,116582,116588,116602,116654,116694,116714,116762,116782,116786,116788,116814,116828,116834,116836,116840,116854,116878,116892,116920,116930,116936,116944,116958,116966,116972,116986,117006,117048,117104,117118,117122,117124,117136,117150,117152,117180,117190,117196,117208,117230,117234,117236,117304,117360,117374,117472,117500,117506,117508,117512,117520,117536,117564,117568,117624,117638,117644,117656,117680,117694,117710,117724,117730,117732,117736,117750,117782,117798,117804,117818,117830,117848,117874,117876,117894,117936,117950,117966,117986,117988,117992,118022,118028,118040,118064,118078,118112,118140,118172,118210,118212,118216,118224,118238,118246,118266,118306,118312,118338,118352,118366,118374,118394,118402,118404,118408,118416,118430,118432,118460,118476,118514,118516,118574,118578,118580,118606,118620,118626,118628,118632,118678,118694,118700,118730,118738,118740,118830,118834,118836,118862,118876,118882,118884,118888,118902,118926,118940,118968,118978,118980,118984,118992,119006,119014,119020,119034,119068,119096,119152,119166,119170,119172,119176,119184,119198,119200,119228,119238,119244,119256,119278,119282,119284,119324,119352,119408,119422,119520,119548,119554,119556,119560,119568,119582,119584,119612,119616,119672,119686,119692,119704,119728,119742,119758,119772,119778,119780,119784,119798,119920,119934,120032,120060,120256,120312,120324,120328,120336,120352,120384,120440,120560,120582,120588,120600,120624,120638,120672,120700,120718,120732,120760,120770,120772,120776,120784,120798,120806,120812,120870,120876,120890,120902,120908,120920,120946,120948,120966,120972,120984,121008,121022,121038,121058,121060,121064,121078,121100,121112,121136,121150,121184,121212,121244,121282,121284,121288,121296,121318,121338,121356,121368,121392,121406,121440,121468,121536,121592,121656,121730,121732,121736,121744,121758,121760,121804,121842,121844,121890,121922,121924,121928,121936,121950,121958,121978,121986,121988,121992,122e3,122014,122016,122044,122060,122098,122100,122116,122120,122128,122142,122144,122172,122176,122232,122246,122264,122318,122338,122340,122344,122414,122418,122420,122446,122460,122466,122468,122472,122510,122524,122552,122562,122564,122568,122576,122598,122618,122646,122662,122668,122694,122700,122712,122738,122740,122762,122770,122772,122786,122788,122792,123018,123026,123028,123042,123044,123048,123062,123098,123146,123154,123156,123170,123172,123176,123190,123202,123204,123208,123216,123238,123244,123258,123290,123314,123316,123402,123410,123412,123426,123428,123432,123446,123458,123464,123472,123486,123494,123500,123514,123522,123524,123528,123536,123552,123580,123590,123596,123608,123630,123634,123636,123674,123698,123700,123740,123746,123748,123752,123834,123914,123922,123924,123938,123944,123958,123970,123976,123984,123998,124006,124012,124026,124034,124036,124048,124062,124064,124092,124102,124108,124120,124142,124146,124148,124162,124164,124168,124176,124190,124192,124220,124224,124280,124294,124300,124312,124336,124350,124366,124380,124386,124388,124392,124406,124442,124462,124466,124468,124494,124508,124514,124520,124558,124572,124600,124610,124612,124616,124624,124646,124666,124694,124710,124716,124730,124742,124748,124760,124786,124788,124818,124820,124834,124836,124840,124854,124946,124948,124962,124964,124968,124982,124994,124996,125e3,125008,125022,125030,125036,125050,125058,125060,125064,125072,125086,125088,125116,125126,125132,125144,125166,125170,125172,125186,125188,125192,125200,125216,125244,125248,125304,125318,125324,125336,125360,125374,125390,125404,125410,125412,125416,125430,125444,125448,125456,125472,125504,125560,125680,125702,125708,125720,125744,125758,125792,125820,125838,125852,125880,125890,125892,125896,125904,125918,125926,125932,125978,125998,126002,126004,126030,126044,126050,126052,126056,126094,126108,126136,126146,126148,126152,126160,126182,126202,126222,126236,126264,126320,126334,126338,126340,126344,126352,126366,126368,126412,126450,126452,126486,126502,126508,126522,126534,126540,126552,126574,126578,126580,126598,126604,126616,126640,126654,126670,126684,126690,126692,126696,126738,126754,126756,126760,126774,126786,126788,126792,126800,126814,126822,126828,126842,126894,126898,126900,126934,127126,127142,127148,127162,127178,127186,127188,127254,127270,127276,127290,127302,127308,127320,127342,127346,127348,127370,127378,127380,127394,127396,127400,127450,127510,127526,127532,127546,127558,127576,127598,127602,127604,127622,127628,127640,127664,127678,127694,127708,127714,127716,127720,127734,127754,127762,127764,127778,127784,127810,127812,127816,127824,127838,127846,127866,127898,127918,127922,127924,128022,128038,128044,128058,128070,128076,128088,128110,128114,128116,128134,128140,128152,128176,128190,128206,128220,128226,128228,128232,128246,128262,128268,128280,128304,128318,128352,128380,128398,128412,128440,128450,128452,128456,128464,128478,128486,128492,128506,128522,128530,128532,128546,128548,128552,128566,128578,128580,128584,128592,128606,128614,128634,128642,128644,128648,128656,128670,128672,128700,128716,128754,128756,128794,128814,128818,128820,128846,128860,128866,128868,128872,128886,128918,128934,128940,128954,128978,128980,129178,129198,129202,129204,129238,129258,129306,129326,129330,129332,129358,129372,129378,129380,129384,129398,129430,129446,129452,129466,129482,129490,129492,129562,129582,129586,129588,129614,129628,129634,129636,129640,129654,129678,129692,129720,129730,129732,129736,129744,129758,129766,129772,129814,129830,129836,129850,129862,129868,129880,129902,129906,129908,129930,129938,129940,129954,129956,129960,129974,130010]),t.CODEWORD_TABLE=Int32Array.from([2627,1819,2622,2621,1813,1812,2729,2724,2723,2779,2774,2773,902,896,908,868,865,861,859,2511,873,871,1780,835,2493,825,2491,842,837,844,1764,1762,811,810,809,2483,807,2482,806,2480,815,814,813,812,2484,817,816,1745,1744,1742,1746,2655,2637,2635,2626,2625,2623,2628,1820,2752,2739,2737,2728,2727,2725,2730,2785,2783,2778,2777,2775,2780,787,781,747,739,736,2413,754,752,1719,692,689,681,2371,678,2369,700,697,694,703,1688,1686,642,638,2343,631,2341,627,2338,651,646,643,2345,654,652,1652,1650,1647,1654,601,599,2322,596,2321,594,2319,2317,611,610,608,606,2324,603,2323,615,614,612,1617,1616,1614,1612,616,1619,1618,2575,2538,2536,905,901,898,909,2509,2507,2504,870,867,864,860,2512,875,872,1781,2490,2489,2487,2485,1748,836,834,832,830,2494,827,2492,843,841,839,845,1765,1763,2701,2676,2674,2653,2648,2656,2634,2633,2631,2629,1821,2638,2636,2770,2763,2761,2750,2745,2753,2736,2735,2733,2731,1848,2740,2738,2786,2784,591,588,576,569,566,2296,1590,537,534,526,2276,522,2274,545,542,539,548,1572,1570,481,2245,466,2242,462,2239,492,485,482,2249,496,494,1534,1531,1528,1538,413,2196,406,2191,2188,425,419,2202,415,2199,432,430,427,1472,1467,1464,433,1476,1474,368,367,2160,365,2159,362,2157,2155,2152,378,377,375,2166,372,2165,369,2162,383,381,379,2168,1419,1418,1416,1414,385,1411,384,1423,1422,1420,1424,2461,802,2441,2439,790,786,783,794,2409,2406,2403,750,742,738,2414,756,753,1720,2367,2365,2362,2359,1663,693,691,684,2373,680,2370,702,699,696,704,1690,1687,2337,2336,2334,2332,1624,2329,1622,640,637,2344,634,2342,630,2340,650,648,645,2346,655,653,1653,1651,1649,1655,2612,2597,2595,2571,2568,2565,2576,2534,2529,2526,1787,2540,2537,907,904,900,910,2503,2502,2500,2498,1768,2495,1767,2510,2508,2506,869,866,863,2513,876,874,1782,2720,2713,2711,2697,2694,2691,2702,2672,2670,2664,1828,2678,2675,2647,2646,2644,2642,1823,2639,1822,2654,2652,2650,2657,2771,1855,2765,2762,1850,1849,2751,2749,2747,2754,353,2148,344,342,336,2142,332,2140,345,1375,1373,306,2130,299,2128,295,2125,319,314,311,2132,1354,1352,1349,1356,262,257,2101,253,2096,2093,274,273,267,2107,263,2104,280,278,275,1316,1311,1308,1320,1318,2052,202,2050,2044,2040,219,2063,212,2060,208,2055,224,221,2066,1260,1258,1252,231,1248,229,1266,1264,1261,1268,155,1998,153,1996,1994,1991,1988,165,164,2007,162,2006,159,2003,2e3,172,171,169,2012,166,2010,1186,1184,1182,1179,175,1176,173,1192,1191,1189,1187,176,1194,1193,2313,2307,2305,592,589,2294,2292,2289,578,572,568,2297,580,1591,2272,2267,2264,1547,538,536,529,2278,525,2275,547,544,541,1574,1571,2237,2235,2229,1493,2225,1489,478,2247,470,2244,465,2241,493,488,484,2250,498,495,1536,1533,1530,1539,2187,2186,2184,2182,1432,2179,1430,2176,1427,414,412,2197,409,2195,405,2193,2190,426,424,421,2203,418,2201,431,429,1473,1471,1469,1466,434,1477,1475,2478,2472,2470,2459,2457,2454,2462,803,2437,2432,2429,1726,2443,2440,792,789,785,2401,2399,2393,1702,2389,1699,2411,2408,2405,745,741,2415,758,755,1721,2358,2357,2355,2353,1661,2350,1660,2347,1657,2368,2366,2364,2361,1666,690,687,2374,683,2372,701,698,705,1691,1689,2619,2617,2610,2608,2605,2613,2593,2588,2585,1803,2599,2596,2563,2561,2555,1797,2551,1795,2573,2570,2567,2577,2525,2524,2522,2520,1786,2517,1785,2514,1783,2535,2533,2531,2528,1788,2541,2539,906,903,911,2721,1844,2715,2712,1838,1836,2699,2696,2693,2703,1827,1826,1824,2673,2671,2669,2666,1829,2679,2677,1858,1857,2772,1854,1853,1851,1856,2766,2764,143,1987,139,1986,135,133,131,1984,128,1983,125,1981,138,137,136,1985,1133,1132,1130,112,110,1974,107,1973,104,1971,1969,122,121,119,117,1977,114,1976,124,1115,1114,1112,1110,1117,1116,84,83,1953,81,1952,78,1950,1948,1945,94,93,91,1959,88,1958,85,1955,99,97,95,1961,1086,1085,1083,1081,1078,100,1090,1089,1087,1091,49,47,1917,44,1915,1913,1910,1907,59,1926,56,1925,53,1922,1919,66,64,1931,61,1929,1042,1040,1038,71,1035,70,1032,68,1048,1047,1045,1043,1050,1049,12,10,1869,1867,1864,1861,21,1880,19,1877,1874,1871,28,1888,25,1886,22,1883,982,980,977,974,32,30,991,989,987,984,34,995,994,992,2151,2150,2147,2146,2144,356,355,354,2149,2139,2138,2136,2134,1359,343,341,338,2143,335,2141,348,347,346,1376,1374,2124,2123,2121,2119,1326,2116,1324,310,308,305,2131,302,2129,298,2127,320,318,316,313,2133,322,321,1355,1353,1351,1357,2092,2091,2089,2087,1276,2084,1274,2081,1271,259,2102,256,2100,252,2098,2095,272,269,2108,266,2106,281,279,277,1317,1315,1313,1310,282,1321,1319,2039,2037,2035,2032,1203,2029,1200,1197,207,2053,205,2051,201,2049,2046,2043,220,218,2064,215,2062,211,2059,228,226,223,2069,1259,1257,1254,232,1251,230,1267,1265,1263,2316,2315,2312,2311,2309,2314,2304,2303,2301,2299,1593,2308,2306,590,2288,2287,2285,2283,1578,2280,1577,2295,2293,2291,579,577,574,571,2298,582,581,1592,2263,2262,2260,2258,1545,2255,1544,2252,1541,2273,2271,2269,2266,1550,535,532,2279,528,2277,546,543,549,1575,1573,2224,2222,2220,1486,2217,1485,2214,1482,1479,2238,2236,2234,2231,1496,2228,1492,480,477,2248,473,2246,469,2243,490,487,2251,497,1537,1535,1532,2477,2476,2474,2479,2469,2468,2466,2464,1730,2473,2471,2453,2452,2450,2448,1729,2445,1728,2460,2458,2456,2463,805,804,2428,2427,2425,2423,1725,2420,1724,2417,1722,2438,2436,2434,2431,1727,2444,2442,793,791,788,795,2388,2386,2384,1697,2381,1696,2378,1694,1692,2402,2400,2398,2395,1703,2392,1701,2412,2410,2407,751,748,744,2416,759,757,1807,2620,2618,1806,1805,2611,2609,2607,2614,1802,1801,1799,2594,2592,2590,2587,1804,2600,2598,1794,1793,1791,1789,2564,2562,2560,2557,1798,2554,1796,2574,2572,2569,2578,1847,1846,2722,1843,1842,1840,1845,2716,2714,1835,1834,1832,1830,1839,1837,2700,2698,2695,2704,1817,1811,1810,897,862,1777,829,826,838,1760,1758,808,2481,1741,1740,1738,1743,2624,1818,2726,2776,782,740,737,1715,686,679,695,1682,1680,639,628,2339,647,644,1645,1643,1640,1648,602,600,597,595,2320,593,2318,609,607,604,1611,1610,1608,1606,613,1615,1613,2328,926,924,892,886,899,857,850,2505,1778,824,823,821,819,2488,818,2486,833,831,828,840,1761,1759,2649,2632,2630,2746,2734,2732,2782,2781,570,567,1587,531,527,523,540,1566,1564,476,467,463,2240,486,483,1524,1521,1518,1529,411,403,2192,399,2189,423,416,1462,1457,1454,428,1468,1465,2210,366,363,2158,360,2156,357,2153,376,373,370,2163,1410,1409,1407,1405,382,1402,380,1417,1415,1412,1421,2175,2174,777,774,771,784,732,725,722,2404,743,1716,676,674,668,2363,665,2360,685,1684,1681,626,624,622,2335,620,2333,617,2330,641,635,649,1646,1644,1642,2566,928,925,2530,2527,894,891,888,2501,2499,2496,858,856,854,851,1779,2692,2668,2665,2645,2643,2640,2651,2768,2759,2757,2744,2743,2741,2748,352,1382,340,337,333,1371,1369,307,300,296,2126,315,312,1347,1342,1350,261,258,250,2097,246,2094,271,268,264,1306,1301,1298,276,1312,1309,2115,203,2048,195,2045,191,2041,213,209,2056,1246,1244,1238,225,1234,222,1256,1253,1249,1262,2080,2079,154,1997,150,1995,147,1992,1989,163,160,2004,156,2001,1175,1174,1172,1170,1167,170,1164,167,1185,1183,1180,1177,174,1190,1188,2025,2024,2022,587,586,564,559,556,2290,573,1588,520,518,512,2268,508,2265,530,1568,1565,461,457,2233,450,2230,446,2226,479,471,489,1526,1523,1520,397,395,2185,392,2183,389,2180,2177,410,2194,402,422,1463,1461,1459,1456,1470,2455,799,2433,2430,779,776,773,2397,2394,2390,734,728,724,746,1717,2356,2354,2351,2348,1658,677,675,673,670,667,688,1685,1683,2606,2589,2586,2559,2556,2552,927,2523,2521,2518,2515,1784,2532,895,893,890,2718,2709,2707,2689,2687,2684,2663,2662,2660,2658,1825,2667,2769,1852,2760,2758,142,141,1139,1138,134,132,129,126,1982,1129,1128,1126,1131,113,111,108,105,1972,101,1970,120,118,115,1109,1108,1106,1104,123,1113,1111,82,79,1951,75,1949,72,1946,92,89,86,1956,1077,1076,1074,1072,98,1069,96,1084,1082,1079,1088,1968,1967,48,45,1916,42,1914,39,1911,1908,60,57,54,1923,50,1920,1031,1030,1028,1026,67,1023,65,1020,62,1041,1039,1036,1033,69,1046,1044,1944,1943,1941,11,9,1868,7,1865,1862,1859,20,1878,16,1875,13,1872,970,968,966,963,29,960,26,23,983,981,978,975,33,971,31,990,988,985,1906,1904,1902,993,351,2145,1383,331,330,328,326,2137,323,2135,339,1372,1370,294,293,291,289,2122,286,2120,283,2117,309,303,317,1348,1346,1344,245,244,242,2090,239,2088,236,2085,2082,260,2099,249,270,1307,1305,1303,1300,1314,189,2038,186,2036,183,2033,2030,2026,206,198,2047,194,216,1247,1245,1243,1240,227,1237,1255,2310,2302,2300,2286,2284,2281,565,563,561,558,575,1589,2261,2259,2256,2253,1542,521,519,517,514,2270,511,533,1569,1567,2223,2221,2218,2215,1483,2211,1480,459,456,453,2232,449,474,491,1527,1525,1522,2475,2467,2465,2451,2449,2446,801,800,2426,2424,2421,2418,1723,2435,780,778,775,2387,2385,2382,2379,1695,2375,1693,2396,735,733,730,727,749,1718,2616,2615,2604,2603,2601,2584,2583,2581,2579,1800,2591,2550,2549,2547,2545,1792,2542,1790,2558,929,2719,1841,2710,2708,1833,1831,2690,2688,2686,1815,1809,1808,1774,1756,1754,1737,1736,1734,1739,1816,1711,1676,1674,633,629,1638,1636,1633,1641,598,1605,1604,1602,1600,605,1609,1607,2327,887,853,1775,822,820,1757,1755,1584,524,1560,1558,468,464,1514,1511,1508,1519,408,404,400,1452,1447,1444,417,1458,1455,2208,364,361,358,2154,1401,1400,1398,1396,374,1393,371,1408,1406,1403,1413,2173,2172,772,726,723,1712,672,669,666,682,1678,1675,625,623,621,618,2331,636,632,1639,1637,1635,920,918,884,880,889,849,848,847,846,2497,855,852,1776,2641,2742,2787,1380,334,1367,1365,301,297,1340,1338,1335,1343,255,251,247,1296,1291,1288,265,1302,1299,2113,204,196,192,2042,1232,1230,1224,214,1220,210,1242,1239,1235,1250,2077,2075,151,148,1993,144,1990,1163,1162,1160,1158,1155,161,1152,157,1173,1171,1168,1165,168,1181,1178,2021,2020,2018,2023,585,560,557,1585,516,509,1562,1559,458,447,2227,472,1516,1513,1510,398,396,393,390,2181,386,2178,407,1453,1451,1449,1446,420,1460,2209,769,764,720,712,2391,729,1713,664,663,661,659,2352,656,2349,671,1679,1677,2553,922,919,2519,2516,885,883,881,2685,2661,2659,2767,2756,2755,140,1137,1136,130,127,1125,1124,1122,1127,109,106,102,1103,1102,1100,1098,116,1107,1105,1980,80,76,73,1947,1068,1067,1065,1063,90,1060,87,1075,1073,1070,1080,1966,1965,46,43,40,1912,36,1909,1019,1018,1016,1014,58,1011,55,1008,51,1029,1027,1024,1021,63,1037,1034,1940,1939,1937,1942,8,1866,4,1863,1,1860,956,954,952,949,946,17,14,969,967,964,961,27,957,24,979,976,972,1901,1900,1898,1896,986,1905,1903,350,349,1381,329,327,324,1368,1366,292,290,287,284,2118,304,1341,1339,1337,1345,243,240,237,2086,233,2083,254,1297,1295,1293,1290,1304,2114,190,187,184,2034,180,2031,177,2027,199,1233,1231,1229,1226,217,1223,1241,2078,2076,584,555,554,552,550,2282,562,1586,507,506,504,502,2257,499,2254,515,1563,1561,445,443,441,2219,438,2216,435,2212,460,454,475,1517,1515,1512,2447,798,797,2422,2419,770,768,766,2383,2380,2376,721,719,717,714,731,1714,2602,2582,2580,2548,2546,2543,923,921,2717,2706,2705,2683,2682,2680,1771,1752,1750,1733,1732,1731,1735,1814,1707,1670,1668,1631,1629,1626,1634,1599,1598,1596,1594,1603,1601,2326,1772,1753,1751,1581,1554,1552,1504,1501,1498,1509,1442,1437,1434,401,1448,1445,2206,1392,1391,1389,1387,1384,359,1399,1397,1394,1404,2171,2170,1708,1672,1669,619,1632,1630,1628,1773,1378,1363,1361,1333,1328,1336,1286,1281,1278,248,1292,1289,2111,1218,1216,1210,197,1206,193,1228,1225,1221,1236,2073,2071,1151,1150,1148,1146,152,1143,149,1140,145,1161,1159,1156,1153,158,1169,1166,2017,2016,2014,2019,1582,510,1556,1553,452,448,1506,1500,394,391,387,1443,1441,1439,1436,1450,2207,765,716,713,1709,662,660,657,1673,1671,916,914,879,878,877,882,1135,1134,1121,1120,1118,1123,1097,1096,1094,1092,103,1101,1099,1979,1059,1058,1056,1054,77,1051,74,1066,1064,1061,1071,1964,1963,1007,1006,1004,1002,999,41,996,37,1017,1015,1012,1009,52,1025,1022,1936,1935,1933,1938,942,940,938,935,932,5,2,955,953,950,947,18,943,15,965,962,958,1895,1894,1892,1890,973,1899,1897,1379,325,1364,1362,288,285,1334,1332,1330,241,238,234,1287,1285,1283,1280,1294,2112,188,185,181,178,2028,1219,1217,1215,1212,200,1209,1227,2074,2072,583,553,551,1583,505,503,500,513,1557,1555,444,442,439,436,2213,455,451,1507,1505,1502,796,763,762,760,767,711,710,708,706,2377,718,715,1710,2544,917,915,2681,1627,1597,1595,2325,1769,1749,1747,1499,1438,1435,2204,1390,1388,1385,1395,2169,2167,1704,1665,1662,1625,1623,1620,1770,1329,1282,1279,2109,1214,1207,1222,2068,2065,1149,1147,1144,1141,146,1157,1154,2013,2011,2008,2015,1579,1549,1546,1495,1487,1433,1431,1428,1425,388,1440,2205,1705,658,1667,1664,1119,1095,1093,1978,1057,1055,1052,1062,1962,1960,1005,1003,1e3,997,38,1013,1010,1932,1930,1927,1934,941,939,936,933,6,930,3,951,948,944,1889,1887,1884,1881,959,1893,1891,35,1377,1360,1358,1327,1325,1322,1331,1277,1275,1272,1269,235,1284,2110,1205,1204,1201,1198,182,1195,179,1213,2070,2067,1580,501,1551,1548,440,437,1497,1494,1490,1503,761,709,707,1706,913,912,2198,1386,2164,2161,1621,1766,2103,1208,2058,2054,1145,1142,2005,2002,1999,2009,1488,1429,1426,2200,1698,1659,1656,1975,1053,1957,1954,1001,998,1924,1921,1918,1928,937,934,931,1879,1876,1873,1870,945,1885,1882,1323,1273,1270,2105,1202,1199,1196,1211,2061,2057,1576,1543,1540,1484,1481,1478,1491,1700]),t}()}}]);