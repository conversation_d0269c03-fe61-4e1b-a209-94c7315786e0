"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/archive/page",{

/***/ "(app-pages-browser)/./src/components/receipt-template.tsx":
/*!*********************************************!*\
  !*** ./src/components/receipt-template.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ReceiptTemplate = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = (param, ref)=>{\n    let { order } = param;\n    // Generate barcode data (simple implementation)\n    const generateBarcode = (trackingNumber)=>{\n        // This is a simple barcode representation based on tracking number\n        // In a real app, you'd use a proper barcode library\n        const barcodePattern = trackingNumber.split('').map(()=>'|||').join(' ');\n        return \"||||| \".concat(barcodePattern, \" |||||\");\n    };\n    const getStatusText = (status)=>{\n        const statusMap = {\n            pending: \"في الانتظار\",\n            assigned: \"مسند للمندوب\",\n            \"out-for-delivery\": \"خارج للتوصيل\",\n            delivered: \"تم التسليم\",\n            returned: \"راجع للمرسل\",\n            cancelled: \"ملغي\",\n            postponed: \"مؤجل\"\n        };\n        return statusMap[status] || status;\n    };\n    // Format price in English as required\n    const formatPrice = (amount)=>{\n        return amount.toLocaleString('en-US', {\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        });\n    };\n    // Get current date and time\n    const getCurrentDateTime = ()=>{\n        const now = new Date();\n        return {\n            date: now.toLocaleDateString('ar-IQ'),\n            time: now.toLocaleTimeString('ar-IQ', {\n                hour: '2-digit',\n                minute: '2-digit'\n            })\n        };\n    };\n    const { date, time } = getCurrentDateTime();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: \"receipt-template\",\n        style: {\n            width: \"110mm\",\n            height: \"130mm\",\n            padding: \"5mm\",\n            fontFamily: \"'Arial', 'Tahoma', sans-serif\",\n            fontSize: \"11px\",\n            lineHeight: \"1.3\",\n            color: \"#000\",\n            backgroundColor: \"#fff\",\n            border: \"2px solid #000\",\n            boxSizing: \"border-box\",\n            direction: \"rtl\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"10px\",\n                    borderBottom: \"2px solid #000\",\n                    paddingBottom: \"6px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: \"13px\",\n                            fontWeight: \"bold\",\n                            margin: \"0 0 2px 0\",\n                            color: \"#000\",\n                            letterSpacing: \"0.3px\"\n                        },\n                        children: \"مكتب علي الشيباني للتوصيل السريع فرع الحي\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            margin: \"0\",\n                            fontSize: \"8px\",\n                            color: \"#666\"\n                        },\n                        children: \"خدمة توصيل سريعة وموثوقة\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"10px\",\n                    border: \"2px solid #000\",\n                    padding: \"6px\",\n                    backgroundColor: \"#f0f8ff\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"9px\",\n                            color: \"#666\",\n                            marginBottom: \"2px\"\n                        },\n                        children: \"رقم الوصل\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"18px\",\n                            fontWeight: \"bold\",\n                            color: \"#000\",\n                            letterSpacing: \"1px\"\n                        },\n                        children: order.trackingNumber\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"6px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"رقم هاتف الزبون:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"12px\",\n                            color: \"#0066cc\",\n                            direction: \"ltr\"\n                        },\n                        children: order.recipientPhone\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"6px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"الحالة:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"11px\",\n                            color: order.status === \"delivered\" ? \"#28a745\" : \"#ffc107\",\n                            padding: \"2px 6px\",\n                            borderRadius: \"3px\",\n                            backgroundColor: order.status === \"delivered\" ? \"#d4edda\" : \"#fff3cd\"\n                        },\n                        children: getStatusText(order.status)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"8px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"اسم المندوب:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"11px\",\n                            color: \"#333\"\n                        },\n                        children: order.assignedTo || \"غير محدد\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"10px\",\n                    border: \"2px solid #000\",\n                    padding: \"8px\",\n                    backgroundColor: \"#fff9e6\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"9px\",\n                            color: \"#666\",\n                            marginBottom: \"2px\"\n                        },\n                        children: \"المبلغ المطلوب\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"16px\",\n                            fontWeight: \"bold\",\n                            color: \"#000\",\n                            letterSpacing: \"1px\",\n                            direction: \"ltr\"\n                        },\n                        children: [\n                            order.amount.toLocaleString('en-US'),\n                            \" IQD\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 179,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"8px\",\n                    border: \"1px solid #000\",\n                    padding: \"6px\",\n                    backgroundColor: \"#fff\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"8px\",\n                            color: \"#666\",\n                            marginBottom: \"3px\"\n                        },\n                        children: \"الباركود\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontFamily: \"monospace\",\n                            fontSize: \"10px\",\n                            letterSpacing: \"1px\",\n                            color: \"#000\",\n                            backgroundColor: \"#fff\",\n                            padding: \"3px\"\n                        },\n                        children: generateBarcode(order.trackingNumber)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"8px\",\n                            color: \"#666\",\n                            marginTop: \"2px\"\n                        },\n                        children: order.trackingNumber\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    fontSize: \"8px\",\n                    color: \"#666\",\n                    borderTop: \"1px solid #ccc\",\n                    paddingTop: \"4px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"شكراً لاختياركم خدماتنا\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"2px\"\n                        },\n                        children: [\n                            \"التاريخ: \",\n                            new Date().toLocaleDateString('ar-IQ')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n        lineNumber: 53,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = ReceiptTemplate;\nReceiptTemplate.displayName = \"ReceiptTemplate\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReceiptTemplate);\nvar _c, _c1;\n$RefreshReg$(_c, \"ReceiptTemplate$forwardRef\");\n$RefreshReg$(_c1, \"ReceiptTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/receipt-template.tsx\n"));

/***/ })

});