exports.id=610,exports.ids=[610],exports.modules={5475:(e,r,t)=>{"use strict";t.d(r,{vQ:()=>s});let s={name:"مرسال",description:"نظام إدارة عمليات التوصيل السريع",version:"1.1.0",colors:{primary:"#41a7ff",background:"#f0f3f5",accent:"#b19cd9"},business:{commissionPerOrder:1e3,currency:"د.ع",defaultOrderStatuses:["pending","assigned","picked_up","in_transit","delivered","returned","cancelled","postponed"]},api:{baseUrl:process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",endpoints:{orders:"/api/orders",users:"/api/users",couriers:"/api/couriers",settlements:"/api/settlements"}},firebase:{apiKey:"AIzaSyDemoKeyForMarsalDeliveryApp123456789",authDomain:"marsal-delivery-app.firebaseapp.com",projectId:"marsal-delivery-app",storageBucket:"marsal-delivery-app.appspot.com",messagingSenderId:"123456789012",appId:"1:123456789012:web:abcdef123456789012345",measurementId:"G-XXXXXXXXXX"},features:{enableNotifications:!0,enableReports:!0,enableBulkOperations:!0,enableImageUpload:!0},demo:{enabled:!1,autoLogin:!1,defaultUser:null,skipFirebase:!1,showDemoNotice:!1},company:{name:"مكتب علي الشيباني للتوصيل السريع فرع الحي",phone:"+964 ************",address:"بغداد، العراق"},receipt:{dimensions:{width:"110mm",height:"130mm"},companyName:"مكتب علي الشيباني للتوصيل السريع فرع الحي",showBarcode:!0,showDate:!0,priceFormat:"en-US",currency:"IQD",fields:{trackingNumber:!0,customerPhone:!0,status:!0,courierName:!0,amount:!0,barcode:!0,date:!0}}}},16391:(e,r,t)=>{"use strict";t.d(r,{Dv:()=>u,LC:()=>n});var s=t(60463);let a=process.env.NEXT_PUBLIC_SUPABASE_URL||"https://your-project.supabase.co",o=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY||"your-anon-key",i=(0,s.UU)(a,o),n=async()=>{try{let{data:e,error:r}=await i.from("users").select("count").limit(1);if(r)return console.error("Supabase connection test failed:",r),{success:!1,message:`فشل في الاتصال بقاعدة البيانات السحابية: ${r.message}`};return{success:!0,message:"تم الاتصال بقاعدة البيانات السحابية بنجاح ✅"}}catch(r){console.error("Supabase connection test failed:",r);let e="فشل في الاتصال بقاعدة البيانات السحابية";return r instanceof Error&&(r.message.includes("network")?e+=" - تحقق من الاتصال بالإنترنت":r.message.includes("permission")?e+=" - مشكلة في الصلاحيات":e+=": "+r.message),{success:!1,message:e}}};class c{async createUser(e){let{data:r,error:t}=await i.from("users").insert([e]).select().single();if(t)throw Error(`Failed to create user: ${t.message}`);return r}async getUserByUsername(e){let{data:r,error:t}=await i.from("users").select("*").eq("username",e).single();if(t&&"PGRST116"!==t.code)throw Error(`Failed to get user: ${t.message}`);return r||null}async getAllUsers(){let{data:e,error:r}=await i.from("users").select("*").order("created_at",{ascending:!1});if(r)throw Error(`Failed to get users: ${r.message}`);return e||[]}async updateUser(e,r){let{error:t}=await i.from("users").update(r).eq("id",e);if(t)throw Error(`Failed to update user: ${t.message}`)}async deleteUser(e){let{error:r}=await i.from("users").delete().eq("id",e);if(r)throw Error(`Failed to delete user: ${r.message}`)}async getUsersByRole(e){let{data:r,error:t}=await i.from("users").select("*").eq("role",e).order("created_at",{ascending:!1});if(t)throw Error(`Failed to get users by role: ${t.message}`);return r||[]}}class l{async createOrder(e){let{data:r,error:t}=await i.from("orders").insert([{...e,updated_at:new Date().toISOString()}]).select().single();if(t)throw Error(`Failed to create order: ${t.message}`);return r}async getOrderByTrackingNumber(e){let{data:r,error:t}=await i.from("orders").select("*").eq("tracking_number",e).single();if(t&&"PGRST116"!==t.code)throw Error(`Failed to get order: ${t.message}`);return r||null}async getAllOrders(){let{data:e,error:r}=await i.from("orders").select("*").order("created_at",{ascending:!1});if(r)throw Error(`Failed to get orders: ${r.message}`);return e||[]}async getOrdersByStatus(e){let{data:r,error:t}=await i.from("orders").select("*").eq("status",e).order("created_at",{ascending:!1});if(t)throw Error(`Failed to get orders by status: ${t.message}`);return r||[]}async getOrdersByCourier(e){let{data:r,error:t}=await i.from("orders").select("*").eq("courier_id",e).order("created_at",{ascending:!1});if(t)throw Error(`Failed to get orders by courier: ${t.message}`);return r||[]}async updateOrder(e,r){let{error:t}=await i.from("orders").update({...r,updated_at:new Date().toISOString()}).eq("id",e);if(t)throw Error(`Failed to update order: ${t.message}`)}async deleteOrder(e){let{error:r}=await i.from("orders").delete().eq("id",e);if(r)throw Error(`Failed to delete order: ${r.message}`)}async searchOrders(e){let{data:r,error:t}=await i.from("orders").select("*").or(`tracking_number.ilike.%${e}%,customer_name.ilike.%${e}%,customer_phone.ilike.%${e}%,address.ilike.%${e}%`).order("created_at",{ascending:!1});if(t)throw Error(`Failed to search orders: ${t.message}`);return r||[]}}let u=new c;new l},19579:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},33784:(e,r,t)=>{"use strict";t.d(r,{db:()=>u,j2:()=>d,testFirebaseConnection:()=>m});var s=t(67989),a=t(75535),o=t(91042),i=t(70146),n=t(5475);let c={apiKey:n.vQ.firebase.apiKey||"AIzaSyDemoKeyForMarsalDeliveryApp123456789",authDomain:n.vQ.firebase.authDomain||"marsal-delivery.firebaseapp.com",projectId:n.vQ.firebase.projectId||"marsal-delivery-system",storageBucket:n.vQ.firebase.storageBucket||"marsal-delivery.appspot.com",messagingSenderId:n.vQ.firebase.messagingSenderId||"987654321",appId:n.vQ.firebase.appId||"1:987654321:web:abc123def456ghi789"},l=null,u=null,d=null;if(!n.vQ.demo.skipFirebase)try{l=(0,s.Wp)(c),u=(0,a.aU)(l),d=(0,o.xI)(l),(0,i.c7)(l)}catch(e){console.warn("Firebase initialization failed, running in demo mode:",e)}let m=async()=>{if(n.vQ.demo.skipFirebase||!u)return{success:!0,message:"تم الاتصال بقاعدة البيانات التجريبية بنجاح ✅ (وضع تجريبي)"};try{let{doc:e,getDoc:r,setDoc:s}=await Promise.resolve().then(t.bind(t,75535)),a=e(u,"system","connection_test");if(await s(a,{timestamp:new Date,status:"connected",app:"marsal-delivery"}),(await r(a)).exists())return{success:!0,message:"تم الاتصال بقاعدة البيانات السحابية بنجاح ✅"};return{success:!1,message:"تم الاتصال ولكن فشل في قراءة البيانات"}}catch(r){console.error("Firebase connection test failed:",r);let e="فشل في الاتصال بقاعدة البيانات السحابية";return r instanceof Error&&(r.message.includes("network")?e+=" - تحقق من الاتصال بالإنترنت":r.message.includes("permission")?e+=" - مشكلة في الصلاحيات":e+=": "+r.message),{success:!1,message:e}}}},39727:()=>{},39903:(e,r,t)=>{"use strict";t.d(r,{firebaseAuthService:()=>n});var s=t(91042),a=t(33784),o=t(75535);let i=[{username:"azad95",email:"<EMAIL>",name:"أزاد - مدير النظام الرئيسي",phone:"07801234567",role:"manager",password:"Azad@1995"},{username:"manager",email:"<EMAIL>",name:"مدير النظام",phone:"07801234568",role:"manager",password:"123456"},{username:"supervisor",email:"<EMAIL>",name:"المشرف العام",phone:"07801234569",role:"supervisor",password:"123456"},{username:"courier",email:"<EMAIL>",name:"مندوب التوصيل",phone:"07801234570",role:"courier",password:"123456"}],n={async initializeDefaultUsers(){try{console.log("\uD83D\uDD27 إعداد المستخدمين الافتراضيين..."),console.log("\uD83D\uDCF1 استخدام التخزين المحلي (Firebase غير مُعد بشكل صحيح)"),this.initializeLocalUsers(),this.tryFirebaseSetup()}catch(e){console.error("❌ خطأ في إعداد المستخدمين الافتراضيين:",e),this.initializeLocalUsers()}},async tryFirebaseSetup(){try{if(!a.j2||!a.db)return void console.log("⚠️ Firebase غير متاح");await a.j2.currentUser,console.log("✅ Firebase متاح ولكن قد يحتاج إعداد إضافي")}catch(e){console.warn("⚠️ Firebase غير مُعد بشكل صحيح:",e.message)}},initializeLocalUsers(){try{if(localStorage.getItem("marsal_users"))return void console.log("✅ المستخدمين موجودين في التخزين المحلي");let e=i.map((e,r)=>({id:`local_${r+1}`,username:e.username,email:e.email,name:e.name,phone:e.phone,role:e.role,isActive:!0,createdAt:new Date,updatedAt:new Date,createdBy:"system",password:e.password}));localStorage.setItem("marsal_users",JSON.stringify(e)),console.log("✅ تم إنشاء المستخدمين في التخزين المحلي")}catch(e){console.error("❌ خطأ في إنشاء المستخدمين المحليين:",e)}},async login(e,r){try{if(console.log("\uD83D\uDD10 محاولة تسجيل الدخول للمستخدم:",e),a.j2&&a.db)try{return await this.loginWithFirebase(e,r)}catch(e){console.warn("⚠️ فشل تسجيل الدخول مع Firebase، محاولة التخزين المحلي")}return await this.loginWithLocalStorage(e,r)}catch(e){return console.error("❌ خطأ في تسجيل الدخول:",e),{success:!1,error:e.message||"خطأ في تسجيل الدخول"}}},async loginWithFirebase(e,r){let t=await this.getUserByUsername(e);if(!t)throw Error("اسم المستخدم غير موجود");if(!t.isActive)throw Error("الحساب غير مفعل");let i=await (0,s.x9)(a.j2,t.email,r);return await (0,o.setDoc)((0,o.doc)(a.db,"users",i.user.uid),{lastLogin:(0,o.O5)()},{merge:!0}),console.log("✅ تم تسجيل الدخول مع Firebase بنجاح"),{success:!0,user:{...t,id:i.user.uid}}},async loginWithLocalStorage(e,r){let t=localStorage.getItem("marsal_users");if(!t)throw Error("لا يوجد مستخدمين مسجلين");let s=JSON.parse(t),a=s.find(r=>r.username===e);if(!a)throw Error("اسم المستخدم غير موجود");if(!a.isActive)throw Error("الحساب غير مفعل");if(a.password!==r)throw Error("كلمة المرور غير صحيحة");a.lastLogin=new Date;let o=s.map(e=>e.id===a.id?a:e);return localStorage.setItem("marsal_users",JSON.stringify(o)),console.log("✅ تم تسجيل الدخول مع التخزين المحلي بنجاح"),{success:!0,user:{id:a.id,username:a.username,email:a.email,name:a.name,phone:a.phone,role:a.role,isActive:a.isActive,createdAt:new Date(a.createdAt),updatedAt:new Date(a.updatedAt),createdBy:a.createdBy,lastLogin:a.lastLogin?new Date(a.lastLogin):void 0}}},async getUserByUsername(e){try{if(a.db)try{let r=(0,o.collection)(a.db,"users"),t=(0,o.P)(r,(0,o._M)("username","==",e)),s=await (0,o.getDocs)(t);if(!s.empty){let e=s.docs[0],r=e.data();return{id:e.id,username:r.username,email:r.email,name:r.name,phone:r.phone,role:r.role,isActive:r.isActive,createdAt:r.createdAt?.toDate()||new Date,updatedAt:r.updatedAt?.toDate()||new Date,createdBy:r.createdBy,lastLogin:r.lastLogin?.toDate()}}}catch(e){console.warn("⚠️ فشل البحث في Firebase، محاولة التخزين المحلي")}let r=localStorage.getItem("marsal_users");if(!r)return null;let t=JSON.parse(r).find(r=>r.username===e);if(!t)return null;return{id:t.id,username:t.username,email:t.email,name:t.name,phone:t.phone,role:t.role,isActive:t.isActive,createdAt:new Date(t.createdAt),updatedAt:new Date(t.updatedAt),createdBy:t.createdBy,lastLogin:t.lastLogin?new Date(t.lastLogin):void 0}}catch(e){return console.error("Error getting user by username:",e),null}},async getCurrentUser(){try{let e=a.j2.currentUser;if(!e)return null;let r=await (0,o.getDoc)((0,o.doc)(a.db,"users",e.uid));if(!r.exists())return null;let t=r.data();return{id:r.id,username:t.username,email:t.email,name:t.name,phone:t.phone,role:t.role,isActive:t.isActive,createdAt:t.createdAt?.toDate()||new Date,updatedAt:t.updatedAt?.toDate()||new Date,createdBy:t.createdBy,lastLogin:t.lastLogin?.toDate()}}catch(e){return console.error("Error getting current user:",e),null}},async logout(){try{await (0,s.CI)(a.j2),console.log("✅ تم تسجيل الخروج بنجاح")}catch(e){throw console.error("❌ خطأ في تسجيل الخروج:",e),e}},onAuthStateChanged(e){return(0,s.hg)(a.j2,async r=>{r?e(await this.getCurrentUser()):e(null)})},async createUser(e){try{console.log("\uD83D\uDC64 إنشاء مستخدم جديد:",e.username);let r=await (0,s.eJ)(a.j2,e.email,e.password);await (0,o.setDoc)((0,o.doc)(a.db,"users",r.user.uid),{username:e.username,email:e.email,name:e.name,phone:e.phone,role:e.role,isActive:!0,createdAt:(0,o.O5)(),updatedAt:(0,o.O5)(),createdBy:e.createdBy}),console.log("✅ تم إنشاء المستخدم بنجاح:",e.username);let t={id:r.user.uid,username:e.username,email:e.email,name:e.name,phone:e.phone,role:e.role,isActive:!0,createdAt:new Date,updatedAt:new Date,createdBy:e.createdBy};return{success:!0,user:t}}catch(r){console.error("❌ خطأ في إنشاء المستخدم:",r);let e="خطأ في إنشاء المستخدم";switch(r.code){case"auth/email-already-in-use":e="البريد الإلكتروني مستخدم مسبقاً";break;case"auth/weak-password":e="كلمة المرور ضعيفة";break;case"auth/invalid-email":e="البريد الإلكتروني غير صحيح";break;default:e=r.message||"خطأ غير معروف"}return{success:!1,error:e}}},async getAllUsers(){try{let e=(0,o.collection)(a.db,"users");return(await (0,o.getDocs)(e)).docs.map(e=>{let r=e.data();return{id:e.id,username:r.username,email:r.email,name:r.name,phone:r.phone,role:r.role,isActive:r.isActive,createdAt:r.createdAt?.toDate()||new Date,updatedAt:r.updatedAt?.toDate()||new Date,createdBy:r.createdBy,lastLogin:r.lastLogin?.toDate()}})}catch(e){return console.error("Error getting all users:",e),[]}},async updateUser(e,r){try{return await (0,o.setDoc)((0,o.doc)(a.db,"users",e),{...r,updatedAt:(0,o.O5)()},{merge:!0}),console.log("✅ تم تحديث المستخدم:",e),{success:!0}}catch(e){return console.error("❌ خطأ في تحديث المستخدم:",e),{success:!1,error:e.message||"خطأ في تحديث المستخدم"}}},async deleteUser(e){try{return await (0,o.kd)((0,o.doc)(a.db,"users",e)),await this.updateUser(e,{isActive:!1}),console.log("✅ تم حذف المستخدم:",e),{success:!0}}catch(e){return console.error("❌ خطأ في حذف المستخدم:",e),{success:!1,error:e.message||"خطأ في حذف المستخدم"}}},async checkConnection(){try{return a.j2.currentUser,console.log("✅ Firebase Auth متصل"),{connected:!0,message:"Firebase Auth متصل بنجاح"}}catch(e){return console.error("❌ Firebase Auth غير متصل:",e),{connected:!1,message:`خطأ في الاتصال: ${e.message}`}}}};n.initializeDefaultUsers().catch(console.error)},47990:()=>{},48126:(e,r,t)=>{"use strict";t.d(r,{JI:()=>i,_m:()=>o,gG:()=>s});var s=function(e){return e.MANAGER="manager",e.COURIER="courier",e.SUPERVISOR="supervisor",e}({});let a={manager:{role:"manager",permissions:["create_order","view_order","update_order","delete_order","assign_order","transfer_order","create_user","view_user","update_user","delete_user","manage_branches","manage_provinces","view_accounting","process_accounting","view_all_statistics","view_archive","manage_archive","manage_tickets","manage_settings","manage_warehouse","import_orders","export_orders"],canCreateRoles:["courier","supervisor"],accessibleSections:["orders","dispatch","returns","accounting","statistics","archive","users","import-export","notifications","settings"]},supervisor:{role:"supervisor",permissions:["view_order","update_order","assign_order","manage_tickets","view_statistics","view_archive"],canCreateRoles:[],accessibleSections:["orders","statistics","archive","notifications","settings"]},courier:{role:"courier",permissions:["view_order","update_order"],canCreateRoles:[],accessibleSections:["orders","archive","notifications","settings"]}};function o(e,r){return a[e]?.permissions.includes(r)||!1}function i(e){return a[e]?.accessibleSections||[]}},51937:(e,r,t)=>{Promise.resolve().then(t.bind(t,86088))},53923:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},61135:()=>{},63523:(e,r,t)=>{"use strict";t.d(r,{y:()=>i}),t(43210);var s=t(48126),a=t(16391);class o{async login(e){try{console.log("\uD83D\uDD10 تسجيل الدخول مع Firebase...");let{firebaseAuthService:r}=await Promise.resolve().then(t.bind(t,39903)),s=await r.login(e.username,e.password);if(!s.success||!s.user)throw Error(s.error||"فشل في تسجيل الدخول");let o=s.user;try{o=await a.Dv.getUserByUsername(e.username)}catch(r){return console.warn("Database not available, using fallback auth:",r),this.fallbackLogin(e)}if(!o)return this.fallbackLogin(e);if(!o.is_active)throw Error("الحساب غير مفعل");if("123456"!==e.password)throw Error("كلمة المرور غير صحيحة");let i={id:o.id,username:o.username,name:o.name,phone:o.phone,role:o.role,permissions:[],locationId:o.location_id||"main_center",location:o.location||{id:"main_center",name:"المركز العام",type:"company"},createdBy:o.created_by,createdAt:new Date(o.created_at),isActive:o.is_active,accessToken:"supabase_access_token",refreshToken:"supabase_refresh_token"};return this.currentUser=i,this.notifyListeners(),i}catch(e){if(console.error("Login error:",e),e instanceof Error)throw e;throw Error("فشل في تسجيل الدخول")}}async fallbackLogin(e){let r={manager:{id:"manager_1",username:"manager",name:"مدير النظام",phone:"07901234567",role:"manager",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"system",createdAt:new Date,isActive:!0,accessToken:"fallback_access_token",refreshToken:"fallback_refresh_token"},supervisor:{id:"supervisor_1",username:"supervisor",name:"متابع النظام",phone:"07901234568",role:"supervisor",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"manager_1",createdAt:new Date,isActive:!0,accessToken:"fallback_access_token_supervisor",refreshToken:"fallback_refresh_token_supervisor"},courier:{id:"courier_1",username:"courier",name:"مندوب التوصيل",phone:"07901234570",role:"courier",permissions:[],locationId:"main_center",location:{id:"main_center",name:"المركز العام",type:"company"},createdBy:"manager_1",createdAt:new Date,isActive:!0,accessToken:"fallback_access_token_courier",refreshToken:"fallback_refresh_token_courier"}}[e.username];if(!r||"123456"!==e.password)throw Error("بيانات الدخول غير صحيحة");return this.currentUser=r,this.notifyListeners(),r}async logout(){this.currentUser=null,this.notifyListeners()}getCurrentUser(){return this.currentUser,this.currentUser}hasPermission(e){let r=this.getCurrentUser();return!!r&&(0,s._m)(r.role,e)}getAccessibleSections(){let e=this.getCurrentUser();return e?(0,s.JI)(e.role):[]}canCreateRole(e){let r=this.getCurrentUser();return!!r&&(({manager:["supervisor","courier"],supervisor:["courier"],courier:[]})[r.role]?.includes(e)||!1)}addListener(e){this.listeners.push(e)}removeListener(e){this.listeners=this.listeners.filter(r=>r!==e)}notifyListeners(){this.listeners.forEach(e=>e(this.currentUser))}async updateProfile(e){if(!this.currentUser)throw Error("لم يتم تسجيل الدخول");return await new Promise(e=>setTimeout(e,500)),this.currentUser={...this.currentUser,...e},this.notifyListeners(),this.currentUser}async changePassword(e,r){if(!this.currentUser)throw Error("لم يتم تسجيل الدخول");if(await new Promise(e=>setTimeout(e,1e3)),"123456"!==e)throw Error("كلمة المرور الحالية غير صحيحة");console.log("تم تغيير كلمة المرور بنجاح")}async validateSession(){if(!this.getCurrentUser())return!1;try{return await new Promise(e=>setTimeout(e,200)),!0}catch(e){return await this.logout(),!1}}constructor(){this.currentUser=null,this.listeners=[]}}let i=new o},67958:(e,r,t)=>{"use strict";t.d(r,{A:()=>l,AuthProvider:()=>c});var s=t(60687),a=t(43210),o=t(16189);t(63523),t(5475);var i=t(39903);let n=(0,a.createContext)(void 0);function c({children:e}){let[r,t]=(0,a.useState)(null),[c,l]=(0,a.useState)(!1),[u,d]=(0,a.useState)(!0),m=(0,o.useRouter)(),h=async(e,r)=>{try{console.log("\uD83D\uDD10 AuthProvider: محاولة تسجيل الدخول مع Firebase...");let s=await i.firebaseAuthService.login(e,r);if(!s.success||!s.user)return console.error("❌ فشل تسجيل الدخول:",s.error),!1;let a={...s.user,accessToken:"firebase_token",refreshToken:"firebase_refresh"};return t(a),l(!0),console.log("✅ تم تسجيل الدخول بنجاح:",s.user.name),!0}catch(e){return console.error("❌ خطأ في تسجيل الدخول:",e),!1}},p=async()=>{try{console.log("\uD83D\uDEAA AuthProvider: تسجيل الخروج من Firebase..."),await i.firebaseAuthService.logout(),t(null),l(!1),m.push("/login")}catch(e){console.error("Logout error:",e),m.push("/login")}};return u?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse",children:(0,s.jsx)("div",{className:"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"مرسال"}),(0,s.jsx)("p",{className:"text-gray-500",children:"جاري التحقق من بيانات الدخول..."})]})}):(0,s.jsx)(n.Provider,{value:{user:r,isAuthenticated:c,login:h,logout:p,loading:u},children:e})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},86088:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>a});var s=t(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\components\\auth-provider.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\components\\auth-provider.tsx","useAuth")},88385:(e,r,t)=>{Promise.resolve().then(t.bind(t,67958))},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,generateViewport:()=>c,metadata:()=>n});var s=t(37413),a=t(25091),o=t.n(a);t(61135);var i=t(86088);let n={title:"مرسال - نظام إدارة التوصيل",description:"نظام إدارة عمليات شركات التوصيل السريع",manifest:"/manifest.json"};function c(){return{width:"device-width",initialScale:1,maximumScale:5,userScalable:!0,themeColor:"#3B82F6",colorScheme:"light dark",viewportFit:"cover"}}function l({children:e}){return(0,s.jsx)("html",{lang:"ar",dir:"rtl",className:"h-full",children:(0,s.jsx)("body",{className:`${o().variable} antialiased h-full w-full overflow-x-hidden`,children:(0,s.jsx)(i.AuthProvider,{children:(0,s.jsx)("div",{className:"min-h-screen w-full max-w-full overflow-x-hidden",children:e})})})})}}};