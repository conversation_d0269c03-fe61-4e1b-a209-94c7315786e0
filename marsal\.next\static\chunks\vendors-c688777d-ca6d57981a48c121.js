"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[37],{10782:(t,r,e)=>{e.d(r,{A:()=>h});var i=e(22868),n=e(322),o=e(10077),a=e(1933),s=e(38988);let h=function(){function t(t,r,e,i){if(this.width=t,this.height=r,this.rowSize=e,this.bits=i,null==r&&(r=t),this.height=r,t<1||r<1)throw new s.A("Both dimensions must be greater than 0");null==e&&(e=Math.floor((t+31)/32)),this.rowSize=e,null==i&&(this.bits=new Int32Array(this.rowSize*this.height))}return t.parseFromBooleanArray=function(r){for(var e=r.length,i=r[0].length,n=new t(i,e),o=0;o<e;o++)for(var a=r[o],s=0;s<i;s++)a[s]&&n.set(s,o);return n},t.parseFromString=function(r,e,i){if(null===r)throw new s.A("stringRepresentation cannot be null");for(var n=Array(r.length),o=0,a=0,h=-1,u=0,f=0;f<r.length;)if("\n"===r.charAt(f)||"\r"===r.charAt(f)){if(o>a){if(-1===h)h=o-a;else if(o-a!==h)throw new s.A("row lengths do not match");a=o,u++}f++}else if(r.substring(f,f+e.length)===e)f+=e.length,n[o]=!0,o++;else if(r.substring(f,f+i.length)===i)f+=i.length,n[o]=!1,o++;else throw new s.A("illegal character encountered: "+r.substring(f));if(o>a){if(-1===h)h=o-a;else if(o-a!==h)throw new s.A("row lengths do not match");u++}for(var c=new t(h,u),l=0;l<o;l++)n[l]&&c.set(Math.floor(l%h),Math.floor(l/h));return c},t.prototype.get=function(t,r){var e=r*this.rowSize+Math.floor(t/32);return(this.bits[e]>>>(31&t)&1)!=0},t.prototype.set=function(t,r){var e=r*this.rowSize+Math.floor(t/32);this.bits[e]|=1<<(31&t)},t.prototype.unset=function(t,r){var e=r*this.rowSize+Math.floor(t/32);this.bits[e]&=~(1<<(31&t))},t.prototype.flip=function(t,r){var e=r*this.rowSize+Math.floor(t/32);this.bits[e]^=1<<(31&t)},t.prototype.xor=function(t){if(this.width!==t.getWidth()||this.height!==t.getHeight()||this.rowSize!==t.getRowSize())throw new s.A("input matrix dimensions do not match");for(var r=new i.A(Math.floor(this.width/32)+1),e=this.rowSize,n=this.bits,o=0,a=this.height;o<a;o++)for(var h=o*e,u=t.getRow(o,r).getBitArray(),f=0;f<e;f++)n[h+f]^=u[f]},t.prototype.clear=function(){for(var t=this.bits,r=t.length,e=0;e<r;e++)t[e]=0},t.prototype.setRegion=function(t,r,e,i){if(r<0||t<0)throw new s.A("Left and top must be nonnegative");if(i<1||e<1)throw new s.A("Height and width must be at least 1");var n=t+e,o=r+i;if(o>this.height||n>this.width)throw new s.A("The region must fit inside the matrix");for(var a=this.rowSize,h=this.bits,u=r;u<o;u++)for(var f=u*a,c=t;c<n;c++)h[f+Math.floor(c/32)]|=1<<(31&c)},t.prototype.getRow=function(t,r){null==r||r.getSize()<this.width?r=new i.A(this.width):r.clear();for(var e=this.rowSize,n=this.bits,o=t*e,a=0;a<e;a++)r.setBulk(32*a,n[o+a]);return r},t.prototype.setRow=function(t,r){n.A.arraycopy(r.getBitArray(),0,this.bits,t*this.rowSize,this.rowSize)},t.prototype.rotate180=function(){for(var t=this.getWidth(),r=this.getHeight(),e=new i.A(t),n=new i.A(t),o=0,a=Math.floor((r+1)/2);o<a;o++)e=this.getRow(o,e),n=this.getRow(r-1-o,n),e.reverse(),n.reverse(),this.setRow(o,n),this.setRow(r-1-o,e)},t.prototype.getEnclosingRectangle=function(){for(var t=this.width,r=this.height,e=this.rowSize,i=this.bits,n=t,o=r,a=-1,s=-1,h=0;h<r;h++)for(var u=0;u<e;u++){var f=i[h*e+u];if(0!==f){if(h<o&&(o=h),h>s&&(s=h),32*u<n){for(var c=0;f<<31-c==0;)c++;32*u+c<n&&(n=32*u+c)}if(32*u+31>a){for(var c=31;f>>>c==0;)c--;32*u+c>a&&(a=32*u+c)}}}return a<n||s<o?null:Int32Array.from([n,o,a-n+1,s-o+1])},t.prototype.getTopLeftOnBit=function(){for(var t=this.rowSize,r=this.bits,e=0;e<r.length&&0===r[e];)e++;if(e===r.length)return null;for(var i=e/t,n=e%t*32,o=r[e],a=0;o<<31-a==0;)a++;return n+=a,Int32Array.from([n,i])},t.prototype.getBottomRightOnBit=function(){for(var t=this.rowSize,r=this.bits,e=r.length-1;e>=0&&0===r[e];)e--;if(e<0)return null;for(var i=Math.floor(e/t),n=32*Math.floor(e%t),o=r[e],a=31;o>>>a==0;)a--;return n+=a,Int32Array.from([n,i])},t.prototype.getWidth=function(){return this.width},t.prototype.getHeight=function(){return this.height},t.prototype.getRowSize=function(){return this.rowSize},t.prototype.equals=function(r){return r instanceof t&&this.width===r.width&&this.height===r.height&&this.rowSize===r.rowSize&&o.A.equals(this.bits,r.bits)},t.prototype.hashCode=function(){var t=this.width;return 31*(t=31*(t=31*(t=31*t+this.width)+this.height)+this.rowSize)+o.A.hashCode(this.bits)},t.prototype.toString=function(t,r,e){return void 0===t&&(t="X "),void 0===r&&(r="  "),void 0===e&&(e="\n"),this.buildToString(t,r,e)},t.prototype.buildToString=function(t,r,e){for(var i=new a.A,n=0,o=this.height;n<o;n++){for(var s=0,h=this.width;s<h;s++)i.append(this.get(s,n)?t:r);i.append(e)}return i.toString()},t.prototype.clone=function(){return new t(this.width,this.height,this.rowSize,this.bits.slice())},t}()},20367:(t,r,e)=>{e.d(r,{A:()=>n});var i=e(92727);let n=function(){function t(){}return t.setGridSampler=function(r){t.gridSampler=r},t.getInstance=function(){return t.gridSampler},t.gridSampler=new i.A,t}()},22868:(t,r,e)=>{e.d(r,{A:()=>s});var i=e(38988),n=e(10077),o=e(63479),a=e(322);let s=function(){function t(r,e){void 0===r?(this.size=0,this.bits=new Int32Array(1)):(this.size=r,null==e?this.bits=t.makeArray(r):this.bits=e)}return t.prototype.getSize=function(){return this.size},t.prototype.getSizeInBytes=function(){return Math.floor((this.size+7)/8)},t.prototype.ensureCapacity=function(r){if(r>32*this.bits.length){var e=t.makeArray(r);a.A.arraycopy(this.bits,0,e,0,this.bits.length),this.bits=e}},t.prototype.get=function(t){return(this.bits[Math.floor(t/32)]&1<<(31&t))!=0},t.prototype.set=function(t){this.bits[Math.floor(t/32)]|=1<<(31&t)},t.prototype.flip=function(t){this.bits[Math.floor(t/32)]^=1<<(31&t)},t.prototype.getNextSet=function(t){var r=this.size;if(t>=r)return r;var e=this.bits,i=Math.floor(t/32),n=e[i];n&=~((1<<(31&t))-1);for(var a=e.length;0===n;){if(++i===a)return r;n=e[i]}var s=32*i+o.A.numberOfTrailingZeros(n);return s>r?r:s},t.prototype.getNextUnset=function(t){var r=this.size;if(t>=r)return r;var e=this.bits,i=Math.floor(t/32),n=~e[i];n&=~((1<<(31&t))-1);for(var a=e.length;0===n;){if(++i===a)return r;n=~e[i]}var s=32*i+o.A.numberOfTrailingZeros(n);return s>r?r:s},t.prototype.setBulk=function(t,r){this.bits[Math.floor(t/32)]=r},t.prototype.setRange=function(t,r){if(r<t||t<0||r>this.size)throw new i.A;if(r!==t)for(var e=Math.floor(t/32),n=Math.floor(--r/32),o=this.bits,a=e;a<=n;a++){var s=a>e?0:31&t,h=(2<<(a<n?31:31&r))-(1<<s);o[a]|=h}},t.prototype.clear=function(){for(var t=this.bits.length,r=this.bits,e=0;e<t;e++)r[e]=0},t.prototype.isRange=function(t,r,e){if(r<t||t<0||r>this.size)throw new i.A;if(r===t)return!0;for(var n=Math.floor(t/32),o=Math.floor(--r/32),a=this.bits,s=n;s<=o;s++){var h=s>n?0:31&t,u=(2<<(s<o?31:31&r))-(1<<h)|0;if((a[s]&u)!==(e?u:0))return!1}return!0},t.prototype.appendBit=function(t){this.ensureCapacity(this.size+1),t&&(this.bits[Math.floor(this.size/32)]|=1<<(31&this.size)),this.size++},t.prototype.appendBits=function(t,r){if(r<0||r>32)throw new i.A("Num bits must be between 0 and 32");this.ensureCapacity(this.size+r);for(var e=r;e>0;e--)this.appendBit((t>>e-1&1)==1)},t.prototype.appendBitArray=function(t){var r=t.size;this.ensureCapacity(this.size+r);for(var e=0;e<r;e++)this.appendBit(t.get(e))},t.prototype.xor=function(t){if(this.size!==t.size)throw new i.A("Sizes don't match");for(var r=this.bits,e=0,n=r.length;e<n;e++)r[e]^=t.bits[e]},t.prototype.toBytes=function(t,r,e,i){for(var n=0;n<i;n++){for(var o=0,a=0;a<8;a++)this.get(t)&&(o|=1<<7-a),t++;r[e+n]=o}},t.prototype.getBitArray=function(){return this.bits},t.prototype.reverse=function(){for(var t=new Int32Array(this.bits.length),r=Math.floor((this.size-1)/32),e=r+1,i=this.bits,n=0;n<e;n++){var o=i[n];o=(o=(o=(o=(o=o>>1&0x55555555|(0x55555555&o)<<1)>>2&0x33333333|(0x33333333&o)<<2)>>4&0xf0f0f0f|(0xf0f0f0f&o)<<4)>>8&0xff00ff|(0xff00ff&o)<<8)>>16&65535|(65535&o)<<16,t[r-n]=o}if(this.size!==32*e){for(var a=32*e-this.size,s=t[0]>>>a,n=1;n<e;n++){var h=t[n];s|=h<<32-a,t[n-1]=s,s=h>>>a}t[e-1]=s}this.bits=t},t.makeArray=function(t){return new Int32Array(Math.floor((t+31)/32))},t.prototype.equals=function(r){return r instanceof t&&this.size===r.size&&n.A.equals(this.bits,r.bits)},t.prototype.hashCode=function(){return 31*this.size+n.A.hashCode(this.bits)},t.prototype.toString=function(){for(var t="",r=0,e=this.size;r<e;r++)(7&r)==0&&(t+=" "),t+=this.get(r)?"X":".";return t},t.prototype.clone=function(){return new t(this.size,this.bits.slice())},t.prototype.toArray=function(){for(var t=[],r=0,e=this.size;r<e;r++)t.push(this.get(r));return t},t}()},23510:(t,r,e)=>{e.d(r,{A:()=>a});var i=e(79417),n=e(59612),o=e(63623);let a=function(){function t(){}return t.castAsNonUtf8Char=function(t,r){void 0===r&&(r=null);var e=r?r.getName():this.ISO88591;return o.A.decode(new Uint8Array([t]),e)},t.guessEncoding=function(r,e){if(null!=e&&void 0!==e.get(i.A.CHARACTER_SET))return e.get(i.A.CHARACTER_SET).toString();for(var n=r.length,o=!0,a=!0,s=!0,h=0,u=0,f=0,c=0,l=0,p=0,S=0,I=0,d=0,g=0,y=0,v=r.length>3&&239===r[0]&&187===r[1]&&191===r[2],O=0;O<n&&(o||a||s);O++){var w=255&r[O];s&&(h>0?(128&w)==0?s=!1:h--:(128&w)!=0&&((64&w)==0?s=!1:(h++,(32&w)==0?u++:(h++,(16&w)==0?f++:(h++,(8&w)==0?c++:s=!1))))),o&&(w>127&&w<160?o=!1:w>159&&(w<192||215===w||247===w)&&y++),a&&(l>0?w<64||127===w||w>252?a=!1:l--:128===w||160===w||w>239?a=!1:w>160&&w<224?(p++,I=0,++S>d&&(d=S)):w>127?(l++,S=0,++I>g&&(g=I)):(S=0,I=0))}return(s&&h>0&&(s=!1),a&&l>0&&(a=!1),s&&(v||u+f+c>0))?t.UTF8:a&&(t.ASSUME_SHIFT_JIS||d>=3||g>=3)?t.SHIFT_JIS:o&&a?2===d&&2===p||10*y>=n?t.SHIFT_JIS:t.ISO88591:o?t.ISO88591:a?t.SHIFT_JIS:s?t.UTF8:t.PLATFORM_DEFAULT_ENCODING},t.format=function(t){for(var r=[],e=1;e<arguments.length;e++)r[e-1]=arguments[e];var i=-1;return t.replace(/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g,function(t,e,n,o,a,s){if("%%"===t)return"%";if(void 0!==r[++i]){t=o?parseInt(o.substr(1)):void 0;var h,u=a?parseInt(a.substr(1)):void 0;switch(s){case"s":h=r[i];break;case"c":h=r[i][0];break;case"f":h=parseFloat(r[i]).toFixed(t);break;case"p":h=parseFloat(r[i]).toPrecision(t);break;case"e":h=parseFloat(r[i]).toExponential(t);break;case"x":h=parseInt(r[i]).toString(u||16);break;case"d":h=parseFloat(parseInt(r[i],u||10).toPrecision(t)).toFixed(0)}h="object"==typeof h?JSON.stringify(h):(+h).toString(u);for(var f=parseInt(n),c=n&&n[0]+""=="0"?"0":" ";h.length<f;)h=void 0!==e?h+c:c+h;return h}})},t.getBytes=function(t,r){return o.A.encode(t,r)},t.getCharCode=function(t,r){return void 0===r&&(r=0),t.charCodeAt(r)},t.getCharAt=function(t){return String.fromCharCode(t)},t.SHIFT_JIS=n.A.SJIS.getName(),t.GB2312="GB2312",t.ISO88591=n.A.ISO8859_1.getName(),t.EUC_JP="EUC_JP",t.UTF8=n.A.UTF8.getName(),t.PLATFORM_DEFAULT_ENCODING=t.UTF8,t.ASSUME_SHIFT_JIS=!1,t}()},38972:(t,r,e)=>{e.d(r,{A:()=>a});var i=e(55695),n=e(10782),o=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function i(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}}();let a=function(t){function r(r){var e=t.call(this,r)||this;return e.matrix=null,e}return o(r,t),r.prototype.getBlackMatrix=function(){if(null!==this.matrix)return this.matrix;var e=this.getLuminanceSource(),i=e.getWidth(),o=e.getHeight();if(i>=r.MINIMUM_DIMENSION&&o>=r.MINIMUM_DIMENSION){var a=e.getMatrix(),s=i>>r.BLOCK_SIZE_POWER;(i&r.BLOCK_SIZE_MASK)!=0&&s++;var h=o>>r.BLOCK_SIZE_POWER;(o&r.BLOCK_SIZE_MASK)!=0&&h++;var u=r.calculateBlackPoints(a,s,h,i,o),f=new n.A(i,o);r.calculateThresholdForBlock(a,s,h,i,o,u,f),this.matrix=f}else this.matrix=t.prototype.getBlackMatrix.call(this);return this.matrix},r.prototype.createBinarizer=function(t){return new r(t)},r.calculateThresholdForBlock=function(t,e,i,n,o,a,s){for(var h=o-r.BLOCK_SIZE,u=n-r.BLOCK_SIZE,f=0;f<i;f++){var c=f<<r.BLOCK_SIZE_POWER;c>h&&(c=h);for(var l=r.cap(f,2,i-3),p=0;p<e;p++){var S=p<<r.BLOCK_SIZE_POWER;S>u&&(S=u);for(var I=r.cap(p,2,e-3),d=0,g=-2;g<=2;g++){var y=a[l+g];d+=y[I-2]+y[I-1]+y[I]+y[I+1]+y[I+2]}var v=d/25;r.thresholdBlock(t,S,c,v,n,s)}}},r.cap=function(t,r,e){return t<r?r:t>e?e:t},r.thresholdBlock=function(t,e,i,n,o,a){for(var s=0,h=i*o+e;s<r.BLOCK_SIZE;s++,h+=o)for(var u=0;u<r.BLOCK_SIZE;u++)(255&t[h+u])<=n&&a.set(e+u,i+s)},r.calculateBlackPoints=function(t,e,i,n,o){for(var a=o-r.BLOCK_SIZE,s=n-r.BLOCK_SIZE,h=Array(i),u=0;u<i;u++){h[u]=new Int32Array(e);var f=u<<r.BLOCK_SIZE_POWER;f>a&&(f=a);for(var c=0;c<e;c++){var l=c<<r.BLOCK_SIZE_POWER;l>s&&(l=s);for(var p=0,S=255,I=0,d=0,g=f*n+l;d<r.BLOCK_SIZE;d++,g+=n){for(var y=0;y<r.BLOCK_SIZE;y++){var v=255&t[g+y];p+=v,v<S&&(S=v),v>I&&(I=v)}if(I-S>r.MIN_DYNAMIC_RANGE)for(d++,g+=n;d<r.BLOCK_SIZE;d++,g+=n)for(var y=0;y<r.BLOCK_SIZE;y++)p+=255&t[g+y]}var O=p>>2*r.BLOCK_SIZE_POWER;if(I-S<=r.MIN_DYNAMIC_RANGE&&(O=S/2,u>0&&c>0)){var w=(h[u-1][c]+2*h[u][c-1]+h[u-1][c-1])/4;S<w&&(O=w)}h[u][c]=O}}return h},r.BLOCK_SIZE_POWER=3,r.BLOCK_SIZE=1<<r.BLOCK_SIZE_POWER,r.BLOCK_SIZE_MASK=r.BLOCK_SIZE-1,r.MINIMUM_DIMENSION=5*r.BLOCK_SIZE,r.MIN_DYNAMIC_RANGE=24,r}(i.A)},39798:(t,r,e)=>{e.d(r,{A:()=>i});let i=function(){function t(t,r,e,i,n,o,a,s,h){this.a11=t,this.a21=r,this.a31=e,this.a12=i,this.a22=n,this.a32=o,this.a13=a,this.a23=s,this.a33=h}return t.quadrilateralToQuadrilateral=function(r,e,i,n,o,a,s,h,u,f,c,l,p,S,I,d){var g=t.quadrilateralToSquare(r,e,i,n,o,a,s,h);return t.squareToQuadrilateral(u,f,c,l,p,S,I,d).times(g)},t.prototype.transformPoints=function(t){for(var r=t.length,e=this.a11,i=this.a12,n=this.a13,o=this.a21,a=this.a22,s=this.a23,h=this.a31,u=this.a32,f=this.a33,c=0;c<r;c+=2){var l=t[c],p=t[c+1],S=n*l+s*p+f;t[c]=(e*l+o*p+h)/S,t[c+1]=(i*l+a*p+u)/S}},t.prototype.transformPointsWithValues=function(t,r){for(var e=this.a11,i=this.a12,n=this.a13,o=this.a21,a=this.a22,s=this.a23,h=this.a31,u=this.a32,f=this.a33,c=t.length,l=0;l<c;l++){var p=t[l],S=r[l],I=n*p+s*S+f;t[l]=(e*p+o*S+h)/I,r[l]=(i*p+a*S+u)/I}},t.squareToQuadrilateral=function(r,e,i,n,o,a,s,h){var u=r-i+o-s,f=e-n+a-h;if(0===u&&0===f)return new t(i-r,o-i,r,n-e,a-n,e,0,0,1);var c=i-o,l=s-o,p=n-a,S=h-a,I=c*S-l*p,d=(u*S-l*f)/I,g=(c*f-u*p)/I;return new t(i-r+d*i,s-r+g*s,r,n-e+d*n,h-e+g*h,e,d,g,1)},t.quadrilateralToSquare=function(r,e,i,n,o,a,s,h){return t.squareToQuadrilateral(r,e,i,n,o,a,s,h).buildAdjoint()},t.prototype.buildAdjoint=function(){return new t(this.a22*this.a33-this.a23*this.a32,this.a23*this.a31-this.a21*this.a33,this.a21*this.a32-this.a22*this.a31,this.a13*this.a32-this.a12*this.a33,this.a11*this.a33-this.a13*this.a31,this.a12*this.a31-this.a11*this.a32,this.a12*this.a23-this.a13*this.a22,this.a13*this.a21-this.a11*this.a23,this.a11*this.a22-this.a12*this.a21)},t.prototype.times=function(r){return new t(this.a11*r.a11+this.a21*r.a12+this.a31*r.a13,this.a11*r.a21+this.a21*r.a22+this.a31*r.a23,this.a11*r.a31+this.a21*r.a32+this.a31*r.a33,this.a12*r.a11+this.a22*r.a12+this.a32*r.a13,this.a12*r.a21+this.a22*r.a22+this.a32*r.a23,this.a12*r.a31+this.a22*r.a32+this.a32*r.a33,this.a13*r.a11+this.a23*r.a12+this.a33*r.a13,this.a13*r.a21+this.a23*r.a22+this.a33*r.a23,this.a13*r.a31+this.a23*r.a32+this.a33*r.a33)},t}()},48852:(t,r,e)=>{e.d(r,{A:()=>n});var i=e(438);let n=function(){function t(){}return t.checkAndNudgePoints=function(t,r){for(var e=t.getWidth(),n=t.getHeight(),o=!0,a=0;a<r.length&&o;a+=2){var s=Math.floor(r[a]),h=Math.floor(r[a+1]);if(s<-1||s>e||h<-1||h>n)throw new i.A;o=!1,-1===s?(r[a]=0,o=!0):s===e&&(r[a]=e-1,o=!0),-1===h?(r[a+1]=0,o=!0):h===n&&(r[a+1]=n-1,o=!0)}o=!0;for(var a=r.length-2;a>=0&&o;a-=2){var s=Math.floor(r[a]),h=Math.floor(r[a+1]);if(s<-1||s>e||h<-1||h>n)throw new i.A;o=!1,-1===s?(r[a]=0,o=!0):s===e&&(r[a]=e-1,o=!0),-1===h?(r[a+1]=0,o=!0):h===n&&(r[a+1]=n-1,o=!0)}},t}()},55695:(t,r,e)=>{e.d(r,{A:()=>h});var i=e(19116),n=e(22868),o=e(10782),a=e(438),s=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function i(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}}();let h=function(t){function r(e){var i=t.call(this,e)||this;return i.luminances=r.EMPTY,i.buckets=new Int32Array(r.LUMINANCE_BUCKETS),i}return s(r,t),r.prototype.getBlackRow=function(t,e){var i=this.getLuminanceSource(),o=i.getWidth();null==e||e.getSize()<o?e=new n.A(o):e.clear(),this.initArrays(o);for(var a=i.getRow(t,this.luminances),s=this.buckets,h=0;h<o;h++)s[(255&a[h])>>r.LUMINANCE_SHIFT]++;var u=r.estimateBlackPoint(s);if(o<3)for(var h=0;h<o;h++)(255&a[h])<u&&e.set(h);else for(var f=255&a[0],c=255&a[1],h=1;h<o-1;h++){var l=255&a[h+1];(4*c-f-l)/2<u&&e.set(h),f=c,c=l}return e},r.prototype.getBlackMatrix=function(){var t=this.getLuminanceSource(),e=t.getWidth(),i=t.getHeight(),n=new o.A(e,i);this.initArrays(e);for(var a=this.buckets,s=1;s<5;s++)for(var h=Math.floor(i*s/5),u=t.getRow(h,this.luminances),f=Math.floor(4*e/5),c=Math.floor(e/5);c<f;c++){var l=255&u[c];a[l>>r.LUMINANCE_SHIFT]++}for(var p=r.estimateBlackPoint(a),S=t.getMatrix(),s=0;s<i;s++)for(var I=s*e,c=0;c<e;c++){var l=255&S[I+c];l<p&&n.set(c,s)}return n},r.prototype.createBinarizer=function(t){return new r(t)},r.prototype.initArrays=function(t){this.luminances.length<t&&(this.luminances=new Uint8ClampedArray(t));for(var e=this.buckets,i=0;i<r.LUMINANCE_BUCKETS;i++)e[i]=0},r.estimateBlackPoint=function(t){for(var e=t.length,i=0,n=0,o=0,s=0;s<e;s++)t[s]>o&&(n=s,o=t[s]),t[s]>i&&(i=t[s]);for(var h=0,u=0,s=0;s<e;s++){var f=s-n,c=t[s]*f*f;c>u&&(h=s,u=c)}if(n>h){var l=n;n=h,h=l}if(h-n<=e/16)throw new a.A;for(var p=h-1,S=-1,s=h-1;s>n;s--){var I=s-n,c=I*I*(h-s)*(i-t[s]);c>S&&(p=s,S=c)}return p<<r.LUMINANCE_SHIFT},r.LUMINANCE_BITS=5,r.LUMINANCE_SHIFT=8-r.LUMINANCE_BITS,r.LUMINANCE_BUCKETS=1<<r.LUMINANCE_BITS,r.EMPTY=Uint8ClampedArray.from([0]),r}(i.A)},55701:(t,r,e)=>{e.d(r,{A:()=>i});let i=function(){function t(t,r,e,i,n,o){void 0===n&&(n=-1),void 0===o&&(o=-1),this.rawBytes=t,this.text=r,this.byteSegments=e,this.ecLevel=i,this.structuredAppendSequenceNumber=n,this.structuredAppendParity=o,this.numBits=null==t?0:8*t.length}return t.prototype.getRawBytes=function(){return this.rawBytes},t.prototype.getNumBits=function(){return this.numBits},t.prototype.setNumBits=function(t){this.numBits=t},t.prototype.getText=function(){return this.text},t.prototype.getByteSegments=function(){return this.byteSegments},t.prototype.getECLevel=function(){return this.ecLevel},t.prototype.getErrorsCorrected=function(){return this.errorsCorrected},t.prototype.setErrorsCorrected=function(t){this.errorsCorrected=t},t.prototype.getErasures=function(){return this.erasures},t.prototype.setErasures=function(t){this.erasures=t},t.prototype.getOther=function(){return this.other},t.prototype.setOther=function(t){this.other=t},t.prototype.hasStructuredAppend=function(){return this.structuredAppendParity>=0&&this.structuredAppendSequenceNumber>=0},t.prototype.getStructuredAppendParity=function(){return this.structuredAppendParity},t.prototype.getStructuredAppendSequenceNumber=function(){return this.structuredAppendSequenceNumber},t}()},56451:(t,r,e)=>{e.d(r,{A:()=>i});let i=function(){function t(t,r){this.bits=t,this.points=r}return t.prototype.getBits=function(){return this.bits},t.prototype.getPoints=function(){return this.points},t}()},58789:(t,r,e)=>{e.d(r,{R:()=>l});var i=e(2257),n=e(91375),o=e(63623),a=e(23510),s=function(t){var r="function"==typeof Symbol&&Symbol.iterator,e=r&&t[r],i=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")},h=function(){function t(t){this.charset=t,this.name=t.name}return t.prototype.canEncode=function(t){try{return null!=o.A.encode(t,this.charset)}catch(t){return!1}},t}(),u=function(){function t(t,r,e){this.ENCODERS=["IBM437","ISO-8859-2","ISO-8859-3","ISO-8859-4","ISO-8859-5","ISO-8859-6","ISO-8859-7","ISO-8859-8","ISO-8859-9","ISO-8859-10","ISO-8859-11","ISO-8859-13","ISO-8859-14","ISO-8859-15","ISO-8859-16","windows-1250","windows-1251","windows-1252","windows-1256","Shift_JIS"].map(function(t){return new h(i.A.forName(t))}),this.encoders=[];var o,a,u,f,c,l,p=[];p.push(new h(n.A.ISO_8859_1));for(var S=null!=r&&r.name.startsWith("UTF"),I=0;I<t.length;I++){var d=!1;try{for(var g=(o=void 0,s(p)),y=g.next();!y.done;y=g.next()){var v=y.value,O=t.charAt(I);if(O.charCodeAt(0)===e||v.canEncode(O)){d=!0;break}}}catch(t){o={error:t}}finally{try{y&&!y.done&&(a=g.return)&&a.call(g)}finally{if(o)throw o.error}}if(!d)try{for(var w=(u=void 0,s(this.ENCODERS)),_=w.next();!_.done;_=w.next()){var v=_.value;if(v.canEncode(t.charAt(I))){p.push(v),d=!0;break}}}catch(t){u={error:t}}finally{try{_&&!_.done&&(f=w.return)&&f.call(w)}finally{if(u)throw u.error}}d||(S=!0)}if(1!==p.length||S){this.encoders=[];var A=0;try{for(var E=s(p),C=E.next();!C.done;C=E.next()){var v=C.value;this.encoders[A++]=v}}catch(t){c={error:t}}finally{try{C&&!C.done&&(l=E.return)&&l.call(E)}finally{if(c)throw c.error}}}else this.encoders=[p[0]];var b=-1;if(null!=r){for(var I=0;I<this.encoders.length;I++)if(null!=this.encoders[I]&&r.name===this.encoders[I].name){b=I;break}}this.priorityEncoderIndex=b}return t.prototype.length=function(){return this.encoders.length},t.prototype.getCharsetName=function(t){if(!(t<this.length()))throw Error("index must be less than length");return this.encoders[t].name},t.prototype.getCharset=function(t){if(!(t<this.length()))throw Error("index must be less than length");return this.encoders[t].charset},t.prototype.getECIValue=function(t){return this.encoders[t].charset.getValueIdentifier()},t.prototype.getPriorityEncoderIndex=function(){return this.priorityEncoderIndex},t.prototype.canEncode=function(t,r){if(!(r<this.length()))throw Error("index must be less than length");return!0},t.prototype.encode=function(t,r){if(!(r<this.length()))throw Error("index must be less than length");return o.A.encode(a.A.getCharAt(t),this.encoders[r].name)},t}(),f=e(63479),c=e(1933),l=function(){function t(t,r,e){this.fnc1=e;var i=new u(t,r,e);if(1===i.length())for(var n=0;n<this.bytes.length;n++){var o=t.charAt(n).charCodeAt(0);this.bytes[n]=o===e?1e3:o}else this.bytes=this.encodeMinimally(t,i,e)}return t.prototype.getFNC1Character=function(){return this.fnc1},t.prototype.length=function(){return this.bytes.length},t.prototype.haveNCharacters=function(t,r){if(t+r-1>=this.bytes.length)return!1;for(var e=0;e<r;e++)if(this.isECI(t+e))return!1;return!0},t.prototype.charAt=function(t){if(t<0||t>=this.length())throw Error(""+t);if(this.isECI(t))throw Error("value at "+t+" is not a character but an ECI");return this.isFNC1(t)?this.fnc1:this.bytes[t]},t.prototype.subSequence=function(t,r){if(t<0||t>r||r>this.length())throw Error(""+t);for(var e=new c.A,i=t;i<r;i++){if(this.isECI(i))throw Error("value at "+i+" is not a character but an ECI");e.append(this.charAt(i))}return e.toString()},t.prototype.isECI=function(t){if(t<0||t>=this.length())throw Error(""+t);return this.bytes[t]>255&&this.bytes[t]<=999},t.prototype.isFNC1=function(t){if(t<0||t>=this.length())throw Error(""+t);return 1e3===this.bytes[t]},t.prototype.getECIValue=function(t){if(t<0||t>=this.length())throw Error(""+t);if(!this.isECI(t))throw Error("value at "+t+" is not an ECI but a character");return this.bytes[t]-256},t.prototype.addEdge=function(t,r,e){(null==t[r][e.encoderIndex]||t[r][e.encoderIndex].cachedTotalSize>e.cachedTotalSize)&&(t[r][e.encoderIndex]=e)},t.prototype.addEdges=function(t,r,e,i,n,o){var a=t.charAt(i).charCodeAt(0),s=0,h=r.length();r.getPriorityEncoderIndex()>=0&&(a===o||r.canEncode(a,r.getPriorityEncoderIndex()))&&(h=(s=r.getPriorityEncoderIndex())+1);for(var u=s;u<h;u++)(a===o||r.canEncode(a,u))&&this.addEdge(e,i+1,new p(a,r,u,n,o))},t.prototype.encodeMinimally=function(t,r,e){var i=t.length,n=new p[i+1][r.length()];this.addEdges(t,r,n,0,null,e);for(var o=1;o<=i;o++){for(var a=0;a<r.length();a++)null!=n[o][a]&&o<i&&this.addEdges(t,r,n,o,n[o][a],e);for(var a=0;a<r.length();a++)n[o-1][a]=null}for(var s=-1,h=f.A.MAX_VALUE,a=0;a<r.length();a++)if(null!=n[i][a]){var u=n[i][a];u.cachedTotalSize<h&&(h=u.cachedTotalSize,s=a)}if(s<0)throw Error('Failed to encode "'+t+'"');for(var c=[],l=n[i][s];null!=l;){if(l.isFNC1())c.unshift(1e3);else for(var S=r.encode(l.c,l.encoderIndex),o=S.length-1;o>=0;o--)c.unshift(255&S[o]);(null===l.previous?0:l.previous.encoderIndex)!==l.encoderIndex&&c.unshift(256+r.getECIValue(l.encoderIndex)),l=l.previous}for(var I=[],o=0;o<I.length;o++)I[o]=c[o];return I},t}(),p=function(){function t(t,r,e,i,n){this.c=t,this.encoderSet=r,this.encoderIndex=e,this.previous=i,this.fnc1=n,this.c=t===n?1e3:t;var o=this.isFNC1()?1:r.encode(t,e).length;(null===i?0:i.encoderIndex)!==e&&(o+=3),null!=i&&(o+=i.cachedTotalSize),this.cachedTotalSize=o}return t.prototype.isFNC1=function(){return 1e3===this.c},t}()},59612:(t,r,e)=>{e.d(r,{A:()=>a});var i,n=e(71534),o=function(t){var r="function"==typeof Symbol&&Symbol.iterator,e=r&&t[r],i=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")};!function(t){t[t.Cp437=0]="Cp437",t[t.ISO8859_1=1]="ISO8859_1",t[t.ISO8859_2=2]="ISO8859_2",t[t.ISO8859_3=3]="ISO8859_3",t[t.ISO8859_4=4]="ISO8859_4",t[t.ISO8859_5=5]="ISO8859_5",t[t.ISO8859_6=6]="ISO8859_6",t[t.ISO8859_7=7]="ISO8859_7",t[t.ISO8859_8=8]="ISO8859_8",t[t.ISO8859_9=9]="ISO8859_9",t[t.ISO8859_10=10]="ISO8859_10",t[t.ISO8859_11=11]="ISO8859_11",t[t.ISO8859_13=12]="ISO8859_13",t[t.ISO8859_14=13]="ISO8859_14",t[t.ISO8859_15=14]="ISO8859_15",t[t.ISO8859_16=15]="ISO8859_16",t[t.SJIS=16]="SJIS",t[t.Cp1250=17]="Cp1250",t[t.Cp1251=18]="Cp1251",t[t.Cp1252=19]="Cp1252",t[t.Cp1256=20]="Cp1256",t[t.UnicodeBigUnmarked=21]="UnicodeBigUnmarked",t[t.UTF8=22]="UTF8",t[t.ASCII=23]="ASCII",t[t.Big5=24]="Big5",t[t.GB18030=25]="GB18030",t[t.EUC_KR=26]="EUC_KR"}(i||(i={}));let a=function(){function t(r,e,i){for(var n,a,s=[],h=3;h<arguments.length;h++)s[h-3]=arguments[h];this.valueIdentifier=r,this.name=i,"number"==typeof e?this.values=Int32Array.from([e]):this.values=e,this.otherEncodingNames=s,t.VALUE_IDENTIFIER_TO_ECI.set(r,this),t.NAME_TO_ECI.set(i,this);for(var u=this.values,f=0,c=u.length;f!==c;f++){var l=u[f];t.VALUES_TO_ECI.set(l,this)}try{for(var p=o(s),S=p.next();!S.done;S=p.next()){var I=S.value;t.NAME_TO_ECI.set(I,this)}}catch(t){n={error:t}}finally{try{S&&!S.done&&(a=p.return)&&a.call(p)}finally{if(n)throw n.error}}}return t.prototype.getValueIdentifier=function(){return this.valueIdentifier},t.prototype.getName=function(){return this.name},t.prototype.getValue=function(){return this.values[0]},t.getCharacterSetECIByValue=function(r){if(r<0||r>=900)throw new n.A("incorect value");var e=t.VALUES_TO_ECI.get(r);if(void 0===e)throw new n.A("incorect value");return e},t.getCharacterSetECIByName=function(r){var e=t.NAME_TO_ECI.get(r);if(void 0===e)throw new n.A("incorect value");return e},t.prototype.equals=function(r){return r instanceof t&&this.getName()===r.getName()},t.VALUE_IDENTIFIER_TO_ECI=new Map,t.VALUES_TO_ECI=new Map,t.NAME_TO_ECI=new Map,t.Cp437=new t(i.Cp437,Int32Array.from([0,2]),"Cp437"),t.ISO8859_1=new t(i.ISO8859_1,Int32Array.from([1,3]),"ISO-8859-1","ISO88591","ISO8859_1"),t.ISO8859_2=new t(i.ISO8859_2,4,"ISO-8859-2","ISO88592","ISO8859_2"),t.ISO8859_3=new t(i.ISO8859_3,5,"ISO-8859-3","ISO88593","ISO8859_3"),t.ISO8859_4=new t(i.ISO8859_4,6,"ISO-8859-4","ISO88594","ISO8859_4"),t.ISO8859_5=new t(i.ISO8859_5,7,"ISO-8859-5","ISO88595","ISO8859_5"),t.ISO8859_6=new t(i.ISO8859_6,8,"ISO-8859-6","ISO88596","ISO8859_6"),t.ISO8859_7=new t(i.ISO8859_7,9,"ISO-8859-7","ISO88597","ISO8859_7"),t.ISO8859_8=new t(i.ISO8859_8,10,"ISO-8859-8","ISO88598","ISO8859_8"),t.ISO8859_9=new t(i.ISO8859_9,11,"ISO-8859-9","ISO88599","ISO8859_9"),t.ISO8859_10=new t(i.ISO8859_10,12,"ISO-8859-10","ISO885910","ISO8859_10"),t.ISO8859_11=new t(i.ISO8859_11,13,"ISO-8859-11","ISO885911","ISO8859_11"),t.ISO8859_13=new t(i.ISO8859_13,15,"ISO-8859-13","ISO885913","ISO8859_13"),t.ISO8859_14=new t(i.ISO8859_14,16,"ISO-8859-14","ISO885914","ISO8859_14"),t.ISO8859_15=new t(i.ISO8859_15,17,"ISO-8859-15","ISO885915","ISO8859_15"),t.ISO8859_16=new t(i.ISO8859_16,18,"ISO-8859-16","ISO885916","ISO8859_16"),t.SJIS=new t(i.SJIS,20,"SJIS","Shift_JIS"),t.Cp1250=new t(i.Cp1250,21,"Cp1250","windows-1250"),t.Cp1251=new t(i.Cp1251,22,"Cp1251","windows-1251"),t.Cp1252=new t(i.Cp1252,23,"Cp1252","windows-1252"),t.Cp1256=new t(i.Cp1256,24,"Cp1256","windows-1256"),t.UnicodeBigUnmarked=new t(i.UnicodeBigUnmarked,25,"UnicodeBigUnmarked","UTF-16BE","UnicodeBig"),t.UTF8=new t(i.UTF8,26,"UTF8","UTF-8"),t.ASCII=new t(i.ASCII,Int32Array.from([27,170]),"ASCII","US-ASCII"),t.Big5=new t(i.Big5,28,"Big5"),t.GB18030=new t(i.GB18030,29,"GB18030","GB2312","EUC_CN","GBK"),t.EUC_KR=new t(i.EUC_KR,30,"EUC_KR","EUC-KR"),t}()},85770:(t,r,e)=>{e.d(r,{A:()=>n});var i=e(38988);let n=function(){function t(t){this.bytes=t,this.byteOffset=0,this.bitOffset=0}return t.prototype.getBitOffset=function(){return this.bitOffset},t.prototype.getByteOffset=function(){return this.byteOffset},t.prototype.readBits=function(t){if(t<1||t>32||t>this.available())throw new i.A(""+t);var r=0,e=this.bitOffset,n=this.byteOffset,o=this.bytes;if(e>0){var a=8-e,s=t<a?t:a,h=a-s,u=255>>8-s<<h;r=(o[n]&u)>>h,t-=s,8===(e+=s)&&(e=0,n++)}if(t>0){for(;t>=8;)r=r<<8|255&o[n],n++,t-=8;if(t>0){var h=8-t,u=255>>h<<h;r=r<<t|(o[n]&u)>>h,e+=t}}return this.bitOffset=e,this.byteOffset=n,r},t.prototype.available=function(){return 8*(this.bytes.length-this.byteOffset)-this.bitOffset},t}()},92727:(t,r,e)=>{e.d(r,{A:()=>h});var i=e(48852),n=e(10782),o=e(39798),a=e(438),s=function(){var t=function(r,e){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(r,e)};return function(r,e){function i(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}}();let h=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return s(r,t),r.prototype.sampleGrid=function(t,r,e,i,n,a,s,h,u,f,c,l,p,S,I,d,g,y,v){var O=o.A.quadrilateralToQuadrilateral(i,n,a,s,h,u,f,c,l,p,S,I,d,g,y,v);return this.sampleGridWithTransform(t,r,e,O)},r.prototype.sampleGridWithTransform=function(t,r,e,o){if(r<=0||e<=0)throw new a.A;for(var s=new n.A(r,e),h=new Float32Array(2*r),u=0;u<e;u++){for(var f=h.length,c=u+.5,l=0;l<f;l+=2)h[l]=l/2+.5,h[l+1]=c;o.transformPoints(h),i.A.checkAndNudgePoints(t,h);try{for(var l=0;l<f;l+=2)t.get(Math.floor(h[l]),Math.floor(h[l+1]))&&s.set(l/2,u)}catch(t){throw new a.A}}return s},r}(i.A)}}]);