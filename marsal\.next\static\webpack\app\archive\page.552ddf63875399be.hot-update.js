"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/archive/page",{

/***/ "(app-pages-browser)/./src/components/receipt-template.tsx":
/*!*********************************************!*\
  !*** ./src/components/receipt-template.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ReceiptTemplate = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = (param, ref)=>{\n    let { order } = param;\n    // Generate barcode data (simple implementation)\n    const generateBarcode = (trackingNumber)=>{\n        // This is a simple barcode representation based on tracking number\n        // In a real app, you'd use a proper barcode library\n        const barcodePattern = trackingNumber.split('').map(()=>'|||').join(' ');\n        return \"||||| \".concat(barcodePattern, \" |||||\");\n    };\n    const getStatusText = (status)=>{\n        const statusMap = {\n            pending: \"في الانتظار\",\n            assigned: \"مسند للمندوب\",\n            \"out-for-delivery\": \"خارج للتوصيل\",\n            delivered: \"تم التسليم\",\n            returned: \"راجع للمرسل\",\n            cancelled: \"ملغي\",\n            postponed: \"مؤجل\"\n        };\n        return statusMap[status] || status;\n    };\n    // Format price in English as required\n    const formatPrice = (amount)=>{\n        return amount.toLocaleString('en-US', {\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        });\n    };\n    // Get current date and time\n    const getCurrentDateTime = ()=>{\n        const now = new Date();\n        return {\n            date: now.toLocaleDateString('ar-IQ'),\n            time: now.toLocaleTimeString('ar-IQ', {\n                hour: '2-digit',\n                minute: '2-digit'\n            })\n        };\n    };\n    const { date, time } = getCurrentDateTime();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: \"receipt-template\",\n        style: {\n            width: \"110mm\",\n            height: \"130mm\",\n            padding: \"5mm\",\n            fontFamily: \"'Arial', 'Tahoma', sans-serif\",\n            fontSize: \"11px\",\n            lineHeight: \"1.3\",\n            color: \"#000\",\n            backgroundColor: \"#fff\",\n            border: \"2px solid #000\",\n            boxSizing: \"border-box\",\n            direction: \"rtl\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"8px\",\n                    borderBottom: \"2px solid #000\",\n                    paddingBottom: \"5px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: \"14px\",\n                            fontWeight: \"bold\",\n                            margin: \"0 0 3px 0\",\n                            color: \"#000\",\n                            letterSpacing: \"0.5px\",\n                            lineHeight: \"1.1\"\n                        },\n                        children: \"مكتب علي الشيباني للتوصيل السريع فرع الحي\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            margin: \"0\",\n                            fontSize: \"8px\",\n                            color: \"#666\",\n                            fontStyle: \"italic\"\n                        },\n                        children: \"خدمة توصيل سريعة وموثوقة\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"10px\",\n                    border: \"2px solid #000\",\n                    padding: \"6px\",\n                    backgroundColor: \"#f0f8ff\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"9px\",\n                            color: \"#666\",\n                            marginBottom: \"2px\"\n                        },\n                        children: \"رقم الوصل\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"18px\",\n                            fontWeight: \"bold\",\n                            color: \"#000\",\n                            letterSpacing: \"1px\"\n                        },\n                        children: order.trackingNumber\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"6px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"رقم هاتف الزبون:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"12px\",\n                            color: \"#0066cc\",\n                            direction: \"ltr\"\n                        },\n                        children: order.recipientPhone\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"6px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"الحالة:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"11px\",\n                            color: order.status === \"delivered\" ? \"#28a745\" : \"#ffc107\",\n                            padding: \"2px 6px\",\n                            borderRadius: \"3px\",\n                            backgroundColor: order.status === \"delivered\" ? \"#d4edda\" : \"#fff3cd\"\n                        },\n                        children: getStatusText(order.status)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"8px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"اسم المندوب:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"11px\",\n                            color: \"#333\"\n                        },\n                        children: order.assignedTo || \"غير محدد\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"10px\",\n                    border: \"2px solid #000\",\n                    padding: \"8px\",\n                    backgroundColor: \"#fff9e6\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"9px\",\n                            color: \"#666\",\n                            marginBottom: \"2px\"\n                        },\n                        children: \"المبلغ المطلوب\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"16px\",\n                            fontWeight: \"bold\",\n                            color: \"#000\",\n                            letterSpacing: \"1px\",\n                            direction: \"ltr\"\n                        },\n                        children: [\n                            order.amount.toLocaleString('en-US'),\n                            \" IQD\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"8px\",\n                    border: \"1px solid #000\",\n                    padding: \"6px\",\n                    backgroundColor: \"#fff\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"8px\",\n                            color: \"#666\",\n                            marginBottom: \"3px\"\n                        },\n                        children: \"الباركود\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontFamily: \"monospace\",\n                            fontSize: \"10px\",\n                            letterSpacing: \"1px\",\n                            color: \"#000\",\n                            backgroundColor: \"#fff\",\n                            padding: \"3px\"\n                        },\n                        children: generateBarcode(order.trackingNumber)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"8px\",\n                            color: \"#666\",\n                            marginTop: \"2px\"\n                        },\n                        children: order.trackingNumber\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 201,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    fontSize: \"8px\",\n                    color: \"#666\",\n                    borderTop: \"1px solid #ccc\",\n                    paddingTop: \"4px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"شكراً لاختياركم خدماتنا\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"2px\"\n                        },\n                        children: [\n                            \"التاريخ: \",\n                            new Date().toLocaleDateString('ar-IQ')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 225,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n        lineNumber: 53,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = ReceiptTemplate;\nReceiptTemplate.displayName = \"ReceiptTemplate\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReceiptTemplate);\nvar _c, _c1;\n$RefreshReg$(_c, \"ReceiptTemplate$forwardRef\");\n$RefreshReg$(_c1, \"ReceiptTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/receipt-template.tsx\n"));

/***/ })

});