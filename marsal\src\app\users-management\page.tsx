'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, Plus, Edit, Trash2, Users, UserPlus } from 'lucide-react';
import { firebaseAuthService } from '@/lib/firebase-auth';
import { User } from '@/types';
import Header from '@/components/header';
import { useAuth } from '@/components/auth-provider';

export default function UsersManagementPage() {
  const { user: currentUser } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form state
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    name: '',
    phone: '',
    role: 'courier',
    password: ''
  });

  // Check if current user is manager
  const isManager = currentUser?.role === 'manager';

  useEffect(() => {
    if (isManager) {
      loadUsers();
    }
  }, [isManager]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const allUsers = await firebaseAuthService.getAllUsers();
      setUsers(allUsers);
    } catch (error) {
      console.error('Error loading users:', error);
      setError('فشل في تحميل المستخدمين');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.username || !formData.email || !formData.name || !formData.password) {
      setError('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setCreating(true);
    setError('');
    setSuccess('');

    try {
      const result = await firebaseAuthService.createUser({
        ...formData,
        createdBy: currentUser?.username || 'unknown'
      });

      if (result.success) {
        setSuccess(`تم إنشاء المستخدم ${formData.username} بنجاح`);
        setFormData({
          username: '',
          email: '',
          name: '',
          phone: '',
          role: 'courier',
          password: ''
        });
        setShowCreateForm(false);
        await loadUsers(); // Reload users list
      } else {
        setError(result.error || 'فشل في إنشاء المستخدم');
      }
    } catch (error: any) {
      setError(error.message || 'حدث خطأ غير متوقع');
    } finally {
      setCreating(false);
    }
  };

  const handleToggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      const result = await firebaseAuthService.updateUser(userId, {
        isActive: !currentStatus
      });

      if (result.success) {
        setSuccess(`تم ${!currentStatus ? 'تفعيل' : 'تعطيل'} المستخدم بنجاح`);
        await loadUsers();
      } else {
        setError(result.error || 'فشل في تحديث حالة المستخدم');
      }
    } catch (error: any) {
      setError(error.message || 'حدث خطأ في تحديث المستخدم');
    }
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      manager: { label: 'مدير', color: 'bg-red-100 text-red-800' },
      supervisor: { label: 'متابع', color: 'bg-blue-100 text-blue-800' },
      courier: { label: 'مندوب', color: 'bg-green-100 text-green-800' }
    };

    const config = roleConfig[role as keyof typeof roleConfig] || { label: role, color: 'bg-gray-100 text-gray-800' };
    
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  if (!isManager) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Alert variant="destructive">
            <AlertDescription>
              ليس لديك صلاحية للوصول إلى هذه الصفحة. هذه الصفحة مخصصة للمديرين فقط.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <Users className="h-6 w-6" />
              إدارة المستخدمين
            </h1>
            <p className="text-gray-600 mt-1">إدارة حسابات المستخدمين في النظام</p>
          </div>
          
          <Button 
            onClick={() => setShowCreateForm(!showCreateForm)}
            className="flex items-center gap-2"
          >
            <UserPlus className="h-4 w-4" />
            إضافة مستخدم جديد
          </Button>
        </div>

        {/* Messages */}
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 border-green-200 bg-green-50">
            <AlertDescription className="text-green-800">{success}</AlertDescription>
          </Alert>
        )}

        {/* Create User Form */}
        {showCreateForm && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>إنشاء مستخدم جديد</CardTitle>
              <CardDescription>
                أدخل بيانات المستخدم الجديد
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleCreateUser} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="username">اسم المستخدم *</Label>
                    <Input
                      id="username"
                      value={formData.username}
                      onChange={(e) => setFormData({...formData, username: e.target.value})}
                      placeholder="أدخل اسم المستخدم"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">البريد الإلكتروني *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      placeholder="أدخل البريد الإلكتروني"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="name">الاسم الكامل *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="أدخل الاسم الكامل"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">رقم الهاتف</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      placeholder="أدخل رقم الهاتف"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="role">الدور *</Label>
                    <Select value={formData.role} onValueChange={(value) => setFormData({...formData, role: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الدور" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="manager">مدير</SelectItem>
                        <SelectItem value="supervisor">متابع</SelectItem>
                        <SelectItem value="courier">مندوب</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">كلمة المرور *</Label>
                    <Input
                      id="password"
                      type="password"
                      value={formData.password}
                      onChange={(e) => setFormData({...formData, password: e.target.value})}
                      placeholder="أدخل كلمة المرور"
                      required
                    />
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button type="submit" disabled={creating}>
                    {creating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        جاري الإنشاء...
                      </>
                    ) : (
                      <>
                        <Plus className="mr-2 h-4 w-4" />
                        إنشاء المستخدم
                      </>
                    )}
                  </Button>
                  
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setShowCreateForm(false)}
                  >
                    إلغاء
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Users List */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المستخدمين</CardTitle>
            <CardDescription>
              جميع المستخدمين المسجلين في النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                جاري تحميل المستخدمين...
              </div>
            ) : users.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                لا يوجد مستخدمين مسجلين
              </div>
            ) : (
              <div className="space-y-4">
                {users.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-medium">{user.name}</h3>
                        {getRoleBadge(user.role)}
                        <Badge variant={user.isActive ? "default" : "secondary"}>
                          {user.isActive ? 'نشط' : 'معطل'}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600 space-y-1">
                        <p><strong>اسم المستخدم:</strong> {user.username}</p>
                        <p><strong>البريد:</strong> {user.email}</p>
                        {user.phone && <p><strong>الهاتف:</strong> {user.phone}</p>}
                        <p><strong>تاريخ الإنشاء:</strong> {user.createdAt.toLocaleDateString('ar-SA')}</p>
                        {user.lastLogin && (
                          <p><strong>آخر دخول:</strong> {user.lastLogin.toLocaleDateString('ar-SA')}</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleToggleUserStatus(user.id, user.isActive)}
                      >
                        {user.isActive ? 'تعطيل' : 'تفعيل'}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
