"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   STATUS_COLORS: () => (/* binding */ STATUS_COLORS),\n/* harmony export */   STATUS_LABELS: () => (/* binding */ STATUS_LABELS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Application configuration\nconst APP_CONFIG = {\n    name: \"مرسال\",\n    description: \"نظام إدارة عمليات التوصيل السريع\",\n    version: \"1.1.0\",\n    // Colors\n    colors: {\n        primary: \"#41a7ff\",\n        background: \"#f0f3f5\",\n        accent: \"#b19cd9\"\n    },\n    // Business rules\n    business: {\n        commissionPerOrder: 1000,\n        currency: \"د.ع\",\n        defaultOrderStatuses: [\n            \"pending\",\n            \"assigned\",\n            \"picked_up\",\n            \"in_transit\",\n            \"delivered\",\n            \"returned\",\n            \"cancelled\",\n            \"postponed\"\n        ]\n    },\n    // API endpoints (when backend is ready)\n    api: {\n        baseUrl: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\",\n        endpoints: {\n            orders: \"/api/orders\",\n            users: \"/api/users\",\n            couriers: \"/api/couriers\",\n            settlements: \"/api/settlements\"\n        }\n    },\n    // Firebase config keys (to be replaced with actual values)\n    firebase: {\n        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\n    },\n    // Features flags\n    features: {\n        enableNotifications: true,\n        enableReports: true,\n        enableBulkOperations: true,\n        enableImageUpload: true\n    },\n    // Demo mode settings - إعدادات الوضع التجريبي\n    demo: {\n        enabled: true,\n        autoLogin: true,\n        defaultUser: 'manager',\n        skipFirebase: true,\n        showDemoNotice: true // إظهار تنبيه الوضع التجريبي\n    },\n    // Company info\n    company: {\n        name: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        phone: '+964 ************',\n        address: 'بغداد، العراق'\n    },\n    // Receipt settings - إعدادات الوصل\n    receipt: {\n        dimensions: {\n            width: '110mm',\n            height: '130mm'\n        },\n        companyName: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        showBarcode: true,\n        showDate: true,\n        priceFormat: 'en-US',\n        currency: 'IQD',\n        fields: {\n            trackingNumber: true,\n            customerPhone: true,\n            status: true,\n            courierName: true,\n            amount: true,\n            barcode: true,\n            date: true\n        }\n    }\n};\n// Status labels in Arabic\nconst STATUS_LABELS = {\n    pending: \"في الانتظار\",\n    assigned: \"مسند\",\n    picked_up: \"تم الاستلام\",\n    in_transit: \"في الطريق\",\n    delivered: \"تم التسليم\",\n    returned: \"راجع\",\n    cancelled: \"ملغي\",\n    postponed: \"مؤجل\"\n};\n// Status colors for UI\nconst STATUS_COLORS = {\n    pending: \"text-yellow-600 bg-yellow-100\",\n    assigned: \"text-blue-600 bg-blue-100\",\n    picked_up: \"text-purple-600 bg-purple-100\",\n    in_transit: \"text-orange-600 bg-orange-100\",\n    delivered: \"text-green-600 bg-green-100\",\n    returned: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    postponed: \"text-gray-600 bg-gray-100\"\n};\n// User roles\nconst USER_ROLES = {\n    admin: \"مدير\",\n    manager: \"مدير فرع\",\n    dispatcher: \"موزع\",\n    courier: \"مندوب\",\n    accountant: \"محاسب\",\n    viewer: \"مشاهد\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/statistics.ts":
/*!*******************************!*\
  !*** ./src/lib/statistics.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   statisticsService: () => (/* binding */ statisticsService)\n/* harmony export */ });\n/* harmony import */ var _mock_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mock-data */ \"(app-pages-browser)/./src/lib/mock-data.ts\");\n// Statistics service - Updated to use Supabase only (Firebase removed)\n\nconst statisticsService = {\n    async getOverallStats () {\n        try {\n            const ordersRef = collection(db, 'orders');\n            // Get all orders\n            const allOrdersSnapshot = await getDocs(ordersRef);\n            const totalOrders = allOrdersSnapshot.size;\n            // Get delivered orders\n            const deliveredQuery = query(ordersRef, where('status', '==', 'delivered'));\n            const deliveredSnapshot = await getDocs(deliveredQuery);\n            const deliveredOrders = deliveredSnapshot.size;\n            // Get returned orders\n            const returnedQuery = query(ordersRef, where('status', '==', 'returned'));\n            const returnedSnapshot = await getDocs(returnedQuery);\n            const returnedOrders = returnedSnapshot.size;\n            // Get pending orders\n            const pendingQuery = query(ordersRef, where('status', '==', 'pending'));\n            const pendingSnapshot = await getDocs(pendingQuery);\n            const pendingOrders = pendingSnapshot.size;\n            // Calculate total amount from delivered orders\n            let totalAmount = 0;\n            deliveredSnapshot.docs.forEach((doc)=>{\n                const data = doc.data();\n                totalAmount += data.amount || 0;\n            });\n            // Calculate commission (1000 IQD per delivered order)\n            const totalCommission = deliveredOrders * 1000;\n            return {\n                totalOrders,\n                deliveredOrders,\n                returnedOrders,\n                pendingOrders,\n                totalAmount,\n                totalCommission\n            };\n        } catch (error) {\n            console.warn('Firebase not available, using mock data:', error);\n            // Fallback to mock data\n            const totalOrders = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.length;\n            const deliveredOrders = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.filter((o)=>o.status === 'delivered').length;\n            const returnedOrders = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.filter((o)=>o.status === 'returned').length;\n            const pendingOrders = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.filter((o)=>o.status === 'pending').length;\n            const totalAmount = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.filter((o)=>o.status === 'delivered').reduce((sum, o)=>sum + o.amount, 0);\n            const totalCommission = deliveredOrders * 1000;\n            return {\n                totalOrders,\n                deliveredOrders,\n                returnedOrders,\n                pendingOrders,\n                totalAmount,\n                totalCommission\n            };\n        }\n    },\n    async getTodayStats () {\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const todayTimestamp = Timestamp.fromDate(today);\n        const ordersRef = collection(db, 'orders');\n        const todayQuery = query(ordersRef, where('createdAt', '>=', todayTimestamp));\n        const snapshot = await getDocs(todayQuery);\n        return snapshot.size;\n    },\n    async getCourierStats (courierId) {\n        const ordersRef = collection(db, 'orders');\n        const courierQuery = query(ordersRef, where('assignedTo', '==', courierId));\n        const snapshot = await getDocs(courierQuery);\n        let delivered = 0;\n        let returned = 0;\n        let pending = 0;\n        let totalAmount = 0;\n        snapshot.docs.forEach((doc)=>{\n            const data = doc.data();\n            switch(data.status){\n                case 'delivered':\n                    delivered++;\n                    totalAmount += data.amount || 0;\n                    break;\n                case 'returned':\n                    returned++;\n                    break;\n                case 'pending':\n                case 'assigned':\n                case 'picked_up':\n                case 'in_transit':\n                    pending++;\n                    break;\n            }\n        });\n        return {\n            totalOrders: snapshot.size,\n            deliveredOrders: delivered,\n            returnedOrders: returned,\n            pendingOrders: pending,\n            totalAmount,\n            totalCommission: delivered * 1000\n        };\n    },\n    async getMonthlyStats (year, month) {\n        const startDate = new Date(year, month - 1, 1);\n        const endDate = new Date(year, month, 0, 23, 59, 59);\n        const ordersRef = collection(db, 'orders');\n        const monthQuery = query(ordersRef, where('createdAt', '>=', Timestamp.fromDate(startDate)), where('createdAt', '<=', Timestamp.fromDate(endDate)));\n        const snapshot = await getDocs(monthQuery);\n        let delivered = 0;\n        let returned = 0;\n        let totalAmount = 0;\n        snapshot.docs.forEach((doc)=>{\n            const data = doc.data();\n            if (data.status === 'delivered') {\n                delivered++;\n                totalAmount += data.amount || 0;\n            } else if (data.status === 'returned') {\n                returned++;\n            }\n        });\n        return {\n            totalOrders: snapshot.size,\n            deliveredOrders: delivered,\n            returnedOrders: returned,\n            pendingOrders: snapshot.size - delivered - returned,\n            totalAmount,\n            totalCommission: delivered * 1000\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/statistics.ts\n"));

/***/ })

});