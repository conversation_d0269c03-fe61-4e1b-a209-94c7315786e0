"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/statistics.ts":
/*!*******************************!*\
  !*** ./src/lib/statistics.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   statisticsService: () => (/* binding */ statisticsService)\n/* harmony export */ });\n/* harmony import */ var _mock_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mock-data */ \"(app-pages-browser)/./src/lib/mock-data.ts\");\n// Statistics service - Updated to use Supabase only (Firebase completely removed)\n\nconst statisticsService = {\n    async getOverallStats () {\n        try {\n            const ordersRef = collection(db, 'orders');\n            // Get all orders\n            const allOrdersSnapshot = await getDocs(ordersRef);\n            const totalOrders = allOrdersSnapshot.size;\n            // Get delivered orders\n            const deliveredQuery = query(ordersRef, where('status', '==', 'delivered'));\n            const deliveredSnapshot = await getDocs(deliveredQuery);\n            const deliveredOrders = deliveredSnapshot.size;\n            // Get returned orders\n            const returnedQuery = query(ordersRef, where('status', '==', 'returned'));\n            const returnedSnapshot = await getDocs(returnedQuery);\n            const returnedOrders = returnedSnapshot.size;\n            // Get pending orders\n            const pendingQuery = query(ordersRef, where('status', '==', 'pending'));\n            const pendingSnapshot = await getDocs(pendingQuery);\n            const pendingOrders = pendingSnapshot.size;\n            // Calculate total amount from delivered orders\n            let totalAmount = 0;\n            deliveredSnapshot.docs.forEach((doc)=>{\n                const data = doc.data();\n                totalAmount += data.amount || 0;\n            });\n            // Calculate commission (1000 IQD per delivered order)\n            const totalCommission = deliveredOrders * 1000;\n            return {\n                totalOrders,\n                deliveredOrders,\n                returnedOrders,\n                pendingOrders,\n                totalAmount,\n                totalCommission\n            };\n        } catch (error) {\n            console.warn('Firebase not available, using mock data:', error);\n            // Fallback to mock data\n            const totalOrders = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.length;\n            const deliveredOrders = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.filter((o)=>o.status === 'delivered').length;\n            const returnedOrders = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.filter((o)=>o.status === 'returned').length;\n            const pendingOrders = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.filter((o)=>o.status === 'pending').length;\n            const totalAmount = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockOrders.filter((o)=>o.status === 'delivered').reduce((sum, o)=>sum + o.amount, 0);\n            const totalCommission = deliveredOrders * 1000;\n            return {\n                totalOrders,\n                deliveredOrders,\n                returnedOrders,\n                pendingOrders,\n                totalAmount,\n                totalCommission\n            };\n        }\n    },\n    async getTodayStats () {\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const todayTimestamp = Timestamp.fromDate(today);\n        const ordersRef = collection(db, 'orders');\n        const todayQuery = query(ordersRef, where('createdAt', '>=', todayTimestamp));\n        const snapshot = await getDocs(todayQuery);\n        return snapshot.size;\n    },\n    async getCourierStats (courierId) {\n        const ordersRef = collection(db, 'orders');\n        const courierQuery = query(ordersRef, where('assignedTo', '==', courierId));\n        const snapshot = await getDocs(courierQuery);\n        let delivered = 0;\n        let returned = 0;\n        let pending = 0;\n        let totalAmount = 0;\n        snapshot.docs.forEach((doc)=>{\n            const data = doc.data();\n            switch(data.status){\n                case 'delivered':\n                    delivered++;\n                    totalAmount += data.amount || 0;\n                    break;\n                case 'returned':\n                    returned++;\n                    break;\n                case 'pending':\n                case 'assigned':\n                case 'picked_up':\n                case 'in_transit':\n                    pending++;\n                    break;\n            }\n        });\n        return {\n            totalOrders: snapshot.size,\n            deliveredOrders: delivered,\n            returnedOrders: returned,\n            pendingOrders: pending,\n            totalAmount,\n            totalCommission: delivered * 1000\n        };\n    },\n    async getMonthlyStats (year, month) {\n        const startDate = new Date(year, month - 1, 1);\n        const endDate = new Date(year, month, 0, 23, 59, 59);\n        const ordersRef = collection(db, 'orders');\n        const monthQuery = query(ordersRef, where('createdAt', '>=', Timestamp.fromDate(startDate)), where('createdAt', '<=', Timestamp.fromDate(endDate)));\n        const snapshot = await getDocs(monthQuery);\n        let delivered = 0;\n        let returned = 0;\n        let totalAmount = 0;\n        snapshot.docs.forEach((doc)=>{\n            const data = doc.data();\n            if (data.status === 'delivered') {\n                delivered++;\n                totalAmount += data.amount || 0;\n            } else if (data.status === 'returned') {\n                returned++;\n            }\n        });\n        return {\n            totalOrders: snapshot.size,\n            deliveredOrders: delivered,\n            returnedOrders: returned,\n            pendingOrders: snapshot.size - delivered - returned,\n            totalAmount,\n            totalCommission: delivered * 1000\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/statistics.ts\n"));

/***/ })

});