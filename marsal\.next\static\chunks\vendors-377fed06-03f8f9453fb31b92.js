"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4409],{37548:(r,e,n)=>{n.d(e,{f:()=>o});var t=n(39249);function u(r){return r}function o(r){void 0===r&&(r={});var e,n,o,a,c=(e=null,void 0===n&&(n=u),o=[],a=!1,{read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return o.length?o[o.length-1]:null},useMedium:function(r){var e=n(r,a);return o.push(e),function(){o=o.filter(function(r){return r!==e})}},assignSyncMedium:function(r){for(a=!0;o.length;){var e=o;o=[],e.forEach(r)}o={push:function(e){return r(e)},filter:function(){return o}}},assignMedium:function(r){a=!0;var e=[];if(o.length){var n=o;o=[],n.forEach(r),e=o}var t=function(){var n=e;e=[],n.forEach(r)},u=function(){return Promise.resolve().then(t)};u(),o={push:function(r){e.push(r),u()},filter:function(r){return e=e.filter(r),o}}}});return c.options=(0,t.Cl)({async:!0,ssr:!1},r),c}},39249:(r,e,n)=>{n.d(e,{Cl:()=>t,Tt:()=>u,fX:()=>o});var t=function(){return(t=Object.assign||function(r){for(var e,n=1,t=arguments.length;n<t;n++)for(var u in e=arguments[n])Object.prototype.hasOwnProperty.call(e,u)&&(r[u]=e[u]);return r}).apply(this,arguments)};function u(r,e){var n={};for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&0>e.indexOf(t)&&(n[t]=r[t]);if(null!=r&&"function"==typeof Object.getOwnPropertySymbols)for(var u=0,t=Object.getOwnPropertySymbols(r);u<t.length;u++)0>e.indexOf(t[u])&&Object.prototype.propertyIsEnumerable.call(r,t[u])&&(n[t[u]]=r[t[u]]);return n}Object.create;function o(r,e,n){if(n||2==arguments.length)for(var t,u=0,o=e.length;u<o;u++)!t&&u in e||(t||(t=Array.prototype.slice.call(e,0,u)),t[u]=e[u]);return r.concat(t||Array.prototype.slice.call(e))}Object.create,"function"==typeof SuppressedError&&SuppressedError},50514:(r,e,n)=>{n.d(e,{m:()=>a});var t=n(39249),u=n(12115),o=function(r){var e=r.sideCar,n=(0,t.Tt)(r,["sideCar"]);if(!e)throw Error("Sidecar: please provide `sideCar` property to import the right car");var o=e.read();if(!o)throw Error("Sidecar medium not found");return u.createElement(o,(0,t.Cl)({},n))};function a(r,e){return r.useMedium(e),o}o.isSideCarExport=!0},70464:(r,e,n)=>{n.d(e,{S:()=>c});var t=n(12115);function u(r,e){return"function"==typeof r?r(e):r&&(r.current=e),r}var o="undefined"!=typeof window?t.useLayoutEffect:t.useEffect,a=new WeakMap;function c(r,e){var n,c,i,f=(n=e||null,c=function(e){return r.forEach(function(r){return u(r,e)})},(i=(0,t.useState)(function(){return{value:n,callback:c,facade:{get current(){return i.value},set current(value){var r=i.value;r!==value&&(i.value=value,i.callback(value,r))}}}})[0]).callback=c,i.facade);return o(function(){var e=a.get(f);if(e){var n=new Set(e),t=new Set(r),o=f.current;n.forEach(function(r){t.has(r)||u(r,null)}),t.forEach(function(r){n.has(r)||u(r,o)})}a.set(f,r)},[r]),f}}}]);