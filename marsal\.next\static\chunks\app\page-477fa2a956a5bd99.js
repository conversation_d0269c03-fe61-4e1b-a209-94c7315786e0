(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{16641:(e,t,s)=>{"use strict";s.d(t,{Ys:()=>r,rB:()=>a});let r=[{id:"1",trackingNumber:"MRS001",senderName:"أحمد محمد",senderPhone:"07901234567",senderAddress:"بغداد - الكرادة - شارع الرشيد",recipientName:"فاطمة علي",recipientPhone:"07801234567",recipientAddress:"بغداد - الجادرية - قرب الجامعة",amount:5e4,status:"pending",notes:"يرجى التسليم في المساء",createdAt:new Date("2024-01-15T10:30:00"),updatedAt:new Date("2024-01-15T10:30:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-15T10:30:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"}]},{id:"2",trackingNumber:"MRS002",senderName:"سارة أحمد",senderPhone:"07701234567",senderAddress:"بغداد - المنصور - شارع الأميرات",recipientName:"محمد حسن",recipientPhone:"07601234567",recipientAddress:"بغداد - الكاظمية - شارع الإمام",amount:75e3,status:"assigned",assignedTo:"courier1",createdAt:new Date("2024-01-15T09:15:00"),updatedAt:new Date("2024-01-15T11:00:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-15T09:15:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"},{status:"assigned",timestamp:new Date("2024-01-15T11:00:00"),updatedBy:"dispatcher1",notes:"تم إسناد الطلب للمندوب"}]},{id:"3",trackingNumber:"MRS003",senderName:"خالد عبدالله",senderPhone:"07501234567",senderAddress:"بغداد - الدورة - شارع الصناعة",recipientName:"زينب محمد",recipientPhone:"07401234567",recipientAddress:"بغداد - الشعلة - قرب المجمع",amount:12e4,status:"delivered",assignedTo:"courier2",deliveredAt:new Date("2024-01-14T16:30:00"),createdAt:new Date("2024-01-14T08:00:00"),updatedAt:new Date("2024-01-14T16:30:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-14T08:00:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"},{status:"assigned",timestamp:new Date("2024-01-14T09:00:00"),updatedBy:"dispatcher1",notes:"تم إسناد الطلب للمندوب"},{status:"delivered",timestamp:new Date("2024-01-14T16:30:00"),updatedBy:"courier2",notes:"تم تسليم الطلب بنجاح"}]}],a=[{id:"admin1",email:"<EMAIL>",name:"أحمد المدير",username:"admin",phone:"07901234567",role:"admin",isActive:!0,createdAt:new Date("2024-01-01T00:00:00"),updatedAt:new Date("2024-01-01T00:00:00")},{id:"courier1",email:"<EMAIL>",name:"علي حسين",username:"courier1",phone:"07801234567",role:"courier",isActive:!0,createdAt:new Date("2024-01-02T00:00:00"),updatedAt:new Date("2024-01-02T00:00:00")},{id:"courier2",email:"<EMAIL>",name:"حسام محمد",username:"courier2",phone:"07701234567",role:"courier",isActive:!0,createdAt:new Date("2024-01-03T00:00:00"),updatedAt:new Date("2024-01-03T00:00:00")},{id:"dispatcher1",email:"<EMAIL>",name:"سارة الموزعة",username:"dispatcher",phone:"07601234567",role:"dispatcher",isActive:!0,createdAt:new Date("2024-01-04T00:00:00"),updatedAt:new Date("2024-01-04T00:00:00")}]},30160:(e,t,s)=>{"use strict";s.d(t,{E8:()=>n,Rm:()=>d,tv:()=>c});var r=s(59345),a=s(35317),i=s(56104);let n=async()=>{try{return console.log("\uD83D\uDD25 بدء إعداد قاعدة البيانات Firebase..."),console.log("\uD83D\uDC65 إنشاء المستخدمين الافتراضيين..."),await r.firebaseAuthService.initializeDefaultUsers(),console.log("\uD83D\uDCE6 إنشاء طلبات تجريبية..."),await o(),console.log("\uD83D\uDD14 إنشاء إشعارات تجريبية..."),await l(),console.log("✅ تم إعداد قاعدة البيانات Firebase بنجاح!"),{success:!0,message:"تم إعداد قاعدة البيانات بنجاح مع جميع البيانات المطلوبة"}}catch(e){return console.error("❌ خطأ في إعداد قاعدة البيانات:",e),{success:!1,message:"فشل في إعداد قاعدة البيانات: ".concat(e.message)}}},o=async()=>{if(!i.db)throw Error("Firebase غير متصل");let e=[{trackingNumber:"ORDER_001",customerName:"أحمد محمد علي",customerPhone:"07701234567",address:"بغداد - الكرادة - شارع الرشيد",amount:25e3,status:"pending",courierName:"",assignedTo:"",notes:"طلب تجريبي - يرجى التوصيل صباحاً",createdBy:"system"},{trackingNumber:"ORDER_002",customerName:"فاطمة علي حسن",customerPhone:"07701234568",address:"البصرة - المعقل - حي الجمهورية",amount:35e3,status:"assigned",courierName:"مندوب التوصيل",assignedTo:"courier_1",notes:"طلب عاجل - يرجى التوصيل اليوم",createdBy:"manager"},{trackingNumber:"ORDER_003",customerName:"محمد حسين كريم",customerPhone:"07701234569",address:"أربيل - عنكاوا - شارع الكنائس",amount:45e3,status:"delivered",courierName:"مندوب التوصيل",assignedTo:"courier_1",notes:"تم التسليم بنجاح",createdBy:"supervisor",deliveredAt:new Date},{trackingNumber:"ORDER_004",customerName:"زينب أحمد محمود",customerPhone:"07701234570",address:"النجف - المدينة القديمة - قرب الحرم",amount:3e4,status:"returned",courierName:"مندوب التوصيل",assignedTo:"courier_1",notes:"تم الإرجاع - العنوان غير صحيح",createdBy:"supervisor"},{trackingNumber:"ORDER_005",customerName:"علي حسن جعفر",customerPhone:"07701234571",address:"كربلاء - حي الحسين - شارع الإمام علي",amount:55e3,status:"in_transit",courierName:"مندوب التوصيل",assignedTo:"courier_1",notes:"في الطريق للتسليم",createdBy:"manager"}],t=(0,a.collection)(i.db,"orders");for(let s of e)try{await (0,a.gS)(t,{...s,createdAt:(0,a.O5)(),updatedAt:(0,a.O5)()}),console.log("✅ تم إنشاء الطلب: ".concat(s.trackingNumber))}catch(e){console.error("❌ خطأ في إنشاء الطلب ".concat(s.trackingNumber,":"),e)}},l=async()=>{if(!i.db)throw Error("Firebase غير متصل");let e=(0,a.collection)(i.db,"notifications");for(let t of[{title:"طلب جديد",message:"تم إسناد طلب ORDER_002 إليك",type:"order_assigned",userId:"courier_1",orderId:"ORDER_002",isRead:!1},{title:"تحديث حالة الطلب",message:"تم تسليم الطلب ORDER_003 بنجاح",type:"status_changed",userId:"manager",orderId:"ORDER_003",isRead:!1},{title:"طلب مرتجع",message:"تم إرجاع الطلب ORDER_004 - العنوان غير صحيح",type:"order_returned",userId:"supervisor",orderId:"ORDER_004",isRead:!1},{title:"مستخدم جديد",message:"تم إنشاء حساب مندوب جديد",type:"user_created",userId:"manager",isRead:!0},{title:"تقرير يومي",message:"تم تسليم 5 طلبات اليوم بنجاح",type:"daily_report",userId:"manager",isRead:!1}])try{await (0,a.gS)(e,{...t,createdAt:(0,a.O5)()}),console.log("✅ تم إنشاء الإشعار: ".concat(t.title))}catch(e){console.error("❌ خطأ في إنشاء الإشعار:",e)}},c=async()=>{try{let{testFirebaseConnection:e}=await Promise.resolve().then(s.bind(s,56104)),t=await e();if(!t.success)return{success:!1,message:"فشل الاتصال بـ Firebase: ".concat(t.message)};let a=await r.firebaseAuthService.checkConnection();if(!a.connected)return{success:!1,message:"فشل في نظام المصادقة: ".concat(a.message)};return{success:!0,message:"Firebase مُعد بشكل صحيح ومتصل"}}catch(e){return{success:!1,message:"خطأ في فحص Firebase: ".concat(e.message)}}},d=async()=>{try{if(!i.db)throw Error("Firebase غير متصل");let{getDocs:e,collection:t}=await Promise.resolve().then(s.bind(s,35317)),r=(await e(t(i.db,"users"))).size,a=(await e(t(i.db,"orders"))).size,n=(await e(t(i.db,"notifications"))).size;return{users:r,orders:a,notifications:n,total:r+a+n}}catch(e){return console.error("خطأ في جلب إحصائيات قاعدة البيانات:",e),{users:0,orders:0,notifications:0,total:0}}}},39599:(e,t,s)=>{"use strict";s.d(t,{BC:()=>n,_m:()=>a});let r={manager:["orders","dispatch","returns","accounting","statistics","archive","users","import-export","notifications","settings"],supervisor:["orders","statistics","archive","users","notifications","settings"],courier:["orders","archive","notifications","statistics"]};function a(e,t){var s;return(null==(s=r[e])?void 0:s.includes(t))||!1}let i=[{id:"orders",href:"/orders",label:"إدارة الطلبات",description:"عرض ومتابعة وتعديل الطلبات",icon:"Package",color:"from-blue-500 to-blue-600",roles:["manager","supervisor","courier"]},{id:"dispatch",href:"/dispatch",label:"إسناد الطلبات",description:"توزيع الطلبات على المندوبين",icon:"TruckIcon",color:"from-green-500 to-emerald-600",roles:["manager"]},{id:"returns",href:"/returns",label:"إدارة الرواجع",description:"استلام الطلبات الراجعة",icon:"RotateCcw",color:"from-orange-500 to-amber-600",roles:["manager"]},{id:"accounting",href:"/accounting",label:"المحاسبة",description:"التسويات المالية مع المندوبين",icon:"Calculator",color:"from-purple-500 to-violet-600",roles:["manager"]},{id:"statistics",href:"/statistics",label:"الإحصائيات",description:"التقارير والإحصائيات التفصيلية",icon:"BarChart3",color:"from-cyan-500 to-blue-600",roles:["manager","supervisor"]},{id:"archive",href:"/archive",label:"الأرشيف",description:"السجلات المنتهية",icon:"Archive",color:"from-gray-500 to-slate-600",roles:["manager","supervisor","courier"]},{id:"users",href:"/users",label:"إدارة الموظفين",description:"إدارة حسابات المستخدمين",icon:"Users",color:"from-indigo-500 to-purple-600",roles:["manager","supervisor"]},{id:"import-export",href:"/import-export",label:"استيراد وتصدير",description:"عمليات مجمعة على الطلبات",icon:"Upload",color:"from-teal-500 to-cyan-600",roles:["manager"]},{id:"notifications",href:"/notifications",label:"الإشعارات",description:"التنبيهات والتحديثات",icon:"Bell",color:"from-yellow-500 to-orange-600",roles:["manager","supervisor","courier"]},{id:"settings",href:"/settings",label:"الإعدادات",description:"تخصيص التطبيق",icon:"Settings",color:"from-pink-500 to-rose-600",roles:["manager","supervisor","courier"]}];function n(e){return i.filter(t=>t.roles.includes(e))}},50329:(e,t,s)=>{Promise.resolve().then(s.bind(s,77350))},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(95155);s(12115);var a=s(59434);function i(e){let{className:t,type:s,...i}=e;return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},77350:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>q});var r=s(95155),a=s(12115),i=s(66695),n=s(37192),o=s(37108),l=s(40646),c=s(14186),d=s(40133),u=s(6740),m=s(17580),x=s(29799);function g(e){let{title:t,value:s,icon:a,color:n,trend:o}=e;return(0,r.jsxs)(i.Zp,{className:"hover:shadow-xl transition-all duration-300 hover:scale-105 glass border-0 overflow-hidden group relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br opacity-5 group-hover:opacity-10 transition-opacity"}),(0,r.jsxs)(i.Wu,{className:"p-6 relative",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:t}),(0,r.jsx)("p",{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent",children:s}),o&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm font-medium ".concat(o.isPositive?"text-green-600":"text-red-600"),children:[(0,r.jsx)("span",{className:"text-lg",children:o.isPositive?"\uD83D\uDCC8":"\uD83D\uDCC9"}),Math.abs(o.value),"% من الأسبوع الماضي"]})]}),(0,r.jsx)("div",{className:"p-4 rounded-2xl bg-gradient-to-r ".concat(n," shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110"),children:(0,r.jsx)(a,{className:"h-8 w-8 text-white"})})]}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"})]})]})}function h(e){let{stats:t}=e,s=t||{totalOrders:1234,deliveredOrders:987,returnedOrders:45,pendingOrders:156,totalAmount:125e6,totalCommission:987e3,activeCouriers:12,todayOrders:89,completionRate:85.2},a=[{title:"إجمالي الطلبات",value:s.totalOrders.toLocaleString(),icon:o.A,color:"from-blue-500 to-blue-600",trend:{value:12,isPositive:!0}},{title:"الطلبات المسلمة",value:s.deliveredOrders.toLocaleString(),icon:l.A,color:"from-green-500 to-emerald-600",trend:{value:8,isPositive:!0}},{title:"الطلبات المعلقة",value:s.pendingOrders.toLocaleString(),icon:c.A,color:"from-orange-500 to-amber-600",trend:{value:3,isPositive:!1}},{title:"الرواجع",value:s.returnedOrders.toLocaleString(),icon:d.A,color:"from-red-500 to-rose-600",trend:{value:2,isPositive:!1}},{title:"المبالغ المحصلة",value:"".concat((s.totalAmount/1e6).toFixed(1),"M د.ع"),icon:u.A,color:"from-purple-500 to-violet-600",trend:{value:15,isPositive:!0}},{title:"المندوبين النشطين",value:(s.activeCouriers||0).toLocaleString(),icon:m.A,color:"text-indigo-600",trend:{value:1,isPositive:!0}},{title:"طلبات اليوم",value:(s.todayOrders||0).toLocaleString(),icon:x.A,color:"text-cyan-600",trend:{value:25,isPositive:!0}},{title:"معدل الإنجاز",value:"".concat(s.completionRate||0,"%"),icon:l.A,color:"text-emerald-600",trend:{value:2.1,isPositive:!0}}];return(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:a.map((e,t)=>(0,r.jsx)(g,{title:e.title,value:e.value,icon:e.icon,color:e.color,trend:e.trend},t))})}var p=s(30285),v=s(62523),b=s(47924),f=s(84355),N=s(71007),j=s(19420),w=s(6874),y=s.n(w),A=s(35695);let D=[{id:"1",trackingNumber:"MRS001",recipientName:"فاطمة علي",recipientPhone:"07801234567",status:"pending",amount:5e4},{id:"2",trackingNumber:"MRS002",recipientName:"محمد حسن",recipientPhone:"07901234567",status:"delivered",amount:75e3}],k={pending:"في الانتظار",assigned:"مسند",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي"},O={pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100"};function R(){let e=(0,A.useRouter)(),[t,s]=(0,a.useState)(""),[n,l]=(0,a.useState)([]),[c,d]=(0,a.useState)(!1),[u,m]=(0,a.useState)(!1),x=async e=>{e.preventDefault(),t.trim()&&(d(!0),m(!0),setTimeout(()=>{l(D.filter(e=>e.trackingNumber.toLowerCase().includes(t.toLowerCase())||e.recipientPhone.includes(t)||e.recipientName.includes(t))),d(!1)},500))};return(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(b.A,{className:"h-5 w-5"}),"البحث السريع"]}),(0,r.jsx)(i.BT,{children:"ابحث عن طلب باستخدام رقم الوصل أو هاتف المستلم أو اسم المستلم"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsxs)("form",{onSubmit:x,className:"flex gap-2",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)(v.p,{placeholder:"رقم الوصل أو هاتف المستلم أو اسم المستلم...",value:t,onChange:e=>s(e.target.value),onKeyPress:s=>{if("Enter"===s.key){if(s.preventDefault(),1===n.length)e.push("/orders/".concat(n[0].id));else if(t.trim()){let r=D.find(e=>e.trackingNumber.toLowerCase()===t.trim().toLowerCase());r?e.push("/orders/".concat(r.id)):x(s)}}},className:"pr-10"}),(0,r.jsx)(p.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>alert("سيتم فتح الكاميرا لقراءة الباركود قريباً"),children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})]}),(0,r.jsx)(p.$,{type:"submit",disabled:c,children:c?"جاري البحث...":"بحث"}),u&&(0,r.jsx)(p.$,{variant:"outline",onClick:()=>{s(""),l([]),m(!1)},children:"مسح"})]}),u&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"font-medium",children:"نتائج البحث"}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[n.length," نتيجة"]})]}),0===n.length?(0,r.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,r.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,r.jsxs)("p",{children:['لا توجد نتائج للبحث "',t,'"']})]}):(0,r.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:n.map(e=>(0,r.jsx)(y(),{href:"/orders/".concat(e.id),children:(0,r.jsx)("div",{className:"p-3 border border-border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 text-primary"}),(0,r.jsx)("span",{className:"font-medium",children:e.trackingNumber}),(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(O[e.status]),children:k[e.status]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(N.A,{className:"h-3 w-3"}),e.recipientName]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(j.A,{className:"h-3 w-3"}),e.recipientPhone]})]})]}),(0,r.jsx)("div",{className:"text-left",children:(0,r.jsxs)("p",{className:"font-semibold",children:[e.amount.toLocaleString()," د.ع"]})})]})})},e.id))})]})]})]})}var _=s(26126),S=s(51154),T=s(54861),P=s(23861),C=s(54213),E=s(30160);function B(){let[e,t]=(0,a.useState)("checking"),[s,n]=(0,a.useState)({users:0,orders:0,notifications:0,total:0}),[c,d]=(0,a.useState)(!0);(0,a.useEffect)(()=>{u()},[]);let u=async()=>{try{if(d(!0),(await (0,E.tv)()).success){t("connected");let e=await (0,E.Rm)();n(e)}else t("disconnected")}catch(e){t("disconnected")}finally{d(!1)}};return(0,r.jsx)(i.Zp,{className:"".concat((()=>{if(c)return"border-yellow-200 bg-yellow-50";switch(e){case"connected":return"border-green-200 bg-green-50";case"disconnected":return"border-red-200 bg-red-50";default:return"border-yellow-200 bg-yellow-50"}})()),children:(0,r.jsxs)(i.Wu,{className:"pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(()=>{if(c)return(0,r.jsx)(S.A,{className:"h-4 w-4 animate-spin text-yellow-600"});switch(e){case"connected":return(0,r.jsx)(l.A,{className:"h-4 w-4 text-green-600"});case"disconnected":return(0,r.jsx)(T.A,{className:"h-4 w-4 text-red-600"});default:return(0,r.jsx)(S.A,{className:"h-4 w-4 animate-spin text-yellow-600"})}})(),(0,r.jsx)("span",{className:"font-medium",children:(()=>{if(c)return"جاري الفحص...";switch(e){case"connected":return"متصل بـ Firebase";case"disconnected":return"غير متصل بـ Firebase";default:return"جاري الفحص..."}})()})]}),"connected"===e&&(0,r.jsx)(_.E,{variant:"default",className:"bg-green-100 text-green-800",children:"نشط"}),"disconnected"===e&&(0,r.jsx)(_.E,{variant:"destructive",children:"غير متصل"})]}),"connected"===e&&s.total>0&&(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(m.A,{className:"h-6 w-6 text-blue-600 mx-auto mb-1"}),(0,r.jsx)("p",{className:"text-lg font-bold",children:s.users}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:"مستخدمين"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(o.A,{className:"h-6 w-6 text-green-600 mx-auto mb-1"}),(0,r.jsx)("p",{className:"text-lg font-bold",children:s.orders}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:"طلبات"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(P.A,{className:"h-6 w-6 text-yellow-600 mx-auto mb-1"}),(0,r.jsx)("p",{className:"text-lg font-bold",children:s.notifications}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:"إشعارات"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(C.A,{className:"h-6 w-6 text-purple-600 mx-auto mb-1"}),(0,r.jsx)("p",{className:"text-lg font-bold",children:s.total}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:"إجمالي"})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:["disconnected"===e&&(0,r.jsxs)(p.$,{size:"sm",variant:"outline",onClick:()=>window.open("/firebase-setup","_blank"),className:"flex-1",children:[(0,r.jsx)(C.A,{className:"h-4 w-4 mr-1"}),"إعداد Firebase"]}),"connected"===e&&0===s.total&&(0,r.jsxs)(p.$,{size:"sm",onClick:()=>window.open("/firebase-setup","_blank"),className:"flex-1",children:[(0,r.jsx)(C.A,{className:"h-4 w-4 mr-1"}),"إعداد البيانات"]}),(0,r.jsx)(p.$,{size:"sm",variant:"outline",onClick:u,disabled:c,children:c?(0,r.jsx)(S.A,{className:"h-4 w-4 animate-spin"}):"تحديث"})]})]})})}var z=s(32044),M=s(35317),F=s(56104),L=s(16641);let I={async getOverallStats(){try{let e=(0,M.collection)(F.db,"orders"),t=(await (0,M.getDocs)(e)).size,s=(0,M.P)(e,(0,M._M)("status","==","delivered")),r=await (0,M.getDocs)(s),a=r.size,i=(0,M.P)(e,(0,M._M)("status","==","returned")),n=(await (0,M.getDocs)(i)).size,o=(0,M.P)(e,(0,M._M)("status","==","pending")),l=(await (0,M.getDocs)(o)).size,c=0;return r.docs.forEach(e=>{let t=e.data();c+=t.amount||0}),{totalOrders:t,deliveredOrders:a,returnedOrders:n,pendingOrders:l,totalAmount:c,totalCommission:1e3*a}}catch(a){console.warn("Firebase not available, using mock data:",a);let e=L.Ys.length,t=L.Ys.filter(e=>"delivered"===e.status).length,s=L.Ys.filter(e=>"returned"===e.status).length,r=L.Ys.filter(e=>"pending"===e.status).length;return{totalOrders:e,deliveredOrders:t,returnedOrders:s,pendingOrders:r,totalAmount:L.Ys.filter(e=>"delivered"===e.status).reduce((e,t)=>e+t.amount,0),totalCommission:1e3*t}}},async getTodayStats(){let e=new Date;e.setHours(0,0,0,0);let t=M.Dc.fromDate(e),s=(0,M.collection)(F.db,"orders"),r=(0,M.P)(s,(0,M._M)("createdAt",">=",t));return(await (0,M.getDocs)(r)).size},async getCourierStats(e){let t=(0,M.collection)(F.db,"orders"),s=(0,M.P)(t,(0,M._M)("assignedTo","==",e)),r=await (0,M.getDocs)(s),a=0,i=0,n=0,o=0;return r.docs.forEach(e=>{let t=e.data();switch(t.status){case"delivered":a++,o+=t.amount||0;break;case"returned":i++;break;case"pending":case"assigned":case"picked_up":case"in_transit":n++}}),{totalOrders:r.size,deliveredOrders:a,returnedOrders:i,pendingOrders:n,totalAmount:o,totalCommission:1e3*a}},async getMonthlyStats(e,t){let s=new Date(e,t-1,1),r=new Date(e,t,0,23,59,59),a=(0,M.collection)(F.db,"orders"),i=(0,M.P)(a,(0,M._M)("createdAt",">=",M.Dc.fromDate(s)),(0,M._M)("createdAt","<=",M.Dc.fromDate(r))),n=await (0,M.getDocs)(i),o=0,l=0,c=0;return n.docs.forEach(e=>{let t=e.data();"delivered"===t.status?(o++,c+=t.amount||0):"returned"===t.status&&l++}),{totalOrders:n.size,deliveredOrders:o,returnedOrders:l,pendingOrders:n.size-o-l,totalAmount:c,totalCommission:1e3*o}}};var Z=s(39599),W=s(23843),Y=s(72713),$=s(39022),U=s(29869),H=s(381),Q=s(81284);let K={Package:o.A,TruckIcon:x.A,RotateCcw:d.A,Calculator:u.A,BarChart3:Y.A,Archive:$.A,Users:m.A,Upload:U.A,Bell:P.A,Settings:H.A,Info:Q.A};function q(){let{user:e}=(0,z.A)(),[t,s]=(0,a.useState)(null),[l,c]=(0,a.useState)(!0);(0,a.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await I.getOverallStats(),t=await I.getTodayStats();s({...e,todayOrders:t,activeCouriers:12,completionRate:e.totalOrders>0?Math.round(e.deliveredOrders/e.totalOrders*100):0})}catch(e){console.error("Error loading stats:",e),s({totalOrders:1234,deliveredOrders:987,returnedOrders:45,pendingOrders:156,totalAmount:125e6,totalCommission:987e3,todayOrders:89,activeCouriers:12,completionRate:85})}finally{c(!1)}},u=e?(0,Z.BC)(e.role):[];return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 p-4 md:p-6 lg:p-8 animated-bg",dir:"rtl",children:[(0,r.jsx)(n.A,{}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,r.jsxs)("div",{className:"text-center space-y-6 relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl blur-3xl"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl shadow-2xl mb-6 shadow-glow animate-pulse",children:(0,r.jsx)(o.A,{className:"h-12 w-12 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-4 animate-pulse",children:"مرسال"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-xl max-w-3xl mx-auto leading-relaxed glass p-6 rounded-2xl",children:"\uD83D\uDE80 نظام إدارة التوصيل السريع والموثوق - حلول متطورة لإدارة الطلبات والتوصيل بأحدث التقنيات"})]})]})]}),(0,r.jsx)(R,{}),(0,r.jsx)("div",{className:"max-w-md mx-auto",children:(0,r.jsx)(B,{})}),W.vQ.demo.enabled&&W.vQ.demo.showDemoNotice&&(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsx)(i.Zp,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 shadow-lg",children:(0,r.jsx)(i.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center",children:(0,r.jsx)(Q.A,{className:"h-6 w-6 text-white"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"\uD83C\uDFAF الوضع التجريبي مُفعل"}),(0,r.jsxs)("p",{className:"text-blue-700 text-sm leading-relaxed",children:["أنت تستخدم النسخة التجريبية من نظام مرسال. جميع البيانات تجريبية ولن يتم حفظها بشكل دائم.",(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"بيانات الدخول:"})," manager / 123456 أو supervisor / 123456 أو courier / 123456"]})]})]})})})}),t&&(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsx)(h,{stats:t})}),e&&u.length>0?(0,r.jsxs)("div",{className:"mt-12",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-800 dark:text-white mb-2",children:"أقسام النظام"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"اختر القسم الذي تريد الوصول إليه"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:u.map(e=>{let t=K[e.icon];return(0,r.jsx)(y(),{href:e.href,children:(0,r.jsxs)(i.Zp,{className:"group card-hover cursor-pointer border-2 hover:border-blue-300 glass gpu-accelerated",children:[(0,r.jsxs)(i.aR,{className:"text-center pb-4",children:[(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ".concat(e.color," shadow-lg mb-4 group-hover:scale-110 transition-transform duration-300 will-change-transform"),children:t&&(0,r.jsx)(t,{className:"h-8 w-8 text-white"})}),(0,r.jsx)(i.ZB,{className:"text-lg font-bold text-gray-800 dark:text-white group-hover:text-blue-600 transition-colors",children:e.label})]}),(0,r.jsx)(i.Wu,{className:"text-center pt-0",children:(0,r.jsx)(i.BT,{className:"text-sm text-gray-600 dark:text-gray-300 leading-relaxed",children:e.description})})]})},e.id)})})]}):null,(0,r.jsx)("div",{className:"text-center mt-12",children:(0,r.jsxs)(i.Zp,{className:"max-w-2xl mx-auto glass border-2 border-blue-200",children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsxs)(i.ZB,{className:"text-2xl text-blue-600",children:["مرحباً، ",(null==e?void 0:e.name)||"مستخدم"]}),(0,r.jsx)(i.BT,{className:"text-lg",children:(null==e?void 0:e.role)==="manager"?"مدير النظام":(null==e?void 0:e.role)==="supervisor"?"متابع":"مندوب"})]}),(0,r.jsxs)(i.Wu,{children:[(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300 leading-relaxed",children:"مرحباً بك في نظام مرسال لإدارة التوصيل. يمكنك استخدام القائمة الجانبية للتنقل بين أقسام النظام المختلفة."}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-800 dark:text-blue-200 mb-2",children:"معلومات سريعة:"}),(0,r.jsxs)("div",{className:"text-sm text-blue-700 dark:text-blue-300 space-y-1",children:[(0,r.jsxs)("div",{children:["\uD83D\uDCC5 التاريخ: ",new Date().toLocaleDateString("ar-SA")]}),(0,r.jsxs)("div",{children:["⏰ الوقت: ",new Date().toLocaleTimeString("ar-SA")]}),(0,r.jsxs)("div",{children:["\uD83D\uDC64 المستخدم: ",(null==e?void 0:e.name)||"غير محدد"]}),(0,r.jsxs)("div",{children:["\uD83D\uDD11 الدور: ",(null==e?void 0:e.role)==="manager"?"مدير":(null==e?void 0:e.role)==="supervisor"?"متابع":"مندوب"]})]})]})]})]})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8541,1336,9948,4709,875,416,8779,622,2432,7979,1899,9744,4495,5138,6321,2652,5030,3719,9473,558,9401,6325,6077,4409,1124,9385,4927,37,2041,4979,8321,2900,3610,9988,2158,8058,2044,7192,7358],()=>t(50329)),_N_E=e.O()}]);