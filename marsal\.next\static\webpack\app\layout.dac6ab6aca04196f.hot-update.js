"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d50d2c89d75e\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJFOlxcTWFyc2FsXFxtYXJzYWxcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImQ1MGQyYzg5ZDc1ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   STATUS_COLORS: () => (/* binding */ STATUS_COLORS),\n/* harmony export */   STATUS_LABELS: () => (/* binding */ STATUS_LABELS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Application configuration\nconst APP_CONFIG = {\n    name: \"مرسال\",\n    description: \"نظام إدارة عمليات التوصيل السريع\",\n    version: \"1.1.0\",\n    // Colors\n    colors: {\n        primary: \"#41a7ff\",\n        background: \"#f0f3f5\",\n        accent: \"#b19cd9\"\n    },\n    // Business rules\n    business: {\n        commissionPerOrder: 1000,\n        currency: \"د.ع\",\n        defaultOrderStatuses: [\n            \"pending\",\n            \"assigned\",\n            \"picked_up\",\n            \"in_transit\",\n            \"delivered\",\n            \"returned\",\n            \"cancelled\",\n            \"postponed\"\n        ]\n    },\n    // API endpoints (when backend is ready)\n    api: {\n        baseUrl: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\",\n        endpoints: {\n            orders: \"/api/orders\",\n            users: \"/api/users\",\n            couriers: \"/api/couriers\",\n            settlements: \"/api/settlements\"\n        }\n    },\n    // Firebase config keys (to be replaced with actual values)\n    firebase: {\n        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\n    },\n    // Features flags\n    features: {\n        enableNotifications: true,\n        enableReports: true,\n        enableBulkOperations: true,\n        enableImageUpload: true\n    },\n    // Demo mode settings - إعدادات الوضع التجريبي\n    demo: {\n        enabled: true,\n        autoLogin: true,\n        defaultUser: 'manager',\n        skipFirebase: true,\n        showDemoNotice: true // إظهار تنبيه الوضع التجريبي\n    },\n    // Company info\n    company: {\n        name: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        phone: '+*********** 4567',\n        address: 'بغداد، العراق'\n    },\n    // Receipt settings - إعدادات الوصل الجديد المحدث\n    receipt: {\n        // الأبعاد المطلوبة (110×130 ملم)\n        dimensions: {\n            width: '110mm',\n            height: '130mm'\n        },\n        // معلومات الشركة الجديدة\n        company: {\n            name: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n            subtitle: 'خدمة توصيل سريعة وموثوقة',\n            phone: '+*********** 4567',\n            address: 'بغداد، العراق'\n        },\n        // تصميم الوصل المحسن\n        layout: {\n            header: {\n                fontSize: '14px',\n                fontWeight: 'bold',\n                textAlign: 'center',\n                marginBottom: '8px',\n                borderBottom: '2px solid #000',\n                padding: '5px'\n            },\n            trackingSection: {\n                fontSize: '16px',\n                fontWeight: 'bold',\n                backgroundColor: '#f8f9fa',\n                border: '2px solid #000',\n                borderRadius: '3px',\n                padding: '5px',\n                textAlign: 'center'\n            },\n            orderInfo: {\n                backgroundColor: '#fafafa',\n                border: '1px solid #ddd',\n                borderRadius: '3px'\n            },\n            pricing: {\n                fontSize: '20px',\n                fontWeight: 'bold',\n                backgroundColor: '#fff3cd',\n                border: '3px solid #000',\n                borderRadius: '4px',\n                textAlign: 'center'\n            },\n            barcode: {\n                height: '25px',\n                backgroundColor: '#f8f9fa',\n                border: '2px solid #000',\n                borderRadius: '3px',\n                fontSize: '9px'\n            }\n        },\n        // الإعدادات الأساسية\n        companyName: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        showBarcode: true,\n        showDate: true,\n        showTime: true,\n        priceFormat: 'en-US',\n        currency: 'IQD',\n        // الحقول المطلوبة\n        fields: {\n            trackingNumber: true,\n            customerPhone: true,\n            status: true,\n            courierName: true,\n            amount: true,\n            barcode: true,\n            date: true,\n            time: true // الوقت\n        }\n    }\n};\n// Status labels in Arabic\nconst STATUS_LABELS = {\n    pending: \"في الانتظار\",\n    assigned: \"مسند\",\n    picked_up: \"تم الاستلام\",\n    in_transit: \"في الطريق\",\n    delivered: \"تم التسليم\",\n    returned: \"راجع\",\n    cancelled: \"ملغي\",\n    postponed: \"مؤجل\"\n};\n// Status colors for UI\nconst STATUS_COLORS = {\n    pending: \"text-yellow-600 bg-yellow-100\",\n    assigned: \"text-blue-600 bg-blue-100\",\n    picked_up: \"text-purple-600 bg-purple-100\",\n    in_transit: \"text-orange-600 bg-orange-100\",\n    delivered: \"text-green-600 bg-green-100\",\n    returned: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    postponed: \"text-gray-600 bg-gray-100\"\n};\n// User roles\nconst USER_ROLES = {\n    admin: \"مدير\",\n    manager: \"مدير فرع\",\n    dispatcher: \"موزع\",\n    courier: \"مندوب\",\n    accountant: \"محاسب\",\n    viewer: \"مشاهد\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});