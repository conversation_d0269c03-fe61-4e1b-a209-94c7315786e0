import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/auth-provider";
import NetworkMonitor from "@/components/network-monitor";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "مرسال - نظام إدارة التوصيل",
  description: "نظام إدارة عمليات شركات التوصيل السريع",
  manifest: "/manifest.json",
};

export function generateViewport() {
  return {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 5,
    userScalable: true,
    themeColor: '#3B82F6',
    colorScheme: 'light dark',
    viewportFit: 'cover'
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl" className="h-full">
      <body className={`${inter.variable} antialiased h-full w-full overflow-x-hidden`}>
        <NetworkMonitor />
        <AuthProvider>
          <div className="min-h-screen w-full max-w-full overflow-x-hidden">
            {children}
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
