/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/settings/page";
exports.ids = ["app/settings/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=E%3A%5CMarsal%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMarsal%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=E%3A%5CMarsal%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMarsal%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(rsc)/./src/app/settings/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'settings',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/settings/page\",\n        pathname: \"/settings\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=E%3A%5CMarsal%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMarsal%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth-provider.tsx */ \"(rsc)/./src/components/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNNYXJzYWwlNUMlNUNtYXJzYWwlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTJDJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtaW50ZXIlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDTWFyc2FsJTVDJTVDbWFyc2FsJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q01hcnNhbCU1QyU1Q21hcnNhbCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0xBQTZIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJFOlxcXFxNYXJzYWxcXFxcbWFyc2FsXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(rsc)/./src/app/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNNYXJzYWwlNUMlNUNtYXJzYWwlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNzZXR0aW5ncyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBcUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXE1hcnNhbFxcXFxtYXJzYWxcXFxcc3JjXFxcXGFwcFxcXFxzZXR0aW5nc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFxNYXJzYWxcXG1hcnNhbFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a7c038f9bc65\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxcTWFyc2FsXFxtYXJzYWxcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE3YzAzOGY5YmM2NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   generateViewport: () => (/* binding */ generateViewport),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth-provider */ \"(rsc)/./src/components/auth-provider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"مرسال - نظام إدارة التوصيل\",\n    description: \"نظام إدارة عمليات شركات التوصيل السريع\",\n    manifest: \"/manifest.json\"\n};\nfunction generateViewport() {\n    return {\n        width: 'device-width',\n        initialScale: 1,\n        maximumScale: 5,\n        userScalable: true,\n        themeColor: '#3B82F6',\n        colorScheme: 'light dark',\n        viewportFit: 'cover'\n    };\n}\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased h-full w-full overflow-x-hidden`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_provider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen w-full max-w-full overflow-x-hidden\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Marsal\\marsal\\src\\app\\settings\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/auth-provider.tsx":
/*!******************************************!*\
  !*** ./src/components/auth-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Marsal\\marsal\\src\\components\\auth-provider.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Marsal\\marsal\\src\\components\\auth-provider.tsx",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth-provider.tsx */ \"(ssr)/./src/components/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNNYXJzYWwlNUMlNUNtYXJzYWwlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTJDJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtaW50ZXIlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDTWFyc2FsJTVDJTVDbWFyc2FsJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q01hcnNhbCU1QyU1Q21hcnNhbCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0xBQTZIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJFOlxcXFxNYXJzYWxcXFxcbWFyc2FsXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(ssr)/./src/app/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNNYXJzYWwlNUMlNUNtYXJzYWwlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNzZXR0aW5ncyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBcUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXE1hcnNhbFxcXFxtYXJzYWxcXFxcc3JjXFxcXGFwcFxcXFxzZXR0aW5nc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Download,Eye,EyeOff,Home,Palette,RefreshCw,Save,Settings,Shield,TestTube,User,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction SettingsPage() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('profile');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dbTestLoading, setDbTestLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dbTestResult, setDbTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Mock user profile\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"أحمد محمد\",\n        email: \"<EMAIL>\",\n        phone: \"07901234567\",\n        role: \"مدير\"\n    });\n    // Mock app settings\n    const [appSettings, setAppSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        notifications: {\n            email: true,\n            sms: true,\n            push: false\n        },\n        theme: 'light',\n        language: 'ar',\n        commissionPerOrder: 1000,\n        autoAssignment: false\n    });\n    const [passwordData, setPasswordData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: \"\",\n        newPassword: \"\",\n        confirmPassword: \"\"\n    });\n    // Database testing states\n    const [dbStatus, setDbStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [dbData, setDbData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDbData, setShowDbData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleProfileUpdate = async ()=>{\n        setLoading(true);\n        try {\n            // Mock API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            alert(\"تم تحديث الملف الشخصي بنجاح\");\n        } catch (error) {\n            alert(\"حدث خطأ أثناء التحديث\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordChange = async ()=>{\n        if (passwordData.newPassword !== passwordData.confirmPassword) {\n            alert(\"كلمة المرور الجديدة غير متطابقة\");\n            return;\n        }\n        if (passwordData.newPassword.length < 6) {\n            alert(\"كلمة المرور يجب أن تكون 6 أحرف على الأقل\");\n            return;\n        }\n        setLoading(true);\n        try {\n            // Mock API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            alert(\"تم تغيير كلمة المرور بنجاح\");\n            setPasswordData({\n                currentPassword: \"\",\n                newPassword: \"\",\n                confirmPassword: \"\"\n            });\n        } catch (error) {\n            alert(\"حدث خطأ أثناء تغيير كلمة المرور\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSettingsUpdate = async ()=>{\n        setLoading(true);\n        try {\n            // Mock API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            alert(\"تم حفظ الإعدادات بنجاح\");\n        } catch (error) {\n            alert(\"حدث خطأ أثناء حفظ الإعدادات\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDatabaseTest = async ()=>{\n        setDbTestLoading(true);\n        setDbTestResult(null);\n        try {\n            const result = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_5__.testSupabaseConnection)();\n            setDbTestResult(result);\n        } catch (error) {\n            setDbTestResult({\n                success: false,\n                message: 'حدث خطأ أثناء اختبار الاتصال'\n            });\n        } finally{\n            setDbTestLoading(false);\n        }\n    };\n    // Database testing functions\n    const testDatabase = async ()=>{\n        setDbStatus('testing');\n        try {\n            // Simulate database test\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Mock database data\n            const mockData = {\n                connection: 'متصل',\n                collections: [\n                    'orders',\n                    'users',\n                    'couriers',\n                    'settings'\n                ],\n                totalOrders: 1250,\n                totalUsers: 45,\n                lastBackup: '2024-01-15 14:30:00',\n                version: 'Firebase v9.15.0'\n            };\n            setDbData(mockData);\n            setDbStatus('success');\n        } catch (error) {\n            setDbStatus('error');\n        }\n    };\n    const exportDatabase = ()=>{\n        // Simulate database export\n        const exportData = {\n            orders: [],\n            users: [],\n            settings: {},\n            exportDate: new Date().toISOString()\n        };\n        const blob = new Blob([\n            JSON.stringify(exportData, null, 2)\n        ], {\n            type: 'application/json'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `marsal_backup_${new Date().toISOString().split('T')[0]}.json`;\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 animated-bg p-6\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"flex items-center gap-2 glass\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this),\n                                \"العودة للرئيسية\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-gray-500 to-gray-700 rounded-3xl shadow-2xl mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-8 w-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent mb-2\",\n                            children: \"الإعدادات\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-300 text-lg\",\n                            children: \"تخصيص التطبيق وإدارة الحساب\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('profile'),\n                                                className: `w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${activeTab === 'profile' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground hover:bg-accent'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"الملف الشخصي\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('notifications'),\n                                                className: `w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${activeTab === 'notifications' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground hover:bg-accent'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"الإشعارات\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('appearance'),\n                                                className: `w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${activeTab === 'appearance' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground hover:bg-accent'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"المظهر\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('system'),\n                                                className: `w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${activeTab === 'system' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground hover:bg-accent'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"النظام\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('database'),\n                                                className: `w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${activeTab === 'database' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground hover:bg-accent'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"قاعدة البيانات\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('security'),\n                                                className: `w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${activeTab === 'security' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground hover:bg-accent'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"الأمان\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 space-y-6\",\n                            children: [\n                                activeTab === 'profile' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"الملف الشخصي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"تحديث معلوماتك الشخصية\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium mb-2\",\n                                                                    children: \"الاسم الكامل\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: userProfile.name,\n                                                                    onChange: (e)=>setUserProfile((prev)=>({\n                                                                                ...prev,\n                                                                                name: e.target.value\n                                                                            })),\n                                                                    placeholder: \"أدخل الاسم الكامل\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium mb-2\",\n                                                                    children: \"رقم الهاتف\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: userProfile.phone,\n                                                                    onChange: (e)=>setUserProfile((prev)=>({\n                                                                                ...prev,\n                                                                                phone: e.target.value\n                                                                            })),\n                                                                    placeholder: \"07xxxxxxxxx\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"البريد الإلكتروني\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"email\",\n                                                            value: userProfile.email,\n                                                            onChange: (e)=>setUserProfile((prev)=>({\n                                                                        ...prev,\n                                                                        email: e.target.value\n                                                                    })),\n                                                            placeholder: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"الدور\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            value: userProfile.role,\n                                                            disabled: true,\n                                                            className: \"bg-muted\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleProfileUpdate,\n                                                    disabled: loading,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        loading ? \"جاري الحفظ...\" : \"حفظ التغييرات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === 'notifications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إعدادات الإشعارات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"تخصيص طريقة استلام الإشعارات\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"إشعارات البريد الإلكتروني\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 354,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"استلام الإشعارات عبر البريد الإلكتروني\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 355,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: appSettings.notifications.email,\n                                                                    onChange: (e)=>setAppSettings((prev)=>({\n                                                                                ...prev,\n                                                                                notifications: {\n                                                                                    ...prev.notifications,\n                                                                                    email: e.target.checked\n                                                                                }\n                                                                            })),\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"إشعارات SMS\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"استلام الإشعارات عبر الرسائل النصية\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 370,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: appSettings.notifications.sms,\n                                                                    onChange: (e)=>setAppSettings((prev)=>({\n                                                                                ...prev,\n                                                                                notifications: {\n                                                                                    ...prev.notifications,\n                                                                                    sms: e.target.checked\n                                                                                }\n                                                                            })),\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"الإشعارات الفورية\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"إشعارات فورية في المتصفح\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: appSettings.notifications.push,\n                                                                    onChange: (e)=>setAppSettings((prev)=>({\n                                                                                ...prev,\n                                                                                notifications: {\n                                                                                    ...prev.notifications,\n                                                                                    push: e.target.checked\n                                                                                }\n                                                                            })),\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleSettingsUpdate,\n                                                    disabled: loading,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        loading ? \"جاري الحفظ...\" : \"حفظ الإعدادات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === 'appearance' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"المظهر\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"تخصيص مظهر التطبيق\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"المظهر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: appSettings.theme,\n                                                            onChange: (e)=>setAppSettings((prev)=>({\n                                                                        ...prev,\n                                                                        theme: e.target.value\n                                                                    })),\n                                                            className: \"w-full p-3 border border-border rounded-md bg-background\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"light\",\n                                                                    children: \"فاتح\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"dark\",\n                                                                    children: \"داكن\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"auto\",\n                                                                    children: \"تلقائي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"اللغة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: appSettings.language,\n                                                            onChange: (e)=>setAppSettings((prev)=>({\n                                                                        ...prev,\n                                                                        language: e.target.value\n                                                                    })),\n                                                            className: \"w-full p-3 border border-border rounded-md bg-background\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"ar\",\n                                                                    children: \"العربية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"en\",\n                                                                    children: \"English\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleSettingsUpdate,\n                                                    disabled: loading,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        loading ? \"جاري الحفظ...\" : \"حفظ الإعدادات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === 'system' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إعدادات النظام\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"إعدادات عامة للنظام\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"العمولة لكل طلب (دينار عراقي)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            value: appSettings.commissionPerOrder,\n                                                            onChange: (e)=>setAppSettings((prev)=>({\n                                                                        ...prev,\n                                                                        commissionPerOrder: parseInt(e.target.value) || 0\n                                                                    })),\n                                                            placeholder: \"1000\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"الإسناد التلقائي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"إسناد الطلبات تلقائياً للمندوبين\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: appSettings.autoAssignment,\n                                                            onChange: (e)=>setAppSettings((prev)=>({\n                                                                        ...prev,\n                                                                        autoAssignment: e.target.checked\n                                                                    })),\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t pt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium mb-4\",\n                                                            children: \"اختبار الاتصال بقاعدة البيانات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: handleDatabaseTest,\n                                                                    disabled: dbTestLoading,\n                                                                    variant: \"outline\",\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        dbTestLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4 animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        dbTestLoading ? \"جاري الاختبار...\" : \"اختبار الاتصال\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                dbTestResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `p-4 rounded-lg border ${dbTestResult.success ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800'}`,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                dbTestResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 509,\n                                                                                    columnNumber: 31\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 511,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: dbTestResult.success ? 'نجح الاتصال' : 'فشل الاتصال'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 513,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 507,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"mt-1 text-sm\",\n                                                                            children: dbTestResult.message\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleSettingsUpdate,\n                                                    disabled: loading,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        loading ? \"جاري الحفظ...\" : \"حفظ الإعدادات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"الأمان\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"تغيير كلمة المرور وإعدادات الأمان\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"كلمة المرور الحالية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    type: showPassword ? \"text\" : \"password\",\n                                                                    value: passwordData.currentPassword,\n                                                                    onChange: (e)=>setPasswordData((prev)=>({\n                                                                                ...prev,\n                                                                                currentPassword: e.target.value\n                                                                            })),\n                                                                    placeholder: \"أدخل كلمة المرور الحالية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2\",\n                                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 41\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 74\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"كلمة المرور الجديدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"password\",\n                                                            value: passwordData.newPassword,\n                                                            onChange: (e)=>setPasswordData((prev)=>({\n                                                                        ...prev,\n                                                                        newPassword: e.target.value\n                                                                    })),\n                                                            placeholder: \"أدخل كلمة المرور الجديدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"تأكيد كلمة المرور الجديدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"password\",\n                                                            value: passwordData.confirmPassword,\n                                                            onChange: (e)=>setPasswordData((prev)=>({\n                                                                        ...prev,\n                                                                        confirmPassword: e.target.value\n                                                                    })),\n                                                            placeholder: \"أعد إدخال كلمة المرور الجديدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handlePasswordChange,\n                                                    disabled: loading || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 584,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        loading ? \"جاري التغيير...\" : \"تغيير كلمة المرور\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === 'database' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"اختبار قاعدة البيانات\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                            children: \"اختبار الاتصال بقاعدة البيانات والتحقق من حالتها\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"حالة الاتصال:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `px-3 py-1 rounded-full text-sm font-medium ${dbStatus === 'success' ? 'bg-green-100 text-green-800' : dbStatus === 'error' ? 'bg-red-100 text-red-800' : dbStatus === 'testing' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                                                                    children: dbStatus === 'success' ? 'متصل' : dbStatus === 'error' ? 'خطأ' : dbStatus === 'testing' ? 'جاري الاختبار...' : 'غير مختبر'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: testDatabase,\n                                                            disabled: dbStatus === 'testing',\n                                                            className: \"w-full\",\n                                                            children: dbStatus === 'testing' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4 animate-spin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"جاري الاختبار...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4 ml-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 633,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"اختبار قاعدة البيانات\"\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        dbData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-green-50 rounded-lg border border-green-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 643,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-800\",\n                                                                            children: \"قاعدة البيانات متصلة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 642,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"الحالة:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 650,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: dbData.connection\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 651,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 649,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"إجمالي الطلبات:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 654,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: dbData.totalOrders\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 655,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 653,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"المستخدمين:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 658,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: dbData.totalUsers\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 659,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 657,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"آخر نسخة احتياطية:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 662,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: dbData.lastBackup\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 663,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"إدارة قاعدة البيانات\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: ()=>setShowDbData(!showDbData),\n                                                            variant: \"outline\",\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                showDbData ? 'إخفاء' : 'عرض',\n                                                                \" قاعدة البيانات\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: exportDatabase,\n                                                            variant: \"outline\",\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"تصدير نسخة احتياطية\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 689,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 17\n                                        }, this),\n                                        showDbData && dbData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Download_Eye_EyeOff_Home_Palette_RefreshCw_Save_Settings_Shield_TestTube_User_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"بيانات قاعدة البيانات\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            children: JSON.stringify(dbData, null, 2)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/settings/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth-provider.tsx":
/*!******************************************!*\
  !*** ./src/components/auth-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check if user is logged in on app start\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": async ()=>{\n                    try {\n                        // Demo mode auto-login - تسجيل دخول تلقائي في الوضع التجريبي\n                        if (_lib_config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.demo.enabled && _lib_config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.demo.autoLogin) {\n                            try {\n                                console.log('Starting demo auto-login...');\n                                // استخدام timeout لتجنب التعليق\n                                const loginPromise = _lib_auth__WEBPACK_IMPORTED_MODULE_3__.authService.login({\n                                    username: _lib_config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.demo.defaultUser,\n                                    password: '123456'\n                                });\n                                const timeoutPromise = new Promise({\n                                    \"AuthProvider.useEffect.checkAuth\": (_, reject)=>setTimeout({\n                                            \"AuthProvider.useEffect.checkAuth\": ()=>reject(new Error('Login timeout'))\n                                        }[\"AuthProvider.useEffect.checkAuth\"], 3000)\n                                }[\"AuthProvider.useEffect.checkAuth\"]);\n                                const demoUser = await Promise.race([\n                                    loginPromise,\n                                    timeoutPromise\n                                ]);\n                                console.log('Demo user logged in:', demoUser);\n                                setUser(demoUser);\n                                setIsAuthenticated(true);\n                                if (false) {}\n                                setLoading(false);\n                                return;\n                            } catch (error) {\n                                console.error('Demo auto-login failed:', error);\n                                // في حالة فشل التسجيل التلقائي، استخدم النظام الاحتياطي\n                                console.log('Using fallback authentication...');\n                                // إنشاء مستخدم تجريبي مباشرة\n                                const fallbackUser = {\n                                    id: 'manager_fallback',\n                                    username: 'manager',\n                                    name: 'مدير النظام',\n                                    phone: '07901234567',\n                                    role: 'manager',\n                                    permissions: [],\n                                    locationId: 'main_center',\n                                    location: {\n                                        id: 'main_center',\n                                        name: 'المركز العام',\n                                        type: 'company'\n                                    },\n                                    createdBy: 'system',\n                                    createdAt: new Date(),\n                                    isActive: true,\n                                    accessToken: 'fallback_token',\n                                    refreshToken: 'fallback_refresh'\n                                };\n                                setUser(fallbackUser);\n                                setIsAuthenticated(true);\n                                if (false) {}\n                                setLoading(false);\n                                return;\n                            }\n                        }\n                        if (false) {}\n                    } catch (error) {\n                        console.error('Error checking authentication:', error);\n                        // Clear invalid data\n                        if (false) {}\n                    } finally{\n                        // Always set loading to false after a short delay to ensure UI updates\n                        setTimeout({\n                            \"AuthProvider.useEffect.checkAuth\": ()=>{\n                                setLoading(false);\n                            }\n                        }[\"AuthProvider.useEffect.checkAuth\"], 300);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (username, password)=>{\n        try {\n            const userData = await _lib_auth__WEBPACK_IMPORTED_MODULE_3__.authService.login({\n                username,\n                password\n            });\n            // Update state\n            setUser(userData);\n            setIsAuthenticated(true);\n            // Store in localStorage\n            if (false) {}\n            return true;\n        } catch (error) {\n            console.error('Login error:', error);\n            return false;\n        }\n    };\n    const logout = ()=>{\n        try {\n            // Use authService logout\n            _lib_auth__WEBPACK_IMPORTED_MODULE_3__.authService.logout();\n            // Update state\n            setUser(null);\n            setIsAuthenticated(false);\n            // Clear localStorage\n            if (false) {}\n            // Redirect to login\n            router.push('/login');\n        } catch (error) {\n            console.error('Logout error:', error);\n            // Force redirect even if there's an error\n            router.push('/login');\n        }\n    };\n    const value = {\n        user,\n        isAuthenticated,\n        login,\n        logout,\n        loading\n    };\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-700 mb-2\",\n                        children: \"مرسال\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"جاري التحقق من بيانات الدخول...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n            lineNumber: 206,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Input({ className, type, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        \"data-slot\": \"input\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\", \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLFNBQVNFLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxJQUFJLEVBQUUsR0FBR0MsT0FBc0M7SUFDekUscUJBQ0UsOERBQUNDO1FBQ0NGLE1BQU1BO1FBQ05HLGFBQVU7UUFDVkosV0FBV0YsOENBQUVBLENBQ1gsbWNBQ0EsaUZBQ0EsMEdBQ0FFO1FBRUQsR0FBR0UsS0FBSzs7Ozs7O0FBR2Y7QUFFZ0IiLCJzb3VyY2VzIjpbIkU6XFxNYXJzYWxcXG1hcnNhbFxcc3JjXFxjb21wb25lbnRzXFx1aVxcaW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZnVuY3Rpb24gSW5wdXQoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH06IFJlYWN0LkNvbXBvbmVudFByb3BzPFwiaW5wdXRcIj4pIHtcbiAgcmV0dXJuIChcbiAgICA8aW5wdXRcbiAgICAgIHR5cGU9e3R5cGV9XG4gICAgICBkYXRhLXNsb3Q9XCJpbnB1dFwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBzZWxlY3Rpb246YmctcHJpbWFyeSBzZWxlY3Rpb246dGV4dC1wcmltYXJ5LWZvcmVncm91bmQgZGFyazpiZy1pbnB1dC8zMCBib3JkZXItaW5wdXQgZmxleCBoLTkgdy1mdWxsIG1pbi13LTAgcm91bmRlZC1tZCBib3JkZXIgYmctdHJhbnNwYXJlbnQgcHgtMyBweS0xIHRleHQtYmFzZSBzaGFkb3cteHMgdHJhbnNpdGlvbi1bY29sb3IsYm94LXNoYWRvd10gb3V0bGluZS1ub25lIGZpbGU6aW5saW5lLWZsZXggZmlsZTpoLTcgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxuICAgICAgICBcImZvY3VzLXZpc2libGU6Ym9yZGVyLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcvNTAgZm9jdXMtdmlzaWJsZTpyaW5nLVszcHhdXCIsXG4gICAgICAgIFwiYXJpYS1pbnZhbGlkOnJpbmctZGVzdHJ1Y3RpdmUvMjAgZGFyazphcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS80MCBhcmlhLWludmFsaWQ6Ym9yZGVyLWRlc3RydWN0aXZlXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJpbnB1dCIsImRhdGEtc2xvdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types_roles__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/roles */ \"(ssr)/./src/types/roles.ts\");\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n// نظام المصادقة والصلاحيات\n\n\n\nclass AuthService {\n    // تسجيل الدخول\n    async login(credentials) {\n        try {\n            // محاولة البحث عن المستخدم في قاعدة البيانات\n            let user = null;\n            try {\n                user = await _supabase__WEBPACK_IMPORTED_MODULE_2__.userService.getUserByUsername(credentials.username);\n            } catch (dbError) {\n                console.warn('Database not available, using fallback auth:', dbError);\n                // في حالة عدم توفر قاعدة البيانات، استخدم المستخدمين التجريبيين\n                return this.fallbackLogin(credentials);\n            }\n            if (!user) {\n                // إذا لم يوجد المستخدم في قاعدة البيانات، جرب المستخدمين التجريبيين\n                return this.fallbackLogin(credentials);\n            }\n            if (!user.is_active) {\n                throw new Error('الحساب غير مفعل');\n            }\n            // في الوضع التجريبي، نقبل كلمة المرور 123456 لجميع المستخدمين\n            if (credentials.password !== '123456') {\n                throw new Error('كلمة المرور غير صحيحة');\n            }\n            // تحويل بيانات المستخدم من Supabase إلى AuthUser\n            const authUser = {\n                id: user.id,\n                username: user.username,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                permissions: [],\n                locationId: user.location_id || 'main_center',\n                location: user.location || {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: user.created_by,\n                createdAt: new Date(user.created_at),\n                isActive: user.is_active,\n                accessToken: 'supabase_access_token',\n                refreshToken: 'supabase_refresh_token'\n            };\n            this.currentUser = authUser;\n            this.notifyListeners();\n            // حفظ في localStorage\n            if (false) {}\n            return authUser;\n        } catch (error) {\n            console.error('Login error:', error);\n            if (error instanceof Error) {\n                throw error;\n            }\n            throw new Error('فشل في تسجيل الدخول');\n        }\n    }\n    // تسجيل الدخول الاحتياطي (في حالة عدم توفر قاعدة البيانات)\n    async fallbackLogin(credentials) {\n        // بيانات تجريبية للمستخدمين\n        const mockUsers = {\n            'manager': {\n                id: 'manager_1',\n                username: 'manager',\n                name: 'مدير النظام',\n                phone: '07901234567',\n                role: 'manager',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'system',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token',\n                refreshToken: 'fallback_refresh_token'\n            },\n            'supervisor': {\n                id: 'supervisor_1',\n                username: 'supervisor',\n                name: 'متابع النظام',\n                phone: '07901234568',\n                role: 'supervisor',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'manager_1',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token_supervisor',\n                refreshToken: 'fallback_refresh_token_supervisor'\n            },\n            'courier': {\n                id: 'courier_1',\n                username: 'courier',\n                name: 'مندوب التوصيل',\n                phone: '07901234570',\n                role: 'courier',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'manager_1',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token_courier',\n                refreshToken: 'fallback_refresh_token_courier'\n            }\n        };\n        const user = mockUsers[credentials.username];\n        if (!user || credentials.password !== '123456') {\n            throw new Error('بيانات الدخول غير صحيحة');\n        }\n        this.currentUser = user;\n        this.notifyListeners();\n        // حفظ في localStorage\n        if (false) {}\n        return user;\n    }\n    // تسجيل الخروج\n    async logout() {\n        this.currentUser = null;\n        if (false) {}\n        this.notifyListeners();\n    }\n    // الحصول على المستخدم الحالي\n    getCurrentUser() {\n        if (!this.currentUser && \"undefined\" !== 'undefined') {}\n        return this.currentUser;\n    }\n    // التحقق من الصلاحية\n    hasPermission(permission) {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        return (0,_types_roles__WEBPACK_IMPORTED_MODULE_1__.hasPermission)(user.role, permission);\n    }\n    // الحصول على الأقسام المتاحة\n    getAccessibleSections() {\n        const user = this.getCurrentUser();\n        if (!user) return [];\n        return (0,_types_roles__WEBPACK_IMPORTED_MODULE_1__.getAccessibleSections)(user.role);\n    }\n    // التحقق من إمكانية إنشاء دور معين\n    canCreateRole(targetRole) {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        const rolePermissions = {\n            'manager': [\n                'supervisor',\n                'courier'\n            ],\n            'supervisor': [\n                'courier'\n            ],\n            'courier': []\n        };\n        return rolePermissions[user.role]?.includes(targetRole) || false;\n    }\n    // إضافة مستمع للتغييرات\n    addListener(listener) {\n        this.listeners.push(listener);\n    }\n    // إزالة مستمع\n    removeListener(listener) {\n        this.listeners = this.listeners.filter((l)=>l !== listener);\n    }\n    notifyListeners() {\n        this.listeners.forEach((listener)=>listener(this.currentUser));\n    }\n    // تحديث بيانات المستخدم\n    async updateProfile(data) {\n        if (!this.currentUser) {\n            throw new Error('لم يتم تسجيل الدخول');\n        }\n        // محاكاة API call\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        this.currentUser = {\n            ...this.currentUser,\n            ...data\n        };\n        if (false) {}\n        this.notifyListeners();\n        return this.currentUser;\n    }\n    // تغيير كلمة المرور\n    async changePassword(currentPassword, newPassword) {\n        if (!this.currentUser) {\n            throw new Error('لم يتم تسجيل الدخول');\n        }\n        // محاكاة API call\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // في التطبيق الحقيقي، سيتم التحقق من كلمة المرور الحالية\n        if (currentPassword !== '123456') {\n            throw new Error('كلمة المرور الحالية غير صحيحة');\n        }\n        // تحديث كلمة المرور (في التطبيق الحقيقي)\n        console.log('تم تغيير كلمة المرور بنجاح');\n    }\n    // التحقق من صحة الجلسة\n    async validateSession() {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        try {\n            // محاكاة التحقق من صحة الجلسة\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            return true;\n        } catch (error) {\n            await this.logout();\n            return false;\n        }\n    }\n    constructor(){\n        this.currentUser = null;\n        this.listeners = [];\n    }\n}\nconst authService = new AuthService();\n// Hook للاستخدام في React\nfunction useAuth() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(authService.getCurrentUser());\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const listener = {\n                \"useAuth.useEffect.listener\": (newUser)=>setUser(newUser)\n            }[\"useAuth.useEffect.listener\"];\n            authService.addListener(listener);\n            return ({\n                \"useAuth.useEffect\": ()=>authService.removeListener(listener)\n            })[\"useAuth.useEffect\"];\n        }\n    }[\"useAuth.useEffect\"], []);\n    return {\n        user,\n        login: authService.login.bind(authService),\n        logout: authService.logout.bind(authService),\n        hasPermission: authService.hasPermission.bind(authService),\n        getAccessibleSections: authService.getAccessibleSections.bind(authService),\n        canCreateRole: authService.canCreateRole.bind(authService),\n        updateProfile: authService.updateProfile.bind(authService),\n        changePassword: authService.changePassword.bind(authService),\n        validateSession: authService.validateSession.bind(authService)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   STATUS_COLORS: () => (/* binding */ STATUS_COLORS),\n/* harmony export */   STATUS_LABELS: () => (/* binding */ STATUS_LABELS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES)\n/* harmony export */ });\n// Application configuration\nconst APP_CONFIG = {\n    name: \"مرسال\",\n    description: \"نظام إدارة عمليات التوصيل السريع\",\n    version: \"1.1.0\",\n    // Colors\n    colors: {\n        primary: \"#41a7ff\",\n        background: \"#f0f3f5\",\n        accent: \"#b19cd9\"\n    },\n    // Business rules\n    business: {\n        commissionPerOrder: 1000,\n        currency: \"د.ع\",\n        defaultOrderStatuses: [\n            \"pending\",\n            \"assigned\",\n            \"picked_up\",\n            \"in_transit\",\n            \"delivered\",\n            \"returned\",\n            \"cancelled\",\n            \"postponed\"\n        ]\n    },\n    // API endpoints (when backend is ready)\n    api: {\n        baseUrl: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\",\n        endpoints: {\n            orders: \"/api/orders\",\n            users: \"/api/users\",\n            couriers: \"/api/couriers\",\n            settlements: \"/api/settlements\"\n        }\n    },\n    // Firebase config keys (to be replaced with actual values)\n    firebase: {\n        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\n    },\n    // Features flags\n    features: {\n        enableNotifications: true,\n        enableReports: true,\n        enableBulkOperations: true,\n        enableImageUpload: true\n    },\n    // Demo mode settings - إعدادات الوضع التجريبي\n    demo: {\n        enabled: true,\n        autoLogin: true,\n        defaultUser: 'manager',\n        skipFirebase: true,\n        showDemoNotice: true // إظهار تنبيه الوضع التجريبي\n    },\n    // Company info\n    company: {\n        name: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        phone: '+*********** 4567',\n        address: 'بغداد، العراق'\n    },\n    // Receipt settings - إعدادات الوصل\n    receipt: {\n        dimensions: {\n            width: '110mm',\n            height: '130mm'\n        },\n        companyName: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        showBarcode: true,\n        showDate: true,\n        priceFormat: 'en-US',\n        currency: 'IQD',\n        fields: {\n            trackingNumber: true,\n            customerPhone: true,\n            status: true,\n            courierName: true,\n            amount: true,\n            barcode: true,\n            date: true\n        }\n    }\n};\n// Status labels in Arabic\nconst STATUS_LABELS = {\n    pending: \"في الانتظار\",\n    assigned: \"مسند\",\n    picked_up: \"تم الاستلام\",\n    in_transit: \"في الطريق\",\n    delivered: \"تم التسليم\",\n    returned: \"راجع\",\n    cancelled: \"ملغي\",\n    postponed: \"مؤجل\"\n};\n// Status colors for UI\nconst STATUS_COLORS = {\n    pending: \"text-yellow-600 bg-yellow-100\",\n    assigned: \"text-blue-600 bg-blue-100\",\n    picked_up: \"text-purple-600 bg-purple-100\",\n    in_transit: \"text-orange-600 bg-orange-100\",\n    delivered: \"text-green-600 bg-green-100\",\n    returned: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    postponed: \"text-gray-600 bg-gray-100\"\n};\n// User roles\nconst USER_ROLES = {\n    admin: \"مدير\",\n    manager: \"مدير فرع\",\n    dispatcher: \"موزع\",\n    courier: \"مندوب\",\n    accountant: \"محاسب\",\n    viewer: \"مشاهد\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseOrderService: () => (/* binding */ SupabaseOrderService),\n/* harmony export */   SupabaseUserService: () => (/* binding */ SupabaseUserService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   orderService: () => (/* binding */ orderService),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   testSupabaseConnection: () => (/* binding */ testSupabaseConnection),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Supabase configuration - إعدادات قاعدة البيانات السحابية\nconst supabaseUrl = \"https://ltxyomylyagbhueuyws.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0eHlvbXlseWFnYmh1ZXV1eXdzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MDMyNzQsImV4cCI6MjA2NzI3OTI3NH0.NeoJk-ReFqIy0QC8e-9lbg55tmu6snAmQOby0kgJDYo\" || 0;\n// Create Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Test connection function - اختبار الاتصال بقاعدة البيانات السحابية\nconst testSupabaseConnection = async ()=>{\n    try {\n        // Test connection by trying to fetch from a system table\n        const { data, error } = await supabase.from('users').select('count').limit(1);\n        if (error) {\n            console.error('Supabase connection test failed:', error);\n            return {\n                success: false,\n                message: `فشل في الاتصال بقاعدة البيانات السحابية: ${error.message}`\n            };\n        }\n        return {\n            success: true,\n            message: 'تم الاتصال بقاعدة البيانات السحابية بنجاح ✅'\n        };\n    } catch (error) {\n        console.error('Supabase connection test failed:', error);\n        let errorMessage = 'فشل في الاتصال بقاعدة البيانات السحابية';\n        if (error instanceof Error) {\n            if (error.message.includes('network')) {\n                errorMessage += ' - تحقق من الاتصال بالإنترنت';\n            } else if (error.message.includes('permission')) {\n                errorMessage += ' - مشكلة في الصلاحيات';\n            } else {\n                errorMessage += ': ' + error.message;\n            }\n        }\n        return {\n            success: false,\n            message: errorMessage\n        };\n    }\n};\n// User Service - خدمة المستخدمين\nclass SupabaseUserService {\n    async createUser(userData) {\n        const { data, error } = await supabase.from('users').insert([\n            userData\n        ]).select().single();\n        if (error) throw new Error(`Failed to create user: ${error.message}`);\n        return data;\n    }\n    async getUserByUsername(username) {\n        const { data, error } = await supabase.from('users').select('*').eq('username', username).single();\n        if (error && error.code !== 'PGRST116') {\n            throw new Error(`Failed to get user: ${error.message}`);\n        }\n        return data || null;\n    }\n    async getAllUsers() {\n        const { data, error } = await supabase.from('users').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get users: ${error.message}`);\n        return data || [];\n    }\n    async updateUser(id, updates) {\n        const { error } = await supabase.from('users').update(updates).eq('id', id);\n        if (error) throw new Error(`Failed to update user: ${error.message}`);\n    }\n    async deleteUser(id) {\n        const { error } = await supabase.from('users').delete().eq('id', id);\n        if (error) throw new Error(`Failed to delete user: ${error.message}`);\n    }\n    async getUsersByRole(role) {\n        const { data, error } = await supabase.from('users').select('*').eq('role', role).order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get users by role: ${error.message}`);\n        return data || [];\n    }\n}\n// Order Service - خدمة الطلبات\nclass SupabaseOrderService {\n    async createOrder(orderData) {\n        const { data, error } = await supabase.from('orders').insert([\n            {\n                ...orderData,\n                updated_at: new Date().toISOString()\n            }\n        ]).select().single();\n        if (error) throw new Error(`Failed to create order: ${error.message}`);\n        return data;\n    }\n    async getOrderByTrackingNumber(trackingNumber) {\n        const { data, error } = await supabase.from('orders').select('*').eq('tracking_number', trackingNumber).single();\n        if (error && error.code !== 'PGRST116') {\n            throw new Error(`Failed to get order: ${error.message}`);\n        }\n        return data || null;\n    }\n    async getAllOrders() {\n        const { data, error } = await supabase.from('orders').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get orders: ${error.message}`);\n        return data || [];\n    }\n    async getOrdersByStatus(status) {\n        const { data, error } = await supabase.from('orders').select('*').eq('status', status).order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get orders by status: ${error.message}`);\n        return data || [];\n    }\n    async getOrdersByCourier(courierId) {\n        const { data, error } = await supabase.from('orders').select('*').eq('courier_id', courierId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get orders by courier: ${error.message}`);\n        return data || [];\n    }\n    async updateOrder(id, updates) {\n        const { error } = await supabase.from('orders').update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq('id', id);\n        if (error) throw new Error(`Failed to update order: ${error.message}`);\n    }\n    async deleteOrder(id) {\n        const { error } = await supabase.from('orders').delete().eq('id', id);\n        if (error) throw new Error(`Failed to delete order: ${error.message}`);\n    }\n    async searchOrders(query) {\n        const { data, error } = await supabase.from('orders').select('*').or(`tracking_number.ilike.%${query}%,customer_name.ilike.%${query}%,customer_phone.ilike.%${query}%,address.ilike.%${query}%`).order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to search orders: ${error.message}`);\n        return data || [];\n    }\n}\n// Create service instances\nconst userService = new SupabaseUserService();\nconst orderService = new SupabaseOrderService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateCommission: () => (/* binding */ calculateCommission),\n/* harmony export */   calculateNetAmount: () => (/* binding */ calculateNetAmount),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatPhone: () => (/* binding */ formatPhone),\n/* harmony export */   generateTrackingNumber: () => (/* binding */ generateTrackingNumber),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getStatusLabel: () => (/* binding */ getStatusLabel),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateIraqiPhone: () => (/* binding */ validateIraqiPhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Format currency in Iraqi Dinar\nfunction formatCurrency(amount) {\n    return `${amount.toLocaleString('ar-IQ')} د.ع`;\n}\n// Format date in Arabic\nfunction formatDate(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleDateString('ar-IQ', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n}\n// Format date and time in Arabic\nfunction formatDateTime(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleString('ar-IQ', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    });\n}\n// Generate tracking number\nfunction generateTrackingNumber() {\n    const prefix = 'MRS';\n    const timestamp = Date.now().toString().slice(-6);\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n    return `${prefix}${timestamp}${random}`;\n}\n// Validate Iraqi phone number\nfunction validateIraqiPhone(phone) {\n    const phoneRegex = /^(07[3-9]|075)\\d{8}$/;\n    return phoneRegex.test(phone.replace(/\\s+/g, ''));\n}\n// Format phone number\nfunction formatPhone(phone) {\n    const cleaned = phone.replace(/\\D/g, '');\n    if (cleaned.length === 11 && cleaned.startsWith('07')) {\n        return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;\n    }\n    return phone;\n}\n// Calculate commission\nfunction calculateCommission(orderCount, commissionPerOrder = 1000) {\n    return orderCount * commissionPerOrder;\n}\n// Calculate net amount after commission\nfunction calculateNetAmount(totalAmount, commission) {\n    return totalAmount - commission;\n}\n// Truncate text\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n}\n// Get status color class\nfunction getStatusColor(status) {\n    const colors = {\n        pending: \"text-yellow-600 bg-yellow-100\",\n        assigned: \"text-blue-600 bg-blue-100\",\n        picked_up: \"text-purple-600 bg-purple-100\",\n        in_transit: \"text-orange-600 bg-orange-100\",\n        delivered: \"text-green-600 bg-green-100\",\n        returned: \"text-red-600 bg-red-100\",\n        cancelled: \"text-gray-600 bg-gray-100\",\n        postponed: \"text-gray-600 bg-gray-100\"\n    };\n    return colors[status] || \"text-gray-600 bg-gray-100\";\n}\n// Get status label in Arabic\nfunction getStatusLabel(status) {\n    const labels = {\n        pending: \"في الانتظار\",\n        assigned: \"مسند\",\n        picked_up: \"تم الاستلام\",\n        in_transit: \"في الطريق\",\n        delivered: \"تم التسليم\",\n        returned: \"راجع\",\n        cancelled: \"ملغي\",\n        postponed: \"مؤجل\"\n    };\n    return labels[status] || status;\n}\n// Sleep function for demos\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n// Check if running on mobile\nfunction isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n// Copy text to clipboard\nasync function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (err) {\n        console.error('Failed to copy text: ', err);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/roles.ts":
/*!****************************!*\
  !*** ./src/types/roles.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ORDER_STATUSES: () => (/* binding */ ORDER_STATUSES),\n/* harmony export */   Permission: () => (/* binding */ Permission),\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   canCreateRole: () => (/* binding */ canCreateRole),\n/* harmony export */   getAccessibleSections: () => (/* binding */ getAccessibleSections),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\n// نظام الأدوار والصلاحيات الشامل\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    // الأدوار الأساسية فقط\n    UserRole[\"MANAGER\"] = \"manager\";\n    UserRole[\"COURIER\"] = \"courier\";\n    UserRole[\"SUPERVISOR\"] = \"supervisor\"; // متابع\n    return UserRole;\n}({});\nvar Permission = /*#__PURE__*/ function(Permission) {\n    // إدارة الطلبات\n    Permission[\"CREATE_ORDER\"] = \"create_order\";\n    Permission[\"VIEW_ORDER\"] = \"view_order\";\n    Permission[\"UPDATE_ORDER\"] = \"update_order\";\n    Permission[\"DELETE_ORDER\"] = \"delete_order\";\n    Permission[\"ASSIGN_ORDER\"] = \"assign_order\";\n    Permission[\"TRANSFER_ORDER\"] = \"transfer_order\";\n    // إدارة المستخدمين\n    Permission[\"CREATE_USER\"] = \"create_user\";\n    Permission[\"VIEW_USER\"] = \"view_user\";\n    Permission[\"UPDATE_USER\"] = \"update_user\";\n    Permission[\"DELETE_USER\"] = \"delete_user\";\n    // إدارة الفروع والمراكز\n    Permission[\"MANAGE_BRANCHES\"] = \"manage_branches\";\n    Permission[\"MANAGE_PROVINCES\"] = \"manage_provinces\";\n    // المحاسبة\n    Permission[\"VIEW_ACCOUNTING\"] = \"view_accounting\";\n    Permission[\"PROCESS_ACCOUNTING\"] = \"process_accounting\";\n    // الإحصائيات\n    Permission[\"VIEW_STATISTICS\"] = \"view_statistics\";\n    Permission[\"VIEW_ALL_STATISTICS\"] = \"view_all_statistics\";\n    // الأرشيف\n    Permission[\"VIEW_ARCHIVE\"] = \"view_archive\";\n    Permission[\"MANAGE_ARCHIVE\"] = \"manage_archive\";\n    // التذاكر\n    Permission[\"CREATE_TICKET\"] = \"create_ticket\";\n    Permission[\"MANAGE_TICKETS\"] = \"manage_tickets\";\n    // الإعدادات\n    Permission[\"MANAGE_SETTINGS\"] = \"manage_settings\";\n    // المخزن\n    Permission[\"MANAGE_WAREHOUSE\"] = \"manage_warehouse\";\n    // الاستيراد والتصدير\n    Permission[\"IMPORT_ORDERS\"] = \"import_orders\";\n    Permission[\"EXPORT_ORDERS\"] = \"export_orders\";\n    return Permission;\n}({});\nconst ROLE_PERMISSIONS = {\n    [\"manager\"]: {\n        role: \"manager\",\n        permissions: [\n            \"create_order\",\n            \"view_order\",\n            \"update_order\",\n            \"delete_order\",\n            \"assign_order\",\n            \"transfer_order\",\n            \"create_user\",\n            \"view_user\",\n            \"update_user\",\n            \"delete_user\",\n            \"manage_branches\",\n            \"manage_provinces\",\n            \"view_accounting\",\n            \"process_accounting\",\n            \"view_all_statistics\",\n            \"view_archive\",\n            \"manage_archive\",\n            \"manage_tickets\",\n            \"manage_settings\",\n            \"manage_warehouse\",\n            \"import_orders\",\n            \"export_orders\"\n        ],\n        canCreateRoles: [\n            \"courier\",\n            \"supervisor\"\n        ],\n        accessibleSections: [\n            'orders',\n            'dispatch',\n            'returns',\n            'accounting',\n            'statistics',\n            'archive',\n            'users',\n            'import-export',\n            'notifications',\n            'settings'\n        ]\n    },\n    [\"supervisor\"]: {\n        role: \"supervisor\",\n        permissions: [\n            \"view_order\",\n            \"update_order\",\n            \"assign_order\",\n            \"manage_tickets\",\n            \"view_statistics\",\n            \"view_archive\"\n        ],\n        canCreateRoles: [],\n        accessibleSections: [\n            'orders',\n            'statistics',\n            'archive',\n            'notifications',\n            'settings'\n        ]\n    },\n    [\"courier\"]: {\n        role: \"courier\",\n        permissions: [\n            \"view_order\",\n            \"update_order\"\n        ],\n        canCreateRoles: [],\n        accessibleSections: [\n            'orders',\n            'archive',\n            'notifications',\n            'settings'\n        ]\n    }\n};\nconst ORDER_STATUSES = [\n    {\n        id: 'delivered',\n        name: 'تم التسليم',\n        color: 'bg-green-600',\n        description: 'تم تسليم الطلب بنجاح',\n        requiresPhoto: true,\n        requiresReason: false,\n        isDelivered: true,\n        isReturned: false\n    },\n    {\n        id: 'returned_to_courier',\n        name: 'راجع عند المندوب',\n        color: 'bg-amber-600',\n        description: 'الطلب راجع عند المندوب',\n        requiresPhoto: true,\n        requiresReason: true,\n        isDelivered: false,\n        isReturned: true\n    },\n    {\n        id: 'partial_delivery',\n        name: 'تسليم جزئي',\n        color: 'bg-blue-500',\n        description: 'تم تسليم جزء من الطلب',\n        requiresPhoto: true,\n        requiresReason: false,\n        isDelivered: true,\n        isReturned: false\n    },\n    {\n        id: 'price_change',\n        name: 'تغيير السعر',\n        color: 'bg-purple-500',\n        description: 'تم تغيير سعر الطلب',\n        requiresPhoto: false,\n        requiresReason: true,\n        isDelivered: false,\n        isReturned: false\n    },\n    {\n        id: 'postponed',\n        name: 'مؤجل',\n        color: 'bg-gray-500',\n        description: 'تم تأجيل التسليم',\n        requiresPhoto: true,\n        requiresReason: true,\n        isDelivered: false,\n        isReturned: false\n    }\n];\nfunction hasPermission(userRole, permission) {\n    return ROLE_PERMISSIONS[userRole]?.permissions.includes(permission) || false;\n}\nfunction canCreateRole(userRole, targetRole) {\n    return ROLE_PERMISSIONS[userRole]?.canCreateRoles.includes(targetRole) || false;\n}\nfunction getAccessibleSections(userRole) {\n    return ROLE_PERMISSIONS[userRole]?.accessibleSections || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/roles.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/tailwind-merge","vendor-chunks/whatwg-url","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/webidl-conversions","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=E%3A%5CMarsal%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMarsal%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();