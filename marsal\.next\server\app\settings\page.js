(()=>{var e={};e.id=662,e.ids=[662],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8819:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12594:(e,s,t)=>{Promise.resolve().then(t.bind(t,94462))},12597:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17695:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),l=t.n(i),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,74198)),"E:\\Marsal\\marsal\\src\\app\\settings\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["E:\\Marsal\\marsal\\src\\app\\settings\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,s,t)=>{"use strict";t.d(s,{$:()=>c});var a=t(60687);t(43210);var r=t(8730),i=t(24224),l=t(4780);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:s,size:t,asChild:i=!1,...c}){let d=i?r.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,l.cn)(n({variant:s,size:t,className:e})),...c})}},31158:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},32192:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>d,ZB:()=>n,Zp:()=>i,aR:()=>l});var a=t(60687);t(43210);var r=t(4780);function i({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function l({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function n({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...s})}function c({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...s})}function d({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...s})}},49546:(e,s,t)=>{Promise.resolve().then(t.bind(t,74198))},55511:e=>{"use strict";e.exports=require("crypto")},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},61611:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74198:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\settings\\page.tsx","default")},77972:e=>{"use strict";e.exports=require("https")},78122:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84027:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},87891:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var a=t(60687);t(43210);var r=t(4780);function i({className:e,type:s,...t}){return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},94462:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>M});var a=t(60687),r=t(43210),i=t(44493),l=t(29523),n=t(89667),c=t(32192),d=t(84027),o=t(58869),x=t(97051),m=t(62688);let u=(0,m.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]);var h=t(61611);let p=(0,m.A)("test-tube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]),g=(0,m.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var f=t(8819),j=t(78122),v=t(87891),b=t(70663),y=t(12597),N=t(13861),w=t(5336),k=t(31158),A=t(16391),C=t(85814),P=t.n(C);function M(){let[e,s]=(0,r.useState)("profile"),[t,m]=(0,r.useState)(!1),[C,M]=(0,r.useState)(!1),[q,_]=(0,r.useState)(!1),[z,S]=(0,r.useState)(null),[$,B]=(0,r.useState)({name:"أحمد محمد",email:"<EMAIL>",phone:"07901234567",role:"مدير"}),[Z,R]=(0,r.useState)({notifications:{email:!0,sms:!0,push:!1},theme:"light",language:"ar",commissionPerOrder:1e3,autoAssignment:!1}),[T,E]=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[O,V]=(0,r.useState)("idle"),[W,L]=(0,r.useState)(null),[G,H]=(0,r.useState)(!1),U=async()=>{m(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("تم تحديث الملف الشخصي بنجاح")}catch(e){alert("حدث خطأ أثناء التحديث")}finally{m(!1)}},D=async()=>{if(T.newPassword!==T.confirmPassword)return void alert("كلمة المرور الجديدة غير متطابقة");if(T.newPassword.length<6)return void alert("كلمة المرور يجب أن تكون 6 أحرف على الأقل");m(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("تم تغيير كلمة المرور بنجاح"),E({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){alert("حدث خطأ أثناء تغيير كلمة المرور")}finally{m(!1)}},I=async()=>{m(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("تم حفظ الإعدادات بنجاح")}catch(e){alert("حدث خطأ أثناء حفظ الإعدادات")}finally{m(!1)}},F=async()=>{_(!0),S(null);try{let e=await (0,A.LC)();S(e)}catch(e){S({success:!1,message:"حدث خطأ أثناء اختبار الاتصال"})}finally{_(!1)}},J=async()=>{V("testing");try{await new Promise(e=>setTimeout(e,2e3)),L({connection:"متصل",collections:["orders","users","couriers","settings"],totalOrders:1250,totalUsers:45,lastBackup:"2024-01-15 14:30:00",version:"Firebase v9.15.0"}),V("success")}catch(e){V("error")}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 animated-bg p-6",dir:"rtl",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto space-y-8",children:[(0,a.jsx)("div",{className:"flex items-center gap-4 mb-6",children:(0,a.jsx)(P(),{href:"/",children:(0,a.jsxs)(l.$,{variant:"outline",size:"sm",className:"flex items-center gap-2 glass",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),"العودة للرئيسية"]})})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-gray-500 to-gray-700 rounded-3xl shadow-2xl mb-4",children:(0,a.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,a.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent mb-2",children:"الإعدادات"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-lg",children:"تخصيص التطبيق وإدارة الحساب"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("nav",{className:"space-y-2",children:[(0,a.jsxs)("button",{onClick:()=>s("profile"),className:`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${"profile"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"}`,children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),"الملف الشخصي"]}),(0,a.jsxs)("button",{onClick:()=>s("notifications"),className:`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${"notifications"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"}`,children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),"الإشعارات"]}),(0,a.jsxs)("button",{onClick:()=>s("appearance"),className:`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${"appearance"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"}`,children:[(0,a.jsx)(u,{className:"h-4 w-4"}),"المظهر"]}),(0,a.jsxs)("button",{onClick:()=>s("system"),className:`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${"system"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"}`,children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"النظام"]}),(0,a.jsxs)("button",{onClick:()=>s("database"),className:`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${"database"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"}`,children:[(0,a.jsx)(p,{className:"h-4 w-4"}),"قاعدة البيانات"]}),(0,a.jsxs)("button",{onClick:()=>s("security"),className:`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${"security"===e?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-accent"}`,children:[(0,a.jsx)(g,{className:"h-4 w-4"}),"الأمان"]})]})})})}),(0,a.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:["profile"===e&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),"الملف الشخصي"]}),(0,a.jsx)(i.BT,{children:"تحديث معلوماتك الشخصية"})]}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"الاسم الكامل"}),(0,a.jsx)(n.p,{value:$.name,onChange:e=>B(s=>({...s,name:e.target.value})),placeholder:"أدخل الاسم الكامل"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"رقم الهاتف"}),(0,a.jsx)(n.p,{value:$.phone,onChange:e=>B(s=>({...s,phone:e.target.value})),placeholder:"07xxxxxxxxx"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"البريد الإلكتروني"}),(0,a.jsx)(n.p,{type:"email",value:$.email,onChange:e=>B(s=>({...s,email:e.target.value})),placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"الدور"}),(0,a.jsx)(n.p,{value:$.role,disabled:!0,className:"bg-muted"})]}),(0,a.jsxs)(l.$,{onClick:U,disabled:t,className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),t?"جاري الحفظ...":"حفظ التغييرات"]})]})]}),"notifications"===e&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),"إعدادات الإشعارات"]}),(0,a.jsx)(i.BT,{children:"تخصيص طريقة استلام الإشعارات"})]}),(0,a.jsxs)(i.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"إشعارات البريد الإلكتروني"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"استلام الإشعارات عبر البريد الإلكتروني"})]}),(0,a.jsx)("input",{type:"checkbox",checked:Z.notifications.email,onChange:e=>R(s=>({...s,notifications:{...s.notifications,email:e.target.checked}})),className:"w-4 h-4"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"إشعارات SMS"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"استلام الإشعارات عبر الرسائل النصية"})]}),(0,a.jsx)("input",{type:"checkbox",checked:Z.notifications.sms,onChange:e=>R(s=>({...s,notifications:{...s.notifications,sms:e.target.checked}})),className:"w-4 h-4"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"الإشعارات الفورية"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"إشعارات فورية في المتصفح"})]}),(0,a.jsx)("input",{type:"checkbox",checked:Z.notifications.push,onChange:e=>R(s=>({...s,notifications:{...s.notifications,push:e.target.checked}})),className:"w-4 h-4"})]})]}),(0,a.jsxs)(l.$,{onClick:I,disabled:t,className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),t?"جاري الحفظ...":"حفظ الإعدادات"]})]})]}),"appearance"===e&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(u,{className:"h-5 w-5"}),"المظهر"]}),(0,a.jsx)(i.BT,{children:"تخصيص مظهر التطبيق"})]}),(0,a.jsxs)(i.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"المظهر"}),(0,a.jsxs)("select",{value:Z.theme,onChange:e=>R(s=>({...s,theme:e.target.value})),className:"w-full p-3 border border-border rounded-md bg-background",children:[(0,a.jsx)("option",{value:"light",children:"فاتح"}),(0,a.jsx)("option",{value:"dark",children:"داكن"}),(0,a.jsx)("option",{value:"auto",children:"تلقائي"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اللغة"}),(0,a.jsxs)("select",{value:Z.language,onChange:e=>R(s=>({...s,language:e.target.value})),className:"w-full p-3 border border-border rounded-md bg-background",children:[(0,a.jsx)("option",{value:"ar",children:"العربية"}),(0,a.jsx)("option",{value:"en",children:"English"})]})]}),(0,a.jsxs)(l.$,{onClick:I,disabled:t,className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),t?"جاري الحفظ...":"حفظ الإعدادات"]})]})]}),"system"===e&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5"}),"إعدادات النظام"]}),(0,a.jsx)(i.BT,{children:"إعدادات عامة للنظام"})]}),(0,a.jsxs)(i.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"العمولة لكل طلب (دينار عراقي)"}),(0,a.jsx)(n.p,{type:"number",value:Z.commissionPerOrder,onChange:e=>R(s=>({...s,commissionPerOrder:parseInt(e.target.value)||0})),placeholder:"1000"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"الإسناد التلقائي"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"إسناد الطلبات تلقائياً للمندوبين"})]}),(0,a.jsx)("input",{type:"checkbox",checked:Z.autoAssignment,onChange:e=>R(s=>({...s,autoAssignment:e.target.checked})),className:"w-4 h-4"})]}),(0,a.jsxs)("div",{className:"border-t pt-6",children:[(0,a.jsx)("h3",{className:"font-medium mb-4",children:"اختبار الاتصال بقاعدة البيانات"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(l.$,{onClick:F,disabled:q,variant:"outline",className:"flex items-center gap-2",children:[q?(0,a.jsx)(j.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(h.A,{className:"h-4 w-4"}),q?"جاري الاختبار...":"اختبار الاتصال"]}),z&&(0,a.jsxs)("div",{className:`p-4 rounded-lg border ${z.success?"bg-green-50 border-green-200 text-green-800":"bg-red-50 border-red-200 text-red-800"}`,children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[z.success?(0,a.jsx)(v.A,{className:"h-4 w-4"}):(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:z.success?"نجح الاتصال":"فشل الاتصال"})]}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:z.message})]})]})]}),(0,a.jsxs)(l.$,{onClick:I,disabled:t,className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),t?"جاري الحفظ...":"حفظ الإعدادات"]})]})]}),"security"===e&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(g,{className:"h-5 w-5"}),"الأمان"]}),(0,a.jsx)(i.BT,{children:"تغيير كلمة المرور وإعدادات الأمان"})]}),(0,a.jsxs)(i.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"كلمة المرور الحالية"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.p,{type:C?"text":"password",value:T.currentPassword,onChange:e=>E(s=>({...s,currentPassword:e.target.value})),placeholder:"أدخل كلمة المرور الحالية"}),(0,a.jsx)("button",{type:"button",onClick:()=>M(!C),className:"absolute left-3 top-1/2 transform -translate-y-1/2",children:C?(0,a.jsx)(y.A,{className:"h-4 w-4"}):(0,a.jsx)(N.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"كلمة المرور الجديدة"}),(0,a.jsx)(n.p,{type:"password",value:T.newPassword,onChange:e=>E(s=>({...s,newPassword:e.target.value})),placeholder:"أدخل كلمة المرور الجديدة"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"تأكيد كلمة المرور الجديدة"}),(0,a.jsx)(n.p,{type:"password",value:T.confirmPassword,onChange:e=>E(s=>({...s,confirmPassword:e.target.value})),placeholder:"أعد إدخال كلمة المرور الجديدة"})]}),(0,a.jsxs)(l.$,{onClick:D,disabled:t||!T.currentPassword||!T.newPassword||!T.confirmPassword,className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),t?"جاري التغيير...":"تغيير كلمة المرور"]})]})]}),"database"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(p,{className:"h-5 w-5"}),"اختبار قاعدة البيانات"]}),(0,a.jsx)(i.BT,{children:"اختبار الاتصال بقاعدة البيانات والتحقق من حالتها"})]}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{children:"حالة الاتصال:"}),(0,a.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${"success"===O?"bg-green-100 text-green-800":"error"===O?"bg-red-100 text-red-800":"testing"===O?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:"success"===O?"متصل":"error"===O?"خطأ":"testing"===O?"جاري الاختبار...":"غير مختبر"})]}),(0,a.jsx)(l.$,{onClick:J,disabled:"testing"===O,className:"w-full",children:"testing"===O?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"جاري الاختبار..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p,{className:"h-4 w-4 ml-2"}),"اختبار قاعدة البيانات"]})}),W&&(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,a.jsx)(w.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("span",{className:"font-medium text-green-800",children:"قاعدة البيانات متصلة"})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"الحالة:"}),(0,a.jsx)("span",{className:"font-medium",children:W.connection})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"إجمالي الطلبات:"}),(0,a.jsx)("span",{className:"font-medium",children:W.totalOrders})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"المستخدمين:"}),(0,a.jsx)("span",{className:"font-medium",children:W.totalUsers})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"آخر نسخة احتياطية:"}),(0,a.jsx)("span",{className:"font-medium",children:W.lastBackup})]})]})]})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5"}),"إدارة قاعدة البيانات"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)(l.$,{onClick:()=>H(!G),variant:"outline",className:"w-full",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 ml-2"}),G?"إخفاء":"عرض"," قاعدة البيانات"]}),(0,a.jsxs)(l.$,{onClick:()=>{let e=new Blob([JSON.stringify({orders:[],users:[],settings:{},exportDate:new Date().toISOString()},null,2)],{type:"application/json"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download=`marsal_backup_${new Date().toISOString().split("T")[0]}.json`,t.click(),URL.revokeObjectURL(s)},variant:"outline",className:"w-full",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 ml-2"}),"تصدير نسخة احتياطية"]})]})]}),G&&W&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5"}),"بيانات قاعدة البيانات"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-auto",children:(0,a.jsx)("pre",{children:JSON.stringify(W,null,2)})})})]})]})]})]})]})})}},94735:e=>{"use strict";e.exports=require("events")},97051:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,437,839,814,690],()=>t(17695));module.exports=a})();