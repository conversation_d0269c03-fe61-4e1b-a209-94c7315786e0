(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5969],{30285:(e,r,s)=>{"use strict";s.d(r,{$:()=>c});var n=s(95155);s(12115);var t=s(99708),a=s(74466),i=s(59434);let d=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:r,variant:s,size:a,asChild:c=!1,...l}=e,o=c?t.DX:"button";return(0,n.jsx)(o,{"data-slot":"button",className:(0,i.cn)(d({variant:s,size:a,className:r})),...l})}},33183:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>g});var n=s(95155),t=s(12115),a=s(35695),i=s(66695),d=s(30285),c=s(62523),l=s(88539),o=s(37108),m=s(92138),x=s(4229),u=s(6874),h=s.n(u);let p={id:"1",trackingNumber:"MRS001",senderName:"أحمد محمد",senderPhone:"07901234567",senderAddress:"بغداد - الكرادة - شارع الرشيد",recipientName:"فاطمة علي",recipientPhone:"07801234567",recipientAddress:"بغداد - الجادرية - قرب الجامعة",amount:5e4,notes:"يرجى التسليم في المساء"};function g(e){let{params:r}=e,s=(0,a.useRouter)(),[u,g]=(0,t.useState)("");(0,t.useState)(()=>{r.then(e=>g(e.id))});let[b,f]=(0,t.useState)({senderName:p.senderName,senderPhone:p.senderPhone,senderAddress:p.senderAddress,recipientName:p.recipientName,recipientPhone:p.recipientPhone,recipientAddress:p.recipientAddress,amount:p.amount,notes:p.notes}),[v,j]=(0,t.useState)(!1),[N,y]=(0,t.useState)({}),w=e=>{let{name:r,value:s}=e.target;f(e=>({...e,[r]:"amount"===r?parseInt(s)||0:s})),N[r]&&y(e=>({...e,[r]:""}))},k=()=>{let e={};return b.senderName.trim()||(e.senderName="اسم المرسل مطلوب"),b.senderPhone.trim()?/^07\d{9}$/.test(b.senderPhone)||(e.senderPhone="رقم الهاتف غير صحيح (يجب أن يبدأ بـ 07)"):e.senderPhone="هاتف المرسل مطلوب",b.recipientName.trim()||(e.recipientName="اسم المستلم مطلوب"),b.recipientPhone.trim()?/^07\d{9}$/.test(b.recipientPhone)||(e.recipientPhone="رقم الهاتف غير صحيح (يجب أن يبدأ بـ 07)"):e.recipientPhone="هاتف المستلم مطلوب",(!b.amount||b.amount<=0)&&(e.amount="المبلغ مطلوب ويجب أن يكون أكبر من صفر"),y(e),0===Object.keys(e).length},P=async e=>{if(e.preventDefault(),k()){j(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("تم تحديث الطلب بنجاح!"),s.push("/orders/".concat(u))}catch(e){console.error("Error updating order:",e),alert("حدث خطأ أثناء تحديث الطلب")}finally{j(!1)}}};return(0,n.jsx)("div",{className:"min-h-screen bg-background p-4 md:p-6 lg:p-8",dir:"rtl",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h1",{className:"text-3xl font-bold text-primary flex items-center gap-2",children:[(0,n.jsx)(o.A,{className:"h-8 w-8"}),"تحديث الطلب ",p.trackingNumber]}),(0,n.jsx)("p",{className:"text-muted-foreground mt-2",children:"تحديث حالة ومعلومات الطلب"})]}),(0,n.jsx)(h(),{href:"/orders/".concat(p.id),children:(0,n.jsxs)(d.$,{variant:"outline",className:"flex items-center gap-2",children:[(0,n.jsx)(m.A,{className:"h-4 w-4"}),"العودة لتفاصيل الطلب"]})})]}),(0,n.jsxs)(i.Zp,{children:[(0,n.jsx)(i.aR,{children:(0,n.jsx)(i.ZB,{children:"معلومات الطلب"})}),(0,n.jsx)(i.Wu,{children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"رقم الوصل"}),(0,n.jsx)("p",{className:"font-semibold",children:p.trackingNumber})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"المستلم"}),(0,n.jsx)("p",{className:"font-medium",children:p.recipientName})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"الهاتف"}),(0,n.jsx)("p",{className:"font-medium",children:p.recipientPhone})]})]})})]}),(0,n.jsxs)("form",{onSubmit:P,className:"space-y-6",children:[(0,n.jsxs)(i.Zp,{children:[(0,n.jsxs)(i.aR,{children:[(0,n.jsx)(i.ZB,{children:"معلومات المرسل"}),(0,n.jsx)(i.BT,{children:"بيانات الشخص أو الجهة المرسلة"})]}),(0,n.jsxs)(i.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اسم المرسل *"}),(0,n.jsx)(c.p,{name:"senderName",value:b.senderName,onChange:w,placeholder:"أدخل اسم المرسل",className:N.senderName?"border-red-500":""}),N.senderName&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:N.senderName})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium mb-2",children:"هاتف المرسل *"}),(0,n.jsx)(c.p,{name:"senderPhone",value:b.senderPhone,onChange:w,placeholder:"07xxxxxxxxx",className:N.senderPhone?"border-red-500":""}),N.senderPhone&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:N.senderPhone})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium mb-2",children:"عنوان المرسل *"}),(0,n.jsx)(l.T,{name:"senderAddress",value:b.senderAddress,onChange:w,placeholder:"أدخل العنوان الكامل للمرسل",className:N.senderAddress?"border-red-500":"",rows:3}),N.senderAddress&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:N.senderAddress})]})]})]}),(0,n.jsxs)(i.Zp,{children:[(0,n.jsxs)(i.aR,{children:[(0,n.jsx)(i.ZB,{children:"معلومات المستلم"}),(0,n.jsx)(i.BT,{children:"بيانات الشخص المستلم للطلب"})]}),(0,n.jsxs)(i.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اسم المستلم *"}),(0,n.jsx)(c.p,{name:"recipientName",value:b.recipientName,onChange:w,placeholder:"أدخل اسم المستلم",className:N.recipientName?"border-red-500":""}),N.recipientName&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:N.recipientName})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium mb-2",children:"هاتف المستلم *"}),(0,n.jsx)(c.p,{name:"recipientPhone",value:b.recipientPhone,onChange:w,placeholder:"07xxxxxxxxx",className:N.recipientPhone?"border-red-500":""}),N.recipientPhone&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:N.recipientPhone})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium mb-2",children:"عنوان المستلم *"}),(0,n.jsx)(l.T,{name:"recipientAddress",value:b.recipientAddress,onChange:w,placeholder:"أدخل العنوان الكامل للمستلم",className:N.recipientAddress?"border-red-500":"",rows:3}),N.recipientAddress&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:N.recipientAddress})]})]})]}),(0,n.jsxs)(i.Zp,{children:[(0,n.jsxs)(i.aR,{children:[(0,n.jsx)(i.ZB,{children:"تفاصيل الطلب"}),(0,n.jsx)(i.BT,{children:"المبلغ والملاحظات"})]}),(0,n.jsxs)(i.Wu,{className:"space-y-4",children:[(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium mb-2",children:"المبلغ (دينار عراقي) *"}),(0,n.jsx)(c.p,{name:"amount",type:"number",value:b.amount,onChange:w,placeholder:"أدخل المبلغ",className:N.amount?"border-red-500":""}),N.amount&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:N.amount})]})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium mb-2",children:"ملاحظات"}),(0,n.jsx)(l.T,{name:"notes",value:b.notes,onChange:w,placeholder:"أدخل أي ملاحظات إضافية",rows:3})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,n.jsx)(h(),{href:"/orders/".concat(u),children:(0,n.jsx)(d.$,{variant:"outline",disabled:v,children:"إلغاء"})}),(0,n.jsxs)(d.$,{type:"submit",disabled:v,className:"flex items-center gap-2",children:[(0,n.jsx)(x.A,{className:"h-4 w-4"}),v?"جاري الحفظ...":"حفظ التغييرات"]})]})]})]})})}},59434:(e,r,s)=>{"use strict";s.d(r,{Yq:()=>d,cn:()=>a,ps:()=>x,qY:()=>m,r6:()=>c,vv:()=>i,y7:()=>l,zC:()=>o});var n=s(52596),t=s(39688);function a(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,t.QP)((0,n.$)(r))}function i(e){return"".concat(e.toLocaleString("ar-IQ")," د.ع")}function d(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function c(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function l(){let e=Date.now().toString().slice(-6),r=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"".concat("MRS").concat(e).concat(r)}function o(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function m(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function x(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}},62523:(e,r,s)=>{"use strict";s.d(r,{p:()=>a});var n=s(95155);s(12115);var t=s(59434);function a(e){let{className:r,type:s,...a}=e;return(0,n.jsx)("input",{type:s,"data-slot":"input",className:(0,t.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...a})}},66695:(e,r,s)=>{"use strict";s.d(r,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>a,aR:()=>i});var n=s(95155);s(12115);var t=s(59434);function a(e){let{className:r,...s}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,t.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...s})}function i(e){let{className:r,...s}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,t.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...s})}function d(e){let{className:r,...s}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,t.cn)("leading-none font-semibold",r),...s})}function c(e){let{className:r,...s}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,t.cn)("text-muted-foreground text-sm",r),...s})}function l(e){let{className:r,...s}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,t.cn)("px-6",r),...s})}},80015:(e,r,s)=>{Promise.resolve().then(s.bind(s,33183))},88539:(e,r,s)=>{"use strict";s.d(r,{T:()=>i});var n=s(95155),t=s(12115),a=s(59434);let i=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,n.jsx)("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...t})});i.displayName="Textarea"}},e=>{var r=r=>e(e.s=r);e.O(0,[8779,622,2432,7979,1899,9744,4495,5138,6321,2652,5030,3719,9473,558,9401,6325,6077,4409,1124,9385,4927,37,2041,4979,8321,2900,3610,9988,2158,7358],()=>r(80015)),_N_E=e.O()}]);