(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6894],{30285:(e,s,t)=>{"use strict";t.d(s,{$:()=>c});var r=t(95155);t(12115);var a=t(99708),n=t(74466),i=t(59434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:s,variant:t,size:n,asChild:c=!1,...l}=e,o=c?a.DX:"button";return(0,r.jsx)(o,{"data-slot":"button",className:(0,i.cn)(d({variant:t,size:n,className:s})),...l})}},43022:(e,s,t)=>{Promise.resolve().then(t.bind(t,77088))},55365:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>c,TN:()=>l});var r=t(95155),a=t(12115),n=t(74466),i=t(59434);let d=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=a.forwardRef((e,s)=>{let{className:t,variant:a,...n}=e;return(0,r.jsx)("div",{ref:s,role:"alert",className:(0,i.cn)(d({variant:a}),t),...n})});c.displayName="Alert",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),...a})}).displayName="AlertTitle";let l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),...a})});l.displayName="AlertDescription"},62523:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var r=t(95155);t(12115);var a=t(59434);function n(e){let{className:s,type:t,...n}=e;return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...n})}},66695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>n,aR:()=>i});var r=t(95155);t(12115);var a=t(59434);function n(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function i(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function d(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...t})}function c(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...t})}function l(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...t})}},77088:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var r=t(95155),a=t(12115),n=t(35695),i=t(66695),d=t(30285),c=t(62523),l=t(85057),o=t(55365),u=t(51154),m=t(76517),x=t(94449),g=t(54213),v=t(78749),h=t(92657),b=t(59345),p=t(56104);function f(){let[e,s]=(0,a.useState)(""),[t,f]=(0,a.useState)(""),[j,w]=(0,a.useState)(!1),[N,y]=(0,a.useState)(!1),[k,D]=(0,a.useState)(""),[C,F]=(0,a.useState)("checking"),A=(0,n.useRouter)();(0,a.useEffect)(()=>{z()},[]);let z=async()=>{try{F("checking");let e=await (0,p.testFirebaseConnection)();F(e.success?"connected":"disconnected"),e.success||D("فشل الاتصال بـ Firebase: ".concat(e.message))}catch(e){F("disconnected"),D("فشل في اختبار الاتصال بـ Firebase")}},S=async s=>{if(s.preventDefault(),"connected"!==C)return void D("يجب الاتصال بـ Firebase أولاً");if(!e.trim()||!t.trim())return void D("يرجى إدخال اسم المستخدم وكلمة المرور");y(!0),D("");try{console.log("\uD83D\uDD10 محاولة تسجيل الدخول مع Firebase...");let s=await b.firebaseAuthService.login(e.trim(),t);s.success&&s.user?(console.log("✅ تم تسجيل الدخول بنجاح:",s.user.name),localStorage.setItem("currentUser",JSON.stringify(s.user)),localStorage.setItem("isAuthenticated","true"),A.push("/")):D(s.error||"فشل في تسجيل الدخول")}catch(e){console.error("❌ خطأ في تسجيل الدخول:",e),D(e.message||"حدث خطأ غير متوقع")}finally{y(!1)}},_=async e=>{let t={azad95:{username:"azad95",password:"Azad@1995"},manager:{username:"manager",password:"123456"},supervisor:{username:"supervisor",password:"123456"},courier:{username:"courier",password:"123456"}};s(t[e].username),f(t[e].password),setTimeout(()=>{let e=document.querySelector("form");e&&e.dispatchEvent(new Event("submit",{cancelable:!0,bubbles:!0}))},100)};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto h-12 w-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(g.A,{className:"h-6 w-6 text-white"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"تطبيق مرسال"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"تسجيل الدخول مع Firebase"})]}),(0,r.jsx)(i.Zp,{className:"".concat((()=>{switch(C){case"checking":return"border-yellow-200 bg-yellow-50";case"connected":return"border-green-200 bg-green-50";case"disconnected":return"border-red-200 bg-red-50"}})()),children:(0,r.jsxs)(i.Wu,{className:"pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(()=>{switch(C){case"checking":return(0,r.jsx)(u.A,{className:"h-4 w-4 animate-spin"});case"connected":return(0,r.jsx)(m.A,{className:"h-4 w-4 text-green-600"});case"disconnected":return(0,r.jsx)(x.A,{className:"h-4 w-4 text-red-600"})}})(),(0,r.jsx)("span",{className:"text-sm font-medium",children:(()=>{switch(C){case"checking":return"جاري فحص الاتصال بـ Firebase...";case"connected":return"متصل بـ Firebase بنجاح";case"disconnected":return"غير متصل بـ Firebase"}})()})]}),"disconnected"===C&&(0,r.jsx)(d.$,{variant:"outline",size:"sm",onClick:z,className:"mt-2 w-full",children:"\uD83D\uDD04 إعادة المحاولة"})]})}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"تسجيل الدخول"}),(0,r.jsx)(i.BT,{children:"أدخل بيانات الدخول للوصول إلى التطبيق"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("form",{onSubmit:S,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"username",children:"اسم المستخدم"}),(0,r.jsx)(c.p,{id:"username",type:"text",value:e,onChange:e=>s(e.target.value),placeholder:"أدخل اسم المستخدم",disabled:N||"connected"!==C,className:"text-right"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"password",children:"كلمة المرور"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.p,{id:"password",type:j?"text":"password",value:t,onChange:e=>f(e.target.value),placeholder:"أدخل كلمة المرور",disabled:N||"connected"!==C,className:"text-right pr-10"}),(0,r.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>w(!j),disabled:N,children:j?(0,r.jsx)(v.A,{className:"h-4 w-4"}):(0,r.jsx)(h.A,{className:"h-4 w-4"})})]})]}),k&&(0,r.jsx)(o.Fc,{variant:"destructive",children:(0,r.jsx)(o.TN,{children:k})}),(0,r.jsx)(d.$,{type:"submit",className:"w-full",disabled:N||"connected"!==C,children:N?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"جاري تسجيل الدخول..."]}):"تسجيل الدخول"})]})})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{className:"text-sm",children:"تسجيل دخول سريع (للاختبار)"})}),(0,r.jsxs)(i.Wu,{className:"space-y-2",children:[(0,r.jsx)(d.$,{variant:"outline",className:"w-full justify-start bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200",onClick:()=>_("azad95"),disabled:N||"connected"!==C,children:"\uD83D\uDC68‍\uD83D\uDCBC أزاد - المدير الرئيسي (azad95 / Azad@1995)"}),(0,r.jsx)(d.$,{variant:"outline",className:"w-full justify-start",onClick:()=>_("manager"),disabled:N||"connected"!==C,children:"\uD83D\uDC51 مدير النظام (manager / 123456)"}),(0,r.jsx)(d.$,{variant:"outline",className:"w-full justify-start",onClick:()=>_("supervisor"),disabled:N||"connected"!==C,children:"\uD83D\uDC68‍\uD83D\uDCBC المتابع (supervisor / 123456)"}),(0,r.jsx)(d.$,{variant:"outline",className:"w-full justify-start",onClick:()=>_("courier"),disabled:N||"connected"!==C,children:"\uD83D\uDE9A المندوب (courier / 123456)"})]})]}),(0,r.jsxs)("div",{className:"text-center text-sm text-gray-500",children:[(0,r.jsx)("p",{children:"تطبيق مرسال - نظام إدارة التوصيل"}),(0,r.jsx)("p",{children:"مدعوم بـ Firebase"})]})]})})}},85057:(e,s,t)=>{"use strict";t.d(s,{J:()=>l});var r=t(95155),a=t(12115),n=t(40968),i=t(74466),d=t(59434);let c=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(n.b,{ref:s,className:(0,d.cn)(c(),t),...a})});l.displayName=n.b.displayName}},e=>{var s=s=>e(e.s=s);e.O(0,[8541,1336,9948,4709,875,416,8779,622,2432,7979,1899,9744,4495,5138,6321,2652,5030,3719,9473,558,9401,6325,6077,4409,1124,9385,4927,37,2041,4979,8321,2900,3610,9988,2158,8058,7358],()=>s(43022)),_N_E=e.O()}]);