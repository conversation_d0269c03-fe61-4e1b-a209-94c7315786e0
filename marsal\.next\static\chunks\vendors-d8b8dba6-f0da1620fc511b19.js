"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9988],{6537:(e,t,r)=>{r.d(t,{A:()=>u});var n=r(22868),o=r(63479),i=r(6974),a=r(85808),s=r(93682),f=r(38988);let u=function(){function e(){}return e.clearMatrix=function(e){e.clear(255)},e.buildMatrix=function(t,r,n,o,i){e.clearMatrix(i),e.embedBasicPatterns(n,i),e.embedTypeInfo(r,o,i),e.maybeEmbedVersionInfo(n,i),e.embedDataBits(t,o,i)},e.embedBasicPatterns=function(t,r){e.embedPositionDetectionPatternsAndSeparators(r),e.embedDarkDotAtLeftBottomCorner(r),e.maybeEmbedPositionAdjustmentPatterns(t,r),e.embedTimingPatterns(r)},e.embedTypeInfo=function(t,r,o){var i=new n.A;e.makeTypeInfoBits(t,r,i);for(var a=0,s=i.getSize();a<s;++a){var f=i.get(i.getSize()-1-a),u=e.TYPE_INFO_COORDINATES[a],l=u[0],w=u[1];if(o.setBoolean(l,w,f),a<8){var h=o.getWidth()-a-1,c=8;o.setBoolean(h,c,f)}else{var h=8,c=o.getHeight()-7+(a-8);o.setBoolean(h,c,f)}}},e.maybeEmbedVersionInfo=function(t,r){if(!(7>t.getVersionNumber())){var o=new n.A;e.makeVersionInfoBits(t,o);for(var i=17,a=0;a<6;++a)for(var s=0;s<3;++s){var f=o.get(i);i--,r.setBoolean(a,r.getHeight()-11+s,f),r.setBoolean(r.getHeight()-11+s,a,f)}}},e.embedDataBits=function(t,r,n){for(var o=0,i=-1,f=n.getWidth()-1,u=n.getHeight()-1;f>0;){for(6===f&&(f-=1);u>=0&&u<n.getHeight();){for(var l=0;l<2;++l){var w=f-l;if(e.isEmpty(n.get(w,u))){var h=void 0;o<t.getSize()?(h=t.get(o),++o):h=!1,255!==r&&a.A.getDataMaskBit(r,w,u)&&(h=!h),n.setBoolean(w,u,h)}}u+=i}u+=i=-i,f-=2}if(o!==t.getSize())throw new s.A("Not all bits consumed: "+o+"/"+t.getSize())},e.findMSBSet=function(e){return 32-o.A.numberOfLeadingZeros(e)},e.calculateBCHCode=function(t,r){if(0===r)throw new f.A("0 polynomial");var n=e.findMSBSet(r);for(t<<=n-1;e.findMSBSet(t)>=n;)t^=r<<e.findMSBSet(t)-n;return t},e.makeTypeInfoBits=function(t,r,o){if(!i.A.isValidMaskPattern(r))throw new s.A("Invalid mask pattern");var a=t.getBits()<<3|r;o.appendBits(a,5);var f=e.calculateBCHCode(a,e.TYPE_INFO_POLY);o.appendBits(f,10);var u=new n.A;if(u.appendBits(e.TYPE_INFO_MASK_PATTERN,15),o.xor(u),15!==o.getSize())throw new s.A("should not happen but we got: "+o.getSize())},e.makeVersionInfoBits=function(t,r){r.appendBits(t.getVersionNumber(),6);var n=e.calculateBCHCode(t.getVersionNumber(),e.VERSION_INFO_POLY);if(r.appendBits(n,12),18!==r.getSize())throw new s.A("should not happen but we got: "+r.getSize())},e.isEmpty=function(e){return 255===e},e.embedTimingPatterns=function(t){for(var r=8;r<t.getWidth()-8;++r){var n=(r+1)%2;e.isEmpty(t.get(r,6))&&t.setNumber(r,6,n),e.isEmpty(t.get(6,r))&&t.setNumber(6,r,n)}},e.embedDarkDotAtLeftBottomCorner=function(e){if(0===e.get(8,e.getHeight()-8))throw new s.A;e.setNumber(8,e.getHeight()-8,1)},e.embedHorizontalSeparationPattern=function(t,r,n){for(var o=0;o<8;++o){if(!e.isEmpty(n.get(t+o,r)))throw new s.A;n.setNumber(t+o,r,0)}},e.embedVerticalSeparationPattern=function(t,r,n){for(var o=0;o<7;++o){if(!e.isEmpty(n.get(t,r+o)))throw new s.A;n.setNumber(t,r+o,0)}},e.embedPositionAdjustmentPattern=function(t,r,n){for(var o=0;o<5;++o)for(var i=e.POSITION_ADJUSTMENT_PATTERN[o],a=0;a<5;++a)n.setNumber(t+a,r+o,i[a])},e.embedPositionDetectionPattern=function(t,r,n){for(var o=0;o<7;++o)for(var i=e.POSITION_DETECTION_PATTERN[o],a=0;a<7;++a)n.setNumber(t+a,r+o,i[a])},e.embedPositionDetectionPatternsAndSeparators=function(t){var r=e.POSITION_DETECTION_PATTERN[0].length;e.embedPositionDetectionPattern(0,0,t),e.embedPositionDetectionPattern(t.getWidth()-r,0,t),e.embedPositionDetectionPattern(0,t.getWidth()-r,t),e.embedHorizontalSeparationPattern(0,7,t),e.embedHorizontalSeparationPattern(t.getWidth()-8,7,t),e.embedHorizontalSeparationPattern(0,t.getWidth()-8,t),e.embedVerticalSeparationPattern(7,0,t),e.embedVerticalSeparationPattern(t.getHeight()-7-1,0,t),e.embedVerticalSeparationPattern(7,t.getHeight()-7,t)},e.maybeEmbedPositionAdjustmentPatterns=function(t,r){if(!(2>t.getVersionNumber()))for(var n=t.getVersionNumber()-1,o=e.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[n],i=0,a=o.length;i!==a;i++){var s=o[i];if(s>=0)for(var f=0;f!==a;f++){var u=o[f];u>=0&&e.isEmpty(r.get(u,s))&&e.embedPositionAdjustmentPattern(u-2,s-2,r)}}},e.POSITION_DETECTION_PATTERN=Array.from([Int32Array.from([1,1,1,1,1,1,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,1,1,1,1,1,1])]),e.POSITION_ADJUSTMENT_PATTERN=Array.from([Int32Array.from([1,1,1,1,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,0,1,0,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,1,1,1,1])]),e.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE=Array.from([Int32Array.from([-1,-1,-1,-1,-1,-1,-1]),Int32Array.from([6,18,-1,-1,-1,-1,-1]),Int32Array.from([6,22,-1,-1,-1,-1,-1]),Int32Array.from([6,26,-1,-1,-1,-1,-1]),Int32Array.from([6,30,-1,-1,-1,-1,-1]),Int32Array.from([6,34,-1,-1,-1,-1,-1]),Int32Array.from([6,22,38,-1,-1,-1,-1]),Int32Array.from([6,24,42,-1,-1,-1,-1]),Int32Array.from([6,26,46,-1,-1,-1,-1]),Int32Array.from([6,28,50,-1,-1,-1,-1]),Int32Array.from([6,30,54,-1,-1,-1,-1]),Int32Array.from([6,32,58,-1,-1,-1,-1]),Int32Array.from([6,34,62,-1,-1,-1,-1]),Int32Array.from([6,26,46,66,-1,-1,-1]),Int32Array.from([6,26,48,70,-1,-1,-1]),Int32Array.from([6,26,50,74,-1,-1,-1]),Int32Array.from([6,30,54,78,-1,-1,-1]),Int32Array.from([6,30,56,82,-1,-1,-1]),Int32Array.from([6,30,58,86,-1,-1,-1]),Int32Array.from([6,34,62,90,-1,-1,-1]),Int32Array.from([6,28,50,72,94,-1,-1]),Int32Array.from([6,26,50,74,98,-1,-1]),Int32Array.from([6,30,54,78,102,-1,-1]),Int32Array.from([6,28,54,80,106,-1,-1]),Int32Array.from([6,32,58,84,110,-1,-1]),Int32Array.from([6,30,58,86,114,-1,-1]),Int32Array.from([6,34,62,90,118,-1,-1]),Int32Array.from([6,26,50,74,98,122,-1]),Int32Array.from([6,30,54,78,102,126,-1]),Int32Array.from([6,26,52,78,104,130,-1]),Int32Array.from([6,30,56,82,108,134,-1]),Int32Array.from([6,34,60,86,112,138,-1]),Int32Array.from([6,30,58,86,114,142,-1]),Int32Array.from([6,34,62,90,118,146,-1]),Int32Array.from([6,30,54,78,102,126,150]),Int32Array.from([6,24,50,76,102,128,154]),Int32Array.from([6,28,54,80,106,132,158]),Int32Array.from([6,32,58,84,110,136,162]),Int32Array.from([6,26,54,82,110,138,166]),Int32Array.from([6,30,58,86,114,142,170])]),e.TYPE_INFO_COORDINATES=Array.from([Int32Array.from([8,0]),Int32Array.from([8,1]),Int32Array.from([8,2]),Int32Array.from([8,3]),Int32Array.from([8,4]),Int32Array.from([8,5]),Int32Array.from([8,7]),Int32Array.from([8,8]),Int32Array.from([7,8]),Int32Array.from([5,8]),Int32Array.from([4,8]),Int32Array.from([3,8]),Int32Array.from([2,8]),Int32Array.from([1,8]),Int32Array.from([0,8])]),e.VERSION_INFO_POLY=7973,e.TYPE_INFO_POLY=1335,e.TYPE_INFO_MASK_PATTERN=21522,e}()},6974:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(1933);let o=function(){function e(){this.maskPattern=-1}return e.prototype.getMode=function(){return this.mode},e.prototype.getECLevel=function(){return this.ecLevel},e.prototype.getVersion=function(){return this.version},e.prototype.getMaskPattern=function(){return this.maskPattern},e.prototype.getMatrix=function(){return this.matrix},e.prototype.toString=function(){var e=new n.A;return e.append("<<\n"),e.append(" mode: "),e.append(this.mode?this.mode.toString():"null"),e.append("\n ecLevel: "),e.append(this.ecLevel?this.ecLevel.toString():"null"),e.append("\n version: "),e.append(this.version?this.version.toString():"null"),e.append("\n maskPattern: "),e.append(this.maskPattern.toString()),this.matrix?(e.append("\n matrix:\n"),e.append(this.matrix.toString())):e.append("\n matrix: null\n"),e.append(">>\n"),e.toString()},e.prototype.setMode=function(e){this.mode=e},e.prototype.setECLevel=function(e){this.ecLevel=e},e.prototype.setVersion=function(e){this.version=e},e.prototype.setMaskPattern=function(e){this.maskPattern=e},e.prototype.setMatrix=function(e){this.matrix=e},e.isValidMaskPattern=function(t){return t>=0&&t<e.NUM_MASK_PATTERNS},e.NUM_MASK_PATTERNS=8,e}()},21876:(e,t,r)=>{r.d(t,{A:()=>w});var n=r(10782),o=r(78903),i=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},a=function(){function e(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];this.ecCodewordsPerBlock=e,this.ecBlocks=t}return e.prototype.getECCodewordsPerBlock=function(){return this.ecCodewordsPerBlock},e.prototype.getNumBlocks=function(){var e,t,r=0,n=this.ecBlocks;try{for(var o=i(n),a=o.next();!a.done;a=o.next()){var s=a.value;r+=s.getCount()}}catch(t){e={error:t}}finally{try{a&&!a.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}return r},e.prototype.getTotalECCodewords=function(){return this.ecCodewordsPerBlock*this.getNumBlocks()},e.prototype.getECBlocks=function(){return this.ecBlocks},e}(),s=function(){function e(e,t){this.count=e,this.dataCodewords=t}return e.prototype.getCount=function(){return this.count},e.prototype.getDataCodewords=function(){return this.dataCodewords},e}(),f=r(71534),u=r(38988),l=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};let w=function(){function e(e,t){for(var r,n,o=[],i=2;i<arguments.length;i++)o[i-2]=arguments[i];this.versionNumber=e,this.alignmentPatternCenters=t,this.ecBlocks=o;var a=0,s=o[0].getECCodewordsPerBlock(),f=o[0].getECBlocks();try{for(var u=l(f),w=u.next();!w.done;w=u.next()){var h=w.value;a+=h.getCount()*(h.getDataCodewords()+s)}}catch(e){r={error:e}}finally{try{w&&!w.done&&(n=u.return)&&n.call(u)}finally{if(r)throw r.error}}this.totalCodewords=a}return e.prototype.getVersionNumber=function(){return this.versionNumber},e.prototype.getAlignmentPatternCenters=function(){return this.alignmentPatternCenters},e.prototype.getTotalCodewords=function(){return this.totalCodewords},e.prototype.getDimensionForVersion=function(){return 17+4*this.versionNumber},e.prototype.getECBlocksForLevel=function(e){return this.ecBlocks[e.getValue()]},e.getProvisionalVersionForDimension=function(e){if(e%4!=1)throw new f.A;try{return this.getVersionForNumber((e-17)/4)}catch(e){throw new f.A}},e.getVersionForNumber=function(t){if(t<1||t>40)throw new u.A;return e.VERSIONS[t-1]},e.decodeVersionInformation=function(t){for(var r=Number.MAX_SAFE_INTEGER,n=0,i=0;i<e.VERSION_DECODE_INFO.length;i++){var a=e.VERSION_DECODE_INFO[i];if(a===t)return e.getVersionForNumber(i+7);var s=o.A.numBitsDiffering(t,a);s<r&&(n=i+7,r=s)}return r<=3?e.getVersionForNumber(n):null},e.prototype.buildFunctionPattern=function(){var e=this.getDimensionForVersion(),t=new n.A(e);t.setRegion(0,0,9,9),t.setRegion(e-8,0,8,9),t.setRegion(0,e-8,9,8);for(var r=this.alignmentPatternCenters.length,o=0;o<r;o++)for(var i=this.alignmentPatternCenters[o]-2,a=0;a<r;a++)(0!==o||0!==a&&a!==r-1)&&(o!==r-1||0!==a)&&t.setRegion(this.alignmentPatternCenters[a]-2,i,5,5);return t.setRegion(6,9,1,e-17),t.setRegion(9,6,e-17,1),this.versionNumber>6&&(t.setRegion(e-11,0,3,6),t.setRegion(0,e-11,6,3)),t},e.prototype.toString=function(){return""+this.versionNumber},e.VERSION_DECODE_INFO=Int32Array.from([31892,34236,39577,42195,48118,51042,55367,58893,63784,68472,70749,76311,79154,84390,87683,92361,96236,102084,102881,110507,110734,117786,119615,126325,127568,133589,136944,141498,145311,150283,152622,158308,161089,167017]),e.VERSIONS=[new e(1,new Int32Array(0),new a(7,new s(1,19)),new a(10,new s(1,16)),new a(13,new s(1,13)),new a(17,new s(1,9))),new e(2,Int32Array.from([6,18]),new a(10,new s(1,34)),new a(16,new s(1,28)),new a(22,new s(1,22)),new a(28,new s(1,16))),new e(3,Int32Array.from([6,22]),new a(15,new s(1,55)),new a(26,new s(1,44)),new a(18,new s(2,17)),new a(22,new s(2,13))),new e(4,Int32Array.from([6,26]),new a(20,new s(1,80)),new a(18,new s(2,32)),new a(26,new s(2,24)),new a(16,new s(4,9))),new e(5,Int32Array.from([6,30]),new a(26,new s(1,108)),new a(24,new s(2,43)),new a(18,new s(2,15),new s(2,16)),new a(22,new s(2,11),new s(2,12))),new e(6,Int32Array.from([6,34]),new a(18,new s(2,68)),new a(16,new s(4,27)),new a(24,new s(4,19)),new a(28,new s(4,15))),new e(7,Int32Array.from([6,22,38]),new a(20,new s(2,78)),new a(18,new s(4,31)),new a(18,new s(2,14),new s(4,15)),new a(26,new s(4,13),new s(1,14))),new e(8,Int32Array.from([6,24,42]),new a(24,new s(2,97)),new a(22,new s(2,38),new s(2,39)),new a(22,new s(4,18),new s(2,19)),new a(26,new s(4,14),new s(2,15))),new e(9,Int32Array.from([6,26,46]),new a(30,new s(2,116)),new a(22,new s(3,36),new s(2,37)),new a(20,new s(4,16),new s(4,17)),new a(24,new s(4,12),new s(4,13))),new e(10,Int32Array.from([6,28,50]),new a(18,new s(2,68),new s(2,69)),new a(26,new s(4,43),new s(1,44)),new a(24,new s(6,19),new s(2,20)),new a(28,new s(6,15),new s(2,16))),new e(11,Int32Array.from([6,30,54]),new a(20,new s(4,81)),new a(30,new s(1,50),new s(4,51)),new a(28,new s(4,22),new s(4,23)),new a(24,new s(3,12),new s(8,13))),new e(12,Int32Array.from([6,32,58]),new a(24,new s(2,92),new s(2,93)),new a(22,new s(6,36),new s(2,37)),new a(26,new s(4,20),new s(6,21)),new a(28,new s(7,14),new s(4,15))),new e(13,Int32Array.from([6,34,62]),new a(26,new s(4,107)),new a(22,new s(8,37),new s(1,38)),new a(24,new s(8,20),new s(4,21)),new a(22,new s(12,11),new s(4,12))),new e(14,Int32Array.from([6,26,46,66]),new a(30,new s(3,115),new s(1,116)),new a(24,new s(4,40),new s(5,41)),new a(20,new s(11,16),new s(5,17)),new a(24,new s(11,12),new s(5,13))),new e(15,Int32Array.from([6,26,48,70]),new a(22,new s(5,87),new s(1,88)),new a(24,new s(5,41),new s(5,42)),new a(30,new s(5,24),new s(7,25)),new a(24,new s(11,12),new s(7,13))),new e(16,Int32Array.from([6,26,50,74]),new a(24,new s(5,98),new s(1,99)),new a(28,new s(7,45),new s(3,46)),new a(24,new s(15,19),new s(2,20)),new a(30,new s(3,15),new s(13,16))),new e(17,Int32Array.from([6,30,54,78]),new a(28,new s(1,107),new s(5,108)),new a(28,new s(10,46),new s(1,47)),new a(28,new s(1,22),new s(15,23)),new a(28,new s(2,14),new s(17,15))),new e(18,Int32Array.from([6,30,56,82]),new a(30,new s(5,120),new s(1,121)),new a(26,new s(9,43),new s(4,44)),new a(28,new s(17,22),new s(1,23)),new a(28,new s(2,14),new s(19,15))),new e(19,Int32Array.from([6,30,58,86]),new a(28,new s(3,113),new s(4,114)),new a(26,new s(3,44),new s(11,45)),new a(26,new s(17,21),new s(4,22)),new a(26,new s(9,13),new s(16,14))),new e(20,Int32Array.from([6,34,62,90]),new a(28,new s(3,107),new s(5,108)),new a(26,new s(3,41),new s(13,42)),new a(30,new s(15,24),new s(5,25)),new a(28,new s(15,15),new s(10,16))),new e(21,Int32Array.from([6,28,50,72,94]),new a(28,new s(4,116),new s(4,117)),new a(26,new s(17,42)),new a(28,new s(17,22),new s(6,23)),new a(30,new s(19,16),new s(6,17))),new e(22,Int32Array.from([6,26,50,74,98]),new a(28,new s(2,111),new s(7,112)),new a(28,new s(17,46)),new a(30,new s(7,24),new s(16,25)),new a(24,new s(34,13))),new e(23,Int32Array.from([6,30,54,78,102]),new a(30,new s(4,121),new s(5,122)),new a(28,new s(4,47),new s(14,48)),new a(30,new s(11,24),new s(14,25)),new a(30,new s(16,15),new s(14,16))),new e(24,Int32Array.from([6,28,54,80,106]),new a(30,new s(6,117),new s(4,118)),new a(28,new s(6,45),new s(14,46)),new a(30,new s(11,24),new s(16,25)),new a(30,new s(30,16),new s(2,17))),new e(25,Int32Array.from([6,32,58,84,110]),new a(26,new s(8,106),new s(4,107)),new a(28,new s(8,47),new s(13,48)),new a(30,new s(7,24),new s(22,25)),new a(30,new s(22,15),new s(13,16))),new e(26,Int32Array.from([6,30,58,86,114]),new a(28,new s(10,114),new s(2,115)),new a(28,new s(19,46),new s(4,47)),new a(28,new s(28,22),new s(6,23)),new a(30,new s(33,16),new s(4,17))),new e(27,Int32Array.from([6,34,62,90,118]),new a(30,new s(8,122),new s(4,123)),new a(28,new s(22,45),new s(3,46)),new a(30,new s(8,23),new s(26,24)),new a(30,new s(12,15),new s(28,16))),new e(28,Int32Array.from([6,26,50,74,98,122]),new a(30,new s(3,117),new s(10,118)),new a(28,new s(3,45),new s(23,46)),new a(30,new s(4,24),new s(31,25)),new a(30,new s(11,15),new s(31,16))),new e(29,Int32Array.from([6,30,54,78,102,126]),new a(30,new s(7,116),new s(7,117)),new a(28,new s(21,45),new s(7,46)),new a(30,new s(1,23),new s(37,24)),new a(30,new s(19,15),new s(26,16))),new e(30,Int32Array.from([6,26,52,78,104,130]),new a(30,new s(5,115),new s(10,116)),new a(28,new s(19,47),new s(10,48)),new a(30,new s(15,24),new s(25,25)),new a(30,new s(23,15),new s(25,16))),new e(31,Int32Array.from([6,30,56,82,108,134]),new a(30,new s(13,115),new s(3,116)),new a(28,new s(2,46),new s(29,47)),new a(30,new s(42,24),new s(1,25)),new a(30,new s(23,15),new s(28,16))),new e(32,Int32Array.from([6,34,60,86,112,138]),new a(30,new s(17,115)),new a(28,new s(10,46),new s(23,47)),new a(30,new s(10,24),new s(35,25)),new a(30,new s(19,15),new s(35,16))),new e(33,Int32Array.from([6,30,58,86,114,142]),new a(30,new s(17,115),new s(1,116)),new a(28,new s(14,46),new s(21,47)),new a(30,new s(29,24),new s(19,25)),new a(30,new s(11,15),new s(46,16))),new e(34,Int32Array.from([6,34,62,90,118,146]),new a(30,new s(13,115),new s(6,116)),new a(28,new s(14,46),new s(23,47)),new a(30,new s(44,24),new s(7,25)),new a(30,new s(59,16),new s(1,17))),new e(35,Int32Array.from([6,30,54,78,102,126,150]),new a(30,new s(12,121),new s(7,122)),new a(28,new s(12,47),new s(26,48)),new a(30,new s(39,24),new s(14,25)),new a(30,new s(22,15),new s(41,16))),new e(36,Int32Array.from([6,24,50,76,102,128,154]),new a(30,new s(6,121),new s(14,122)),new a(28,new s(6,47),new s(34,48)),new a(30,new s(46,24),new s(10,25)),new a(30,new s(2,15),new s(64,16))),new e(37,Int32Array.from([6,28,54,80,106,132,158]),new a(30,new s(17,122),new s(4,123)),new a(28,new s(29,46),new s(14,47)),new a(30,new s(49,24),new s(10,25)),new a(30,new s(24,15),new s(46,16))),new e(38,Int32Array.from([6,32,58,84,110,136,162]),new a(30,new s(4,122),new s(18,123)),new a(28,new s(13,46),new s(32,47)),new a(30,new s(48,24),new s(14,25)),new a(30,new s(42,15),new s(32,16))),new e(39,Int32Array.from([6,26,54,82,110,138,166]),new a(30,new s(20,117),new s(4,118)),new a(28,new s(40,47),new s(7,48)),new a(30,new s(43,24),new s(22,25)),new a(30,new s(10,15),new s(67,16))),new e(40,Int32Array.from([6,30,58,86,114,142,170]),new a(30,new s(19,118),new s(6,119)),new a(28,new s(18,47),new s(31,48)),new a(30,new s(34,24),new s(34,25)),new a(30,new s(20,15),new s(61,16)))],e}()},26143:(e,t,r)=>{r.d(t,{A:()=>i});var n,o=r(38988);!function(e){e[e.TERMINATOR=0]="TERMINATOR",e[e.NUMERIC=1]="NUMERIC",e[e.ALPHANUMERIC=2]="ALPHANUMERIC",e[e.STRUCTURED_APPEND=3]="STRUCTURED_APPEND",e[e.BYTE=4]="BYTE",e[e.ECI=5]="ECI",e[e.KANJI=6]="KANJI",e[e.FNC1_FIRST_POSITION=7]="FNC1_FIRST_POSITION",e[e.FNC1_SECOND_POSITION=8]="FNC1_SECOND_POSITION",e[e.HANZI=9]="HANZI"}(n||(n={}));let i=function(){function e(t,r,n,o){this.value=t,this.stringValue=r,this.characterCountBitsForVersions=n,this.bits=o,e.FOR_BITS.set(o,this),e.FOR_VALUE.set(t,this)}return e.forBits=function(t){var r=e.FOR_BITS.get(t);if(void 0===r)throw new o.A;return r},e.prototype.getCharacterCountBits=function(e){var t,r=e.getVersionNumber();return t=r<=9?0:r<=26?1:2,this.characterCountBitsForVersions[t]},e.prototype.getValue=function(){return this.value},e.prototype.getBits=function(){return this.bits},e.prototype.equals=function(t){return t instanceof e&&this.value===t.value},e.prototype.toString=function(){return this.stringValue},e.FOR_BITS=new Map,e.FOR_VALUE=new Map,e.TERMINATOR=new e(n.TERMINATOR,"TERMINATOR",Int32Array.from([0,0,0]),0),e.NUMERIC=new e(n.NUMERIC,"NUMERIC",Int32Array.from([10,12,14]),1),e.ALPHANUMERIC=new e(n.ALPHANUMERIC,"ALPHANUMERIC",Int32Array.from([9,11,13]),2),e.STRUCTURED_APPEND=new e(n.STRUCTURED_APPEND,"STRUCTURED_APPEND",Int32Array.from([0,0,0]),3),e.BYTE=new e(n.BYTE,"BYTE",Int32Array.from([8,16,16]),4),e.ECI=new e(n.ECI,"ECI",Int32Array.from([0,0,0]),7),e.KANJI=new e(n.KANJI,"KANJI",Int32Array.from([8,10,12]),8),e.FNC1_FIRST_POSITION=new e(n.FNC1_FIRST_POSITION,"FNC1_FIRST_POSITION",Int32Array.from([0,0,0]),5),e.FNC1_SECOND_POSITION=new e(n.FNC1_SECOND_POSITION,"FNC1_SECOND_POSITION",Int32Array.from([0,0,0]),9),e.HANZI=new e(n.HANZI,"HANZI",Int32Array.from([8,10,12]),13),e}()},35168:(e,t,r)=>{r.d(t,{A:()=>y});var n=r(27217),o=r(22868),i=r(59612),a=r(55192),s=r(29477),f=r(26143),u=r(21876),l=r(85808),w=r(52771),h=r(6974),c=r(6537),d=r(63623),A=function(){function e(e,t){this.dataBytes=e,this.errorCorrectionBytes=t}return e.prototype.getDataBytes=function(){return this.dataBytes},e.prototype.getErrorCorrectionBytes=function(){return this.errorCorrectionBytes},e}(),g=r(93682),p=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};let y=function(){function e(){}return e.calculateMaskPenalty=function(e){return l.A.applyMaskPenaltyRule1(e)+l.A.applyMaskPenaltyRule2(e)+l.A.applyMaskPenaltyRule3(e)+l.A.applyMaskPenaltyRule4(e)},e.encode=function(t,r,a){void 0===a&&(a=null);var s,l=e.DEFAULT_BYTE_MODE_ENCODING,d=null!==a&&void 0!==a.get(n.A.CHARACTER_SET);d&&(l=a.get(n.A.CHARACTER_SET).toString());var A=this.chooseMode(t,l),p=new o.A;if(A===f.A.BYTE&&(d||e.DEFAULT_BYTE_MODE_ENCODING!==l)){var y=i.A.getCharacterSetECIByName(l);void 0!==y&&this.appendECI(y,p)}this.appendModeInfo(A,p);var m=new o.A;if(this.appendBytes(t,A,m,l),null!==a&&void 0!==a.get(n.A.QR_VERSION)){var v=Number.parseInt(a.get(n.A.QR_VERSION).toString(),10);s=u.A.getVersionForNumber(v);var I=this.calculateBitsNeeded(A,p,m,s);if(!this.willFit(I,s,r))throw new g.A("Data too big for requested version")}else s=this.recommendVersion(r,A,p,m);var C=new o.A;C.appendBitArray(p);var M=A===f.A.BYTE?m.getSizeInBytes():t.length;this.appendLengthInfo(M,s,A,C),C.appendBitArray(m);var N=s.getECBlocksForLevel(r),b=s.getTotalCodewords()-N.getTotalECCodewords();this.terminateBits(b,C);var E=this.interleaveWithECBytes(C,s.getTotalCodewords(),b,N.getNumBlocks()),S=new h.A;S.setECLevel(r),S.setMode(A),S.setVersion(s);var _=s.getDimensionForVersion(),B=new w.A(_,_),T=this.chooseMaskPattern(E,r,s,B);return S.setMaskPattern(T),c.A.buildMatrix(E,r,s,T,B),S.setMatrix(B),S},e.recommendVersion=function(e,t,r,n){var o=this.calculateBitsNeeded(t,r,n,u.A.getVersionForNumber(1)),i=this.chooseVersion(o,e),a=this.calculateBitsNeeded(t,r,n,i);return this.chooseVersion(a,e)},e.calculateBitsNeeded=function(e,t,r,n){return t.getSize()+e.getCharacterCountBits(n)+r.getSize()},e.getAlphanumericCode=function(t){return t<e.ALPHANUMERIC_TABLE.length?e.ALPHANUMERIC_TABLE[t]:-1},e.chooseMode=function(t,r){if(void 0===r&&(r=null),i.A.SJIS.getName()===r&&this.isOnlyDoubleByteKanji(t))return f.A.KANJI;for(var n=!1,o=!1,a=0,s=t.length;a<s;++a){var u=t.charAt(a);if(e.isDigit(u))n=!0;else{if(-1===this.getAlphanumericCode(u.charCodeAt(0)))return f.A.BYTE;o=!0}}return o?f.A.ALPHANUMERIC:n?f.A.NUMERIC:f.A.BYTE},e.isOnlyDoubleByteKanji=function(e){try{t=d.A.encode(e,i.A.SJIS)}catch(e){return!1}var t,r=t.length;if(r%2!=0)return!1;for(var n=0;n<r;n+=2){var o=255&t[n];if((o<129||o>159)&&(o<224||o>235))return!1}return!0},e.chooseMaskPattern=function(e,t,r,n){for(var o=Number.MAX_SAFE_INTEGER,i=-1,a=0;a<h.A.NUM_MASK_PATTERNS;a++){c.A.buildMatrix(e,t,r,a,n);var s=this.calculateMaskPenalty(n);s<o&&(o=s,i=a)}return i},e.chooseVersion=function(t,r){for(var n=1;n<=40;n++){var o=u.A.getVersionForNumber(n);if(e.willFit(t,o,r))return o}throw new g.A("Data too big")},e.willFit=function(e,t,r){return t.getTotalCodewords()-t.getECBlocksForLevel(r).getTotalECCodewords()>=(e+7)/8},e.terminateBits=function(e,t){var r=8*e;if(t.getSize()>r)throw new g.A("data bits cannot fit in the QR Code"+t.getSize()+" > "+r);for(var n=0;n<4&&t.getSize()<r;++n)t.appendBit(!1);var o=7&t.getSize();if(o>0)for(var n=o;n<8;n++)t.appendBit(!1);for(var i=e-t.getSizeInBytes(),n=0;n<i;++n)t.appendBits((1&n)==0?236:17,8);if(t.getSize()!==r)throw new g.A("Bits size does not equal capacity")},e.getNumDataBytesAndNumECBytesForBlockID=function(e,t,r,n,o,i){if(n>=r)throw new g.A("Block ID too large");var a=e%r,s=r-a,f=Math.floor(e/r),u=Math.floor(t/r),l=u+1,w=f-u,h=f+1-l;if(w!==h)throw new g.A("EC bytes mismatch");if(r!==s+a)throw new g.A("RS blocks mismatch");if(e!==(u+w)*s+(l+h)*a)throw new g.A("Total bytes mismatch");n<s?(o[0]=u,i[0]=w):(o[0]=l,i[0]=h)},e.interleaveWithECBytes=function(t,r,n,i){if(t.getSizeInBytes()!==n)throw new g.A("Number of bits and data bytes does not match");for(var a,s,f,u,l=0,w=0,h=0,c=[],d=0;d<i;++d){var y=new Int32Array(1),m=new Int32Array(1);e.getNumDataBytesAndNumECBytesForBlockID(r,n,i,d,y,m);var v=y[0],I=new Uint8Array(v);t.toBytes(8*l,I,0,v);var C=e.generateECBytes(I,m[0]);c.push(new A(I,C)),w=Math.max(w,v),h=Math.max(h,C.length),l+=y[0]}if(n!==l)throw new g.A("Data bytes does not match offset");for(var M=new o.A,d=0;d<w;++d)try{for(var N=(a=void 0,p(c)),b=N.next();!b.done;b=N.next()){var E=b.value,I=E.getDataBytes();d<I.length&&M.appendBits(I[d],8)}}catch(e){a={error:e}}finally{try{b&&!b.done&&(s=N.return)&&s.call(N)}finally{if(a)throw a.error}}for(var d=0;d<h;++d)try{for(var S=(f=void 0,p(c)),_=S.next();!_.done;_=S.next()){var E=_.value,C=E.getErrorCorrectionBytes();d<C.length&&M.appendBits(C[d],8)}}catch(e){f={error:e}}finally{try{_&&!_.done&&(u=S.return)&&u.call(S)}finally{if(f)throw f.error}}if(r!==M.getSizeInBytes())throw new g.A("Interleaving error: "+r+" and "+M.getSizeInBytes()+" differ.");return M},e.generateECBytes=function(e,t){for(var r=e.length,n=new Int32Array(r+t),o=0;o<r;o++)n[o]=255&e[o];new s.A(a.A.QR_CODE_FIELD_256).encode(n,t);for(var i=new Uint8Array(t),o=0;o<t;o++)i[o]=n[r+o];return i},e.appendModeInfo=function(e,t){t.appendBits(e.getBits(),4)},e.appendLengthInfo=function(e,t,r,n){var o=r.getCharacterCountBits(t);if(e>=1<<o)throw new g.A(e+" is bigger than "+((1<<o)-1));n.appendBits(e,o)},e.appendBytes=function(t,r,n,o){switch(r){case f.A.NUMERIC:e.appendNumericBytes(t,n);break;case f.A.ALPHANUMERIC:e.appendAlphanumericBytes(t,n);break;case f.A.BYTE:e.append8BitBytes(t,n,o);break;case f.A.KANJI:e.appendKanjiBytes(t,n);break;default:throw new g.A("Invalid mode: "+r)}},e.getDigit=function(e){return e.charCodeAt(0)-48},e.isDigit=function(t){var r=e.getDigit(t);return r>=0&&r<=9},e.appendNumericBytes=function(t,r){for(var n=t.length,o=0;o<n;){var i=e.getDigit(t.charAt(o));if(o+2<n){var a=e.getDigit(t.charAt(o+1)),s=e.getDigit(t.charAt(o+2));r.appendBits(100*i+10*a+s,10),o+=3}else if(o+1<n){var a=e.getDigit(t.charAt(o+1));r.appendBits(10*i+a,7),o+=2}else r.appendBits(i,4),o++}},e.appendAlphanumericBytes=function(t,r){for(var n=t.length,o=0;o<n;){var i=e.getAlphanumericCode(t.charCodeAt(o));if(-1===i)throw new g.A;if(o+1<n){var a=e.getAlphanumericCode(t.charCodeAt(o+1));if(-1===a)throw new g.A;r.appendBits(45*i+a,11),o+=2}else r.appendBits(i,6),o++}},e.append8BitBytes=function(e,t,r){var n;try{n=d.A.encode(e,r)}catch(e){throw new g.A(e)}for(var o=0,i=n.length;o!==i;o++){var a=n[o];t.appendBits(a,8)}},e.appendKanjiBytes=function(e,t){try{r=d.A.encode(e,i.A.SJIS)}catch(e){throw new g.A(e)}for(var r,n=r.length,o=0;o<n;o+=2){var a=(255&r[o])<<8|255&r[o+1],s=-1;if(a>=33088&&a<=40956?s=a-33088:a>=57408&&a<=60351&&(s=a-49472),-1===s)throw new g.A("Invalid byte sequence");var f=(s>>8)*192+(255&s);t.appendBits(f,13)}},e.appendECI=function(e,t){t.appendBits(f.A.ECI.getBits(),4),t.appendBits(e.getValue(),8)},e.ALPHANUMERIC_TABLE=Int32Array.from([-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,36,-1,-1,-1,37,38,-1,-1,-1,-1,39,40,-1,41,42,43,0,1,2,3,4,5,6,7,8,9,44,-1,-1,-1,-1,-1,-1,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,-1,-1,-1,-1,-1]),e.DEFAULT_BYTE_MODE_ENCODING=i.A.UTF8.getName(),e}()},41094:(e,t,r)=>{r.d(t,{A:()=>w});var n=r(85770),o=r(59612),i=r(55701),a=r(23510),s=r(71534),f=r(1933),u=r(63623),l=r(26143);let w=function(){function e(){}return e.decode=function(t,r,a,u){var w=new n.A(t),h=new f.A,c=[],d=-1,A=-1;try{var g=null,p=!1,y=void 0;do{if(4>w.available())y=l.A.TERMINATOR;else{var m=w.readBits(4);y=l.A.forBits(m)}switch(y){case l.A.TERMINATOR:break;case l.A.FNC1_FIRST_POSITION:case l.A.FNC1_SECOND_POSITION:p=!0;break;case l.A.STRUCTURED_APPEND:if(16>w.available())throw new s.A;d=w.readBits(8),A=w.readBits(8);break;case l.A.ECI:var v=e.parseECIValue(w);if(g=o.A.getCharacterSetECIByValue(v),null===g)throw new s.A;break;case l.A.HANZI:var I=w.readBits(4),C=w.readBits(y.getCharacterCountBits(r));I===e.GB2312_SUBSET&&e.decodeHanziSegment(w,h,C);break;default:var M=w.readBits(y.getCharacterCountBits(r));switch(y){case l.A.NUMERIC:e.decodeNumericSegment(w,h,M);break;case l.A.ALPHANUMERIC:e.decodeAlphanumericSegment(w,h,M,p);break;case l.A.BYTE:e.decodeByteSegment(w,h,M,g,c,u);break;case l.A.KANJI:e.decodeKanjiSegment(w,h,M);break;default:throw new s.A}}}while(y!==l.A.TERMINATOR)}catch(e){throw new s.A}return new i.A(t,h.toString(),0===c.length?null:c,null===a?null:a.toString(),d,A)},e.decodeHanziSegment=function(e,t,r){if(13*r>e.available())throw new s.A;for(var n=new Uint8Array(2*r),o=0;r>0;){var i=e.readBits(13),f=i/96<<8|i%96;f<959?f+=41377:f+=42657,n[o]=f>>8&255,n[o+1]=255&f,o+=2,r--}try{t.append(u.A.decode(n,a.A.GB2312))}catch(e){throw new s.A(e)}},e.decodeKanjiSegment=function(e,t,r){if(13*r>e.available())throw new s.A;for(var n=new Uint8Array(2*r),o=0;r>0;){var i=e.readBits(13),f=i/192<<8|i%192;f<7936?f+=33088:f+=49472,n[o]=f>>8,n[o+1]=f,o+=2,r--}try{t.append(u.A.decode(n,a.A.SHIFT_JIS))}catch(e){throw new s.A(e)}},e.decodeByteSegment=function(e,t,r,n,o,i){if(8*r>e.available())throw new s.A;for(var f,l=new Uint8Array(r),w=0;w<r;w++)l[w]=e.readBits(8);f=null===n?a.A.guessEncoding(l,i):n.getName();try{t.append(u.A.decode(l,f))}catch(e){throw new s.A(e)}o.push(l)},e.toAlphaNumericChar=function(t){if(t>=e.ALPHANUMERIC_CHARS.length)throw new s.A;return e.ALPHANUMERIC_CHARS[t]},e.decodeAlphanumericSegment=function(t,r,n,o){for(var i=r.length();n>1;){if(11>t.available())throw new s.A;var a=t.readBits(11);r.append(e.toAlphaNumericChar(Math.floor(a/45))),r.append(e.toAlphaNumericChar(a%45)),n-=2}if(1===n){if(6>t.available())throw new s.A;r.append(e.toAlphaNumericChar(t.readBits(6)))}if(o)for(var f=i;f<r.length();f++)"%"===r.charAt(f)&&(f<r.length()-1&&"%"===r.charAt(f+1)?r.deleteCharAt(f+1):r.setCharAt(f,"\x1d"))},e.decodeNumericSegment=function(t,r,n){for(;n>=3;){if(10>t.available())throw new s.A;var o=t.readBits(10);if(o>=1e3)throw new s.A;r.append(e.toAlphaNumericChar(Math.floor(o/100))),r.append(e.toAlphaNumericChar(Math.floor(o/10)%10)),r.append(e.toAlphaNumericChar(o%10)),n-=3}if(2===n){if(7>t.available())throw new s.A;var i=t.readBits(7);if(i>=100)throw new s.A;r.append(e.toAlphaNumericChar(Math.floor(i/10))),r.append(e.toAlphaNumericChar(i%10))}else if(1===n){if(4>t.available())throw new s.A;var a=t.readBits(4);if(a>=10)throw new s.A;r.append(e.toAlphaNumericChar(a))}},e.parseECIValue=function(e){var t=e.readBits(8);if((128&t)==0)return 127&t;if((192&t)==128)return(63&t)<<8|e.readBits(8);if((224&t)==192)return(31&t)<<16|e.readBits(16);throw new s.A},e.ALPHANUMERIC_CHARS="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",e.GB2312_SUBSET=1,e}()},52771:(e,t,r)=>{r.d(t,{A:()=>a});var n=r(10077),o=r(1933),i=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};let a=function(){function e(e,t){this.width=e,this.height=t;for(var r=Array(t),n=0;n!==t;n++)r[n]=new Uint8Array(e);this.bytes=r}return e.prototype.getHeight=function(){return this.height},e.prototype.getWidth=function(){return this.width},e.prototype.get=function(e,t){return this.bytes[t][e]},e.prototype.getArray=function(){return this.bytes},e.prototype.setNumber=function(e,t,r){this.bytes[t][e]=r},e.prototype.setBoolean=function(e,t,r){this.bytes[t][e]=+!!r},e.prototype.clear=function(e){var t,r;try{for(var o=i(this.bytes),a=o.next();!a.done;a=o.next()){var s=a.value;n.A.fill(s,e)}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}},e.prototype.equals=function(t){if(!(t instanceof e)||this.width!==t.width||this.height!==t.height)return!1;for(var r=0,n=this.height;r<n;++r)for(var o=this.bytes[r],i=t.bytes[r],a=0,s=this.width;a<s;++a)if(o[a]!==i[a])return!1;return!0},e.prototype.toString=function(){for(var e=new o.A,t=0,r=this.height;t<r;++t){for(var n=this.bytes[t],i=0,a=this.width;i<a;++i)switch(n[i]){case 0:e.append(" 0");break;case 1:e.append(" 1");break;default:e.append("  ")}e.append("\n")}return e.toString()},e}()},61536:(e,t,r)=>{r.d(t,{A:()=>a});var n,o=r(19106),i=r(38988);!function(e){e[e.L=0]="L",e[e.M=1]="M",e[e.Q=2]="Q",e[e.H=3]="H"}(n||(n={}));let a=function(){function e(t,r,n){this.value=t,this.stringValue=r,this.bits=n,e.FOR_BITS.set(n,this),e.FOR_VALUE.set(t,this)}return e.prototype.getValue=function(){return this.value},e.prototype.getBits=function(){return this.bits},e.fromString=function(t){switch(t){case"L":return e.L;case"M":return e.M;case"Q":return e.Q;case"H":return e.H;default:throw new o.A(t+"not available")}},e.prototype.toString=function(){return this.stringValue},e.prototype.equals=function(t){return t instanceof e&&this.value===t.value},e.forBits=function(t){if(t<0||t>=e.FOR_BITS.size)throw new i.A;return e.FOR_BITS.get(t)},e.FOR_BITS=new Map,e.FOR_VALUE=new Map,e.L=new e(n.L,"L",1),e.M=new e(n.M,"M",0),e.Q=new e(n.Q,"Q",3),e.H=new e(n.H,"H",2),e}()},78160:(e,t,r)=>{var n;r.d(t,{A:()=>o}),function(e){e[e.DATA_MASK_000=0]="DATA_MASK_000",e[e.DATA_MASK_001=1]="DATA_MASK_001",e[e.DATA_MASK_010=2]="DATA_MASK_010",e[e.DATA_MASK_011=3]="DATA_MASK_011",e[e.DATA_MASK_100=4]="DATA_MASK_100",e[e.DATA_MASK_101=5]="DATA_MASK_101",e[e.DATA_MASK_110=6]="DATA_MASK_110",e[e.DATA_MASK_111=7]="DATA_MASK_111"}(n||(n={}));let o=function(){function e(e,t){this.value=e,this.isMasked=t}return e.prototype.unmaskBitMatrix=function(e,t){for(var r=0;r<t;r++)for(var n=0;n<t;n++)this.isMasked(r,n)&&e.flip(n,r)},e.values=new Map([[n.DATA_MASK_000,new e(n.DATA_MASK_000,function(e,t){return(e+t&1)==0})],[n.DATA_MASK_001,new e(n.DATA_MASK_001,function(e,t){return(1&e)==0})],[n.DATA_MASK_010,new e(n.DATA_MASK_010,function(e,t){return t%3==0})],[n.DATA_MASK_011,new e(n.DATA_MASK_011,function(e,t){return(e+t)%3==0})],[n.DATA_MASK_100,new e(n.DATA_MASK_100,function(e,t){return(Math.floor(e/2)+Math.floor(t/3)&1)==0})],[n.DATA_MASK_101,new e(n.DATA_MASK_101,function(e,t){return e*t%6==0})],[n.DATA_MASK_110,new e(n.DATA_MASK_110,function(e,t){return e*t%6<3})],[n.DATA_MASK_111,new e(n.DATA_MASK_111,function(e,t){return(e+t+e*t%3&1)==0})]]),e}()},78903:(e,t,r)=>{r.d(t,{A:()=>a});var n=r(61536),o=r(63479),i=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};let a=function(){function e(e){this.errorCorrectionLevel=n.A.forBits(e>>3&3),this.dataMask=7&e}return e.numBitsDiffering=function(e,t){return o.A.bitCount(e^t)},e.decodeFormatInformation=function(t,r){var n=e.doDecodeFormatInformation(t,r);return null!==n?n:e.doDecodeFormatInformation(t^e.FORMAT_INFO_MASK_QR,r^e.FORMAT_INFO_MASK_QR)},e.doDecodeFormatInformation=function(t,r){var n,o,a=Number.MAX_SAFE_INTEGER,s=0;try{for(var f=i(e.FORMAT_INFO_DECODE_LOOKUP),u=f.next();!u.done;u=f.next()){var l=u.value,w=l[0];if(w===t||w===r)return new e(l[1]);var h=e.numBitsDiffering(t,w);h<a&&(s=l[1],a=h),t!==r&&(h=e.numBitsDiffering(r,w))<a&&(s=l[1],a=h)}}catch(e){n={error:e}}finally{try{u&&!u.done&&(o=f.return)&&o.call(f)}finally{if(n)throw n.error}}return a<=3?new e(s):null},e.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},e.prototype.getDataMask=function(){return this.dataMask},e.prototype.hashCode=function(){return this.errorCorrectionLevel.getBits()<<3|this.dataMask},e.prototype.equals=function(t){return t instanceof e&&this.errorCorrectionLevel===t.errorCorrectionLevel&&this.dataMask===t.dataMask},e.FORMAT_INFO_MASK_QR=21522,e.FORMAT_INFO_DECODE_LOOKUP=[Int32Array.from([21522,0]),Int32Array.from([20773,1]),Int32Array.from([24188,2]),Int32Array.from([23371,3]),Int32Array.from([17913,4]),Int32Array.from([16590,5]),Int32Array.from([20375,6]),Int32Array.from([19104,7]),Int32Array.from([30660,8]),Int32Array.from([29427,9]),Int32Array.from([32170,10]),Int32Array.from([30877,11]),Int32Array.from([26159,12]),Int32Array.from([25368,13]),Int32Array.from([27713,14]),Int32Array.from([26998,15]),Int32Array.from([5769,16]),Int32Array.from([5054,17]),Int32Array.from([7399,18]),Int32Array.from([6608,19]),Int32Array.from([1890,20]),Int32Array.from([597,21]),Int32Array.from([3340,22]),Int32Array.from([2107,23]),Int32Array.from([13663,24]),Int32Array.from([12392,25]),Int32Array.from([16177,26]),Int32Array.from([14854,27]),Int32Array.from([9396,28]),Int32Array.from([8579,29]),Int32Array.from([11994,30]),Int32Array.from([11245,31])],e}()},85469:(e,t,r)=>{r.d(t,{A:()=>L});var n=r(25969),o=r(10782),i=r(79417),a=r(438),s=r(69071),f=r(43358),u=r(66950),l=r(55192),w=r(60109),h=r(21876),c=r(78903),d=r(78160),A=r(71534),g=function(){function e(e){var t=e.getHeight();if(t<21||(3&t)!=1)throw new A.A;this.bitMatrix=e}return e.prototype.readFormatInformation=function(){if(null!==this.parsedFormatInfo&&void 0!==this.parsedFormatInfo)return this.parsedFormatInfo;for(var e=0,t=0;t<6;t++)e=this.copyBit(t,8,e);e=this.copyBit(7,8,e),e=this.copyBit(8,8,e),e=this.copyBit(8,7,e);for(var r=5;r>=0;r--)e=this.copyBit(8,r,e);for(var n=this.bitMatrix.getHeight(),o=0,i=n-7,r=n-1;r>=i;r--)o=this.copyBit(8,r,o);for(var t=n-8;t<n;t++)o=this.copyBit(t,8,o);if(this.parsedFormatInfo=c.A.decodeFormatInformation(e,o),null!==this.parsedFormatInfo)return this.parsedFormatInfo;throw new A.A},e.prototype.readVersion=function(){if(null!==this.parsedVersion&&void 0!==this.parsedVersion)return this.parsedVersion;var e=this.bitMatrix.getHeight(),t=Math.floor((e-17)/4);if(t<=6)return h.A.getVersionForNumber(t);for(var r=0,n=e-11,o=5;o>=0;o--)for(var i=e-9;i>=n;i--)r=this.copyBit(i,o,r);var a=h.A.decodeVersionInformation(r);if(null!==a&&a.getDimensionForVersion()===e)return this.parsedVersion=a,a;r=0;for(var i=5;i>=0;i--)for(var o=e-9;o>=n;o--)r=this.copyBit(i,o,r);if(null!==(a=h.A.decodeVersionInformation(r))&&a.getDimensionForVersion()===e)return this.parsedVersion=a,a;throw new A.A},e.prototype.copyBit=function(e,t,r){return(this.isMirror?this.bitMatrix.get(t,e):this.bitMatrix.get(e,t))?r<<1|1:r<<1},e.prototype.readCodewords=function(){var e=this.readFormatInformation(),t=this.readVersion(),r=d.A.values.get(e.getDataMask()),n=this.bitMatrix.getHeight();r.unmaskBitMatrix(this.bitMatrix,n);for(var o=t.buildFunctionPattern(),i=!0,a=new Uint8Array(t.getTotalCodewords()),s=0,f=0,u=0,l=n-1;l>0;l-=2){6===l&&l--;for(var w=0;w<n;w++)for(var h=i?n-1-w:w,c=0;c<2;c++)o.get(l-c,h)||(u++,f<<=1,this.bitMatrix.get(l-c,h)&&(f|=1),8===u&&(a[s++]=f,u=0,f=0));i=!i}if(s!==t.getTotalCodewords())throw new A.A;return a},e.prototype.remask=function(){if(null!==this.parsedFormatInfo){var e=d.A.values.get(this.parsedFormatInfo.getDataMask()),t=this.bitMatrix.getHeight();e.unmaskBitMatrix(this.bitMatrix,t)}},e.prototype.setMirror=function(e){this.parsedVersion=null,this.parsedFormatInfo=null,this.isMirror=e},e.prototype.mirror=function(){for(var e=this.bitMatrix,t=0,r=e.getWidth();t<r;t++)for(var n=t+1,o=e.getHeight();n<o;n++)e.get(t,n)!==e.get(n,t)&&(e.flip(n,t),e.flip(t,n))},e}(),p=r(38988),y=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},m=function(){function e(e,t){this.numDataCodewords=e,this.codewords=t}return e.getDataBlocks=function(t,r,n){if(t.length!==r.getTotalCodewords())throw new p.A;var o,i,a,s,f=r.getECBlocksForLevel(n),u=0,l=f.getECBlocks();try{for(var w=y(l),h=w.next();!h.done;h=w.next()){var c=h.value;u+=c.getCount()}}catch(e){o={error:e}}finally{try{h&&!h.done&&(i=w.return)&&i.call(w)}finally{if(o)throw o.error}}var d=Array(u),A=0;try{for(var g=y(l),m=g.next();!m.done;m=g.next())for(var c=m.value,v=0;v<c.getCount();v++){var I=c.getDataCodewords(),C=f.getECCodewordsPerBlock()+I;d[A++]=new e(I,new Uint8Array(C))}}catch(e){a={error:e}}finally{try{m&&!m.done&&(s=g.return)&&s.call(g)}finally{if(a)throw a.error}}for(var M=d[0].codewords.length,N=d.length-1;N>=0&&d[N].codewords.length!==M;)N--;N++;for(var b=M-f.getECCodewordsPerBlock(),E=0,v=0;v<b;v++)for(var S=0;S<A;S++)d[S].codewords[v]=t[E++];for(var S=N;S<A;S++)d[S].codewords[b]=t[E++];for(var _=d[0].codewords.length,v=b;v<_;v++)for(var S=0;S<A;S++){var B=S<N?v:v+1;d[S].codewords[B]=t[E++]}return d},e.prototype.getNumDataCodewords=function(){return this.numDataCodewords},e.prototype.getCodewords=function(){return this.codewords},e}(),v=r(41094),I=function(){function e(e){this.mirrored=e}return e.prototype.isMirrored=function(){return this.mirrored},e.prototype.applyMirroredCorrection=function(e){if(this.mirrored&&null!==e&&!(e.length<3)){var t=e[0];e[0]=e[2],e[2]=t}},e}(),C=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},M=function(){function e(){this.rsDecoder=new w.A(l.A.QR_CODE_FIELD_256)}return e.prototype.decodeBooleanArray=function(e,t){return this.decodeBitMatrix(o.A.parseFromBooleanArray(e),t)},e.prototype.decodeBitMatrix=function(e,t){var r=new g(e),n=null;try{return this.decodeBitMatrixParser(r,t)}catch(e){n=e}try{r.remask(),r.setMirror(!0),r.readVersion(),r.readFormatInformation(),r.mirror();var o=this.decodeBitMatrixParser(r,t);return o.setOther(new I(!0)),o}catch(e){if(null!==n)throw n;throw e}},e.prototype.decodeBitMatrixParser=function(e,t){var r,n,o,i,a=e.readVersion(),s=e.readFormatInformation().getErrorCorrectionLevel(),f=e.readCodewords(),u=m.getDataBlocks(f,a,s),l=0;try{for(var w=C(u),h=w.next();!h.done;h=w.next()){var c=h.value;l+=c.getNumDataCodewords()}}catch(e){r={error:e}}finally{try{h&&!h.done&&(n=w.return)&&n.call(w)}finally{if(r)throw r.error}}var d=new Uint8Array(l),A=0;try{for(var g=C(u),p=g.next();!p.done;p=g.next()){var c=p.value,y=c.getCodewords(),I=c.getNumDataCodewords();this.correctErrors(y,I);for(var M=0;M<I;M++)d[A++]=y[M]}}catch(e){o={error:e}}finally{try{p&&!p.done&&(i=g.return)&&i.call(g)}finally{if(o)throw o.error}}return v.A.decode(d,a,s,t)},e.prototype.correctErrors=function(e,t){var r=new Int32Array(e);try{this.rsDecoder.decode(r,e.length-t)}catch(e){throw new u.A}for(var n=0;n<t;n++)e[n]=r[n]},e}(),N=r(79886),b=r(56451),E=r(20367),S=r(39798),_=r(56595),B=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),T=function(e){function t(t,r,n){var o=e.call(this,t,r)||this;return o.estimatedModuleSize=n,o}return B(t,e),t.prototype.aboutEquals=function(e,t,r){if(Math.abs(t-this.getY())<=e&&Math.abs(r-this.getX())<=e){var n=Math.abs(e-this.estimatedModuleSize);return n<=1||n<=this.estimatedModuleSize}return!1},t.prototype.combineEstimate=function(e,r,n){return new t((this.getX()+r)/2,(this.getY()+e)/2,(this.estimatedModuleSize+n)/2)},t}(_.A),P=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},R=function(){function e(e,t,r,n,o,i,a){this.image=e,this.startX=t,this.startY=r,this.width=n,this.height=o,this.moduleSize=i,this.resultPointCallback=a,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(3)}return e.prototype.find=function(){for(var e=this.startX,t=this.height,r=e+this.width,n=this.startY+t/2,o=new Int32Array(3),i=this.image,s=0;s<t;s++){var f=n+((1&s)==0?Math.floor((s+1)/2):-Math.floor((s+1)/2));o[0]=0,o[1]=0,o[2]=0;for(var u=e;u<r&&!i.get(u,f);)u++;for(var l=0;u<r;){if(i.get(u,f))if(1===l)o[1]++;else if(2===l){if(this.foundPatternCross(o)){var w=this.handlePossibleCenter(o,f,u);if(null!==w)return w}o[0]=o[2],o[1]=1,o[2]=0,l=1}else o[++l]++;else 1===l&&l++,o[l]++;u++}if(this.foundPatternCross(o)){var w=this.handlePossibleCenter(o,f,r);if(null!==w)return w}}if(0!==this.possibleCenters.length)return this.possibleCenters[0];throw new a.A},e.centerFromEnd=function(e,t){return t-e[2]-e[1]/2},e.prototype.foundPatternCross=function(e){for(var t=this.moduleSize,r=t/2,n=0;n<3;n++)if(Math.abs(t-e[n])>=r)return!1;return!0},e.prototype.crossCheckVertical=function(t,r,n,o){var i=this.image,a=i.getHeight(),s=this.crossCheckStateCount;s[0]=0,s[1]=0,s[2]=0;for(var f=t;f>=0&&i.get(r,f)&&s[1]<=n;)s[1]++,f--;if(f<0||s[1]>n)return NaN;for(;f>=0&&!i.get(r,f)&&s[0]<=n;)s[0]++,f--;if(s[0]>n)return NaN;for(f=t+1;f<a&&i.get(r,f)&&s[1]<=n;)s[1]++,f++;if(f===a||s[1]>n)return NaN;for(;f<a&&!i.get(r,f)&&s[2]<=n;)s[2]++,f++;return s[2]>n||5*Math.abs(s[0]+s[1]+s[2]-o)>=2*o?NaN:this.foundPatternCross(s)?e.centerFromEnd(s,f):NaN},e.prototype.handlePossibleCenter=function(t,r,n){var o,i,a=t[0]+t[1]+t[2],s=e.centerFromEnd(t,n),f=this.crossCheckVertical(r,s,2*t[1],a);if(!isNaN(f)){var u=(t[0]+t[1]+t[2])/3;try{for(var l=P(this.possibleCenters),w=l.next();!w.done;w=l.next()){var h=w.value;if(h.aboutEquals(u,f,s))return h.combineEstimate(f,s,u)}}catch(e){o={error:e}}finally{try{w&&!w.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}var c=new T(s,f,u);this.possibleCenters.push(c),null!==this.resultPointCallback&&void 0!==this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(c)}return null},e}(),O=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),D=function(e){function t(t,r,n,o){var i=e.call(this,t,r)||this;return i.estimatedModuleSize=n,i.count=o,void 0===o&&(i.count=1),i}return O(t,e),t.prototype.getEstimatedModuleSize=function(){return this.estimatedModuleSize},t.prototype.getCount=function(){return this.count},t.prototype.aboutEquals=function(e,t,r){if(Math.abs(t-this.getY())<=e&&Math.abs(r-this.getX())<=e){var n=Math.abs(e-this.estimatedModuleSize);return n<=1||n<=this.estimatedModuleSize}return!1},t.prototype.combineEstimate=function(e,r,n){var o=this.count+1;return new t((this.count*this.getX()+r)/o,(this.count*this.getY()+e)/o,(this.count*this.estimatedModuleSize+n)/o,o)},t}(_.A),k=function(){function e(e){this.bottomLeft=e[0],this.topLeft=e[1],this.topRight=e[2]}return e.prototype.getBottomLeft=function(){return this.bottomLeft},e.prototype.getTopLeft=function(){return this.topLeft},e.prototype.getTopRight=function(){return this.topRight},e}(),F=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},x=function(){function e(e,t){this.image=e,this.resultPointCallback=t,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(5),this.resultPointCallback=t}return e.prototype.getImage=function(){return this.image},e.prototype.getPossibleCenters=function(){return this.possibleCenters},e.prototype.find=function(t){var r=null!=t&&void 0!==t.get(i.A.TRY_HARDER),n=null!=t&&void 0!==t.get(i.A.PURE_BARCODE),o=this.image,a=o.getHeight(),s=o.getWidth(),f=Math.floor(3*a/(4*e.MAX_MODULES));(f<e.MIN_SKIP||r)&&(f=e.MIN_SKIP);for(var u=!1,l=new Int32Array(5),w=f-1;w<a&&!u;w+=f){l[0]=0,l[1]=0,l[2]=0,l[3]=0,l[4]=0;for(var h=0,c=0;c<s;c++)if(o.get(c,w))(1&h)==1&&h++,l[h]++;else if((1&h)==0)if(4===h)if(e.foundPatternCross(l)){var d=this.handlePossibleCenter(l,w,c,n);if(!0===d)if(f=2,!0===this.hasSkipped)u=this.haveMultiplyConfirmedCenters();else{var A=this.findRowSkip();A>l[2]&&(w+=A-l[2]-f,c=s-1)}else{l[0]=l[2],l[1]=l[3],l[2]=l[4],l[3]=1,l[4]=0,h=3;continue}h=0,l[0]=0,l[1]=0,l[2]=0,l[3]=0,l[4]=0}else l[0]=l[2],l[1]=l[3],l[2]=l[4],l[3]=1,l[4]=0,h=3;else l[++h]++;else l[h]++;if(e.foundPatternCross(l)){var d=this.handlePossibleCenter(l,w,s,n);!0===d&&(f=l[0],this.hasSkipped&&(u=this.haveMultiplyConfirmedCenters()))}}var g=this.selectBestPatterns();return _.A.orderBestPatterns(g),new k(g)},e.centerFromEnd=function(e,t){return t-e[4]-e[3]-e[2]/2},e.foundPatternCross=function(e){for(var t=0,r=0;r<5;r++){var n=e[r];if(0===n)return!1;t+=n}if(t<7)return!1;var o=t/7,i=o/2;return Math.abs(o-e[0])<i&&Math.abs(o-e[1])<i&&Math.abs(3*o-e[2])<3*i&&Math.abs(o-e[3])<i&&Math.abs(o-e[4])<i},e.prototype.getCrossCheckStateCount=function(){var e=this.crossCheckStateCount;return e[0]=0,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e},e.prototype.crossCheckDiagonal=function(t,r,n,o){for(var i=this.getCrossCheckStateCount(),a=0,s=this.image;t>=a&&r>=a&&s.get(r-a,t-a);)i[2]++,a++;if(t<a||r<a)return!1;for(;t>=a&&r>=a&&!s.get(r-a,t-a)&&i[1]<=n;)i[1]++,a++;if(t<a||r<a||i[1]>n)return!1;for(;t>=a&&r>=a&&s.get(r-a,t-a)&&i[0]<=n;)i[0]++,a++;if(i[0]>n)return!1;var f=s.getHeight(),u=s.getWidth();for(a=1;t+a<f&&r+a<u&&s.get(r+a,t+a);)i[2]++,a++;if(t+a>=f||r+a>=u)return!1;for(;t+a<f&&r+a<u&&!s.get(r+a,t+a)&&i[3]<n;)i[3]++,a++;if(t+a>=f||r+a>=u||i[3]>=n)return!1;for(;t+a<f&&r+a<u&&s.get(r+a,t+a)&&i[4]<n;)i[4]++,a++;return!(i[4]>=n)&&Math.abs(i[0]+i[1]+i[2]+i[3]+i[4]-o)<2*o&&e.foundPatternCross(i)},e.prototype.crossCheckVertical=function(t,r,n,o){for(var i=this.image,a=i.getHeight(),s=this.getCrossCheckStateCount(),f=t;f>=0&&i.get(r,f);)s[2]++,f--;if(f<0)return NaN;for(;f>=0&&!i.get(r,f)&&s[1]<=n;)s[1]++,f--;if(f<0||s[1]>n)return NaN;for(;f>=0&&i.get(r,f)&&s[0]<=n;)s[0]++,f--;if(s[0]>n)return NaN;for(f=t+1;f<a&&i.get(r,f);)s[2]++,f++;if(f===a)return NaN;for(;f<a&&!i.get(r,f)&&s[3]<n;)s[3]++,f++;if(f===a||s[3]>=n)return NaN;for(;f<a&&i.get(r,f)&&s[4]<n;)s[4]++,f++;return s[4]>=n||5*Math.abs(s[0]+s[1]+s[2]+s[3]+s[4]-o)>=2*o?NaN:e.foundPatternCross(s)?e.centerFromEnd(s,f):NaN},e.prototype.crossCheckHorizontal=function(t,r,n,o){for(var i=this.image,a=i.getWidth(),s=this.getCrossCheckStateCount(),f=t;f>=0&&i.get(f,r);)s[2]++,f--;if(f<0)return NaN;for(;f>=0&&!i.get(f,r)&&s[1]<=n;)s[1]++,f--;if(f<0||s[1]>n)return NaN;for(;f>=0&&i.get(f,r)&&s[0]<=n;)s[0]++,f--;if(s[0]>n)return NaN;for(f=t+1;f<a&&i.get(f,r);)s[2]++,f++;if(f===a)return NaN;for(;f<a&&!i.get(f,r)&&s[3]<n;)s[3]++,f++;if(f===a||s[3]>=n)return NaN;for(;f<a&&i.get(f,r)&&s[4]<n;)s[4]++,f++;return s[4]>=n||5*Math.abs(s[0]+s[1]+s[2]+s[3]+s[4]-o)>=o?NaN:e.foundPatternCross(s)?e.centerFromEnd(s,f):NaN},e.prototype.handlePossibleCenter=function(t,r,n,o){var i=t[0]+t[1]+t[2]+t[3]+t[4],a=e.centerFromEnd(t,n),s=this.crossCheckVertical(r,Math.floor(a),t[2],i);if(!isNaN(s)&&!isNaN(a=this.crossCheckHorizontal(Math.floor(a),Math.floor(s),t[2],i))&&(!o||this.crossCheckDiagonal(Math.floor(s),Math.floor(a),t[2],i))){for(var f=i/7,u=!1,l=this.possibleCenters,w=0,h=l.length;w<h;w++){var c=l[w];if(c.aboutEquals(f,s,a)){l[w]=c.combineEstimate(s,a,f),u=!0;break}}if(!u){var d=new D(a,s,f);l.push(d),null!==this.resultPointCallback&&void 0!==this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(d)}return!0}return!1},e.prototype.findRowSkip=function(){if(this.possibleCenters.length<=1)return 0;var t,r,n=null;try{for(var o=F(this.possibleCenters),i=o.next();!i.done;i=o.next()){var a=i.value;if(a.getCount()>=e.CENTER_QUORUM)if(null!=n)return this.hasSkipped=!0,Math.floor((Math.abs(n.getX()-a.getX())-Math.abs(n.getY()-a.getY()))/2);else n=a}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return 0},e.prototype.haveMultiplyConfirmedCenters=function(){var t,r,n,o,i=0,a=0,s=this.possibleCenters.length;try{for(var f=F(this.possibleCenters),u=f.next();!u.done;u=f.next()){var l=u.value;l.getCount()>=e.CENTER_QUORUM&&(i++,a+=l.getEstimatedModuleSize())}}catch(e){t={error:e}}finally{try{u&&!u.done&&(r=f.return)&&r.call(f)}finally{if(t)throw t.error}}if(i<3)return!1;var w=a/s,h=0;try{for(var c=F(this.possibleCenters),d=c.next();!d.done;d=c.next()){var l=d.value;h+=Math.abs(l.getEstimatedModuleSize()-w)}}catch(e){n={error:e}}finally{try{d&&!d.done&&(o=c.return)&&o.call(c)}finally{if(n)throw n.error}}return h<=.05*a},e.prototype.selectBestPatterns=function(){var e,t,r,n,o,i=this.possibleCenters.length;if(i<3)throw new a.A;var s=this.possibleCenters;if(i>3){var f=0,u=0;try{for(var l=F(this.possibleCenters),w=l.next();!w.done;w=l.next()){var h=w.value.getEstimatedModuleSize();f+=h,u+=h*h}}catch(t){e={error:t}}finally{try{w&&!w.done&&(t=l.return)&&t.call(l)}finally{if(e)throw e.error}}o=f/i;var c=Math.sqrt(u/i-o*o);s.sort(function(e,t){var r=Math.abs(t.getEstimatedModuleSize()-o),n=Math.abs(e.getEstimatedModuleSize()-o);return r<n?-1:+(r>n)});for(var d=Math.max(.2*o,c),A=0;A<s.length&&s.length>3;A++)Math.abs(s[A].getEstimatedModuleSize()-o)>d&&(s.splice(A,1),A--)}if(s.length>3){var f=0;try{for(var g=F(s),p=g.next();!p.done;p=g.next()){var y=p.value;f+=y.getEstimatedModuleSize()}}catch(e){r={error:e}}finally{try{p&&!p.done&&(n=g.return)&&n.call(g)}finally{if(r)throw r.error}}o=f/s.length,s.sort(function(e,t){if(t.getCount()!==e.getCount())return t.getCount()-e.getCount();var r=Math.abs(t.getEstimatedModuleSize()-o),n=Math.abs(e.getEstimatedModuleSize()-o);return r<n?1:r>n?-1:0}),s.splice(3)}return[s[0],s[1],s[2]]},e.CENTER_QUORUM=2,e.MIN_SKIP=3,e.MAX_MODULES=57,e}(),V=function(){function e(e){this.image=e}return e.prototype.getImage=function(){return this.image},e.prototype.getResultPointCallback=function(){return this.resultPointCallback},e.prototype.detect=function(e){this.resultPointCallback=null==e?null:e.get(i.A.NEED_RESULT_POINT_CALLBACK);var t=new x(this.image,this.resultPointCallback).find(e);return this.processFinderPatternInfo(t)},e.prototype.processFinderPatternInfo=function(t){var r,n=t.getTopLeft(),o=t.getTopRight(),i=t.getBottomLeft(),s=this.calculateModuleSize(n,o,i);if(s<1)throw new a.A("No pattern found in proccess finder.");var f=e.computeDimension(n,o,i,s),u=h.A.getProvisionalVersionForDimension(f),l=u.getDimensionForVersion()-7,w=null;if(u.getAlignmentPatternCenters().length>0)for(var c=o.getX()-n.getX()+i.getX(),d=o.getY()-n.getY()+i.getY(),A=1-3/l,g=Math.floor(n.getX()+A*(c-n.getX())),p=Math.floor(n.getY()+A*(d-n.getY())),y=4;y<=16;y<<=1)try{w=this.findAlignmentInRegion(s,g,p,y);break}catch(e){if(!(e instanceof a.A))throw e}var m=e.createTransform(n,o,i,w,f),v=e.sampleGrid(this.image,m,f);return r=null===w?[i,n,o]:[i,n,o,w],new b.A(v,r)},e.createTransform=function(e,t,r,n,o){var i,a,s,f,u=o-3.5;return null!==n?(i=n.getX(),a=n.getY(),f=s=u-3):(i=t.getX()-e.getX()+r.getX(),a=t.getY()-e.getY()+r.getY(),s=u,f=u),S.A.quadrilateralToQuadrilateral(3.5,3.5,u,3.5,s,f,3.5,u,e.getX(),e.getY(),t.getX(),t.getY(),i,a,r.getX(),r.getY())},e.sampleGrid=function(e,t,r){return E.A.getInstance().sampleGridWithTransform(e,r,r,t)},e.computeDimension=function(e,t,r,n){var o=Math.floor((N.A.round(_.A.distance(e,t)/n)+N.A.round(_.A.distance(e,r)/n))/2)+7;switch(3&o){case 0:o++;break;case 2:o--;break;case 3:throw new a.A("Dimensions could be not found.")}return o},e.prototype.calculateModuleSize=function(e,t,r){return(this.calculateModuleSizeOneWay(e,t)+this.calculateModuleSizeOneWay(e,r))/2},e.prototype.calculateModuleSizeOneWay=function(e,t){var r=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(e.getX()),Math.floor(e.getY()),Math.floor(t.getX()),Math.floor(t.getY())),n=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(t.getX()),Math.floor(t.getY()),Math.floor(e.getX()),Math.floor(e.getY()));return isNaN(r)?n/7:isNaN(n)?r/7:(r+n)/14},e.prototype.sizeOfBlackWhiteBlackRunBothWays=function(e,t,r,n){var o=this.sizeOfBlackWhiteBlackRun(e,t,r,n),i=1,a=e-(r-e);a<0?(i=e/(e-a),a=0):a>=this.image.getWidth()&&(i=(this.image.getWidth()-1-e)/(a-e),a=this.image.getWidth()-1);var s=Math.floor(t-(n-t)*i);return i=1,s<0?(i=t/(t-s),s=0):s>=this.image.getHeight()&&(i=(this.image.getHeight()-1-t)/(s-t),s=this.image.getHeight()-1),a=Math.floor(e+(a-e)*i),(o+=this.sizeOfBlackWhiteBlackRun(e,t,a,s))-1},e.prototype.sizeOfBlackWhiteBlackRun=function(e,t,r,n){var o=Math.abs(n-t)>Math.abs(r-e);if(o){var i=e;e=t,t=i,i=r,r=n,n=i}for(var a=Math.abs(r-e),s=Math.abs(n-t),f=-a/2,u=e<r?1:-1,l=t<n?1:-1,w=0,h=r+u,c=e,d=t;c!==h;c+=u){var A=o?d:c,g=o?c:d;if(1===w===this.image.get(A,g)){if(2===w)return N.A.distance(c,d,e,t);w++}if((f+=s)>0){if(d===n)break;d+=l,f-=a}}return 2===w?N.A.distance(r+u,n,e,t):NaN},e.prototype.findAlignmentInRegion=function(e,t,r,n){var o=Math.floor(n*e),i=Math.max(0,t-o),s=Math.min(this.image.getWidth()-1,t+o);if(s-i<3*e)throw new a.A("Alignment top exceeds estimated module size.");var f=Math.max(0,r-o),u=Math.min(this.image.getHeight()-1,r+o);if(u-f<3*e)throw new a.A("Alignment bottom exceeds estimated module size.");return new R(this.image,i,f,s-i,u-f,e,this.resultPointCallback).find()},e}();let L=function(){function e(){this.decoder=new M}return e.prototype.getDecoder=function(){return this.decoder},e.prototype.decode=function(t,r){if(null!=r&&void 0!==r.get(i.A.PURE_BARCODE)){var o,a,u=e.extractPureBits(t.getBlackMatrix());o=this.decoder.decodeBitMatrix(u,r),a=e.NO_POINTS}else{var l=new V(t.getBlackMatrix()).detect(r);o=this.decoder.decodeBitMatrix(l.getBits(),r),a=l.getPoints()}o.getOther()instanceof I&&o.getOther().applyMirroredCorrection(a);var w=new s.A(o.getText(),o.getRawBytes(),void 0,a,n.A.QR_CODE,void 0),h=o.getByteSegments();null!==h&&w.putMetadata(f.A.BYTE_SEGMENTS,h);var c=o.getECLevel();return null!==c&&w.putMetadata(f.A.ERROR_CORRECTION_LEVEL,c),o.hasStructuredAppend()&&(w.putMetadata(f.A.STRUCTURED_APPEND_SEQUENCE,o.getStructuredAppendSequenceNumber()),w.putMetadata(f.A.STRUCTURED_APPEND_PARITY,o.getStructuredAppendParity())),w},e.prototype.reset=function(){},e.extractPureBits=function(e){var t=e.getTopLeftOnBit(),r=e.getBottomRightOnBit();if(null===t||null===r)throw new a.A;var n=this.moduleSize(t,e),i=t[1],s=r[1],f=t[0],u=r[0];if(f>=u||i>=s||s-i!=u-f&&(u=f+(s-i))>=e.getWidth())throw new a.A;var l=Math.round((u-f+1)/n),w=Math.round((s-i+1)/n);if(l<=0||w<=0||w!==l)throw new a.A;var h=Math.floor(n/2);i+=h;var c=(f+=h)+Math.floor((l-1)*n)-u;if(c>0){if(c>h)throw new a.A;f-=c}var d=i+Math.floor((w-1)*n)-s;if(d>0){if(d>h)throw new a.A;i-=d}for(var A=new o.A(l,w),g=0;g<w;g++)for(var p=i+Math.floor(g*n),y=0;y<l;y++)e.get(f+Math.floor(y*n),p)&&A.set(y,g);return A},e.moduleSize=function(e,t){for(var r=t.getHeight(),n=t.getWidth(),o=e[0],i=e[1],s=!0,f=0;o<n&&i<r;){if(s!==t.get(o,i)){if(5==++f)break;s=!s}o++,i++}if(o===n||i===r)throw new a.A;return(o-e[0])/7},e.NO_POINTS=[],e}()},85808:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(38988);let o=function(){function e(){}return e.applyMaskPenaltyRule1=function(t){return e.applyMaskPenaltyRule1Internal(t,!0)+e.applyMaskPenaltyRule1Internal(t,!1)},e.applyMaskPenaltyRule2=function(t){for(var r=0,n=t.getArray(),o=t.getWidth(),i=t.getHeight(),a=0;a<i-1;a++)for(var s=n[a],f=0;f<o-1;f++){var u=s[f];u===s[f+1]&&u===n[a+1][f]&&u===n[a+1][f+1]&&r++}return e.N2*r},e.applyMaskPenaltyRule3=function(t){for(var r=0,n=t.getArray(),o=t.getWidth(),i=t.getHeight(),a=0;a<i;a++)for(var s=0;s<o;s++){var f=n[a];s+6<o&&1===f[s]&&0===f[s+1]&&1===f[s+2]&&1===f[s+3]&&1===f[s+4]&&0===f[s+5]&&1===f[s+6]&&(e.isWhiteHorizontal(f,s-4,s)||e.isWhiteHorizontal(f,s+7,s+11))&&r++,a+6<i&&1===n[a][s]&&0===n[a+1][s]&&1===n[a+2][s]&&1===n[a+3][s]&&1===n[a+4][s]&&0===n[a+5][s]&&1===n[a+6][s]&&(e.isWhiteVertical(n,s,a-4,a)||e.isWhiteVertical(n,s,a+7,a+11))&&r++}return r*e.N3},e.isWhiteHorizontal=function(e,t,r){t=Math.max(t,0),r=Math.min(r,e.length);for(var n=t;n<r;n++)if(1===e[n])return!1;return!0},e.isWhiteVertical=function(e,t,r,n){r=Math.max(r,0),n=Math.min(n,e.length);for(var o=r;o<n;o++)if(1===e[o][t])return!1;return!0},e.applyMaskPenaltyRule4=function(t){for(var r=0,n=t.getArray(),o=t.getWidth(),i=t.getHeight(),a=0;a<i;a++)for(var s=n[a],f=0;f<o;f++)1===s[f]&&r++;var u=t.getHeight()*t.getWidth();return Math.floor(10*Math.abs(2*r-u)/u)*e.N4},e.getDataMaskBit=function(e,t,r){var o,i;switch(e){case 0:o=r+t&1;break;case 1:o=1&r;break;case 2:o=t%3;break;case 3:o=(r+t)%3;break;case 4:o=Math.floor(r/2)+Math.floor(t/3)&1;break;case 5:o=(1&(i=r*t))+i%3;break;case 6:o=(1&(i=r*t))+i%3&1;break;case 7:o=(i=r*t)%3+(r+t&1)&1;break;default:throw new n.A("Invalid mask pattern: "+e)}return 0===o},e.applyMaskPenaltyRule1Internal=function(t,r){for(var n=0,o=r?t.getHeight():t.getWidth(),i=r?t.getWidth():t.getHeight(),a=t.getArray(),s=0;s<o;s++){for(var f=0,u=-1,l=0;l<i;l++){var w=r?a[s][l]:a[l][s];w===u?f++:(f>=5&&(n+=e.N1+(f-5)),f=1,u=w)}f>=5&&(n+=e.N1+(f-5))}return n},e.N1=3,e.N2=3,e.N3=40,e.N4=10,e}()},99496:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(25969),o=r(27217),i=r(10782),a=r(61536),s=r(35168),f=r(38988),u=r(39778);let l=function(){function e(){}return e.prototype.encode=function(t,r,i,u,l){if(0===t.length)throw new f.A("Found empty contents");if(r!==n.A.QR_CODE)throw new f.A("Can only encode QR_CODE, but got "+r);if(i<0||u<0)throw new f.A("Requested dimensions are too small: "+i+"x"+u);var w=a.A.L,h=e.QUIET_ZONE_SIZE;null!==l&&(void 0!==l.get(o.A.ERROR_CORRECTION)&&(w=a.A.fromString(l.get(o.A.ERROR_CORRECTION).toString())),void 0!==l.get(o.A.MARGIN)&&(h=Number.parseInt(l.get(o.A.MARGIN).toString(),10)));var c=s.A.encode(t,w,l);return e.renderResult(c,i,u,h)},e.renderResult=function(e,t,r,n){var o=e.getMatrix();if(null===o)throw new u.A;for(var a=o.getWidth(),s=o.getHeight(),f=a+2*n,l=s+2*n,w=Math.max(t,f),h=Math.max(r,l),c=Math.min(Math.floor(w/f),Math.floor(h/l)),d=Math.floor((w-a*c)/2),A=Math.floor((h-s*c)/2),g=new i.A(w,h),p=0,y=A;p<s;p++,y+=c)for(var m=0,v=d;m<a;m++,v+=c)1===o.get(m,p)&&g.setRegion(v,y,c,c);return g},e.QUIET_ZONE_SIZE=4,e}()}}]);