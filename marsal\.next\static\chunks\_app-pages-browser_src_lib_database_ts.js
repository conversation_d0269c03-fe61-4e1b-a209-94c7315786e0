"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_database_ts"],{

/***/ "(app-pages-browser)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   OrderService: () => (/* binding */ OrderService),\n/* harmony export */   UserService: () => (/* binding */ UserService),\n/* harmony export */   localDB: () => (/* binding */ localDB),\n/* harmony export */   notificationService: () => (/* binding */ notificationService),\n/* harmony export */   orderService: () => (/* binding */ orderService),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n// Local Database Service using IndexedDB\n// خدمة قاعدة البيانات المحلية باستخدام IndexedDB\nclass LocalDatabase {\n    async init() {\n        return new Promise((resolve, reject)=>{\n            const request = indexedDB.open(this.dbName, this.version);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>{\n                this.db = request.result;\n                resolve();\n            };\n            request.onupgradeneeded = (event)=>{\n                const db = event.target.result;\n                // Create object stores\n                if (!db.objectStoreNames.contains('users')) {\n                    const userStore = db.createObjectStore('users', {\n                        keyPath: 'id'\n                    });\n                    userStore.createIndex('username', 'username', {\n                        unique: true\n                    });\n                    userStore.createIndex('role', 'role', {\n                        unique: false\n                    });\n                }\n                if (!db.objectStoreNames.contains('orders')) {\n                    const orderStore = db.createObjectStore('orders', {\n                        keyPath: 'id'\n                    });\n                    orderStore.createIndex('trackingNumber', 'trackingNumber', {\n                        unique: true\n                    });\n                    orderStore.createIndex('status', 'status', {\n                        unique: false\n                    });\n                    orderStore.createIndex('courierId', 'courierId', {\n                        unique: false\n                    });\n                    orderStore.createIndex('createdAt', 'createdAt', {\n                        unique: false\n                    });\n                }\n                if (!db.objectStoreNames.contains('settings')) {\n                    db.createObjectStore('settings', {\n                        keyPath: 'id'\n                    });\n                }\n                if (!db.objectStoreNames.contains('notifications')) {\n                    const notificationStore = db.createObjectStore('notifications', {\n                        keyPath: 'id'\n                    });\n                    notificationStore.createIndex('userId', 'userId', {\n                        unique: false\n                    });\n                    notificationStore.createIndex('read', 'read', {\n                        unique: false\n                    });\n                }\n                if (!db.objectStoreNames.contains('statistics')) {\n                    const statStore = db.createObjectStore('statistics', {\n                        keyPath: 'id'\n                    });\n                    statStore.createIndex('type', 'type', {\n                        unique: false\n                    });\n                    statStore.createIndex('date', 'date', {\n                        unique: false\n                    });\n                }\n            };\n        });\n    }\n    // Generic CRUD operations\n    async add(storeName, data) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readwrite');\n            const store = transaction.objectStore(storeName);\n            const request = store.add(data);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve();\n        });\n    }\n    async get(storeName, id) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readonly');\n            const store = transaction.objectStore(storeName);\n            const request = store.get(id);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve(request.result || null);\n        });\n    }\n    async getAll(storeName) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readonly');\n            const store = transaction.objectStore(storeName);\n            const request = store.getAll();\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve(request.result || []);\n        });\n    }\n    async update(storeName, data) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readwrite');\n            const store = transaction.objectStore(storeName);\n            const request = store.put(data);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve();\n        });\n    }\n    async delete(storeName, id) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readwrite');\n            const store = transaction.objectStore(storeName);\n            const request = store.delete(id);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve();\n        });\n    }\n    // Query by index\n    async getByIndex(storeName, indexName, value) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readonly');\n            const store = transaction.objectStore(storeName);\n            const index = store.index(indexName);\n            const request = index.getAll(value);\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve(request.result || []);\n        });\n    }\n    // Clear all data\n    async clear(storeName) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readwrite');\n            const store = transaction.objectStore(storeName);\n            const request = store.clear();\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve();\n        });\n    }\n    // Count records\n    async count(storeName) {\n        if (!this.db) throw new Error('Database not initialized');\n        return new Promise((resolve, reject)=>{\n            const transaction = this.db.transaction([\n                storeName\n            ], 'readonly');\n            const store = transaction.objectStore(storeName);\n            const request = store.count();\n            request.onerror = ()=>reject(request.error);\n            request.onsuccess = ()=>resolve(request.result);\n        });\n    }\n    constructor(){\n        this.dbName = 'MarsalDB';\n        this.version = 1;\n        this.db = null;\n    }\n}\n// Create singleton instance\nconst localDB = new LocalDatabase();\n// Initialize database on module load\nif (true) {\n    localDB.init().catch(console.error);\n}\n// Specialized services for each data type\nclass UserService {\n    async createUser(userData) {\n        const user = {\n            ...userData,\n            id: \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            createdAt: new Date()\n        };\n        await localDB.add('users', user);\n        return user;\n    }\n    async getUserByUsername(username) {\n        const users = await localDB.getByIndex('users', 'username', username);\n        return users[0] || null;\n    }\n    async getAllUsers() {\n        return localDB.getAll('users');\n    }\n    async updateUser(id, updates) {\n        const user = await localDB.get('users', id);\n        if (!user) throw new Error('User not found');\n        const updatedUser = {\n            ...user,\n            ...updates\n        };\n        await localDB.update('users', updatedUser);\n    }\n    async deleteUser(id) {\n        await localDB.delete('users', id);\n    }\n    async getUsersByRole(role) {\n        return localDB.getByIndex('users', 'role', role);\n    }\n}\nclass OrderService {\n    async createOrder(orderData) {\n        const order = {\n            ...orderData,\n            id: \"order_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        await localDB.add('orders', order);\n        return order;\n    }\n    async getOrderByTrackingNumber(trackingNumber) {\n        const orders = await localDB.getByIndex('orders', 'trackingNumber', trackingNumber);\n        return orders[0] || null;\n    }\n    async getAllOrders() {\n        return localDB.getAll('orders');\n    }\n    async getOrdersByStatus(status) {\n        return localDB.getByIndex('orders', 'status', status);\n    }\n    async getOrdersByCourier(courierId) {\n        return localDB.getByIndex('orders', 'courierId', courierId);\n    }\n    async updateOrder(id, updates) {\n        const order = await localDB.get('orders', id);\n        if (!order) throw new Error('Order not found');\n        const updatedOrder = {\n            ...order,\n            ...updates,\n            updatedAt: new Date()\n        };\n        await localDB.update('orders', updatedOrder);\n    }\n    async deleteOrder(id) {\n        await localDB.delete('orders', id);\n    }\n    async searchOrders(query) {\n        const allOrders = await this.getAllOrders();\n        return allOrders.filter((order)=>order.trackingNumber.includes(query) || order.customerName.includes(query) || order.customerPhone.includes(query) || order.address.includes(query));\n    }\n}\nclass NotificationService {\n    async createNotification(notificationData) {\n        const notification = {\n            ...notificationData,\n            id: \"notification_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            createdAt: new Date()\n        };\n        await localDB.add('notifications', notification);\n        return notification;\n    }\n    async getNotificationsByUser(userId) {\n        return localDB.getByIndex('notifications', 'userId', userId);\n    }\n    async getAllNotifications() {\n        return localDB.getAll('notifications');\n    }\n    async markAsRead(id) {\n        const notification = await localDB.get('notifications', id);\n        if (!notification) throw new Error('Notification not found');\n        notification.read = true;\n        await localDB.update('notifications', notification);\n    }\n    async deleteNotification(id) {\n        await localDB.delete('notifications', id);\n    }\n}\n// Create service instances\nconst userService = new UserService();\nconst orderService = new OrderService();\nconst notificationService = new NotificationService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/database.ts\n"));

/***/ })

}]);