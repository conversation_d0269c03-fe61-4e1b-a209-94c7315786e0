(()=>{var e={};e.id=520,e.ids=[520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12597:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},12890:(e,r,t)=>{Promise.resolve().then(t.bind(t,94934))},13861:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},18463:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94934)),"E:\\Marsal\\marsal\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\Marsal\\marsal\\src\\app\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19080:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(60687);t(43210);var a=t(8730),i=t(24224),n=t(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:i=!1,...l}){let d=i?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:t,className:e})),...l})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=t(60687);t(43210);var a=t(4780);function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},47170:(e,r,t)=>{Promise.resolve().then(t.bind(t,60464))},55511:e=>{"use strict";e.exports=require("crypto")},58869:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},60464:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var s=t(60687),a=t(43210),i=t(16189),n=t(67958),o=t(44493),l=t(29523),d=t(89667),c=t(19080);let u=(0,t(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var p=t(58869),x=t(12597),m=t(13861);function h(){let e=(0,i.useRouter)(),{login:r}=(0,n.A)(),[t,h]=(0,a.useState)(""),[g,b]=(0,a.useState)(""),[v,f]=(0,a.useState)(!1),[y,j]=(0,a.useState)(!1),[w,N]=(0,a.useState)(""),k=async s=>{s.preventDefault(),N(""),j(!0);try{await r(t,g)?e.push("/"):N("بيانات الدخول غير صحيحة")}catch(e){console.error("Login error:",e),N("حدث خطأ أثناء تسجيل الدخول")}finally{j(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 flex items-center justify-center p-4",dir:"rtl",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl shadow-2xl mb-6 animate-pulse",children:(0,s.jsx)(c.A,{className:"h-10 w-10 text-white"})}),(0,s.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2",children:"مرسال"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"نظام إدارة التوصيل السريع"})]}),(0,s.jsxs)(o.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-2xl",children:[(0,s.jsxs)(o.aR,{className:"text-center pb-4",children:[(0,s.jsxs)(o.ZB,{className:"text-2xl font-bold text-gray-800 flex items-center justify-center gap-2",children:[(0,s.jsx)(u,{className:"h-6 w-6 text-blue-600"}),"تسجيل الدخول"]}),(0,s.jsx)(o.BT,{className:"text-gray-600",children:"أدخل بيانات الدخول للوصول إلى النظام"})]}),(0,s.jsxs)(o.Wu,{className:"space-y-6",children:[(0,s.jsxs)("form",{onSubmit:k,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"اسم المستخدم"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(p.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,s.jsx)(d.p,{type:"text",value:t,onChange:e=>h(e.target.value),placeholder:"أدخل اسم المستخدم",className:"pr-10 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500",required:!0,disabled:y})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"كلمة المرور"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,s.jsx)(d.p,{type:v?"text":"password",name:"password",value:g,onChange:e=>b(e.target.value),placeholder:"أدخل كلمة المرور",className:"pr-10 pl-10 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>f(!v),className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:v?(0,s.jsx)(x.A,{className:"h-5 w-5"}):(0,s.jsx)(m.A,{className:"h-5 w-5"})})]})]}),w&&(0,s.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-600 text-sm text-center",children:w})}),(0,s.jsx)(l.$,{type:"submit",disabled:y,className:"w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold rounded-xl shadow-lg transition-all duration-200",children:y?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"جاري تسجيل الدخول..."]}):"تسجيل الدخول"})]}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-blue-800 mb-2",children:"حسابات تجريبية:"}),(0,s.jsxs)("div",{className:"space-y-1 text-xs text-blue-700",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"مدير:"})," admin / admin123"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"مدير فرع:"})," manager / manager123"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"متابع:"})," supervisor / supervisor123"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"مندوب:"})," courier / courier123"]})]})]})]})]}),(0,s.jsxs)(o.Zp,{className:"glass border-0 shadow-xl",children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{className:"text-lg text-center",children:"حسابات تجريبية للاختبار"})}),(0,s.jsx)(o.Wu,{children:(0,s.jsx)("div",{className:"space-y-3",children:[{username:"manager",role:"مدير",password:"123456"},{username:"supervisor",role:"متابع",password:"123456"},{username:"courier",role:"مندوب",password:"123456"}].map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white/50 rounded-lg border border-white/20",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.role}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[e.username," / ",e.password]})]}),(0,s.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{h(e.username),b(e.password)},disabled:y,children:"استخدام"})]},r))})})]}),(0,s.jsx)("div",{className:"text-center mt-8",children:(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"\xa9 2024 مرسال. جميع الحقوق محفوظة."})})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},77972:e=>{"use strict";e.exports=require("https")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687);t(43210);var a=t(4780);function i({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\login\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,437,839,690],()=>t(18463));module.exports=s})();