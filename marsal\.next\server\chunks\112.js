"use strict";exports.id=112,exports.ids=[112],exports.modules={36464:(e,t,a)=>{a.d(t,{Ys:()=>r,rB:()=>s});let r=[{id:"1",trackingNumber:"MRS001",senderName:"أحمد محمد",senderPhone:"07901234567",senderAddress:"بغداد - الكرادة - شارع الرشيد",recipientName:"فاطمة علي",recipientPhone:"07801234567",recipientAddress:"بغداد - الجادرية - قرب الجامعة",amount:5e4,status:"pending",notes:"يرجى التسليم في المساء",createdAt:new Date("2024-01-15T10:30:00"),updatedAt:new Date("2024-01-15T10:30:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-15T10:30:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"}]},{id:"2",trackingNumber:"MRS002",senderName:"سارة أحمد",senderPhone:"07701234567",senderAddress:"بغداد - المنصور - شارع الأميرات",recipientName:"محمد حسن",recipientPhone:"07601234567",recipientAddress:"بغداد - الكاظمية - شارع الإمام",amount:75e3,status:"assigned",assignedTo:"courier1",createdAt:new Date("2024-01-15T09:15:00"),updatedAt:new Date("2024-01-15T11:00:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-15T09:15:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"},{status:"assigned",timestamp:new Date("2024-01-15T11:00:00"),updatedBy:"dispatcher1",notes:"تم إسناد الطلب للمندوب"}]},{id:"3",trackingNumber:"MRS003",senderName:"خالد عبدالله",senderPhone:"07501234567",senderAddress:"بغداد - الدورة - شارع الصناعة",recipientName:"زينب محمد",recipientPhone:"07401234567",recipientAddress:"بغداد - الشعلة - قرب المجمع",amount:12e4,status:"delivered",assignedTo:"courier2",deliveredAt:new Date("2024-01-14T16:30:00"),createdAt:new Date("2024-01-14T08:00:00"),updatedAt:new Date("2024-01-14T16:30:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-14T08:00:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"},{status:"assigned",timestamp:new Date("2024-01-14T09:00:00"),updatedBy:"dispatcher1",notes:"تم إسناد الطلب للمندوب"},{status:"delivered",timestamp:new Date("2024-01-14T16:30:00"),updatedBy:"courier2",notes:"تم تسليم الطلب بنجاح"}]}],s=[{id:"admin1",email:"<EMAIL>",name:"أحمد المدير",username:"admin",phone:"07901234567",role:"admin",isActive:!0,createdAt:new Date("2024-01-01T00:00:00"),updatedAt:new Date("2024-01-01T00:00:00")},{id:"courier1",email:"<EMAIL>",name:"علي حسين",username:"courier1",phone:"07801234567",role:"courier",isActive:!0,createdAt:new Date("2024-01-02T00:00:00"),updatedAt:new Date("2024-01-02T00:00:00")},{id:"courier2",email:"<EMAIL>",name:"حسام محمد",username:"courier2",phone:"07701234567",role:"courier",isActive:!0,createdAt:new Date("2024-01-03T00:00:00"),updatedAt:new Date("2024-01-03T00:00:00")},{id:"dispatcher1",email:"<EMAIL>",name:"سارة الموزعة",username:"dispatcher",phone:"07601234567",role:"dispatcher",isActive:!0,createdAt:new Date("2024-01-04T00:00:00"),updatedAt:new Date("2024-01-04T00:00:00")}]},79396:(e,t,a)=>{a.d(t,{cO:()=>n,pv:()=>o,y7:()=>c});var r=a(75535),s=a(33784),d=a(4780),i=a(36464);let o={async create(e){try{let t=(0,d.y7)(),a=(0,r.collection)(s.db,"orders"),i=await (0,r.gS)(a,{trackingNumber:t,customerName:e.customerName,customerPhone:e.customerPhone,address:e.address,amount:e.amount,status:"pending",courierName:e.courierName||"",assignedTo:e.assignedTo||"",notes:e.notes||"",createdAt:(0,r.O5)(),updatedAt:(0,r.O5)(),createdBy:e.createdBy||"system"});return await this.addStatusHistory(i.id,"pending","تم إنشاء الطلب",e.createdBy||"system"),console.log("✅ تم إنشاء الطلب:",i.id),{id:i.id,trackingNumber:t,customerName:e.customerName,customerPhone:e.customerPhone,address:e.address,amount:e.amount,status:"pending",courierName:e.courierName||"",assignedTo:e.assignedTo||"",notes:e.notes||"",createdAt:new Date,updatedAt:new Date,statusHistory:[{status:"pending",timestamp:new Date,updatedBy:e.createdBy||"system",notes:"تم إنشاء الطلب"}]}}catch(e){throw console.error("Error creating order:",e),e}},async getAll(){try{let e=(0,r.collection)(s.db,"orders"),t=(0,r.P)(e,(0,r.My)("createdAt","desc")),a=await (0,r.getDocs)(t);return await Promise.all(a.docs.map(async e=>{let t=e.data(),a=await this.getStatusHistory(e.id);return{id:e.id,trackingNumber:t.trackingNumber,customerName:t.customerName,customerPhone:t.customerPhone,address:t.address,amount:t.amount,status:t.status,courierName:t.courierName||"",assignedTo:t.assignedTo||"",notes:t.notes||"",createdAt:t.createdAt?.toDate()||new Date,updatedAt:t.updatedAt?.toDate()||new Date,deliveredAt:t.deliveredAt?.toDate(),statusHistory:a}}))}catch(e){return console.error("Error getting orders:",e),i.Ys}},async getStatusHistory(e){try{let t=(0,r.collection)(s.db,"orders",e,"statusHistory"),a=(0,r.P)(t,(0,r.My)("timestamp","desc"));return(await (0,r.getDocs)(a)).docs.map(e=>{let t=e.data();return{status:t.status,timestamp:t.timestamp?.toDate()||new Date,updatedBy:t.updatedBy,notes:t.notes||""}})}catch(e){return console.error("Error getting status history:",e),[]}},async addStatusHistory(e,t,a,d){try{let i=(0,r.collection)(s.db,"orders",e,"statusHistory");await (0,r.gS)(i,{status:t,notes:a,updatedBy:d,timestamp:(0,r.O5)()})}catch(e){console.error("Error adding status history:",e)}},async getById(e){try{let t=(0,r.doc)(s.db,"orders",e),a=await (0,r.getDoc)(t);if(!a.exists())throw Error("Order not found");let d=a.data(),i=await this.getStatusHistory(e);return{id:a.id,trackingNumber:d.trackingNumber,customerName:d.customerName,customerPhone:d.customerPhone,address:d.address,amount:d.amount,status:d.status,courierName:d.courierName||"",assignedTo:d.assignedTo||"",notes:d.notes||"",createdAt:d.createdAt?.toDate()||new Date,updatedAt:d.updatedAt?.toDate()||new Date,deliveredAt:d.deliveredAt?.toDate(),statusHistory:i}}catch(e){throw console.error("Error getting order by ID:",e),Error("Order not found")}},async search(e){try{return(await orderService.searchOrders(e)).map(e=>({id:e.id,trackingNumber:e.tracking_number,customerName:e.customer_name,customerPhone:e.customer_phone,address:e.address,amount:e.amount,status:e.status,courierName:e.courier_name,courierId:e.courier_id,notes:e.notes,createdAt:new Date(e.created_at),updatedAt:new Date(e.updated_at),deliveredAt:e.delivered_at?new Date(e.delivered_at):void 0,statusHistory:[{status:e.status,timestamp:new Date(e.updated_at),updatedBy:"system",notes:e.notes||""}]}))}catch(t){return console.warn("Search failed, using mock data:",t),i.Ys.filter(t=>t.trackingNumber.includes(e)||t.customerName.includes(e)||t.customerPhone.includes(e))}},async updateStatus(e,t,a,d,i){let o=(0,r.doc)(s.db,COLLECTIONS.ORDERS,e),n=r.Dc.now(),c={status:t,timestamp:n.toDate(),updatedBy:d,notes:a,image:i},u={status:t,updatedAt:n,statusHistory:[...(await this.getById(e)).statusHistory,c]};return"delivered"===t?u.deliveredAt=n:"returned"===t&&(u.returnedAt=n),await (0,r.mZ)(o,u),c},async getByStatus(e){let t=(0,r.P)((0,r.collection)(s.db,COLLECTIONS.ORDERS),(0,r._M)("status","==",e),(0,r.My)("createdAt","desc"));return(await (0,r.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate(),updatedAt:e.data().updatedAt?.toDate(),deliveredAt:e.data().deliveredAt?.toDate(),returnedAt:e.data().returnedAt?.toDate(),statusHistory:e.data().statusHistory?.map(e=>({...e,timestamp:e.timestamp?.toDate()}))}))},async getByCourier(e){let t=(0,r.P)((0,r.collection)(s.db,COLLECTIONS.ORDERS),(0,r._M)("assignedTo","==",e),(0,r.My)("createdAt","desc"));return(await (0,r.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate(),updatedAt:e.data().updatedAt?.toDate(),deliveredAt:e.data().deliveredAt?.toDate(),returnedAt:e.data().returnedAt?.toDate(),statusHistory:e.data().statusHistory?.map(e=>({...e,timestamp:e.timestamp?.toDate()}))}))},async assignToCourier(e,t,a){let d=writeBatch(s.db),i=r.Dc.now();for(let o of e){let e=(0,r.doc)(s.db,COLLECTIONS.ORDERS,o),n=await this.getById(o),c={status:"assigned",timestamp:i.toDate(),updatedBy:a,notes:`تم إسناد الطلب للمندوب`};d.update(e,{assignedTo:t,status:"assigned",updatedAt:i,statusHistory:[...n.statusHistory,c]})}await d.commit()},async delete(e){await (0,r.kd)((0,r.doc)(s.db,COLLECTIONS.ORDERS,e))}},n={async create(e){try{let t={username:e.username,name:e.name,phone:e.phone,role:e.role,password_hash:"$2b$10$example_hash_for_123456",is_active:!0,created_by:e.createdBy||"system"},a=await userService.createUser(t);return{id:a.id,username:a.username,name:a.name,phone:a.phone,role:a.role,isActive:a.is_active,createdAt:new Date(a.created_at),updatedAt:new Date(a.created_at),createdBy:a.created_by}}catch(e){throw console.error("Error creating user:",e),e}},async getAll(){try{return(await userService.getAllUsers()).map(e=>({id:e.id,username:e.username,name:e.name,phone:e.phone,role:e.role,isActive:e.is_active,createdAt:new Date(e.created_at),updatedAt:new Date(e.created_at),createdBy:e.created_by}))}catch(e){return console.warn("Error getting users, using mock data:",e),i.rB}},async getCouriers(){try{return(await userService.getUsersByRole("courier")).filter(e=>e.is_active).map(e=>({id:e.id,username:e.username,name:e.name,phone:e.phone,role:e.role,isActive:e.is_active,createdAt:new Date(e.created_at),updatedAt:new Date(e.created_at),createdBy:e.created_by}))}catch(e){return console.warn("Error getting couriers, using mock data:",e),i.rB.filter(e=>"courier"===e.role&&e.isActive)}},async getById(e){let t=(0,r.doc)(s.db,COLLECTIONS.USERS,e),a=await (0,r.getDoc)(t);if(!a.exists())throw Error("User not found");let d=a.data();return{id:a.id,...d,createdAt:d.createdAt?.toDate(),updatedAt:d.updatedAt?.toDate()}},async update(e,t){let a=(0,r.doc)(s.db,COLLECTIONS.USERS,e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})},async delete(e){await (0,r.kd)((0,r.doc)(s.db,COLLECTIONS.USERS,e))}},c={async create(e){let t=r.Dc.now(),a={...e,createdAt:t.toDate(),isSettled:!1};return{id:(await (0,r.gS)((0,r.collection)(s.db,COLLECTIONS.SETTLEMENTS),a)).id,...a}},async getAll(){let e=(0,r.P)((0,r.collection)(s.db,COLLECTIONS.SETTLEMENTS),(0,r.My)("createdAt","desc"));return(await (0,r.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate(),settledAt:e.data().settledAt?.toDate()}))},async markAsSettled(e){let t=(0,r.doc)(s.db,COLLECTIONS.SETTLEMENTS,e);await (0,r.mZ)(t,{isSettled:!0,settledAt:r.Dc.now()})},subscribeToOrders(e){try{let t=(0,r.P)((0,r.collection)(s.db,COLLECTIONS.ORDERS),(0,r.My)("createdAt","desc"));return(0,r.aQ)(t,t=>{let a=t.docs.map(e=>({id:e.id,...e.data()}));e(a)})}catch(e){return console.warn("Firebase subscription not available:",e),()=>{}}},subscribeToUserOrders(e,t){try{let a=(0,r.P)((0,r.collection)(s.db,COLLECTIONS.ORDERS),(0,r._M)("courierId","==",e),(0,r.My)("createdAt","desc"));return(0,r.aQ)(a,e=>{let a=e.docs.map(e=>({id:e.id,...e.data()}));t(a)})}catch(e){return console.warn("Firebase subscription not available:",e),()=>{}}}}},89667:(e,t,a)=>{a.d(t,{p:()=>d});var r=a(60687);a(43210);var s=a(4780);function d({className:e,type:t,...a}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},99270:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};