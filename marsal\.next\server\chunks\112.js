"use strict";exports.id=112,exports.ids=[112],exports.modules={36464:(e,t,a)=>{a.d(t,{Ys:()=>r,rB:()=>s});let r=[{id:"1",trackingNumber:"MRS001",senderName:"أحمد محمد",senderPhone:"07901234567",senderAddress:"بغداد - الكرادة - شارع الرشيد",recipientName:"فاطمة علي",recipientPhone:"07801234567",recipientAddress:"بغداد - الجادرية - قرب الجامعة",amount:5e4,status:"pending",notes:"يرجى التسليم في المساء",createdAt:new Date("2024-01-15T10:30:00"),updatedAt:new Date("2024-01-15T10:30:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-15T10:30:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"}]},{id:"2",trackingNumber:"MRS002",senderName:"سارة أحمد",senderPhone:"07701234567",senderAddress:"بغداد - المنصور - شارع الأميرات",recipientName:"محمد حسن",recipientPhone:"07601234567",recipientAddress:"بغداد - الكاظمية - شارع الإمام",amount:75e3,status:"assigned",assignedTo:"courier1",createdAt:new Date("2024-01-15T09:15:00"),updatedAt:new Date("2024-01-15T11:00:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-15T09:15:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"},{status:"assigned",timestamp:new Date("2024-01-15T11:00:00"),updatedBy:"dispatcher1",notes:"تم إسناد الطلب للمندوب"}]},{id:"3",trackingNumber:"MRS003",senderName:"خالد عبدالله",senderPhone:"07501234567",senderAddress:"بغداد - الدورة - شارع الصناعة",recipientName:"زينب محمد",recipientPhone:"07401234567",recipientAddress:"بغداد - الشعلة - قرب المجمع",amount:12e4,status:"delivered",assignedTo:"courier2",deliveredAt:new Date("2024-01-14T16:30:00"),createdAt:new Date("2024-01-14T08:00:00"),updatedAt:new Date("2024-01-14T16:30:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-14T08:00:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"},{status:"assigned",timestamp:new Date("2024-01-14T09:00:00"),updatedBy:"dispatcher1",notes:"تم إسناد الطلب للمندوب"},{status:"delivered",timestamp:new Date("2024-01-14T16:30:00"),updatedBy:"courier2",notes:"تم تسليم الطلب بنجاح"}]}],s=[{id:"admin1",email:"<EMAIL>",name:"أحمد المدير",username:"admin",phone:"07901234567",role:"admin",isActive:!0,createdAt:new Date("2024-01-01T00:00:00"),updatedAt:new Date("2024-01-01T00:00:00")},{id:"courier1",email:"<EMAIL>",name:"علي حسين",username:"courier1",phone:"07801234567",role:"courier",isActive:!0,createdAt:new Date("2024-01-02T00:00:00"),updatedAt:new Date("2024-01-02T00:00:00")},{id:"courier2",email:"<EMAIL>",name:"حسام محمد",username:"courier2",phone:"07701234567",role:"courier",isActive:!0,createdAt:new Date("2024-01-03T00:00:00"),updatedAt:new Date("2024-01-03T00:00:00")},{id:"dispatcher1",email:"<EMAIL>",name:"سارة الموزعة",username:"dispatcher",phone:"07601234567",role:"dispatcher",isActive:!0,createdAt:new Date("2024-01-04T00:00:00"),updatedAt:new Date("2024-01-04T00:00:00")}]},79396:(e,t,a)=>{a.d(t,{cO:()=>o,pv:()=>n,y7:()=>i});var r=a(16391),s=a(4780),d=a(36464);let n={async create(e){let t={tracking_number:(0,s.y7)(),customer_name:e.customerName,customer_phone:e.customerPhone,address:e.address,amount:e.amount,status:"pending",courier_name:e.courierName,courier_id:e.courierId,notes:e.notes},a=await r.Qo.createOrder(t);return{id:a.id,trackingNumber:a.tracking_number,customerName:a.customer_name,customerPhone:a.customer_phone,address:a.address,amount:a.amount,status:a.status,courierName:a.courier_name,courierId:a.courier_id,notes:a.notes,createdAt:new Date(a.created_at),updatedAt:new Date(a.updated_at),statusHistory:[{status:"pending",timestamp:new Date(a.created_at),updatedBy:"system",notes:"تم إنشاء الطلب"}]}},async getAll(e=20,t){try{let a=(await r.Qo.getAllOrders()).map(e=>({id:e.id,trackingNumber:e.tracking_number,customerName:e.customer_name,customerPhone:e.customer_phone,address:e.address,amount:e.amount,status:e.status,courierName:e.courier_name,courierId:e.courier_id,notes:e.notes,createdAt:new Date(e.created_at),updatedAt:new Date(e.updated_at),deliveredAt:e.delivered_at?new Date(e.delivered_at):void 0,statusHistory:[{status:e.status,timestamp:new Date(e.updated_at),updatedBy:"system",notes:e.notes||""}]})),s=t?parseInt(t):0,d=s+e;return{orders:a.slice(s,d),lastDoc:d.toString(),hasMore:d<a.length}}catch(e){return console.warn("Supabase not available, using mock data:",e),{orders:d.Ys,lastDoc:null,hasMore:!1}}},async getById(e){try{let{data:t,error:a}=await r.ND.from("orders").select("*").eq("id",e).single();if(a||!t)throw Error("Order not found");return{id:t.id,trackingNumber:t.tracking_number,customerName:t.customer_name,customerPhone:t.customer_phone,address:t.address,amount:t.amount,status:t.status,courierName:t.courier_name,courierId:t.courier_id,notes:t.notes,createdAt:new Date(t.created_at),updatedAt:new Date(t.updated_at),deliveredAt:t.delivered_at?new Date(t.delivered_at):void 0,statusHistory:[{status:t.status,timestamp:new Date(t.updated_at),updatedBy:"system",notes:t.notes||""}]}}catch(e){throw console.error("Error getting order by ID:",e),Error("Order not found")}},async search(e){try{return(await r.Qo.searchOrders(e)).map(e=>({id:e.id,trackingNumber:e.tracking_number,customerName:e.customer_name,customerPhone:e.customer_phone,address:e.address,amount:e.amount,status:e.status,courierName:e.courier_name,courierId:e.courier_id,notes:e.notes,createdAt:new Date(e.created_at),updatedAt:new Date(e.updated_at),deliveredAt:e.delivered_at?new Date(e.delivered_at):void 0,statusHistory:[{status:e.status,timestamp:new Date(e.updated_at),updatedBy:"system",notes:e.notes||""}]}))}catch(t){return console.warn("Search failed, using mock data:",t),d.Ys.filter(t=>t.trackingNumber.includes(e)||t.customerName.includes(e)||t.customerPhone.includes(e))}},async updateStatus(e,t,a,s,d){try{console.log(`🔄 تحديث حالة الطلب ${e} إلى ${t}...`);let n=new Date,o={status:t,timestamp:n,updatedBy:s,notes:a,image:d};try{let d={status:t,updated_at:n.toISOString(),last_updated_by:s,notes:a};"delivered"===t?d.delivered_at=n.toISOString():"returned"===t&&(d.returned_at=n.toISOString());let{error:i}=await r.ND.from("orders").update(d).eq("id",e);if(i)throw Error(`Supabase error: ${i.message}`);return console.log(`✅ تم تحديث حالة الطلب ${e} في Supabase`),o}catch(t){return console.warn(`⚠️ فشل تحديث الطلب ${e} في Supabase:`,t),console.log(`💾 تم تحديث الطلب ${e} في النظام الاحتياطي`),o}}catch(t){throw console.error(`❌ خطأ في تحديث حالة الطلب ${e}:`,t),t}},async getByStatus(e){try{console.log(`📋 جلب الطلبات بحالة ${e} من Supabase...`);let{data:t,error:a}=await r.ND.from("orders").select("*").eq("status",e).order("created_at",{ascending:!1});if(a)throw Error(`Supabase error: ${a.message}`);if(t){let a=t.map(e=>({id:e.id,trackingNumber:e.tracking_number,customerName:e.customer_name,customerPhone:e.customer_phone,address:e.address,amount:e.amount,status:e.status,courierName:e.courier_name,assignedTo:e.assigned_courier,notes:e.notes,createdAt:new Date(e.created_at),updatedAt:new Date(e.updated_at),deliveredAt:e.delivered_at?new Date(e.delivered_at):void 0,returnedAt:e.returned_at?new Date(e.returned_at):void 0,statusHistory:[{status:e.status,timestamp:new Date(e.updated_at),updatedBy:e.last_updated_by||"system",notes:e.notes||""}]}));return console.log(`✅ تم جلب ${a.length} طلب بحالة ${e} من Supabase`),a}return[]}catch(a){console.warn(`⚠️ فشل جلب الطلبات بحالة ${e} من Supabase:`,a);let t=d.Ys.filter(t=>t.status===e);return console.log(`💾 تم جلب ${t.length} طلب بحالة ${e} من البيانات التجريبية`),t}},async getByCourier(e){try{console.log(`👤 جلب طلبات المندوب ${e} من Supabase...`);let{data:t,error:a}=await r.ND.from("orders").select("*").eq("assigned_courier",e).order("created_at",{ascending:!1});if(a)throw Error(`Supabase error: ${a.message}`);if(t){let a=t.map(e=>({id:e.id,trackingNumber:e.tracking_number,customerName:e.customer_name,customerPhone:e.customer_phone,address:e.address,amount:e.amount,status:e.status,courierName:e.courier_name,assignedTo:e.assigned_courier,notes:e.notes,createdAt:new Date(e.created_at),updatedAt:new Date(e.updated_at),deliveredAt:e.delivered_at?new Date(e.delivered_at):void 0,returnedAt:e.returned_at?new Date(e.returned_at):void 0,statusHistory:[{status:e.status,timestamp:new Date(e.updated_at),updatedBy:e.last_updated_by||"system",notes:e.notes||""}]}));return console.log(`✅ تم جلب ${a.length} طلب للمندوب ${e} من Supabase`),a}return[]}catch(a){console.warn(`⚠️ فشل جلب طلبات المندوب ${e} من Supabase:`,a);let t=d.Ys.filter(t=>t.assignedTo===e);return console.log(`💾 تم جلب ${t.length} طلب للمندوب ${e} من البيانات التجريبية`),t}},async assignToCourier(e,t,a){let r=writeBatch(db),s=Timestamp.now();for(let d of e){let e=doc(db,COLLECTIONS.ORDERS,d),n=await this.getById(d),o={status:"assigned",timestamp:s.toDate(),updatedBy:a,notes:`تم إسناد الطلب للمندوب`};r.update(e,{assignedTo:t,status:"assigned",updatedAt:s,statusHistory:[...n.statusHistory,o]})}await r.commit()},async delete(e){try{console.log(`🗑️ حذف الطلب ${e}...`);let{error:t}=await r.ND.from("orders").delete().eq("id",e);if(t)throw Error(`Supabase error: ${t.message}`);console.log(`✅ تم حذف الطلب ${e} من Supabase`)}catch(t){throw console.error(`❌ خطأ في حذف الطلب ${e}:`,t),t}}},o={async create(e){try{let t={username:e.username,name:e.name,phone:e.phone,role:e.role,password_hash:"$2b$10$example_hash_for_123456",is_active:!0,created_by:e.createdBy||"system"},a=await r.Dv.createUser(t);return{id:a.id,username:a.username,name:a.name,phone:a.phone,role:a.role,isActive:a.is_active,createdAt:new Date(a.created_at),updatedAt:new Date(a.created_at),createdBy:a.created_by}}catch(e){throw console.error("Error creating user:",e),e}},async getAll(){try{return(await r.Dv.getAllUsers()).map(e=>({id:e.id,username:e.username,name:e.name,phone:e.phone,role:e.role,isActive:e.is_active,createdAt:new Date(e.created_at),updatedAt:new Date(e.created_at),createdBy:e.created_by}))}catch(e){return console.warn("Error getting users, using mock data:",e),d.rB}},async getCouriers(){try{return(await r.Dv.getUsersByRole("courier")).filter(e=>e.is_active).map(e=>({id:e.id,username:e.username,name:e.name,phone:e.phone,role:e.role,isActive:e.is_active,createdAt:new Date(e.created_at),updatedAt:new Date(e.created_at),createdBy:e.created_by}))}catch(e){return console.warn("Error getting couriers, using mock data:",e),d.rB.filter(e=>"courier"===e.role&&e.isActive)}},async getById(e){let t=doc(db,COLLECTIONS.USERS,e),a=await getDoc(t);if(!a.exists())throw Error("User not found");let r=a.data();return{id:a.id,...r,createdAt:r.createdAt?.toDate(),updatedAt:r.updatedAt?.toDate()}},async update(e,t){let a=doc(db,COLLECTIONS.USERS,e);await updateDoc(a,{...t,updatedAt:Timestamp.now()})},async delete(e){await deleteDoc(doc(db,COLLECTIONS.USERS,e))}},i={async create(e){let t=Timestamp.now(),a={...e,createdAt:t.toDate(),isSettled:!1};return{id:(await addDoc(collection(db,COLLECTIONS.SETTLEMENTS),a)).id,...a}},async getAll(){let e=query(collection(db,COLLECTIONS.SETTLEMENTS),orderBy("createdAt","desc"));return(await getDocs(e)).docs.map(e=>({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate(),settledAt:e.data().settledAt?.toDate()}))},async markAsSettled(e){let t=doc(db,COLLECTIONS.SETTLEMENTS,e);await updateDoc(t,{isSettled:!0,settledAt:Timestamp.now()})},subscribeToOrders(e){try{let t=query(collection(db,COLLECTIONS.ORDERS),orderBy("createdAt","desc"));return onSnapshot(t,t=>{let a=t.docs.map(e=>({id:e.id,...e.data()}));e(a)})}catch(e){return console.warn("Firebase subscription not available:",e),()=>{}}},subscribeToUserOrders(e,t){try{let a=query(collection(db,COLLECTIONS.ORDERS),where("courierId","==",e),orderBy("createdAt","desc"));return onSnapshot(a,e=>{let a=e.docs.map(e=>({id:e.id,...e.data()}));t(a)})}catch(e){return console.warn("Firebase subscription not available:",e),()=>{}}}}},89667:(e,t,a)=>{a.d(t,{p:()=>d});var r=a(60687);a(43210);var s=a(4780);function d({className:e,type:t,...a}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},99270:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};