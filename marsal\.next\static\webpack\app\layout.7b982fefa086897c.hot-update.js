"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3c3479c907e0\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJFOlxcTWFyc2FsXFxtYXJzYWxcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjNjMzQ3OWM5MDdlMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/firebase-auth.ts":
/*!**********************************!*\
  !*** ./src/lib/firebase-auth.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   firebaseAuthService: () => (/* binding */ firebaseAuthService)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n// Firebase Authentication service for Marsal Delivery App\n\n\n\n// Default users for initial setup\nconst defaultUsers = [\n    {\n        username: 'azad95',\n        email: '<EMAIL>',\n        name: 'أزاد - مدير النظام الرئيسي',\n        phone: '07801234567',\n        role: 'manager',\n        password: 'Azad@1995'\n    },\n    {\n        username: 'manager',\n        email: '<EMAIL>',\n        name: 'مدير النظام',\n        phone: '07801234568',\n        role: 'manager',\n        password: '123456'\n    },\n    {\n        username: 'supervisor',\n        email: '<EMAIL>',\n        name: 'المشرف العام',\n        phone: '07801234569',\n        role: 'supervisor',\n        password: '123456'\n    },\n    {\n        username: 'courier',\n        email: '<EMAIL>',\n        name: 'مندوب التوصيل',\n        phone: '07801234570',\n        role: 'courier',\n        password: '123456'\n    }\n];\nconst firebaseAuthService = {\n    // Initialize default users (run once) - Local storage fallback\n    async initializeDefaultUsers () {\n        try {\n            console.log('🔧 إعداد المستخدمين الافتراضيين...');\n            // Check if Firebase is available\n            if (!_firebase__WEBPACK_IMPORTED_MODULE_1__.auth || !_firebase__WEBPACK_IMPORTED_MODULE_1__.db) {\n                console.log('⚠️ Firebase غير متاح، استخدام التخزين المحلي');\n                this.initializeLocalUsers();\n                return;\n            }\n            for (const userData of defaultUsers){\n                // Check if user already exists\n                const existingUser = await this.getUserByUsername(userData.username);\n                if (existingUser) {\n                    console.log(\"✅ المستخدم \".concat(userData.username, \" موجود مسبقاً\"));\n                    continue;\n                }\n                // Create Firebase Auth user\n                try {\n                    const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, userData.email, userData.password);\n                    // Create user document in Firestore\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n                        username: userData.username,\n                        email: userData.email,\n                        name: userData.name,\n                        phone: userData.phone,\n                        role: userData.role,\n                        isActive: true,\n                        createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                        updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                        createdBy: 'system'\n                    });\n                    console.log(\"✅ تم إنشاء المستخدم: \".concat(userData.username));\n                } catch (error) {\n                    if (error.code === 'auth/email-already-in-use') {\n                        console.log(\"⚠️ البريد الإلكتروني \".concat(userData.email, \" مستخدم مسبقاً\"));\n                    } else {\n                        console.error(\"❌ خطأ في إنشاء المستخدم \".concat(userData.username, \":\"), error);\n                        // Fallback to local storage\n                        this.initializeLocalUsers();\n                        return;\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ خطأ في إعداد المستخدمين الافتراضيين:', error);\n            // Fallback to local storage\n            this.initializeLocalUsers();\n        }\n    },\n    // Initialize users in local storage as fallback\n    initializeLocalUsers () {\n        try {\n            const existingUsers = localStorage.getItem('marsal_users');\n            if (existingUsers) {\n                console.log('✅ المستخدمين موجودين في التخزين المحلي');\n                return;\n            }\n            const localUsers = defaultUsers.map((userData, index)=>({\n                    id: \"local_\".concat(index + 1),\n                    username: userData.username,\n                    email: userData.email,\n                    name: userData.name,\n                    phone: userData.phone,\n                    role: userData.role,\n                    isActive: true,\n                    createdAt: new Date(),\n                    updatedAt: new Date(),\n                    createdBy: 'system',\n                    password: userData.password // Store password for local auth\n                }));\n            localStorage.setItem('marsal_users', JSON.stringify(localUsers));\n            console.log('✅ تم إنشاء المستخدمين في التخزين المحلي');\n        } catch (error) {\n            console.error('❌ خطأ في إنشاء المستخدمين المحليين:', error);\n        }\n    },\n    // Login with username/password - Firebase or Local\n    async login (username, password) {\n        try {\n            console.log('🔐 محاولة تسجيل الدخول للمستخدم:', username);\n            // Try Firebase first\n            if (_firebase__WEBPACK_IMPORTED_MODULE_1__.auth && _firebase__WEBPACK_IMPORTED_MODULE_1__.db) {\n                try {\n                    return await this.loginWithFirebase(username, password);\n                } catch (error) {\n                    console.warn('⚠️ فشل تسجيل الدخول مع Firebase، محاولة التخزين المحلي');\n                }\n            }\n            // Fallback to local storage\n            return await this.loginWithLocalStorage(username, password);\n        } catch (error) {\n            console.error('❌ خطأ في تسجيل الدخول:', error);\n            return {\n                success: false,\n                error: error.message || 'خطأ في تسجيل الدخول'\n            };\n        }\n    },\n    // Login with Firebase\n    async loginWithFirebase (username, password) {\n        // Get user by username to find email\n        const user = await this.getUserByUsername(username);\n        if (!user) {\n            throw new Error('اسم المستخدم غير موجود');\n        }\n        if (!user.isActive) {\n            throw new Error('الحساب غير مفعل');\n        }\n        // Sign in with email and password\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, user.email, password);\n        // Update last login\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n            lastLogin: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)()\n        }, {\n            merge: true\n        });\n        console.log('✅ تم تسجيل الدخول مع Firebase بنجاح');\n        return {\n            success: true,\n            user: {\n                ...user,\n                id: userCredential.user.uid\n            }\n        };\n    },\n    // Login with local storage\n    async loginWithLocalStorage (username, password) {\n        const usersData = localStorage.getItem('marsal_users');\n        if (!usersData) {\n            throw new Error('لا يوجد مستخدمين مسجلين');\n        }\n        const users = JSON.parse(usersData);\n        const user = users.find((u)=>u.username === username);\n        if (!user) {\n            throw new Error('اسم المستخدم غير موجود');\n        }\n        if (!user.isActive) {\n            throw new Error('الحساب غير مفعل');\n        }\n        if (user.password !== password) {\n            throw new Error('كلمة المرور غير صحيحة');\n        }\n        // Update last login\n        user.lastLogin = new Date();\n        const updatedUsers = users.map((u)=>u.id === user.id ? user : u);\n        localStorage.setItem('marsal_users', JSON.stringify(updatedUsers));\n        console.log('✅ تم تسجيل الدخول مع التخزين المحلي بنجاح');\n        return {\n            success: true,\n            user: {\n                id: user.id,\n                username: user.username,\n                email: user.email,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                isActive: user.isActive,\n                createdAt: new Date(user.createdAt),\n                updatedAt: new Date(user.updatedAt),\n                createdBy: user.createdBy,\n                lastLogin: user.lastLogin ? new Date(user.lastLogin) : undefined\n            }\n        };\n    },\n    // Get user by username - Firebase or Local\n    async getUserByUsername (username) {\n        try {\n            // Try Firebase first\n            if (_firebase__WEBPACK_IMPORTED_MODULE_1__.db) {\n                try {\n                    const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users');\n                    const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('username', '==', username));\n                    const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n                    if (!snapshot.empty) {\n                        var _data_createdAt, _data_updatedAt, _data_lastLogin;\n                        const doc = snapshot.docs[0];\n                        const data = doc.data();\n                        return {\n                            id: doc.id,\n                            username: data.username,\n                            email: data.email,\n                            name: data.name,\n                            phone: data.phone,\n                            role: data.role,\n                            isActive: data.isActive,\n                            createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date(),\n                            updatedAt: ((_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()) || new Date(),\n                            createdBy: data.createdBy,\n                            lastLogin: (_data_lastLogin = data.lastLogin) === null || _data_lastLogin === void 0 ? void 0 : _data_lastLogin.toDate()\n                        };\n                    }\n                } catch (error) {\n                    console.warn('⚠️ فشل البحث في Firebase، محاولة التخزين المحلي');\n                }\n            }\n            // Fallback to local storage\n            const usersData = localStorage.getItem('marsal_users');\n            if (!usersData) {\n                return null;\n            }\n            const users = JSON.parse(usersData);\n            const user = users.find((u)=>u.username === username);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                username: user.username,\n                email: user.email,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                isActive: user.isActive,\n                createdAt: new Date(user.createdAt),\n                updatedAt: new Date(user.updatedAt),\n                createdBy: user.createdBy,\n                lastLogin: user.lastLogin ? new Date(user.lastLogin) : undefined\n            };\n        } catch (error) {\n            console.error('Error getting user by username:', error);\n            return null;\n        }\n    },\n    // Get current user\n    async getCurrentUser () {\n        try {\n            var _data_createdAt, _data_updatedAt, _data_lastLogin;\n            const firebaseUser = _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n            if (!firebaseUser) {\n                return null;\n            }\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', firebaseUser.uid));\n            if (!userDoc.exists()) {\n                return null;\n            }\n            const data = userDoc.data();\n            return {\n                id: userDoc.id,\n                username: data.username,\n                email: data.email,\n                name: data.name,\n                phone: data.phone,\n                role: data.role,\n                isActive: data.isActive,\n                createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date(),\n                updatedAt: ((_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()) || new Date(),\n                createdBy: data.createdBy,\n                lastLogin: (_data_lastLogin = data.lastLogin) === null || _data_lastLogin === void 0 ? void 0 : _data_lastLogin.toDate()\n            };\n        } catch (error) {\n            console.error('Error getting current user:', error);\n            return null;\n        }\n    },\n    // Logout\n    async logout () {\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth);\n            console.log('✅ تم تسجيل الخروج بنجاح');\n        } catch (error) {\n            console.error('❌ خطأ في تسجيل الخروج:', error);\n            throw error;\n        }\n    },\n    // Listen to auth state changes\n    onAuthStateChanged (callback) {\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, async (firebaseUser)=>{\n            if (firebaseUser) {\n                const user = await this.getCurrentUser();\n                callback(user);\n            } else {\n                callback(null);\n            }\n        });\n    },\n    // Create new user (only for managers)\n    async createUser (userData) {\n        try {\n            console.log('👤 إنشاء مستخدم جديد:', userData.username);\n            // Create Firebase Auth user\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, userData.email, userData.password);\n            // Create user document in Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n                username: userData.username,\n                email: userData.email,\n                name: userData.name,\n                phone: userData.phone,\n                role: userData.role,\n                isActive: true,\n                createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                createdBy: userData.createdBy\n            });\n            console.log('✅ تم إنشاء المستخدم بنجاح:', userData.username);\n            const newUser = {\n                id: userCredential.user.uid,\n                username: userData.username,\n                email: userData.email,\n                name: userData.name,\n                phone: userData.phone,\n                role: userData.role,\n                isActive: true,\n                createdAt: new Date(),\n                updatedAt: new Date(),\n                createdBy: userData.createdBy\n            };\n            return {\n                success: true,\n                user: newUser\n            };\n        } catch (error) {\n            console.error('❌ خطأ في إنشاء المستخدم:', error);\n            let errorMessage = 'خطأ في إنشاء المستخدم';\n            switch(error.code){\n                case 'auth/email-already-in-use':\n                    errorMessage = 'البريد الإلكتروني مستخدم مسبقاً';\n                    break;\n                case 'auth/weak-password':\n                    errorMessage = 'كلمة المرور ضعيفة';\n                    break;\n                case 'auth/invalid-email':\n                    errorMessage = 'البريد الإلكتروني غير صحيح';\n                    break;\n                default:\n                    errorMessage = error.message || 'خطأ غير معروف';\n            }\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    },\n    // Get all users (for managers)\n    async getAllUsers () {\n        try {\n            const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users');\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(usersRef);\n            return snapshot.docs.map((doc)=>{\n                var _data_createdAt, _data_updatedAt, _data_lastLogin;\n                const data = doc.data();\n                return {\n                    id: doc.id,\n                    username: data.username,\n                    email: data.email,\n                    name: data.name,\n                    phone: data.phone,\n                    role: data.role,\n                    isActive: data.isActive,\n                    createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date(),\n                    updatedAt: ((_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()) || new Date(),\n                    createdBy: data.createdBy,\n                    lastLogin: (_data_lastLogin = data.lastLogin) === null || _data_lastLogin === void 0 ? void 0 : _data_lastLogin.toDate()\n                };\n            });\n        } catch (error) {\n            console.error('Error getting all users:', error);\n            return [];\n        }\n    },\n    // Update user (for managers)\n    async updateUser (userId, updates) {\n        try {\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId), {\n                ...updates,\n                updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)()\n            }, {\n                merge: true\n            });\n            console.log('✅ تم تحديث المستخدم:', userId);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('❌ خطأ في تحديث المستخدم:', error);\n            return {\n                success: false,\n                error: error.message || 'خطأ في تحديث المستخدم'\n            };\n        }\n    },\n    // Delete user (for managers)\n    async deleteUser (userId) {\n        try {\n            // Delete from Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId));\n            // Note: Deleting from Firebase Auth requires Admin SDK\n            // For now, we just deactivate the user\n            await this.updateUser(userId, {\n                isActive: false\n            });\n            console.log('✅ تم حذف المستخدم:', userId);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('❌ خطأ في حذف المستخدم:', error);\n            return {\n                success: false,\n                error: error.message || 'خطأ في حذف المستخدم'\n            };\n        }\n    },\n    // Check if Firebase is connected\n    async checkConnection () {\n        try {\n            // Try to get current user\n            const user = _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n            console.log('✅ Firebase Auth متصل');\n            return {\n                connected: true,\n                message: 'Firebase Auth متصل بنجاح'\n            };\n        } catch (error) {\n            console.error('❌ Firebase Auth غير متصل:', error);\n            return {\n                connected: false,\n                message: \"خطأ في الاتصال: \".concat(error.message)\n            };\n        }\n    }\n};\n// Auto-initialize default users when the module loads\nfirebaseAuthService.initializeDefaultUsers().catch(console.error);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/firebase-auth.ts\n"));

/***/ })

});