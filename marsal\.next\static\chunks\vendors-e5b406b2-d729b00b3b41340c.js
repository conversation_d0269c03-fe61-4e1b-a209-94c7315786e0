"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4979],{7779:(t,e,r)=>{r.d(e,{A:()=>f});var n=r(58892),o=r(27323),i=r(39894),a=r(93782),s=r(25879),h=r(49202),d=r(9568),u=r(48798),c=r(10077),l=r(63479);let f=function(){function t(){}return t.randomize253State=function(t){var e=a.Qw+(149*t%253+1);return e<=254?e:e-254},t.encodeHighLevel=function(t,e,r,c,l){void 0===e&&(e=0),void 0===r&&(r=null),void 0===c&&(c=null),void 0===l&&(l=!1);var f=new i.S,g=[new n.a,f,new u._,new d.y,new s.b,new o.B],p=new h.Q(t);p.setSymbolShape(e),p.setSizeConstraints(r,c),t.startsWith(a.h_)&&t.endsWith(a.TG)?(p.writeCodeword(a.tf),p.setSkipAtEnd(2),p.pos+=a.h_.length):t.startsWith(a.eB)&&t.endsWith(a.TG)&&(p.writeCodeword(a.mD),p.setSkipAtEnd(2),p.pos+=a.eB.length);var C=a.d2;for(l&&(f.encodeMaximal(p),C=p.getNewEncoding(),p.resetEncoderSignal());p.hasMoreCharacters();)g[C].encode(p),p.getNewEncoding()>=0&&(C=p.getNewEncoding(),p.resetEncoderSignal());var w=p.getCodewordCount();p.updateSymbolInfo();var A=p.getSymbolInfo().getDataCapacity();w<A&&C!==a.d2&&C!==a.mt&&C!==a.uf&&p.writeCodeword("\xfe");var m=p.getCodewords();for(m.length()<A&&m.append(a.Qw);m.length()<A;)m.append(this.randomize253State(m.length()+1));return p.getCodewords().toString()},t.lookAheadTest=function(t,e,r){var n=this.lookAheadTestIntern(t,e,r);if(r===a.VK&&n===a.VK){for(var o=Math.min(e+3,t.length),i=e;i<o;i++)if(!this.isNativeX12(t.charCodeAt(i)))return a.d2}else if(r===a.uf&&n===a.uf){for(var o=Math.min(e+4,t.length),i=e;i<o;i++)if(!this.isNativeEDIFACT(t.charCodeAt(i)))return a.d2}return n},t.lookAheadTestIntern=function(t,e,r){if(e>=t.length)return r;r===a.d2?n=[0,1,1,1,1,1.25]:(n=[1,2,2,2,2,2.25])[r]=0;for(var n,o=0,i=new Uint8Array(6),s=[];;){if(e+o===t.length){c.A.fill(i,0),c.A.fill(s,0);var h=this.findMinimums(n,s,l.A.MAX_VALUE,i),d=this.getMinimumCount(i);if(s[a.d2]===h)return a.d2;if(1===d){if(i[a.mt]>0)return a.mt;if(i[a.uf]>0)return a.uf;if(i[a.VL]>0)return a.VL;if(i[a.VK]>0)return a.VK}return a.fG}var u=t.charCodeAt(e+o);if(o++,this.isDigit(u)?n[a.d2]+=.5:this.isExtendedASCII(u)?(n[a.d2]=Math.ceil(n[a.d2]),n[a.d2]+=2):(n[a.d2]=Math.ceil(n[a.d2]),n[a.d2]++),this.isNativeC40(u)?n[a.fG]+=2/3:this.isExtendedASCII(u)?n[a.fG]+=8/3:n[a.fG]+=4/3,this.isNativeText(u)?n[a.VL]+=2/3:this.isExtendedASCII(u)?n[a.VL]+=8/3:n[a.VL]+=4/3,this.isNativeX12(u)?n[a.VK]+=2/3:this.isExtendedASCII(u)?n[a.VK]+=13/3:n[a.VK]+=10/3,this.isNativeEDIFACT(u)?n[a.uf]+=.75:this.isExtendedASCII(u)?n[a.uf]+=4.25:n[a.uf]+=3.25,this.isSpecialB256(u)?n[a.mt]+=4:n[a.mt]++,o>=4){if(c.A.fill(i,0),c.A.fill(s,0),this.findMinimums(n,s,l.A.MAX_VALUE,i),s[a.d2]<this.min(s[a.mt],s[a.fG],s[a.VL],s[a.VK],s[a.uf]))return a.d2;if(s[a.mt]<s[a.d2]||s[a.mt]+1<this.min(s[a.fG],s[a.VL],s[a.VK],s[a.uf]))return a.mt;if(s[a.uf]+1<this.min(s[a.mt],s[a.fG],s[a.VL],s[a.VK],s[a.d2]))return a.uf;if(s[a.VL]+1<this.min(s[a.mt],s[a.fG],s[a.uf],s[a.VK],s[a.d2]))return a.VL;if(s[a.VK]+1<this.min(s[a.mt],s[a.fG],s[a.uf],s[a.VL],s[a.d2]))return a.VK;if(s[a.fG]+1<this.min(s[a.d2],s[a.mt],s[a.uf],s[a.VL])){if(s[a.fG]<s[a.VK])return a.fG;if(s[a.fG]===s[a.VK]){for(var f=e+o+1;f<t.length;){var g=t.charCodeAt(f);if(this.isX12TermSep(g))return a.VK;if(!this.isNativeX12(g))break;f++}return a.fG}}}}},t.min=function(t,e,r,n,o){var i=Math.min(t,Math.min(e,Math.min(r,n)));return void 0===o?i:Math.min(i,o)},t.findMinimums=function(t,e,r,n){for(var o=0;o<6;o++){var i=e[o]=Math.ceil(t[o]);r>i&&(r=i,c.A.fill(n,0)),r===i&&(n[o]=n[o]+1)}return r},t.getMinimumCount=function(t){for(var e=0,r=0;r<6;r++)e+=t[r];return e||0},t.isDigit=function(t){return t>=48&&t<=57},t.isExtendedASCII=function(t){return t>=128&&t<=255},t.isNativeC40=function(t){return 32===t||t>=48&&t<=57||t>=65&&t<=90},t.isNativeText=function(t){return 32===t||t>=48&&t<=57||t>=97&&t<=122},t.isNativeX12=function(t){return this.isX12TermSep(t)||32===t||t>=48&&t<=57||t>=65&&t<=90},t.isX12TermSep=function(t){return 13===t||42===t||62===t},t.isNativeEDIFACT=function(t){return t>=32&&t<=94},t.isSpecialB256=function(t){return!1},t.determineConsecutiveDigitCount=function(t,e){void 0===e&&(e=0);for(var r=t.length,n=e;n<r&&this.isDigit(t.charCodeAt(n));)n++;return n-e},t.illegalCharacter=function(t){var e=l.A.toHexString(t.charCodeAt(0));throw Error("Illegal character: "+t+" (0x"+(e="0000".substring(0,4-e.length)+e)+")")},t}()},9568:(t,e,r)=>{r.d(e,{y:()=>d});var n=r(23510),o=r(1933),i=r(39894),a=r(7779),s=r(93782),h=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return h(e,t),e.prototype.getEncodingMode=function(){return s.VK},e.prototype.encode=function(t){for(var e=new o.A;t.hasMoreCharacters();){var r=t.getCurrentChar();if(t.pos++,this.encodeChar(r,e),e.length()%3==0&&(this.writeNextTriplet(t,e),a.A.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode())){t.signalEncoderChange(s.d2);break}}this.handleEOD(t,e)},e.prototype.encodeChar=function(t,e){switch(t){case 13:e.append(0);break;case 42:e.append(1);break;case 62:e.append(2);break;case 32:e.append(3);break;default:t>=48&&t<=57?e.append(t-48+4):t>=65&&t<=90?e.append(t-65+14):a.A.illegalCharacter(n.A.getCharAt(t))}return 1},e.prototype.handleEOD=function(t,e){t.updateSymbolInfo();var r=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount(),n=e.length();t.pos-=n,(t.getRemainingCharacters()>1||r>1||t.getRemainingCharacters()!==r)&&t.writeCodeword(s.OM),0>t.getNewEncoding()&&t.signalEncoderChange(s.d2)},e}(i.S)},25879:(t,e,r)=>{r.d(e,{b:()=>s});var n=r(23510),o=r(1933),i=r(93782),a=r(7779),s=function(){function t(){}return t.prototype.getEncodingMode=function(){return i.uf},t.prototype.encode=function(t){for(var e=new o.A;t.hasMoreCharacters();){var r=t.getCurrentChar();if(this.encodeChar(r,e),t.pos++,e.length()>=4){t.writeCodewords(this.encodeToCodewords(e.toString()));var s=e.toString().substring(4);if(e.setLengthToZero(),e.append(s),a.A.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode()){t.signalEncoderChange(i.d2);break}}}e.append(n.A.getCharAt(31)),this.handleEOD(t,e)},t.prototype.handleEOD=function(t,e){try{var r=e.length();if(0===r)return;if(1===r){t.updateSymbolInfo();var n=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount(),o=t.getRemainingCharacters();if(o>n&&(t.updateSymbolInfo(t.getCodewordCount()+1),n=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount()),o<=n&&n<=2)return}if(r>4)throw Error("Count must not exceed 4");var a=r-1,s=this.encodeToCodewords(e.toString()),h=!t.hasMoreCharacters()&&a<=2;if(a<=2){t.updateSymbolInfo(t.getCodewordCount()+a);var n=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount();n>=3&&(h=!1,t.updateSymbolInfo(t.getCodewordCount()+s.length))}h?(t.resetSymbolInfo(),t.pos-=a):t.writeCodewords(s)}finally{t.signalEncoderChange(i.d2)}},t.prototype.encodeChar=function(t,e){t>=32&&t<=63?e.append(t):t>=64&&t<=94?e.append(n.A.getCharAt(t-64)):a.A.illegalCharacter(n.A.getCharAt(t))},t.prototype.encodeToCodewords=function(t){var e=t.length;if(0===e)throw Error("StringBuilder must not be empty");var r=(t.charAt(0).charCodeAt(0)<<18)+((e>=2?t.charAt(1).charCodeAt(0):0)<<12)+((e>=3?t.charAt(2).charCodeAt(0):0)<<6)+(e>=4?t.charAt(3).charCodeAt(0):0),n=new o.A;return n.append(r>>16&255),e>=2&&n.append(r>>8&255),e>=3&&n.append(255&r),n.toString()},t}()},27323:(t,e,r)=>{r.d(e,{B:()=>s});var n=r(23510),o=r(1933),i=r(7779),a=r(93782),s=function(){function t(){}return t.prototype.getEncodingMode=function(){return a.mt},t.prototype.encode=function(t){var e=new o.A;for(e.append(0);t.hasMoreCharacters();){var r=t.getCurrentChar();if(e.append(r),t.pos++,i.A.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode()){t.signalEncoderChange(a.d2);break}}var s=e.length()-1,h=t.getCodewordCount()+s+1;t.updateSymbolInfo(h);var d=t.getSymbolInfo().getDataCapacity()-h>0;if(t.hasMoreCharacters()||d)if(s<=249)e.setCharAt(0,n.A.getCharAt(s));else if(s<=1555)e.setCharAt(0,n.A.getCharAt(Math.floor(s/250)+249)),e.insert(1,n.A.getCharAt(s%250));else throw Error("Message length not in valid ranges: "+s);for(var u=0,r=e.length();u<r;u++)t.writeCodeword(this.randomize255State(e.charAt(u).charCodeAt(0),t.getCodewordCount()+1))},t.prototype.randomize255State=function(t,e){var r=t+(149*e%255+1);return r<=255?r:r-256},t}()},39894:(t,e,r)=>{r.d(e,{S:()=>a});var n=r(1933),o=r(7779),i=r(93782),a=function(){function t(){}return t.prototype.getEncodingMode=function(){return i.fG},t.prototype.encodeMaximal=function(t){for(var e=new n.A,r=0,o=t.pos,a=0;t.hasMoreCharacters();){var s=t.getCurrentChar();t.pos++,r=this.encodeChar(s,e),e.length()%3==0&&(o=t.pos,a=e.length())}if(a!==e.length()){var h=Math.floor(e.length()/3*2),d=Math.floor(t.getCodewordCount()+h+1);t.updateSymbolInfo(d);var u=t.getSymbolInfo().getDataCapacity()-d,c=Math.floor(e.length()%3);(2===c&&2!==u||1===c&&(r>3||1!==u))&&(t.pos=o)}e.length()>0&&t.writeCodeword(i.X7),this.handleEOD(t,e)},t.prototype.encode=function(t){for(var e=new n.A;t.hasMoreCharacters();){var r=t.getCurrentChar();t.pos++;var a=this.encodeChar(r,e),s=2*Math.floor(e.length()/3),h=t.getCodewordCount()+s;t.updateSymbolInfo(h);var d=t.getSymbolInfo().getDataCapacity()-h;if(!t.hasMoreCharacters()){var u=new n.A;for(e.length()%3==2&&2!==d&&(a=this.backtrackOneCharacter(t,e,u,a));e.length()%3==1&&(a>3||1!==d);)a=this.backtrackOneCharacter(t,e,u,a);break}if(e.length()%3==0&&o.A.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode()){t.signalEncoderChange(i.d2);break}}this.handleEOD(t,e)},t.prototype.backtrackOneCharacter=function(t,e,r,n){var o=e.length(),i=e.toString().substring(0,o-n);e.setLengthToZero(),e.append(i),t.pos--;var a=t.getCurrentChar();return n=this.encodeChar(a,r),t.resetSymbolInfo(),n},t.prototype.writeNextTriplet=function(t,e){t.writeCodewords(this.encodeToCodewords(e.toString()));var r=e.toString().substring(3);e.setLengthToZero(),e.append(r)},t.prototype.handleEOD=function(t,e){var r=Math.floor(e.length()/3*2),n=e.length()%3,o=t.getCodewordCount()+r;t.updateSymbolInfo(o);var a=t.getSymbolInfo().getDataCapacity()-o;if(2===n){for(e.append("\0");e.length()>=3;)this.writeNextTriplet(t,e);t.hasMoreCharacters()&&t.writeCodeword(i.eb)}else if(1===a&&1===n){for(;e.length()>=3;)this.writeNextTriplet(t,e);t.hasMoreCharacters()&&t.writeCodeword(i.eb),t.pos--}else if(0===n){for(;e.length()>=3;)this.writeNextTriplet(t,e);(a>0||t.hasMoreCharacters())&&t.writeCodeword(i.eb)}else throw Error("Unexpected case. Please report!");t.signalEncoderChange(i.d2)},t.prototype.encodeChar=function(t,e){var r;return 32===t?(e.append(3),1):t>=48&&t<=57?(e.append(t-48+4),1):t>=65&&t<=90?(e.append(t-65+14),1):t<32?(e.append(0),e.append(t),2):t<=47?(e.append(1),e.append(t-33),2):t<=64?(e.append(1),e.append(t-58+15),2):t<=95?(e.append(1),e.append(t-91+22),2):t<=127?(e.append(2),e.append(t-96),2):(e.append("1\x1e"),2+this.encodeChar(t-128,e))},t.prototype.encodeToCodewords=function(t){var e=1600*t.charCodeAt(0)+40*t.charCodeAt(1)+t.charCodeAt(2)+1,r=new n.A;return r.append(e/256),r.append(e%256),r.toString()},t}()},41205:(t,e,r)=>{r.d(e,{A:()=>D});var n=r(25969),o=r(10782),i=r(79417),a=r(438),s=r(69071),h=r(43358),d=r(322),u=r(66950),c=r(55192),l=r(60109),f=r(71534),g=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},p=function(){function t(t,e,r){this.ecCodewords=t,this.ecBlocks=[e],r&&this.ecBlocks.push(r)}return t.prototype.getECCodewords=function(){return this.ecCodewords},t.prototype.getECBlocks=function(){return this.ecBlocks},t}(),C=function(){function t(t,e){this.count=t,this.dataCodewords=e}return t.prototype.getCount=function(){return this.count},t.prototype.getDataCodewords=function(){return this.dataCodewords},t}(),w=function(){function t(t,e,r,n,o,i){this.versionNumber=t,this.symbolSizeRows=e,this.symbolSizeColumns=r,this.dataRegionSizeRows=n,this.dataRegionSizeColumns=o,this.ecBlocks=i;var a,s,h=0,d=i.getECCodewords(),u=i.getECBlocks();try{for(var c=g(u),l=c.next();!l.done;l=c.next()){var f=l.value;h+=f.getCount()*(f.getDataCodewords()+d)}}catch(t){a={error:t}}finally{try{l&&!l.done&&(s=c.return)&&s.call(c)}finally{if(a)throw a.error}}this.totalCodewords=h}return t.prototype.getVersionNumber=function(){return this.versionNumber},t.prototype.getSymbolSizeRows=function(){return this.symbolSizeRows},t.prototype.getSymbolSizeColumns=function(){return this.symbolSizeColumns},t.prototype.getDataRegionSizeRows=function(){return this.dataRegionSizeRows},t.prototype.getDataRegionSizeColumns=function(){return this.dataRegionSizeColumns},t.prototype.getTotalCodewords=function(){return this.totalCodewords},t.prototype.getECBlocks=function(){return this.ecBlocks},t.getVersionForDimensions=function(e,r){var n,o;if((1&e)!=0||(1&r)!=0)throw new f.A;try{for(var i=g(t.VERSIONS),a=i.next();!a.done;a=i.next()){var s=a.value;if(s.symbolSizeRows===e&&s.symbolSizeColumns===r)return s}}catch(t){n={error:t}}finally{try{a&&!a.done&&(o=i.return)&&o.call(i)}finally{if(n)throw n.error}}throw new f.A},t.prototype.toString=function(){return""+this.versionNumber},t.buildVersions=function(){return[new t(1,10,10,8,8,new p(5,new C(1,3))),new t(2,12,12,10,10,new p(7,new C(1,5))),new t(3,14,14,12,12,new p(10,new C(1,8))),new t(4,16,16,14,14,new p(12,new C(1,12))),new t(5,18,18,16,16,new p(14,new C(1,18))),new t(6,20,20,18,18,new p(18,new C(1,22))),new t(7,22,22,20,20,new p(20,new C(1,30))),new t(8,24,24,22,22,new p(24,new C(1,36))),new t(9,26,26,24,24,new p(28,new C(1,44))),new t(10,32,32,14,14,new p(36,new C(1,62))),new t(11,36,36,16,16,new p(42,new C(1,86))),new t(12,40,40,18,18,new p(48,new C(1,114))),new t(13,44,44,20,20,new p(56,new C(1,144))),new t(14,48,48,22,22,new p(68,new C(1,174))),new t(15,52,52,24,24,new p(42,new C(2,102))),new t(16,64,64,14,14,new p(56,new C(2,140))),new t(17,72,72,16,16,new p(36,new C(4,92))),new t(18,80,80,18,18,new p(48,new C(4,114))),new t(19,88,88,20,20,new p(56,new C(4,144))),new t(20,96,96,22,22,new p(68,new C(4,174))),new t(21,104,104,24,24,new p(56,new C(6,136))),new t(22,120,120,18,18,new p(68,new C(6,175))),new t(23,132,132,20,20,new p(62,new C(8,163))),new t(24,144,144,22,22,new p(62,new C(8,156),new C(2,155))),new t(25,8,18,6,16,new p(7,new C(1,5))),new t(26,8,32,6,14,new p(11,new C(1,10))),new t(27,12,26,10,24,new p(14,new C(1,16))),new t(28,12,36,10,16,new p(18,new C(1,22))),new t(29,16,36,14,16,new p(24,new C(1,32))),new t(30,16,48,14,22,new p(28,new C(1,49)))]},t.VERSIONS=t.buildVersions(),t}(),A=r(38988),m=function(){function t(e){var r=e.getHeight();if(r<8||r>144||(1&r)!=0)throw new f.A;this.version=t.readVersion(e),this.mappingBitMatrix=this.extractDataRegion(e),this.readMappingMatrix=new o.A(this.mappingBitMatrix.getWidth(),this.mappingBitMatrix.getHeight())}return t.prototype.getVersion=function(){return this.version},t.readVersion=function(t){var e=t.getHeight(),r=t.getWidth();return w.getVersionForDimensions(e,r)},t.prototype.readCodewords=function(){var t=new Int8Array(this.version.getTotalCodewords()),e=0,r=4,n=0,o=this.mappingBitMatrix.getHeight(),i=this.mappingBitMatrix.getWidth(),a=!1,s=!1,h=!1,d=!1;do if(r!==o||0!==n||a)if(r!==o-2||0!==n||(3&i)==0||s)if(r!==o+4||2!==n||(7&i)!=0||h)if(r!==o-2||0!==n||(7&i)!=4||d){do r<o&&n>=0&&!this.readMappingMatrix.get(n,r)&&(t[e++]=255&this.readUtah(r,n,o,i)),r-=2,n+=2;while(r>=0&&n<i);r+=1,n+=3;do r>=0&&n<i&&!this.readMappingMatrix.get(n,r)&&(t[e++]=255&this.readUtah(r,n,o,i)),r+=2,n-=2;while(r<o&&n>=0);r+=3,n+=1}else t[e++]=255&this.readCorner4(o,i),r-=2,n+=2,d=!0;else t[e++]=255&this.readCorner3(o,i),r-=2,n+=2,h=!0;else t[e++]=255&this.readCorner2(o,i),r-=2,n+=2,s=!0;else t[e++]=255&this.readCorner1(o,i),r-=2,n+=2,a=!0;while(r<o||n<i);if(e!==this.version.getTotalCodewords())throw new f.A;return t},t.prototype.readModule=function(t,e,r,n){return t<0&&(t+=r,e+=4-(r+4&7)),e<0&&(e+=n,t+=4-(n+4&7)),this.readMappingMatrix.set(e,t),this.mappingBitMatrix.get(e,t)},t.prototype.readUtah=function(t,e,r,n){var o=0;return this.readModule(t-2,e-2,r,n)&&(o|=1),o<<=1,this.readModule(t-2,e-1,r,n)&&(o|=1),o<<=1,this.readModule(t-1,e-2,r,n)&&(o|=1),o<<=1,this.readModule(t-1,e-1,r,n)&&(o|=1),o<<=1,this.readModule(t-1,e,r,n)&&(o|=1),o<<=1,this.readModule(t,e-2,r,n)&&(o|=1),o<<=1,this.readModule(t,e-1,r,n)&&(o|=1),o<<=1,this.readModule(t,e,r,n)&&(o|=1),o},t.prototype.readCorner1=function(t,e){var r=0;return this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,1,t,e)&&(r|=1),r<<=1,this.readModule(t-1,2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r<<=1,this.readModule(2,e-1,t,e)&&(r|=1),r<<=1,this.readModule(3,e-1,t,e)&&(r|=1),r},t.prototype.readCorner2=function(t,e){var r=0;return this.readModule(t-3,0,t,e)&&(r|=1),r<<=1,this.readModule(t-2,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(0,e-4,t,e)&&(r|=1),r<<=1,this.readModule(0,e-3,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r},t.prototype.readCorner3=function(t,e){var r=0;return this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,e-1,t,e)&&(r|=1),r<<=1,this.readModule(0,e-3,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-3,t,e)&&(r|=1),r<<=1,this.readModule(1,e-2,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r},t.prototype.readCorner4=function(t,e){var r=0;return this.readModule(t-3,0,t,e)&&(r|=1),r<<=1,this.readModule(t-2,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r<<=1,this.readModule(2,e-1,t,e)&&(r|=1),r<<=1,this.readModule(3,e-1,t,e)&&(r|=1),r},t.prototype.extractDataRegion=function(t){var e=this.version.getSymbolSizeRows(),r=this.version.getSymbolSizeColumns();if(t.getHeight()!==e)throw new A.A("Dimension of bitMatrix must match the version size");for(var n=this.version.getDataRegionSizeRows(),i=this.version.getDataRegionSizeColumns(),a=e/n|0,s=r/i|0,h=new o.A(s*i,a*n),d=0;d<a;++d)for(var u=d*n,c=0;c<s;++c)for(var l=c*i,f=0;f<n;++f)for(var g=d*(n+2)+1+f,p=u+f,C=0;C<i;++C){var w=c*(i+2)+1+C;if(t.get(w,g)){var m=l+C;h.set(m,p)}}return h},t}(),v=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},y=function(){function t(t,e){this.numDataCodewords=t,this.codewords=e}return t.getDataBlocks=function(e,r){var n,o,i,a,s=r.getECBlocks(),h=0,d=s.getECBlocks();try{for(var u=v(d),c=u.next();!c.done;c=u.next()){var l=c.value;h+=l.getCount()}}catch(t){n={error:t}}finally{try{c&&!c.done&&(o=u.return)&&o.call(u)}finally{if(n)throw n.error}}var f=Array(h),g=0;try{for(var p=v(d),C=p.next();!C.done;C=p.next())for(var l=C.value,w=0;w<l.getCount();w++){var m=l.getDataCodewords(),y=s.getECCodewords()+m;f[g++]=new t(m,new Uint8Array(y))}}catch(t){i={error:t}}finally{try{C&&!C.done&&(a=p.return)&&a.call(p)}finally{if(i)throw i.error}}for(var S=f[0].codewords.length-s.getECCodewords(),E=S-1,I=0,w=0;w<E;w++)for(var b=0;b<g;b++)f[b].codewords[w]=e[I++];for(var T=24===r.getVersionNumber(),M=T?8:g,b=0;b<M;b++)f[b].codewords[S-1]=e[I++];for(var _=f[0].codewords.length,w=S;w<_;w++)for(var b=0;b<g;b++){var B=T?(b+8)%g:b,D=T&&B>7?w-1:w;f[B].codewords[D]=e[I++]}if(I!==e.length)throw new A.A;return f},t.prototype.getNumDataCodewords=function(){return this.numDataCodewords},t.prototype.getCodewords=function(){return this.codewords},t}(),S=r(71411),E=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},I=function(){function t(){this.rsDecoder=new l.A(c.A.DATA_MATRIX_FIELD_256)}return t.prototype.decode=function(t){var e,r,n=new m(t),o=n.getVersion(),i=n.readCodewords(),a=y.getDataBlocks(i,o),s=0;try{for(var h=E(a),d=h.next();!d.done;d=h.next()){var u=d.value;s+=u.getNumDataCodewords()}}catch(t){e={error:t}}finally{try{d&&!d.done&&(r=h.return)&&r.call(h)}finally{if(e)throw e.error}}for(var c=new Uint8Array(s),l=a.length,f=0;f<l;f++){var g=a[f],p=g.getCodewords(),C=g.getNumDataCodewords();this.correctErrors(p,C);for(var w=0;w<C;w++)c[w*l+f]=p[w]}return S.A.decode(c)},t.prototype.correctErrors=function(t,e){var r=new Int32Array(t);try{this.rsDecoder.decode(r,t.length-e)}catch(t){throw new u.A}for(var n=0;n<e;n++)t[n]=r[n]},t}(),b=r(17391),T=r(56451),M=r(20367),_=r(56595),B=function(){function t(t){this.image=t,this.rectangleDetector=new b.A(this.image)}return t.prototype.detect=function(){var e=this.rectangleDetector.detect(),r=this.detectSolid1(e);if((r=this.detectSolid2(r))[3]=this.correctTopRight(r),!r[3])throw new a.A;var n=(r=this.shiftToModuleCenter(r))[0],o=r[1],i=r[2],s=r[3],h=this.transitionsBetween(n,s)+1,d=this.transitionsBetween(i,s)+1;(1&h)==1&&(h+=1),(1&d)==1&&(d+=1),4*h<7*d&&4*d<7*h&&(h=d=Math.max(h,d));var u=t.sampleGrid(this.image,n,o,i,s,h,d);return new T.A(u,[n,o,i,s])},t.shiftPoint=function(t,e,r){var n=(e.getX()-t.getX())/(r+1),o=(e.getY()-t.getY())/(r+1);return new _.A(t.getX()+n,t.getY()+o)},t.moveAway=function(t,e,r){var n=t.getX(),o=t.getY();return n<e?n-=1:n+=1,o<r?o-=1:o+=1,new _.A(n,o)},t.prototype.detectSolid1=function(t){var e=t[0],r=t[1],n=t[3],o=t[2],i=this.transitionsBetween(e,r),a=this.transitionsBetween(r,n),s=this.transitionsBetween(n,o),h=this.transitionsBetween(o,e),d=i,u=[o,e,r,n];return d>a&&(d=a,u[0]=e,u[1]=r,u[2]=n,u[3]=o),d>s&&(d=s,u[0]=r,u[1]=n,u[2]=o,u[3]=e),d>h&&(u[0]=n,u[1]=o,u[2]=e,u[3]=r),u},t.prototype.detectSolid2=function(e){var r=e[0],n=e[1],o=e[2],i=e[3],a=this.transitionsBetween(r,i),s=t.shiftPoint(n,o,(a+1)*4),h=t.shiftPoint(o,n,(a+1)*4);return this.transitionsBetween(s,r)<this.transitionsBetween(h,i)?(e[0]=r,e[1]=n,e[2]=o,e[3]=i):(e[0]=n,e[1]=o,e[2]=i,e[3]=r),e},t.prototype.correctTopRight=function(e){var r=e[0],n=e[1],o=e[2],i=e[3],a=this.transitionsBetween(r,i),s=this.transitionsBetween(n,i),h=t.shiftPoint(r,n,(s+1)*4),d=t.shiftPoint(o,n,(a+1)*4);a=this.transitionsBetween(h,i),s=this.transitionsBetween(d,i);var u=new _.A(i.getX()+(o.getX()-n.getX())/(a+1),i.getY()+(o.getY()-n.getY())/(a+1)),c=new _.A(i.getX()+(r.getX()-n.getX())/(s+1),i.getY()+(r.getY()-n.getY())/(s+1));return this.isValid(u)?this.isValid(c)?this.transitionsBetween(h,u)+this.transitionsBetween(d,u)>this.transitionsBetween(h,c)+this.transitionsBetween(d,c)?u:c:u:this.isValid(c)?c:null},t.prototype.shiftToModuleCenter=function(e){var r,n,o=e[0],i=e[1],a=e[2],s=e[3],h=this.transitionsBetween(o,s)+1,d=this.transitionsBetween(a,s)+1,u=t.shiftPoint(o,i,4*d),c=t.shiftPoint(a,i,4*h);h=this.transitionsBetween(u,s)+1,d=this.transitionsBetween(c,s)+1,(1&h)==1&&(h+=1),(1&d)==1&&(d+=1);var l=(o.getX()+i.getX()+a.getX()+s.getX())/4,f=(o.getY()+i.getY()+a.getY()+s.getY())/4;return o=t.moveAway(o,l,f),i=t.moveAway(i,l,f),a=t.moveAway(a,l,f),s=t.moveAway(s,l,f),u=t.shiftPoint(o,i,4*d),u=t.shiftPoint(u,s,4*h),r=t.shiftPoint(i,o,4*d),r=t.shiftPoint(r,a,4*h),c=t.shiftPoint(a,s,4*d),c=t.shiftPoint(c,i,4*h),n=t.shiftPoint(s,a,4*d),[u,r,c,n=t.shiftPoint(n,o,4*h)]},t.prototype.isValid=function(t){return t.getX()>=0&&t.getX()<this.image.getWidth()&&t.getY()>0&&t.getY()<this.image.getHeight()},t.sampleGrid=function(t,e,r,n,o,i,a){return M.A.getInstance().sampleGrid(t,i,a,.5,.5,i-.5,.5,i-.5,a-.5,.5,a-.5,e.getX(),e.getY(),o.getX(),o.getY(),n.getX(),n.getY(),r.getX(),r.getY())},t.prototype.transitionsBetween=function(t,e){var r=Math.trunc(t.getX()),n=Math.trunc(t.getY()),o=Math.trunc(e.getX()),i=Math.trunc(e.getY()),a=Math.abs(i-n)>Math.abs(o-r);if(a){var s=r;r=n,n=s,s=o,o=i,i=s}for(var h=Math.abs(o-r),d=Math.abs(i-n),u=-h/2,c=n<i?1:-1,l=r<o?1:-1,f=0,g=this.image.get(a?n:r,a?r:n),p=r,C=n;p!==o;p+=l){var w=this.image.get(a?C:p,a?p:C);if(w!==g&&(f++,g=w),(u+=d)>0){if(C===i)break;C+=c,u-=h}}return f},t}();let D=function(){function t(){this.decoder=new I}return t.prototype.decode=function(e,r){if(void 0===r&&(r=null),null!=r&&r.has(i.A.PURE_BARCODE)){var o,a,u=t.extractPureBits(e.getBlackMatrix());o=this.decoder.decode(u),a=t.NO_POINTS}else{var c=new B(e.getBlackMatrix()).detect();o=this.decoder.decode(c.getBits()),a=c.getPoints()}var l=o.getRawBytes(),f=new s.A(o.getText(),l,8*l.length,a,n.A.DATA_MATRIX,d.A.currentTimeMillis()),g=o.getByteSegments();null!=g&&f.putMetadata(h.A.BYTE_SEGMENTS,g);var p=o.getECLevel();return null!=p&&f.putMetadata(h.A.ERROR_CORRECTION_LEVEL,p),f},t.prototype.reset=function(){},t.extractPureBits=function(t){var e=t.getTopLeftOnBit(),r=t.getBottomRightOnBit();if(null==e||null==r)throw new a.A;var n=this.moduleSize(e,t),i=e[1],s=r[1],h=e[0],d=(r[0]-h+1)/n,u=(s-i+1)/n;if(d<=0||u<=0)throw new a.A;var c=n/2;i+=c,h+=c;for(var l=new o.A(d,u),f=0;f<u;f++)for(var g=i+f*n,p=0;p<d;p++)t.get(h+p*n,g)&&l.set(p,f);return l},t.moduleSize=function(t,e){for(var r=e.getWidth(),n=t[0],o=t[1];n<r&&e.get(n,o);)n++;if(n===r)throw new a.A;var i=n-t[0];if(0===i)throw new a.A;return i},t.NO_POINTS=[],t}()},48798:(t,e,r)=>{r.d(e,{_:()=>a});var n=r(39894),o=r(93782),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.getEncodingMode=function(){return o.VL},e.prototype.encodeChar=function(t,e){var r;return 32===t?(e.append(3),1):t>=48&&t<=57?(e.append(t-48+4),1):t>=97&&t<=122?(e.append(t-97+14),1):t<32?(e.append(0),e.append(t),2):t<=47?(e.append(1),e.append(t-33),2):t<=64?(e.append(1),e.append(t-58+15),2):t>=91&&t<=95?(e.append(1),e.append(t-91+22),2):96===t?(e.append(2),e.append(0),2):t<=90?(e.append(2),e.append(t-65+1),2):t<=127?(e.append(2),e.append(t-123+27),2):(e.append("1\x1e"),2+this.encodeChar(t-128,e))},e}(n.S)},49202:(t,e,r)=>{r.d(e,{Q:()=>i});var n=r(1933),o=r(72169),i=function(){function t(t){this.msg=t,this.pos=0,this.skipAtEnd=0;for(var e=t.split("").map(function(t){return t.charCodeAt(0)}),r=new n.A,o=0,i=e.length;o<i;o++){var a=String.fromCharCode(255&e[o]);if("?"===a&&"?"!==t.charAt(o))throw Error("Message contains characters outside ISO-8859-1 encoding.");r.append(a)}this.msg=r.toString(),this.shape=0,this.codewords=new n.A,this.newEncoding=-1}return t.prototype.setSymbolShape=function(t){this.shape=t},t.prototype.setSizeConstraints=function(t,e){this.minSize=t,this.maxSize=e},t.prototype.getMessage=function(){return this.msg},t.prototype.setSkipAtEnd=function(t){this.skipAtEnd=t},t.prototype.getCurrentChar=function(){return this.msg.charCodeAt(this.pos)},t.prototype.getCurrent=function(){return this.msg.charCodeAt(this.pos)},t.prototype.getCodewords=function(){return this.codewords},t.prototype.writeCodewords=function(t){this.codewords.append(t)},t.prototype.writeCodeword=function(t){this.codewords.append(t)},t.prototype.getCodewordCount=function(){return this.codewords.length()},t.prototype.getNewEncoding=function(){return this.newEncoding},t.prototype.signalEncoderChange=function(t){this.newEncoding=t},t.prototype.resetEncoderSignal=function(){this.newEncoding=-1},t.prototype.hasMoreCharacters=function(){return this.pos<this.getTotalMessageCharCount()},t.prototype.getTotalMessageCharCount=function(){return this.msg.length-this.skipAtEnd},t.prototype.getRemainingCharacters=function(){return this.getTotalMessageCharCount()-this.pos},t.prototype.getSymbolInfo=function(){return this.symbolInfo},t.prototype.updateSymbolInfo=function(t){void 0===t&&(t=this.getCodewordCount()),(null==this.symbolInfo||t>this.symbolInfo.getDataCapacity())&&(this.symbolInfo=o.A.lookup(t,this.shape,this.minSize,this.maxSize,!0))},t.prototype.resetSymbolInfo=function(){this.symbolInfo=null},t}()},55277:(t,e,r)=>{r.d(e,{A:()=>i});var n=r(1933),o=r(93782);let i=function(){function t(){}return t.encodeECC200=function(t,e){if(t.length!==e.getDataCapacity())throw Error("The number of codewords does not match the selected symbol");var r=new n.A;r.append(t);var o=e.getInterleavedBlockCount();if(1===o){var i=this.createECCBlock(t,e.getErrorCodewords());r.append(i)}else{for(var a=[],s=[],h=0;h<o;h++)a[h]=e.getDataLengthForInterleavedBlock(h+1),s[h]=e.getErrorLengthForInterleavedBlock(h+1);for(var d=0;d<o;d++){for(var u=new n.A,c=d;c<e.getDataCapacity();c+=o)u.append(t.charAt(c));for(var i=this.createECCBlock(u.toString(),s[d]),l=0,f=d;f<s[d]*o;f+=o)r.setCharAt(e.getDataCapacity()+f,i.charAt(l++))}}return r.toString()},t.createECCBlock=function(t,e){for(var r=-1,n=0;n<o.gE.length;n++)if(o.gE[n]===e){r=n;break}if(r<0)throw Error("Illegal number of error correction codewords specified: "+e);for(var i=o.XQ[r],a=[],n=0;n<e;n++)a[n]=0;for(var n=0;n<t.length;n++){for(var s=a[e-1]^t.charAt(n).charCodeAt(0),h=e-1;h>0;h--)0!==s&&0!==i[h]?a[h]=a[h-1]^o.KX[(o.$9[s]+o.$9[i[h]])%255]:a[h]=a[h-1];0!==s&&0!==i[0]?a[0]=o.KX[(o.$9[s]+o.$9[i[0]])%255]:a[0]=0}for(var d=[],n=0;n<e;n++)d[n]=a[e-n-1];return d.map(function(t){return String.fromCharCode(t)}).join("")},t}()},58892:(t,e,r)=>{r.d(e,{a:()=>i});var n=r(93782),o=r(7779),i=function(){function t(){}return t.prototype.getEncodingMode=function(){return n.d2},t.prototype.encode=function(t){if(o.A.determineConsecutiveDigitCount(t.getMessage(),t.pos)>=2)t.writeCodeword(this.encodeASCIIDigits(t.getMessage().charCodeAt(t.pos),t.getMessage().charCodeAt(t.pos+1))),t.pos+=2;else{var e=t.getCurrentChar(),r=o.A.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode());if(r!==this.getEncodingMode())switch(r){case n.mt:t.writeCodeword(n.ah),t.signalEncoderChange(n.mt);return;case n.fG:t.writeCodeword(n.X7),t.signalEncoderChange(n.fG);return;case n.VK:t.writeCodeword(n.Qe),t.signalEncoderChange(n.VK);break;case n.VL:t.writeCodeword(n.dn),t.signalEncoderChange(n.VL);break;case n.uf:t.writeCodeword(n.ij),t.signalEncoderChange(n.uf);break;default:throw Error("Illegal mode: "+r)}else o.A.isExtendedASCII(e)?(t.writeCodeword(n.gn),t.writeCodeword(e-128+1)):t.writeCodeword(e+1),t.pos++}},t.prototype.encodeASCIIDigits=function(t,e){if(o.A.isDigit(t)&&o.A.isDigit(e))return(t-48)*10+(e-48)+130;throw Error("not digits: "+t+e)},t}()},59272:(t,e,r)=>{r.d(e,{A:()=>s});var n=r(22152),o=r(438),i=r(71534),a=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let s=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.decodeRowStringBuffer="",e}return a(e,t),e.findStartGuardPattern=function(t){for(var r,n=!1,o=0,i=Int32Array.from([0,0,0]);!n;){i=Int32Array.from([0,0,0]);var a=(r=e.findGuardPattern(t,o,!1,this.START_END_PATTERN,i))[0],s=a-((o=r[1])-a);s>=0&&(n=t.isRange(s,a,!1))}return r},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var r=t.length;if(0===r)return!1;var n=parseInt(t.charAt(r-1),10);return e.getStandardUPCEANChecksum(t.substring(0,r-1))===n},e.getStandardUPCEANChecksum=function(t){for(var e=t.length,r=0,n=e-1;n>=0;n-=2){var o=t.charAt(n).charCodeAt(0)-48;if(o<0||o>9)throw new i.A;r+=o}r*=3;for(var n=e-2;n>=0;n-=2){var o=t.charAt(n).charCodeAt(0)-48;if(o<0||o>9)throw new i.A;r+=o}return(1e3-r)%10},e.decodeEnd=function(t,r){return e.findGuardPattern(t,r,!1,e.START_END_PATTERN,new Int32Array(e.START_END_PATTERN.length).fill(0))},e.findGuardPatternWithoutCounters=function(t,e,r,n){return this.findGuardPattern(t,e,r,n,new Int32Array(n.length))},e.findGuardPattern=function(t,r,i,a,s){var h=t.getSize();r=i?t.getNextUnset(r):t.getNextSet(r);for(var d=0,u=r,c=a.length,l=i,f=r;f<h;f++)if(t.get(f)!==l)s[d]++;else{if(d===c-1){if(n.A.patternMatchVariance(s,a,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return Int32Array.from([u,f]);u+=s[0]+s[1];for(var g=s.slice(2,s.length),p=0;p<d-1;p++)s[p]=g[p];s[d-1]=0,s[d]=0,d--}else d++;s[d]=1,l=!l}throw new o.A},e.decodeDigit=function(t,r,i,a){this.recordPattern(t,i,r);for(var s=this.MAX_AVG_VARIANCE,h=-1,d=a.length,u=0;u<d;u++){var c=a[u],l=n.A.patternMatchVariance(r,c,e.MAX_INDIVIDUAL_VARIANCE);l<s&&(s=l,h=u)}if(h>=0)return h;throw new o.A},e.MAX_AVG_VARIANCE=.48,e.MAX_INDIVIDUAL_VARIANCE=.7,e.START_END_PATTERN=Int32Array.from([1,1,1]),e.MIDDLE_PATTERN=Int32Array.from([1,1,1,1,1]),e.END_PATTERN=Int32Array.from([1,1,1,1,1,1]),e.L_PATTERNS=[Int32Array.from([3,2,1,1]),Int32Array.from([2,2,2,1]),Int32Array.from([2,1,2,2]),Int32Array.from([1,4,1,1]),Int32Array.from([1,1,3,2]),Int32Array.from([1,2,3,1]),Int32Array.from([1,1,1,4]),Int32Array.from([1,3,1,2]),Int32Array.from([1,2,1,3]),Int32Array.from([3,1,1,2])],e}(n.A)},71411:(t,e,r)=>{r.d(e,{A:()=>c});var n,o=r(55701),i=r(85770),a=r(1933),s=r(63623),h=r(23510),d=r(71534),u=r(39778);!function(t){t[t.PAD_ENCODE=0]="PAD_ENCODE",t[t.ASCII_ENCODE=1]="ASCII_ENCODE",t[t.C40_ENCODE=2]="C40_ENCODE",t[t.TEXT_ENCODE=3]="TEXT_ENCODE",t[t.ANSIX12_ENCODE=4]="ANSIX12_ENCODE",t[t.EDIFACT_ENCODE=5]="EDIFACT_ENCODE",t[t.BASE256_ENCODE=6]="BASE256_ENCODE"}(n||(n={}));let c=function(){function t(){}return t.decode=function(t){var e=new i.A(t),r=new a.A,s=new a.A,h=[],u=n.ASCII_ENCODE;do if(u===n.ASCII_ENCODE)u=this.decodeAsciiSegment(e,r,s);else{switch(u){case n.C40_ENCODE:this.decodeC40Segment(e,r);break;case n.TEXT_ENCODE:this.decodeTextSegment(e,r);break;case n.ANSIX12_ENCODE:this.decodeAnsiX12Segment(e,r);break;case n.EDIFACT_ENCODE:this.decodeEdifactSegment(e,r);break;case n.BASE256_ENCODE:this.decodeBase256Segment(e,r,h);break;default:throw new d.A}u=n.ASCII_ENCODE}while(u!==n.PAD_ENCODE&&e.available()>0);return s.length()>0&&r.append(s.toString()),new o.A(t,r.toString(),0===h.length?null:h,null)},t.decodeAsciiSegment=function(t,e,r){var o=!1;do{var i=t.readBits(8);if(0===i)throw new d.A;if(i<=128){o&&(i+=128),e.append(String.fromCharCode(i-1));break}if(129===i)return n.PAD_ENCODE;else if(i<=229){var a=i-130;a<10&&e.append("0"),e.append(""+a)}else switch(i){case 230:return n.C40_ENCODE;case 231:return n.BASE256_ENCODE;case 232:e.append("\x1d");break;case 233:case 234:case 241:break;case 235:o=!0;break;case 236:e.append("[)>\x1e05\x1d"),r.insert(0,"\x1e\x04");break;case 237:e.append("[)>\x1e06\x1d"),r.insert(0,"\x1e\x04");break;case 238:return n.ANSIX12_ENCODE;case 239:return n.TEXT_ENCODE;case 240:return n.EDIFACT_ENCODE;default:if(254!==i||0!==t.available())throw new d.A}}while(t.available()>0);return n.ASCII_ENCODE},t.decodeC40Segment=function(t,e){var r=!1,n=[],o=0;do{if(8===t.available())return;var i=t.readBits(8);if(254===i)return;this.parseTwoBytes(i,t.readBits(8),n);for(var a=0;a<3;a++){var s=n[a];switch(o){case 0:if(s<3)o=s+1;else if(s<this.C40_BASIC_SET_CHARS.length){var h=this.C40_BASIC_SET_CHARS[s];r?(e.append(String.fromCharCode(h.charCodeAt(0)+128)),r=!1):e.append(h)}else throw new d.A;break;case 1:r?(e.append(String.fromCharCode(s+128)),r=!1):e.append(String.fromCharCode(s)),o=0;break;case 2:if(s<this.C40_SHIFT2_SET_CHARS.length){var h=this.C40_SHIFT2_SET_CHARS[s];r?(e.append(String.fromCharCode(h.charCodeAt(0)+128)),r=!1):e.append(h)}else switch(s){case 27:e.append("\x1d");break;case 30:r=!0;break;default:throw new d.A}o=0;break;case 3:r?(e.append(String.fromCharCode(s+224)),r=!1):e.append(String.fromCharCode(s+96)),o=0;break;default:throw new d.A}}}while(t.available()>0)},t.decodeTextSegment=function(t,e){var r=!1,n=[],o=0;do{if(8===t.available())return;var i=t.readBits(8);if(254===i)return;this.parseTwoBytes(i,t.readBits(8),n);for(var a=0;a<3;a++){var s=n[a];switch(o){case 0:if(s<3)o=s+1;else if(s<this.TEXT_BASIC_SET_CHARS.length){var h=this.TEXT_BASIC_SET_CHARS[s];r?(e.append(String.fromCharCode(h.charCodeAt(0)+128)),r=!1):e.append(h)}else throw new d.A;break;case 1:r?(e.append(String.fromCharCode(s+128)),r=!1):e.append(String.fromCharCode(s)),o=0;break;case 2:if(s<this.TEXT_SHIFT2_SET_CHARS.length){var h=this.TEXT_SHIFT2_SET_CHARS[s];r?(e.append(String.fromCharCode(h.charCodeAt(0)+128)),r=!1):e.append(h)}else switch(s){case 27:e.append("\x1d");break;case 30:r=!0;break;default:throw new d.A}o=0;break;case 3:if(s<this.TEXT_SHIFT3_SET_CHARS.length){var h=this.TEXT_SHIFT3_SET_CHARS[s];r?(e.append(String.fromCharCode(h.charCodeAt(0)+128)),r=!1):e.append(h),o=0}else throw new d.A;break;default:throw new d.A}}}while(t.available()>0)},t.decodeAnsiX12Segment=function(t,e){var r=[];do{if(8===t.available())return;var n=t.readBits(8);if(254===n)return;this.parseTwoBytes(n,t.readBits(8),r);for(var o=0;o<3;o++){var i=r[o];switch(i){case 0:e.append("\r");break;case 1:e.append("*");break;case 2:e.append(">");break;case 3:e.append(" ");break;default:if(i<14)e.append(String.fromCharCode(i+44));else if(i<40)e.append(String.fromCharCode(i+51));else throw new d.A}}}while(t.available()>0)},t.parseTwoBytes=function(t,e,r){var n=(t<<8)+e-1,o=Math.floor(n/1600);r[0]=o,n-=1600*o,o=Math.floor(n/40),r[1]=o,r[2]=n-40*o},t.decodeEdifactSegment=function(t,e){do{if(16>=t.available())return;for(var r=0;r<4;r++){var n=t.readBits(6);if(31===n){var o=8-t.getBitOffset();8!==o&&t.readBits(o);return}(32&n)==0&&(n|=64),e.append(String.fromCharCode(n))}}while(t.available()>0)},t.decodeBase256Segment=function(t,e,r){var n,o=1+t.getByteOffset(),i=this.unrandomize255State(t.readBits(8),o++);if((n=0===i?t.available()/8|0:i<250?i:250*(i-249)+this.unrandomize255State(t.readBits(8),o++))<0)throw new d.A;for(var a=new Uint8Array(n),c=0;c<n;c++){if(8>t.available())throw new d.A;a[c]=this.unrandomize255State(t.readBits(8),o++)}r.push(a);try{e.append(s.A.decode(a,h.A.ISO88591))}catch(t){throw new u.A("Platform does not support required encoding: "+t.message)}},t.unrandomize255State=function(t,e){var r=t-(149*e%255+1);return r>=0?r:r+256},t.C40_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],t.C40_SHIFT2_SET_CHARS=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_"],t.TEXT_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],t.TEXT_SHIFT2_SET_CHARS=t.C40_SHIFT2_SET_CHARS,t.TEXT_SHIFT3_SET_CHARS=["`","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","{","|","}","~",""],t}()},72169:(t,e,r)=>{r.d(e,{A:()=>a});var n=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=function(){function t(t,e,r,n,o,i,a,s){void 0===a&&(a=0),void 0===s&&(s=0),this.rectangular=t,this.dataCapacity=e,this.errorCodewords=r,this.matrixWidth=n,this.matrixHeight=o,this.dataRegions=i,this.rsBlockData=a,this.rsBlockError=s}return t.lookup=function(t,e,r,n,i){var a,s;void 0===e&&(e=0),void 0===r&&(r=null),void 0===n&&(n=null),void 0===i&&(i=!0);try{for(var d=o(h),u=d.next();!u.done;u=d.next()){var c=u.value;if(!(1===e&&c.rectangular||2===e&&!c.rectangular||null!=r&&(c.getSymbolWidth()<r.getWidth()||c.getSymbolHeight()<r.getHeight()))&&!(null!=n&&(c.getSymbolWidth()>n.getWidth()||c.getSymbolHeight()>n.getHeight()))&&t<=c.dataCapacity)return c}}catch(t){a={error:t}}finally{try{u&&!u.done&&(s=d.return)&&s.call(d)}finally{if(a)throw a.error}}if(i)throw Error("Can't find a symbol arrangement that matches the message. Data codewords: "+t);return null},t.prototype.getHorizontalDataRegions=function(){switch(this.dataRegions){case 1:return 1;case 2:case 4:return 2;case 16:return 4;case 36:return 6;default:throw Error("Cannot handle this number of data regions")}},t.prototype.getVerticalDataRegions=function(){switch(this.dataRegions){case 1:case 2:return 1;case 4:return 2;case 16:return 4;case 36:return 6;default:throw Error("Cannot handle this number of data regions")}},t.prototype.getSymbolDataWidth=function(){return this.getHorizontalDataRegions()*this.matrixWidth},t.prototype.getSymbolDataHeight=function(){return this.getVerticalDataRegions()*this.matrixHeight},t.prototype.getSymbolWidth=function(){return this.getSymbolDataWidth()+2*this.getHorizontalDataRegions()},t.prototype.getSymbolHeight=function(){return this.getSymbolDataHeight()+2*this.getVerticalDataRegions()},t.prototype.getCodewordCount=function(){return this.dataCapacity+this.errorCodewords},t.prototype.getInterleavedBlockCount=function(){return this.rsBlockData?this.dataCapacity/this.rsBlockData:1},t.prototype.getDataCapacity=function(){return this.dataCapacity},t.prototype.getErrorCodewords=function(){return this.errorCodewords},t.prototype.getDataLengthForInterleavedBlock=function(t){return this.rsBlockData},t.prototype.getErrorLengthForInterleavedBlock=function(t){return this.rsBlockError},t}();let a=i;var s=function(t){function e(){return t.call(this,!1,1558,620,22,22,36,-1,62)||this}return n(e,t),e.prototype.getInterleavedBlockCount=function(){return 10},e.prototype.getDataLengthForInterleavedBlock=function(t){return t<=8?156:155},e}(i),h=[new i(!1,3,5,8,8,1),new i(!1,5,7,10,10,1),new i(!0,5,7,16,6,1),new i(!1,8,10,12,12,1),new i(!0,10,11,14,6,2),new i(!1,12,12,14,14,1),new i(!0,16,14,24,10,1),new i(!1,18,14,16,16,1),new i(!1,22,18,18,18,1),new i(!0,22,18,16,10,2),new i(!1,30,20,20,20,1),new i(!0,32,24,16,14,2),new i(!1,36,24,22,22,1),new i(!1,44,28,24,24,1),new i(!0,49,28,22,14,2),new i(!1,62,36,14,14,4),new i(!1,86,42,16,16,4),new i(!1,114,48,18,18,4),new i(!1,144,56,20,20,4),new i(!1,174,68,22,22,4),new i(!1,204,84,24,24,4,102,42),new i(!1,280,112,14,14,16,140,56),new i(!1,368,144,16,16,16,92,36),new i(!1,456,192,18,18,16,114,48),new i(!1,576,224,20,20,16,144,56),new i(!1,696,272,22,22,16,174,68),new i(!1,816,336,24,24,16,136,56),new i(!1,1050,408,18,18,36,175,68),new i(!1,1304,496,20,20,36,163,62),new s]},73377:(t,e,r)=>{var n,o=r(25969),i=r(10782),a=r(27217),s=r(52771),h=r(2257);r(58892),r(27323),r(39894);var d=r(73693);r(25879),r(49202);var u=r(55277),c=r(7779),l=r(93782),f=r(58789),g=r(63479),p=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),C=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},w=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},A=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(w(arguments[e]));return t};!function(t){t[t.ASCII=0]="ASCII",t[t.C40=1]="C40",t[t.TEXT=2]="TEXT",t[t.X12=3]="X12",t[t.EDF=4]="EDF",t[t.B256=5]="B256"}(n||(n={}));var m=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_"],v=function(){function t(){}return t.isExtendedASCII=function(t,e){return t!==e&&t>=128&&t<=255},t.isInC40Shift1Set=function(t){return t<=31},t.isInC40Shift2Set=function(t,e){var r,n;try{for(var o=C(m),i=o.next();!i.done;i=o.next())if(i.value.charCodeAt(0)===t)return!0}catch(t){r={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return t===e},t.isInTextShift1Set=function(t){return this.isInC40Shift1Set(t)},t.isInTextShift2Set=function(t,e){return this.isInC40Shift2Set(t,e)},t.encodeHighLevel=function(t,e,r,n){void 0===e&&(e=null),void 0===r&&(r=-1),void 0===n&&(n=0);var o=0;return t.startsWith(l.h_)&&t.endsWith(l.TG)?(o=5,t=t.substring(l.h_.length,t.length-2)):t.startsWith(l.eB)&&t.endsWith(l.TG)&&(o=6,t=t.substring(l.eB.length,t.length-2)),decodeURIComponent(escape(String.fromCharCode.apply(String,A(this.encode(t,e,r,n,o)))))},t.encode=function(t,e,r,n,o){return this.encodeMinimally(new E(t,e,r,n,o)).getBytes()},t.addEdge=function(t,e){var r=e.fromPosition+e.characterLength;(null===t[r][e.getEndMode()]||t[r][e.getEndMode()].cachedTotalSize>e.cachedTotalSize)&&(t[r][e.getEndMode()]=e)},t.getNumberOfC40Words=function(e,r,n,o){for(var i=0,a=r;a<e.length()&&!e.isECI(a);a++){;var s=e.charAt(a);if(n&&c.A.isNativeC40(s)||!n&&c.A.isNativeText(s))i++;else if(t.isExtendedASCII(s,e.getFNC1Character())){var h=255&s;h>=128&&(n&&c.A.isNativeC40(h-128)||!n&&c.A.isNativeText(h-128))?i+=3:i+=4}else i+=2;if(i%3==0||(i-2)%3==0&&a+1===e.length())return o[0]=a-r+1,Math.ceil(i/3)}return o[0]=0,0},t.addEdges=function(e,r,o,i){if(e.isECI(o))return void this.addEdge(r,new S(e,n.ASCII,o,1,i));var a,s,h,d=e.charAt(o);if(null===i||i.getEndMode()!==n.EDF){c.A.isDigit(d)&&e.haveNCharacters(o,2)&&c.A.isDigit(e.charAt(o+1))?this.addEdge(r,new S(e,n.ASCII,o,2,i)):this.addEdge(r,new S(e,n.ASCII,o,1,i));var u=[n.C40,n.TEXT];try{for(var l=C(u),f=l.next();!f.done;f=l.next()){var g=f.value,p=[];t.getNumberOfC40Words(e,o,g===n.C40,p)>0&&this.addEdge(r,new S(e,g,o,p[0],i))}}catch(t){a={error:t}}finally{try{f&&!f.done&&(s=l.return)&&s.call(l)}finally{if(a)throw a.error}}e.haveNCharacters(o,3)&&c.A.isNativeX12(e.charAt(o))&&c.A.isNativeX12(e.charAt(o+1))&&c.A.isNativeX12(e.charAt(o+2))&&this.addEdge(r,new S(e,n.X12,o,3,i)),this.addEdge(r,new S(e,n.B256,o,1,i))}for(h=0;h<3;h++){var w=o+h;if(e.haveNCharacters(w,1)&&c.A.isNativeEDIFACT(e.charAt(w)))this.addEdge(r,new S(e,n.EDF,o,h+1,i));else break}3===h&&e.haveNCharacters(o,4)&&c.A.isNativeEDIFACT(e.charAt(o+3))&&this.addEdge(r,new S(e,n.EDF,o,4,i))},t.encodeMinimally=function(t){var e=t.length(),r=Array(e+1).fill(null).map(function(){return Array(6).fill(0)});this.addEdges(t,r,0,null);for(var n=1;n<=e;n++){for(var o=0;o<6;o++)null!==r[n][o]&&n<e&&this.addEdges(t,r,n,r[n][o]);for(var o=0;o<6;o++)r[n-1][o]=null}for(var i=-1,a=g.A.MAX_VALUE,o=0;o<6;o++)if(null!==r[e][o]){var s=r[e][o],h=o>=1&&o<=3?s.cachedTotalSize+1:s.cachedTotalSize;h<a&&(a=h,i=o)}if(i<0)throw Error('Failed to encode "'+t+'"');return new y(r[e][i])},t}(),y=function(){function t(t){var e=t.input,r=0,o=[],i=[],a=[];(t.mode===n.C40||t.mode===n.TEXT||t.mode===n.X12)&&t.getEndMode()!==n.ASCII&&(r+=this.prepend(S.getBytes(254),o));for(var s=t;null!==s;)r+=this.prepend(s.getDataBytes(),o),(null===s.previous||s.getPreviousStartMode()!==s.getMode())&&(s.getMode()===n.B256&&(r<=249?(o.unshift(r),r++):(o.unshift(r%250),o.unshift(r/250+249),r+=2),i.push(o.length),a.push(r)),this.prepend(s.getLatchBytes(),o),r=0),s=s.previous;5===e.getMacroId()?r+=this.prepend(S.getBytes(236),o):6===e.getMacroId()&&(r+=this.prepend(S.getBytes(237),o)),e.getFNC1Character()>0&&(r+=this.prepend(S.getBytes(232),o));for(var h=0;h<i.length;h++)this.applyRandomPattern(o,o.length-i[h],a[h]);var d=t.getMinSymbolSize(o.length);for(o.length<d&&o.push(129);o.length<d;)o.push(this.randomize253State(o.length+1));this.bytes=new Uint8Array(o.length);for(var h=0;h<this.bytes.length;h++)this.bytes[h]=o[h]}return t.prototype.prepend=function(t,e){for(var r=t.length-1;r>=0;r--)e.unshift(t[r]);return t.length},t.prototype.randomize253State=function(t){var e=129+(149*t%253+1);return e<=254?e:e-254},t.prototype.applyRandomPattern=function(t,e,r){for(var n=0;n<r;n++){var o=e+n,i=(255&t[o])+(149*(o+1)%255+1);t[o]=i<=255?i:i-256}},t.prototype.getBytes=function(){return this.bytes},t}(),S=function(){function t(t,e,r,o,i){if(this.input=t,this.mode=e,this.fromPosition=r,this.characterLength=o,this.previous=i,this.allCodewordCapacities=[3,5,8,10,12,16,18,22,30,32,36,44,49,62,86,114,144,174,204,280,368,456,576,696,816,1050,1304,1558],this.squareCodewordCapacities=[3,5,8,12,18,22,30,36,44,62,86,114,144,174,204,280,368,456,576,696,816,1050,1304,1558],this.rectangularCodewordCapacities=[5,10,16,33,32,49],!(r+o<=t.length()))throw Error("Invalid edge");var a=null!==i?i.cachedTotalSize:0,s=this.getPreviousMode();switch(e){case n.ASCII:a++,(t.isECI(r)||v.isExtendedASCII(t.charAt(r),t.getFNC1Character()))&&a++,(s===n.C40||s===n.TEXT||s===n.X12)&&a++;break;case n.B256:a++,s!==n.B256?a++:250===this.getB256Size()&&a++,s===n.ASCII?a++:(s===n.C40||s===n.TEXT||s===n.X12)&&(a+=2);break;case n.C40:case n.TEXT:case n.X12:e===n.X12?a+=2:a+=2*v.getNumberOfC40Words(t,r,e===n.C40,[]),s===n.ASCII||s===n.B256?a++:s!==e&&(s===n.C40||s===n.TEXT||s===n.X12)&&(a+=2);break;case n.EDF:a+=3,s===n.ASCII||s===n.B256?a++:(s===n.C40||s===n.TEXT||s===n.X12)&&(a+=2)}this.cachedTotalSize=a}return t.prototype.getB256Size=function(){for(var t=0,e=this;null!==e&&e.mode===n.B256&&t<=250;)t++,e=e.previous;return t},t.prototype.getPreviousStartMode=function(){return null===this.previous?n.ASCII:this.previous.mode},t.prototype.getPreviousMode=function(){return null===this.previous?n.ASCII:this.previous.getEndMode()},t.prototype.getEndMode=function(){if(this.mode===n.EDF){if(this.characterLength<4)return n.ASCII;var t=this.getLastASCII();if(t>0&&this.getCodewordsRemaining(this.cachedTotalSize+t)<=2-t)return n.ASCII}if(this.mode===n.C40||this.mode===n.TEXT||this.mode===n.X12){if(this.fromPosition+this.characterLength>=this.input.length()&&0===this.getCodewordsRemaining(this.cachedTotalSize))return n.ASCII;var t=this.getLastASCII();if(1===t&&0===this.getCodewordsRemaining(this.cachedTotalSize+1))return n.ASCII}return this.mode},t.prototype.getMode=function(){return this.mode},t.prototype.getLastASCII=function(){var t=this.input.length(),e=this.fromPosition+this.characterLength;return t-e>4||e>=t?0:t-e==1?+!v.isExtendedASCII(this.input.charAt(e),this.input.getFNC1Character()):t-e==2?v.isExtendedASCII(this.input.charAt(e),this.input.getFNC1Character())||v.isExtendedASCII(this.input.charAt(e+1),this.input.getFNC1Character())?0:c.A.isDigit(this.input.charAt(e))&&c.A.isDigit(this.input.charAt(e+1))?1:2:t-e==3?c.A.isDigit(this.input.charAt(e))&&c.A.isDigit(this.input.charAt(e+1))&&!v.isExtendedASCII(this.input.charAt(e+2),this.input.getFNC1Character())||c.A.isDigit(this.input.charAt(e+1))&&c.A.isDigit(this.input.charAt(e+2))&&!v.isExtendedASCII(this.input.charAt(e),this.input.getFNC1Character())?2:0:c.A.isDigit(this.input.charAt(e))&&c.A.isDigit(this.input.charAt(e+1))&&c.A.isDigit(this.input.charAt(e+2))&&c.A.isDigit(this.input.charAt(e+3))?2:0},t.prototype.getMinSymbolSize=function(t){var e,r,n,o,i,a;switch(this.input.getShapeHint()){case 1:try{for(var s=C(this.squareCodewordCapacities),h=s.next();!h.done;h=s.next()){var d=h.value;if(d>=t)return d}}catch(t){e={error:t}}finally{try{h&&!h.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}break;case 2:try{for(var u=C(this.rectangularCodewordCapacities),c=u.next();!c.done;c=u.next()){var d=c.value;if(d>=t)return d}}catch(t){n={error:t}}finally{try{c&&!c.done&&(o=u.return)&&o.call(u)}finally{if(n)throw n.error}}}try{for(var l=C(this.allCodewordCapacities),f=l.next();!f.done;f=l.next()){var d=f.value;if(d>=t)return d}}catch(t){i={error:t}}finally{try{f&&!f.done&&(a=l.return)&&a.call(l)}finally{if(i)throw i.error}}return this.allCodewordCapacities[this.allCodewordCapacities.length-1]},t.prototype.getCodewordsRemaining=function(t){return this.getMinSymbolSize(t)-t},t.getBytes=function(t,e){var r=new Uint8Array(e?2:1);return r[0]=t,e&&(r[1]=e),r},t.prototype.setC40Word=function(t,e,r,n,o){var i=1600*(255&r)+40*(255&n)+(255&o)+1;t[e]=i/256,t[e+1]=i%256},t.prototype.getX12Value=function(t){return 13===t?0:42===t?1:62===t?2:32===t?3:t>=48&&t<=57?t-44:t>=65&&t<=90?t-51:t},t.prototype.getX12Words=function(){if(this.characterLength%3!=0)throw Error("X12 words must be a multiple of 3");for(var t=new Uint8Array(this.characterLength/3*2),e=0;e<t.length;e+=2)this.setC40Word(t,e,this.getX12Value(this.input.charAt(this.fromPosition+e/2*3)),this.getX12Value(this.input.charAt(this.fromPosition+e/2*3+1)),this.getX12Value(this.input.charAt(this.fromPosition+e/2*3+2)));return t},t.prototype.getShiftValue=function(t,e,r){return e&&v.isInC40Shift1Set(t)||!e&&v.isInTextShift1Set(t)?0:e&&v.isInC40Shift2Set(t,r)||!e&&v.isInTextShift2Set(t,r)?1:2},t.prototype.getC40Value=function(t,e,r,n){if(r===n){if(2!==e)throw Error("FNC1 cannot be used in C40 shift 2");return 27}return t?r<=31?r:32===r?3:r<=47?r-33:r<=57?r-44:r<=64?r-43:r<=90?r-51:r<=95?r-69:r<=127?r-96:r:0===r?0:0===e&&r<=3?r-1:1===e&&r<=31?r:32===r?3:r>=33&&r<=47?r-33:r>=48&&r<=57?r-44:r>=58&&r<=64?r-43:r>=65&&r<=90?r-64:r>=91&&r<=95?r-69:96===r?0:r>=97&&r<=122?r-83:r>=123&&r<=127?r-96:r},t.prototype.getC40Words=function(t,e){for(var r=[],n=0;n<this.characterLength;n++){var o=this.input.charAt(this.fromPosition+n);if(t&&c.A.isNativeC40(o)||!t&&c.A.isNativeText(o))r.push(this.getC40Value(t,0,o,e));else if(v.isExtendedASCII(o,e)){var i=(255&o)-128;if(t&&c.A.isNativeC40(i)||!t&&c.A.isNativeText(i))r.push(1),r.push(30),r.push(this.getC40Value(t,0,i,e));else{r.push(1),r.push(30);var a=this.getShiftValue(i,t,e);r.push(a),r.push(this.getC40Value(t,a,i,e))}}else{var a=this.getShiftValue(o,t,e);r.push(a),r.push(this.getC40Value(t,a,o,e))}}if(r.length%3!=0){if((r.length-2)%3!=0||this.fromPosition+this.characterLength!==this.input.length())throw Error("C40 words must be a multiple of 3");r.push(0)}for(var s=new Uint8Array(r.length/3*2),h=0,n=0;n<r.length;n+=3)this.setC40Word(s,h,255&r[n],255&r[n+1],255&r[n+2]),h+=2;return s},t.prototype.getEDFBytes=function(){for(var t=Math.ceil(this.characterLength/4),e=new Uint8Array(3*t),r=this.fromPosition,n=Math.min(this.fromPosition+this.characterLength-1,this.input.length()-1),o=0;o<t;o+=3){for(var i=[],a=0;a<4;a++)r<=n?i[a]=63&this.input.charAt(r++):i[a]=31*(r===n+1);var s=i[0]<<18;s|=i[1]<<12,s|=i[2]<<6,s|=i[3],e[o]=s>>16&255,e[o+1]=s>>8&255,e[o+2]=255&s}return e},t.prototype.getLatchBytes=function(){switch(this.getPreviousMode()){case n.ASCII:case n.B256:switch(this.mode){case n.B256:return t.getBytes(231);case n.C40:return t.getBytes(230);case n.TEXT:return t.getBytes(239);case n.X12:return t.getBytes(238);case n.EDF:return t.getBytes(240)}break;case n.C40:case n.TEXT:case n.X12:if(this.mode!==this.getPreviousMode())switch(this.mode){case n.ASCII:return t.getBytes(254);case n.B256:return t.getBytes(254,231);case n.C40:return t.getBytes(254,230);case n.TEXT:return t.getBytes(254,239);case n.X12:return t.getBytes(254,238);case n.EDF:return t.getBytes(254,240)}break;case n.EDF:if(this.mode!==n.EDF)throw Error("Cannot switch from EDF to "+this.mode)}return new Uint8Array(0)},t.prototype.getDataBytes=function(){switch(this.mode){case n.ASCII:if(this.input.isECI(this.fromPosition))return t.getBytes(241,this.input.getECIValue(this.fromPosition)+1);if(v.isExtendedASCII(this.input.charAt(this.fromPosition),this.input.getFNC1Character()))return t.getBytes(235,this.input.charAt(this.fromPosition)-127);if(2===this.characterLength)return t.getBytes(10*this.input.charAt(this.fromPosition)+this.input.charAt(this.fromPosition+1)+130);else if(this.input.isFNC1(this.fromPosition))return t.getBytes(232);else return t.getBytes(this.input.charAt(this.fromPosition)+1);case n.B256:return t.getBytes(this.input.charAt(this.fromPosition));case n.C40:return this.getC40Words(!0,this.input.getFNC1Character());case n.TEXT:return this.getC40Words(!1,this.input.getFNC1Character());case n.X12:return this.getX12Words();case n.EDF:return this.getEDFBytes()}},t}(),E=function(t){function e(e,r,n,o,i){var a=t.call(this,e,r,n)||this;return a.shape=o,a.macroId=i,a}return p(e,t),e.prototype.getMacroId=function(){return this.macroId},e.prototype.getShapeHint=function(){return this.shape},e}(f.R),I=r(72169);r(48798),r(9568),function(){function t(){}t.prototype.encode=function(t,e,r,n,i){if(void 0===i&&(i=null),""===t.trim())throw Error("Found empty contents");if(e!==o.A.DATA_MATRIX)throw Error("Can only encode DATA_MATRIX, but got "+e);if(r<0||n<0)throw Error("Requested dimensions can't be negative: "+r+"x"+n);var s,l=0,f=null,g=null;if(null!=i){var p=i.get(a.A.DATA_MATRIX_SHAPE);null!=p&&(l=p);var C=i.get(a.A.MIN_SIZE);null!=C&&(f=C);var w=i.get(a.A.MAX_SIZE);null!=w&&(g=w)}if(null!=i&&i.has(a.A.DATA_MATRIX_COMPACT)&&i.get(a.A.DATA_MATRIX_COMPACT).toString()){var A=i.has(a.A.GS1_FORMAT)&&!!i.get(a.A.GS1_FORMAT).toString(),m=null;i.has(a.A.CHARACTER_SET)&&(m=h.A.forName(i.get(a.A.CHARACTER_SET).toString())),s=v.encodeHighLevel(t,m,A?29:-1,l)}else{var y=null!=i&&i.has(a.A.FORCE_C40)&&!!i.get(a.A.FORCE_C40).toString();s=c.A.encodeHighLevel(t,l,f,g,y)}var S=I.A.lookup(s.length,l,f,g,!0),E=u.A.encodeECC200(s,S),b=new d.A(E,S.getSymbolDataWidth(),S.getSymbolDataHeight());return b.place(),this.encodeLowLevel(b,S,r,n)},t.prototype.encodeLowLevel=function(t,e,r,n){for(var o=e.getSymbolDataWidth(),i=e.getSymbolDataHeight(),a=new s.A(e.getSymbolWidth(),e.getSymbolHeight()),h=0,d=0;d<i;d++){var u=void 0;if(d%e.matrixHeight==0){u=0;for(var c=0;c<e.getSymbolWidth();c++)a.setBoolean(u,h,c%2==0),u++;h++}u=0;for(var c=0;c<o;c++)c%e.matrixWidth==0&&(a.setBoolean(u,h,!0),u++),a.setBoolean(u,h,t.getBit(c,d)),u++,c%e.matrixWidth==e.matrixWidth-1&&(a.setBoolean(u,h,d%2==0),u++);if(h++,d%e.matrixHeight==e.matrixHeight-1){u=0;for(var c=0;c<e.getSymbolWidth();c++)a.setBoolean(u,h,!0),u++;h++}}return this.convertByteMatrixToBitMatrix(a,r,n)},t.prototype.convertByteMatrixToBitMatrix=function(t,e,r){var n,o=t.getWidth(),a=t.getHeight(),s=Math.max(e,o),h=Math.max(r,a),d=Math.min(s/o,h/a),u=(s-o*d)/2,c=(h-a*d)/2;r<a||e<o?(u=0,c=0,n=new i.A(o,a)):n=new i.A(e,r),n.clear();for(var l=0,f=c;l<a;l++,f+=d)for(var g=0,p=u;g<o;g++,p+=d)1===t.get(g,l)&&n.setRegion(p,f,d,d);return n}}()},73693:(t,e,r)=>{r.d(e,{A:()=>o});var n=r(10077);let o=function(){function t(t,e,r){this.codewords=t,this.numcols=e,this.numrows=r,this.bits=new Uint8Array(e*r),n.A.fill(this.bits,2)}return t.prototype.getNumrows=function(){return this.numrows},t.prototype.getNumcols=function(){return this.numcols},t.prototype.getBits=function(){return this.bits},t.prototype.getBit=function(t,e){return 1===this.bits[e*this.numcols+t]},t.prototype.setBit=function(t,e,r){this.bits[e*this.numcols+t]=+!!r},t.prototype.noBit=function(t,e){return 2===this.bits[e*this.numcols+t]},t.prototype.place=function(){var t=0,e=4,r=0;do{e===this.numrows&&0===r&&this.corner1(t++),e===this.numrows-2&&0===r&&this.numcols%4!=0&&this.corner2(t++),e===this.numrows-2&&0===r&&this.numcols%8==4&&this.corner3(t++),e===this.numrows+4&&2===r&&this.numcols%8==0&&this.corner4(t++);do e<this.numrows&&r>=0&&this.noBit(r,e)&&this.utah(e,r,t++),e-=2,r+=2;while(e>=0&&r<this.numcols);e++,r+=3;do e>=0&&r<this.numcols&&this.noBit(r,e)&&this.utah(e,r,t++),e+=2,r-=2;while(e<this.numrows&&r>=0);e+=3,r++}while(e<this.numrows||r<this.numcols);this.noBit(this.numcols-1,this.numrows-1)&&(this.setBit(this.numcols-1,this.numrows-1,!0),this.setBit(this.numcols-2,this.numrows-2,!0))},t.prototype.module=function(t,e,r,n){t<0&&(t+=this.numrows,e+=4-(this.numrows+4)%8),e<0&&(e+=this.numcols,t+=4-(this.numcols+4)%8);var o=this.codewords.charCodeAt(r);o&=1<<8-n,this.setBit(e,t,0!==o)},t.prototype.utah=function(t,e,r){this.module(t-2,e-2,r,1),this.module(t-2,e-1,r,2),this.module(t-1,e-2,r,3),this.module(t-1,e-1,r,4),this.module(t-1,e,r,5),this.module(t,e-2,r,6),this.module(t,e-1,r,7),this.module(t,e,r,8)},t.prototype.corner1=function(t){this.module(this.numrows-1,0,t,1),this.module(this.numrows-1,1,t,2),this.module(this.numrows-1,2,t,3),this.module(0,this.numcols-2,t,4),this.module(0,this.numcols-1,t,5),this.module(1,this.numcols-1,t,6),this.module(2,this.numcols-1,t,7),this.module(3,this.numcols-1,t,8)},t.prototype.corner2=function(t){this.module(this.numrows-3,0,t,1),this.module(this.numrows-2,0,t,2),this.module(this.numrows-1,0,t,3),this.module(0,this.numcols-4,t,4),this.module(0,this.numcols-3,t,5),this.module(0,this.numcols-2,t,6),this.module(0,this.numcols-1,t,7),this.module(1,this.numcols-1,t,8)},t.prototype.corner3=function(t){this.module(this.numrows-3,0,t,1),this.module(this.numrows-2,0,t,2),this.module(this.numrows-1,0,t,3),this.module(0,this.numcols-2,t,4),this.module(0,this.numcols-1,t,5),this.module(1,this.numcols-1,t,6),this.module(2,this.numcols-1,t,7),this.module(3,this.numcols-1,t,8)},t.prototype.corner4=function(t){this.module(this.numrows-1,0,t,1),this.module(this.numrows-1,this.numcols-1,t,2),this.module(0,this.numcols-3,t,3),this.module(0,this.numcols-2,t,4),this.module(0,this.numcols-1,t,5),this.module(1,this.numcols-3,t,6),this.module(1,this.numcols-2,t,7),this.module(1,this.numcols-1,t,8)},t}()},93782:(t,e,r)=>{r.d(e,{$9:()=>s,KX:()=>h,OM:()=>m,Qe:()=>p,Qw:()=>d,TG:()=>S,VK:()=>T,VL:()=>b,X7:()=>u,XQ:()=>a,ah:()=>c,d2:()=>E,dn:()=>C,eB:()=>y,eb:()=>A,fG:()=>I,gE:()=>i,gn:()=>l,h_:()=>v,ij:()=>w,mD:()=>g,mt:()=>_,tf:()=>f,uf:()=>M});var n,o,i=[5,7,10,11,12,14,18,20,24,28,36,42,48,56,62,68],a=[[228,48,15,111,62],[23,68,144,134,240,92,254],[28,24,185,166,223,248,116,255,110,61],[175,138,205,12,194,168,39,245,60,97,120],[41,153,158,91,61,42,142,213,97,178,100,242],[156,97,192,252,95,9,157,119,138,45,18,186,83,185],[83,195,100,39,188,75,66,61,241,213,109,129,94,254,225,48,90,188],[15,195,244,9,233,71,168,2,188,160,153,145,253,79,108,82,27,174,186,172],[52,190,88,205,109,39,176,21,155,197,251,223,155,21,5,172,254,124,12,181,184,96,50,193],[211,231,43,97,71,96,103,174,37,151,170,53,75,34,249,121,17,138,110,213,141,136,120,151,233,168,93,255],[245,127,242,218,130,250,162,181,102,120,84,179,220,251,80,182,229,18,2,4,68,33,101,137,95,119,115,44,175,184,59,25,225,98,81,112],[77,193,137,31,19,38,22,153,247,105,122,2,245,133,242,8,175,95,100,9,167,105,214,111,57,121,21,1,253,57,54,101,248,202,69,50,150,177,226,5,9,5],[245,132,172,223,96,32,117,22,238,133,238,231,205,188,237,87,191,106,16,147,118,23,37,90,170,205,131,88,120,100,66,138,186,240,82,44,176,87,187,147,160,175,69,213,92,253,225,19],[175,9,223,238,12,17,220,208,100,29,175,170,230,192,215,235,150,159,36,223,38,200,132,54,228,146,218,234,117,203,29,232,144,238,22,150,201,117,62,207,164,13,137,245,127,67,247,28,155,43,203,107,233,53,143,46],[242,93,169,50,144,210,39,118,202,188,201,189,143,108,196,37,185,112,134,230,245,63,197,190,250,106,185,221,175,64,114,71,161,44,147,6,27,218,51,63,87,10,40,130,188,17,163,31,176,170,4,107,232,7,94,166,224,124,86,47,11,204],[220,228,173,89,251,149,159,56,89,33,147,244,154,36,73,127,213,136,248,180,234,197,158,177,68,122,93,213,15,160,227,236,66,139,153,185,202,167,179,25,220,232,96,210,231,136,223,239,181,241,59,52,172,25,49,232,211,189,64,54,108,153,132,63,96,103,82,186]],s=(n=function(t,e){for(var r=1,n=0;n<255;n++)e[n]=r,t[r]=n,(r*=2)>=256&&(r^=301);return{LOG:t,ALOG:e}}([],[])).LOG,h=n.ALOG;!function(t){t[t.FORCE_NONE=0]="FORCE_NONE",t[t.FORCE_SQUARE=1]="FORCE_SQUARE",t[t.FORCE_RECTANGLE=2]="FORCE_RECTANGLE"}(o||(o={}));var d=129,u=230,c=231,l=235,f=236,g=237,p=238,C=239,w=240,A=254,m=254,v="[)>\x1e05\x1d",y="[)>\x1e06\x1d",S="\x1e\x04",E=0,I=1,b=2,T=3,M=4,_=5}}]);