(()=>{var e={};e.id=586,e.ids=[586],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,s,t)=>{"use strict";t.d(s,{Yq:()=>c,cn:()=>i,ps:()=>m,qY:()=>u,r6:()=>o,vv:()=>n,y7:()=>d,zC:()=>l});var r=t(49384),a=t(82348);function i(...e){return(0,a.QP)((0,r.$)(e))}function n(e){return`${e.toLocaleString("ar-IQ")} د.ع`}function c(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function o(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function d(){let e=Date.now().toString().slice(-6),s=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return`MRS${e}${s}`}function l(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function u(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function m(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12686:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\firebase-setup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\firebase-setup\\page.tsx","default")},19080:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,s,t)=>{"use strict";t.d(s,{$:()=>o});var r=t(60687);t(43210);var a=t(8730),i=t(24224),n=t(4780);let c=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:s,size:t,asChild:i=!1,...o}){let d=i?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,n.cn)(c({variant:s,size:t,className:e})),...o})}},30522:(e,s,t)=>{Promise.resolve().then(t.bind(t,74052))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37366:e=>{"use strict";e.exports=require("dns")},41312:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41862:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>i,aR:()=>n});var r=t(60687);t(43210);var a=t(4780);function i({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function n({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function c({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...s})}function o({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...s})}function d({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...s})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61611:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74052:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var r=t(60687),a=t(43210),i=t(44493),n=t(29523),c=t(91821),o=t(41862),d=t(5336),l=t(35071),u=t(61611),m=t(78122),x=t(41312),p=t(19080),g=t(97051),h=t(95560);function b(){let[e,s]=(0,a.useState)(!1),[t,b]=(0,a.useState)(!0),[v,f]=(0,a.useState)(!1),[y,j]=(0,a.useState)(""),[N,w]=(0,a.useState)(""),[k,A]=(0,a.useState)("checking"),[_,R]=(0,a.useState)({users:0,orders:0,notifications:0,total:0}),q=async()=>{try{b(!0),j("");let e=await (0,h.tv)();if(e.success){A("connected"),f(!0);let e=await (0,h.Rm)();R(e),e.total>0?w("Firebase مُعد بشكل صحيح ويحتوي على بيانات"):(w("Firebase متصل ولكن يحتاج إلى إعداد البيانات"),f(!1))}else A("disconnected"),f(!1),j(e.message)}catch(e){A("disconnected"),f(!1),j(`خطأ في فحص Firebase: ${e.message}`)}finally{b(!1)}},F=async()=>{s(!0),j(""),w("");try{console.log("\uD83D\uDD25 بدء إعداد Firebase...");let e=await (0,h.E8)();if(e.success){w(e.message),f(!0);let s=await (0,h.Rm)();R(s),console.log("✅ تم إعداد Firebase بنجاح")}else j(e.message),console.error("❌ فشل إعداد Firebase:",e.message)}catch(e){j(`حدث خطأ غير متوقع: ${e.message}`),console.error("❌ خطأ في إعداد Firebase:",e)}finally{s(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-4xl",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(u.A,{className:"h-8 w-8 text-white"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إعداد قاعدة البيانات Firebase"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"إعداد وتهيئة قاعدة البيانات السحابية لتطبيق مرسال"})]}),(0,r.jsx)(i.Zp,{className:`mb-6 ${(()=>{switch(k){case"checking":return"border-yellow-200 bg-yellow-50";case"connected":return"border-green-200 bg-green-50";case"disconnected":return"border-red-200 bg-red-50"}})()}`,children:(0,r.jsx)(i.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(()=>{switch(k){case"checking":return(0,r.jsx)(o.A,{className:"h-5 w-5 animate-spin text-yellow-600"});case"connected":return(0,r.jsx)(d.A,{className:"h-5 w-5 text-green-600"});case"disconnected":return(0,r.jsx)(l.A,{className:"h-5 w-5 text-red-600"})}})(),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:(()=>{switch(k){case"checking":return"جاري فحص الاتصال...";case"connected":return"متصل بـ Firebase";case"disconnected":return"غير متصل بـ Firebase"}})()}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"connected"===k?"Firebase جاهز للاستخدام":"disconnected"===k?"يرجى التحقق من إعدادات Firebase":"جاري التحقق من الاتصال..."})]})]}),(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:q,disabled:t,children:[t?(0,r.jsx)(o.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"إعادة الفحص"]})]})})}),"connected"===k&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold",children:_.users}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"المستخدمين"})]})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(p.A,{className:"h-8 w-8 text-green-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold",children:_.orders}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"الطلبات"})]})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(g.A,{className:"h-8 w-8 text-yellow-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold",children:_.notifications}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"الإشعارات"})]})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-purple-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold",children:_.total}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"إجمالي البيانات"})]})]})})})]}),y&&(0,r.jsxs)(c.Fc,{variant:"destructive",className:"mb-6",children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)(c.TN,{children:y})]}),N&&(0,r.jsxs)(c.Fc,{className:"mb-6 border-green-200 bg-green-50",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)(c.TN,{className:"text-green-800",children:N})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"h-5 w-5"}),"إعداد قاعدة البيانات"]}),(0,r.jsx)(i.BT,{children:"إعداد المستخدمين والطلبات والإشعارات الافتراضية في Firebase"})]}),(0,r.jsx)(i.Wu,{className:"space-y-4",children:v?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(d.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-green-800 mb-2",children:"تم إعداد قاعدة البيانات بنجاح!"}),(0,r.jsx)("p",{className:"text-green-600 mb-4",children:"جميع البيانات المطلوبة متوفرة ويمكنك الآن استخدام التطبيق"}),(0,r.jsxs)("div",{className:"flex gap-2 justify-center",children:[(0,r.jsx)(n.$,{onClick:()=>window.open("/firebase-login","_blank"),className:"bg-green-600 hover:bg-green-700",children:"تسجيل الدخول للتطبيق"}),(0,r.jsx)(n.$,{variant:"outline",onClick:F,disabled:e,children:"إعادة الإعداد"})]})]}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(u.A,{className:"h-16 w-16 text-blue-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"إعداد قاعدة البيانات مطلوب"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"انقر على الزر أدناه لإنشاء المستخدمين والطلبات الافتراضية"}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6 text-right",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"سيتم إنشاء:"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• 4 مستخدمين افتراضيين (azad95, manager, supervisor, courier)"}),(0,r.jsx)("li",{children:"• 5 طلبات تجريبية بحالات مختلفة"}),(0,r.jsx)("li",{children:"• 5 إشعارات تجريبية"}),(0,r.jsx)("li",{children:"• إعدادات النظام الأساسية"})]})]}),(0,r.jsx)(n.$,{onClick:F,disabled:e||"connected"!==k,size:"lg",className:"bg-blue-600 hover:bg-blue-700",children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.A,{className:"mr-2 h-5 w-5 animate-spin"}),"جاري الإعداد..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"mr-2 h-5 w-5"}),"بدء إعداد قاعدة البيانات"]})})]})})]}),(0,r.jsxs)("div",{className:"text-center mt-8 text-sm text-gray-500",children:[(0,r.jsx)("p",{children:"تطبيق مرسال - نظام إدارة التوصيل"}),(0,r.jsx)("p",{children:"مدعوم بـ Firebase"})]})]})})}},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83666:(e,s,t)=>{Promise.resolve().then(t.bind(t,12686))},91645:e=>{"use strict";e.exports=require("net")},91821:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>o,TN:()=>d});var r=t(60687),a=t(43210),i=t(24224),n=t(4780);let c=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef(({className:e,variant:s,...t},a)=>(0,r.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(c({variant:s}),e),...t}));o.displayName="Alert",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle";let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...s}));d.displayName="AlertDescription"},94031:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),c=t(30893),o={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);t.d(s,o);let d={children:["",{children:["firebase-setup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,12686)),"E:\\Marsal\\marsal\\src\\app\\firebase-setup\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["E:\\Marsal\\marsal\\src\\app\\firebase-setup\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/firebase-setup/page",pathname:"/firebase-setup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")},95560:(e,s,t)=>{"use strict";t.d(s,{E8:()=>n,Rm:()=>l,tv:()=>d});var r=t(39903),a=t(75535),i=t(33784);let n=async()=>{try{return console.log("\uD83D\uDD25 بدء إعداد قاعدة البيانات Firebase..."),console.log("\uD83D\uDC65 إنشاء المستخدمين الافتراضيين..."),await r.firebaseAuthService.initializeDefaultUsers(),console.log("\uD83D\uDCE6 إنشاء طلبات تجريبية..."),await c(),console.log("\uD83D\uDD14 إنشاء إشعارات تجريبية..."),await o(),console.log("✅ تم إعداد قاعدة البيانات Firebase بنجاح!"),{success:!0,message:"تم إعداد قاعدة البيانات بنجاح مع جميع البيانات المطلوبة"}}catch(e){return console.error("❌ خطأ في إعداد قاعدة البيانات:",e),{success:!1,message:`فشل في إعداد قاعدة البيانات: ${e.message}`}}},c=async()=>{if(!i.db)throw Error("Firebase غير متصل");let e=[{trackingNumber:"ORDER_001",customerName:"أحمد محمد علي",customerPhone:"07701234567",address:"بغداد - الكرادة - شارع الرشيد",amount:25e3,status:"pending",courierName:"",assignedTo:"",notes:"طلب تجريبي - يرجى التوصيل صباحاً",createdBy:"system"},{trackingNumber:"ORDER_002",customerName:"فاطمة علي حسن",customerPhone:"07701234568",address:"البصرة - المعقل - حي الجمهورية",amount:35e3,status:"assigned",courierName:"مندوب التوصيل",assignedTo:"courier_1",notes:"طلب عاجل - يرجى التوصيل اليوم",createdBy:"manager"},{trackingNumber:"ORDER_003",customerName:"محمد حسين كريم",customerPhone:"07701234569",address:"أربيل - عنكاوا - شارع الكنائس",amount:45e3,status:"delivered",courierName:"مندوب التوصيل",assignedTo:"courier_1",notes:"تم التسليم بنجاح",createdBy:"supervisor",deliveredAt:new Date},{trackingNumber:"ORDER_004",customerName:"زينب أحمد محمود",customerPhone:"07701234570",address:"النجف - المدينة القديمة - قرب الحرم",amount:3e4,status:"returned",courierName:"مندوب التوصيل",assignedTo:"courier_1",notes:"تم الإرجاع - العنوان غير صحيح",createdBy:"supervisor"},{trackingNumber:"ORDER_005",customerName:"علي حسن جعفر",customerPhone:"07701234571",address:"كربلاء - حي الحسين - شارع الإمام علي",amount:55e3,status:"in_transit",courierName:"مندوب التوصيل",assignedTo:"courier_1",notes:"في الطريق للتسليم",createdBy:"manager"}],s=(0,a.collection)(i.db,"orders");for(let t of e)try{await (0,a.gS)(s,{...t,createdAt:(0,a.O5)(),updatedAt:(0,a.O5)()}),console.log(`✅ تم إنشاء الطلب: ${t.trackingNumber}`)}catch(e){console.error(`❌ خطأ في إنشاء الطلب ${t.trackingNumber}:`,e)}},o=async()=>{if(!i.db)throw Error("Firebase غير متصل");let e=(0,a.collection)(i.db,"notifications");for(let s of[{title:"طلب جديد",message:"تم إسناد طلب ORDER_002 إليك",type:"order_assigned",userId:"courier_1",orderId:"ORDER_002",isRead:!1},{title:"تحديث حالة الطلب",message:"تم تسليم الطلب ORDER_003 بنجاح",type:"status_changed",userId:"manager",orderId:"ORDER_003",isRead:!1},{title:"طلب مرتجع",message:"تم إرجاع الطلب ORDER_004 - العنوان غير صحيح",type:"order_returned",userId:"supervisor",orderId:"ORDER_004",isRead:!1},{title:"مستخدم جديد",message:"تم إنشاء حساب مندوب جديد",type:"user_created",userId:"manager",isRead:!0},{title:"تقرير يومي",message:"تم تسليم 5 طلبات اليوم بنجاح",type:"daily_report",userId:"manager",isRead:!1}])try{await (0,a.gS)(e,{...s,createdAt:(0,a.O5)()}),console.log(`✅ تم إنشاء الإشعار: ${s.title}`)}catch(e){console.error(`❌ خطأ في إنشاء الإشعار:`,e)}},d=async()=>{try{let{testFirebaseConnection:e}=await Promise.resolve().then(t.bind(t,33784)),s=await e();if(!s.success)return{success:!1,message:`فشل الاتصال بـ Firebase: ${s.message}`};let a=await r.firebaseAuthService.checkConnection();if(!a.connected)return{success:!1,message:`فشل في نظام المصادقة: ${a.message}`};return{success:!0,message:"Firebase مُعد بشكل صحيح ومتصل"}}catch(e){return{success:!1,message:`خطأ في فحص Firebase: ${e.message}`}}},l=async()=>{try{if(!i.db)throw Error("Firebase غير متصل");let{getDocs:e,collection:s}=await Promise.resolve().then(t.bind(t,75535)),r=(await e(s(i.db,"users"))).size,a=(await e(s(i.db,"orders"))).size,n=(await e(s(i.db,"notifications"))).size;return{users:r,orders:a,notifications:n,total:r+a+n}}catch(e){return console.error("خطأ في جلب إحصائيات قاعدة البيانات:",e),{users:0,orders:0,notifications:0,total:0}}}},97051:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,863,860,610],()=>t(94031));module.exports=r})();