@echo off
chcp 65001 >nul
title تشغيل تطبيق مرسال

echo.
echo ========================================
echo           🚀 تطبيق مرسال 🚀
echo     نظام إدارة التوصيل السريع
echo ========================================
echo.

echo 📋 التحقق من متطلبات التشغيل...

:: التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo 💡 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js مثبت

:: التحقق من وجود npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح!
    pause
    exit /b 1
)

echo ✅ npm متاح

:: الانتقال إلى مجلد التطبيق
cd /d "%~dp0marsal"

:: التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود!
    echo 💡 تأكد من وجود ملفات التطبيق
    pause
    exit /b 1
)

echo ✅ ملفات التطبيق موجودة

:: التحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات!
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
) else (
    echo ✅ المكتبات مثبتة
)

echo.
echo 🚀 تشغيل تطبيق مرسال...
echo.
echo 📱 سيتم فتح التطبيق على: http://localhost:3000
echo 🔑 بيانات الدخول:
echo    - مدير: manager / 123456
echo    - متابع: supervisor / 123456
echo    - مندوب: courier / 123456
echo.
echo ⏳ انتظر قليلاً حتى يتم تحميل التطبيق...
echo.

:: تشغيل التطبيق
start "" "http://localhost:3000"
npm run dev

pause
