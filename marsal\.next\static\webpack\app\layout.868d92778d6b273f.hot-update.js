"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"53713a7ebbad\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJFOlxcTWFyc2FsXFxtYXJzYWxcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjUzNzEzYTdlYmJhZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/firebase-auth.ts":
/*!**********************************!*\
  !*** ./src/lib/firebase-auth.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   firebaseAuthService: () => (/* binding */ firebaseAuthService)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n// Firebase Authentication service for Marsal Delivery App\n\n\n\n// Default users for initial setup\nconst defaultUsers = [\n    {\n        username: 'azad95',\n        email: '<EMAIL>',\n        name: 'أزاد - مدير النظام الرئيسي',\n        phone: '07801234567',\n        role: 'manager',\n        password: 'Azad@1995'\n    },\n    {\n        username: 'manager',\n        email: '<EMAIL>',\n        name: 'مدير النظام',\n        phone: '07801234568',\n        role: 'manager',\n        password: '123456'\n    },\n    {\n        username: 'supervisor',\n        email: '<EMAIL>',\n        name: 'المشرف العام',\n        phone: '07801234569',\n        role: 'supervisor',\n        password: '123456'\n    },\n    {\n        username: 'courier',\n        email: '<EMAIL>',\n        name: 'مندوب التوصيل',\n        phone: '07801234570',\n        role: 'courier',\n        password: '123456'\n    }\n];\nconst firebaseAuthService = {\n    // Initialize default users (run once)\n    async initializeDefaultUsers () {\n        try {\n            console.log('🔧 إعداد المستخدمين الافتراضيين...');\n            for (const userData of defaultUsers){\n                // Check if user already exists\n                const existingUser = await this.getUserByUsername(userData.username);\n                if (existingUser) {\n                    console.log(\"✅ المستخدم \".concat(userData.username, \" موجود مسبقاً\"));\n                    continue;\n                }\n                // Create Firebase Auth user\n                try {\n                    const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, userData.email, userData.password);\n                    // Create user document in Firestore\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n                        username: userData.username,\n                        email: userData.email,\n                        name: userData.name,\n                        phone: userData.phone,\n                        role: userData.role,\n                        isActive: true,\n                        createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                        updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)(),\n                        createdBy: 'system'\n                    });\n                    console.log(\"✅ تم إنشاء المستخدم: \".concat(userData.username));\n                } catch (error) {\n                    if (error.code === 'auth/email-already-in-use') {\n                        console.log(\"⚠️ البريد الإلكتروني \".concat(userData.email, \" مستخدم مسبقاً\"));\n                    } else {\n                        console.error(\"❌ خطأ في إنشاء المستخدم \".concat(userData.username, \":\"), error);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ خطأ في إعداد المستخدمين الافتراضيين:', error);\n        }\n    },\n    // Login with username/password\n    async login (username, password) {\n        try {\n            console.log('🔐 محاولة تسجيل الدخول للمستخدم:', username);\n            // Get user by username to find email\n            const user = await this.getUserByUsername(username);\n            if (!user) {\n                return {\n                    success: false,\n                    error: 'اسم المستخدم غير موجود'\n                };\n            }\n            if (!user.isActive) {\n                return {\n                    success: false,\n                    error: 'الحساب غير مفعل'\n                };\n            }\n            // Sign in with email and password\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, user.email, password);\n            // Update last login\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userCredential.user.uid), {\n                lastLogin: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.serverTimestamp)()\n            }, {\n                merge: true\n            });\n            console.log('✅ تم تسجيل الدخول بنجاح');\n            return {\n                success: true,\n                user: {\n                    ...user,\n                    id: userCredential.user.uid\n                }\n            };\n        } catch (error) {\n            console.error('❌ خطأ في تسجيل الدخول:', error);\n            let errorMessage = 'خطأ في تسجيل الدخول';\n            switch(error.code){\n                case 'auth/user-not-found':\n                    errorMessage = 'المستخدم غير موجود';\n                    break;\n                case 'auth/wrong-password':\n                    errorMessage = 'كلمة المرور غير صحيحة';\n                    break;\n                case 'auth/invalid-email':\n                    errorMessage = 'البريد الإلكتروني غير صحيح';\n                    break;\n                case 'auth/user-disabled':\n                    errorMessage = 'الحساب معطل';\n                    break;\n                case 'auth/too-many-requests':\n                    errorMessage = 'محاولات كثيرة، يرجى المحاولة لاحقاً';\n                    break;\n                default:\n                    errorMessage = error.message || 'خطأ غير معروف';\n            }\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    },\n    // Get user by username\n    async getUserByUsername (username) {\n        try {\n            var _data_createdAt, _data_updatedAt, _data_lastLogin;\n            const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users');\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('username', '==', username));\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (snapshot.empty) {\n                return null;\n            }\n            const doc = snapshot.docs[0];\n            const data = doc.data();\n            return {\n                id: doc.id,\n                username: data.username,\n                email: data.email,\n                name: data.name,\n                phone: data.phone,\n                role: data.role,\n                isActive: data.isActive,\n                createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date(),\n                updatedAt: ((_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()) || new Date(),\n                createdBy: data.createdBy,\n                lastLogin: (_data_lastLogin = data.lastLogin) === null || _data_lastLogin === void 0 ? void 0 : _data_lastLogin.toDate()\n            };\n        } catch (error) {\n            console.error('Error getting user by username:', error);\n            return null;\n        }\n    },\n    // Get current user\n    async getCurrentUser () {\n        try {\n            var _data_createdAt, _data_updatedAt, _data_lastLogin;\n            const firebaseUser = _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n            if (!firebaseUser) {\n                return null;\n            }\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', firebaseUser.uid));\n            if (!userDoc.exists()) {\n                return null;\n            }\n            const data = userDoc.data();\n            return {\n                id: userDoc.id,\n                username: data.username,\n                email: data.email,\n                name: data.name,\n                phone: data.phone,\n                role: data.role,\n                isActive: data.isActive,\n                createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date(),\n                updatedAt: ((_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()) || new Date(),\n                createdBy: data.createdBy,\n                lastLogin: (_data_lastLogin = data.lastLogin) === null || _data_lastLogin === void 0 ? void 0 : _data_lastLogin.toDate()\n            };\n        } catch (error) {\n            console.error('Error getting current user:', error);\n            return null;\n        }\n    },\n    // Logout\n    async logout () {\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth);\n            console.log('✅ تم تسجيل الخروج بنجاح');\n        } catch (error) {\n            console.error('❌ خطأ في تسجيل الخروج:', error);\n            throw error;\n        }\n    },\n    // Listen to auth state changes\n    onAuthStateChanged (callback) {\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, async (firebaseUser)=>{\n            if (firebaseUser) {\n                const user = await this.getCurrentUser();\n                callback(user);\n            } else {\n                callback(null);\n            }\n        });\n    },\n    // Check if Firebase is connected\n    async checkConnection () {\n        try {\n            // Try to get current user\n            const user = _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n            console.log('✅ Firebase Auth متصل');\n            return {\n                connected: true,\n                message: 'Firebase Auth متصل بنجاح'\n            };\n        } catch (error) {\n            console.error('❌ Firebase Auth غير متصل:', error);\n            return {\n                connected: false,\n                message: \"خطأ في الاتصال: \".concat(error.message)\n            };\n        }\n    }\n};\n// Auto-initialize default users when the module loads\nfirebaseAuthService.initializeDefaultUsers().catch(console.error);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/firebase-auth.ts\n"));

/***/ })

});