// Updated to use Supabase instead of Firebase
import { supabase, userService, orderService, type User as SupabaseUser, type Order as SupabaseOrder } from './supabase';
import { Order, User, Settlement, StatusUpdate } from '@/types';
import { generateTrackingNumber } from './utils';
import { mockOrders, mockUsers, mockSettlements } from './mock-data';

// Orders Service - Updated to use Supabase
export const ordersService = {
  // Create new order
  async create(orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt' | 'trackingNumber' | 'statusHistory'>) {
    const trackingNumber = generateTrackingNumber();

    const supabaseOrderData = {
      tracking_number: trackingNumber,
      customer_name: orderData.customerName,
      customer_phone: orderData.customerPhone,
      address: orderData.address,
      amount: orderData.amount,
      status: 'pending',
      courier_name: orderData.courierName,
      courier_id: orderData.courierId,
      notes: orderData.notes
    };

    const order = await orderService.createOrder(supabaseOrderData);

    // Convert back to frontend format
    return {
      id: order.id,
      trackingNumber: order.tracking_number,
      customerName: order.customer_name,
      customerPhone: order.customer_phone,
      address: order.address,
      amount: order.amount,
      status: order.status,
      courierName: order.courier_name,
      courierId: order.courier_id,
      notes: order.notes,
      createdAt: new Date(order.created_at),
      updatedAt: new Date(order.updated_at),
      statusHistory: [{
        status: 'pending',
        timestamp: new Date(order.created_at),
        updatedBy: 'system',
        notes: 'تم إنشاء الطلب'
      }]
    } as Order;
  },

  // Get all orders with pagination
  async getAll(pageSize = 20, lastDoc?: any) {
    try {
      const supabaseOrders = await orderService.getAllOrders();

      // Convert from Supabase format to frontend format
      const orders = supabaseOrders.map(order => ({
        id: order.id,
        trackingNumber: order.tracking_number,
        customerName: order.customer_name,
        customerPhone: order.customer_phone,
        address: order.address,
        amount: order.amount,
        status: order.status,
        courierName: order.courier_name,
        courierId: order.courier_id,
        notes: order.notes,
        createdAt: new Date(order.created_at),
        updatedAt: new Date(order.updated_at),
        deliveredAt: order.delivered_at ? new Date(order.delivered_at) : undefined,
        statusHistory: [{
          status: order.status,
          timestamp: new Date(order.updated_at),
          updatedBy: 'system',
          notes: order.notes || ''
        }]
      })) as Order[];

      // Simple pagination simulation
      const startIndex = lastDoc ? parseInt(lastDoc) : 0;
      const endIndex = startIndex + pageSize;
      const paginatedOrders = orders.slice(startIndex, endIndex);

      return {
        orders: paginatedOrders,
        lastDoc: endIndex.toString(),
        hasMore: endIndex < orders.length
      };
    } catch (error) {
      console.warn('Supabase not available, using mock data:', error);
      return {
        orders: mockOrders,
        lastDoc: null,
        hasMore: false
      };
    }
  },

  // Get order by ID
  async getById(id: string) {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('id', id)
        .single();

      if (error || !data) {
        throw new Error('Order not found');
      }

      return {
        id: data.id,
        trackingNumber: data.tracking_number,
        customerName: data.customer_name,
        customerPhone: data.customer_phone,
        address: data.address,
        amount: data.amount,
        status: data.status,
        courierName: data.courier_name,
        courierId: data.courier_id,
        notes: data.notes,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
        deliveredAt: data.delivered_at ? new Date(data.delivered_at) : undefined,
        statusHistory: [{
          status: data.status,
          timestamp: new Date(data.updated_at),
          updatedBy: 'system',
          notes: data.notes || ''
        }]
      } as Order;
    } catch (error) {
      console.error('Error getting order by ID:', error);
      throw new Error('Order not found');
    }
  },

  // Search orders
  async search(searchTerm: string) {
    try {
      const supabaseOrders = await orderService.searchOrders(searchTerm);

      return supabaseOrders.map(order => ({
        id: order.id,
        trackingNumber: order.tracking_number,
        customerName: order.customer_name,
        customerPhone: order.customer_phone,
        address: order.address,
        amount: order.amount,
        status: order.status,
        courierName: order.courier_name,
        courierId: order.courier_id,
        notes: order.notes,
        createdAt: new Date(order.created_at),
        updatedAt: new Date(order.updated_at),
        deliveredAt: order.delivered_at ? new Date(order.delivered_at) : undefined,
        statusHistory: [{
          status: order.status,
          timestamp: new Date(order.updated_at),
          updatedBy: 'system',
          notes: order.notes || ''
        }]
      })) as Order[];
    } catch (error) {
      console.warn('Search failed, using mock data:', error);
      return mockOrders.filter(order =>
        order.trackingNumber.includes(searchTerm) ||
        order.customerName.includes(searchTerm) ||
        order.customerPhone.includes(searchTerm)
      );
    }
    const orders = new Map();

    results.forEach(snapshot => {
      snapshot.docs.forEach(doc => {
        if (!orders.has(doc.id)) {
          const data = doc.data();
          orders.set(doc.id, {
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate(),
            updatedAt: data.updatedAt?.toDate(),
            deliveredAt: data.deliveredAt?.toDate(),
            returnedAt: data.returnedAt?.toDate(),
            statusHistory: data.statusHistory?.map((update: any) => ({
              ...update,
              timestamp: update.timestamp?.toDate()
            }))
          });
        }
      });
    });

    return Array.from(orders.values()) as Order[];
  },

  // Update order status
  async updateStatus(orderId: string, status: string, notes: string, updatedBy: string, image?: string) {
    const orderRef = doc(db, COLLECTIONS.ORDERS, orderId);
    const now = Timestamp.now();
    
    const statusUpdate: StatusUpdate = {
      status: status as any,
      timestamp: now.toDate(),
      updatedBy,
      notes,
      image
    };

    const updateData: any = {
      status,
      updatedAt: now,
      [`statusHistory`]: [...(await this.getById(orderId)).statusHistory, statusUpdate]
    };

    if (status === 'delivered') {
      updateData.deliveredAt = now;
    } else if (status === 'returned') {
      updateData.returnedAt = now;
    }

    await updateDoc(orderRef, updateData);
    return statusUpdate;
  },

  // Get orders by status
  async getByStatus(status: string) {
    const q = query(
      collection(db, COLLECTIONS.ORDERS),
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );
    
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate(),
      deliveredAt: doc.data().deliveredAt?.toDate(),
      returnedAt: doc.data().returnedAt?.toDate(),
      statusHistory: doc.data().statusHistory?.map((update: any) => ({
        ...update,
        timestamp: update.timestamp?.toDate()
      }))
    })) as Order[];
  },

  // Get orders by courier
  async getByCourier(courierId: string) {
    const q = query(
      collection(db, COLLECTIONS.ORDERS),
      where('assignedTo', '==', courierId),
      orderBy('createdAt', 'desc')
    );
    
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate(),
      deliveredAt: doc.data().deliveredAt?.toDate(),
      returnedAt: doc.data().returnedAt?.toDate(),
      statusHistory: doc.data().statusHistory?.map((update: any) => ({
        ...update,
        timestamp: update.timestamp?.toDate()
      }))
    })) as Order[];
  },

  // Assign orders to courier
  async assignToCourier(orderIds: string[], courierId: string, assignedBy: string) {
    const batch = writeBatch(db);
    const now = Timestamp.now();

    for (const orderId of orderIds) {
      const orderRef = doc(db, COLLECTIONS.ORDERS, orderId);
      const order = await this.getById(orderId);
      
      const statusUpdate: StatusUpdate = {
        status: 'assigned',
        timestamp: now.toDate(),
        updatedBy: assignedBy,
        notes: `تم إسناد الطلب للمندوب`
      };

      batch.update(orderRef, {
        assignedTo: courierId,
        status: 'assigned',
        updatedAt: now,
        statusHistory: [...order.statusHistory, statusUpdate]
      });
    }

    await batch.commit();
  },

  // Delete order
  async delete(id: string) {
    await deleteDoc(doc(db, COLLECTIONS.ORDERS, id));
  }
};

// Users Service - Updated to use Supabase
export const usersService = {
  // Create user
  async create(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      const supabaseUserData = {
        username: userData.username,
        name: userData.name,
        phone: userData.phone,
        role: userData.role,
        password_hash: '$2b$10$example_hash_for_123456', // In production, hash the password properly
        is_active: true,
        created_by: userData.createdBy || 'system'
      };

      const user = await userService.createUser(supabaseUserData);

      return {
        id: user.id,
        username: user.username,
        name: user.name,
        phone: user.phone,
        role: user.role,
        isActive: user.is_active,
        createdAt: new Date(user.created_at),
        updatedAt: new Date(user.created_at),
        createdBy: user.created_by
      } as User;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  },

  // Get all users
  async getAll() {
    try {
      const supabaseUsers = await userService.getAllUsers();

      return supabaseUsers.map(user => ({
        id: user.id,
        username: user.username,
        name: user.name,
        phone: user.phone,
        role: user.role,
        isActive: user.is_active,
        createdAt: new Date(user.created_at),
        updatedAt: new Date(user.created_at),
        createdBy: user.created_by
      })) as User[];
    } catch (error) {
      console.warn('Error getting users, using mock data:', error);
      return mockUsers;
    }
  },

  // Get couriers only
  async getCouriers() {
    try {
      const couriers = await userService.getUsersByRole('courier');

      return couriers.filter(user => user.is_active).map(user => ({
        id: user.id,
        username: user.username,
        name: user.name,
        phone: user.phone,
        role: user.role,
        isActive: user.is_active,
        createdAt: new Date(user.created_at),
        updatedAt: new Date(user.created_at),
        createdBy: user.created_by
      })) as User[];
    } catch (error) {
      console.warn('Error getting couriers, using mock data:', error);
      return mockUsers.filter(user => user.role === 'courier' && user.isActive);
    }
  },

  // Get user by ID
  async getById(id: string) {
    const docRef = doc(db, COLLECTIONS.USERS, id);
    const docSnap = await getDoc(docRef);
    
    if (!docSnap.exists()) {
      throw new Error('User not found');
    }

    const data = docSnap.data();
    return {
      id: docSnap.id,
      ...data,
      createdAt: data.createdAt?.toDate(),
      updatedAt: data.updatedAt?.toDate()
    } as User;
  },

  // Update user
  async update(id: string, userData: Partial<User>) {
    const userRef = doc(db, COLLECTIONS.USERS, id);
    await updateDoc(userRef, {
      ...userData,
      updatedAt: Timestamp.now()
    });
  },

  // Delete user
  async delete(id: string) {
    await deleteDoc(doc(db, COLLECTIONS.USERS, id));
  }
};

// Settlements Service
export const settlementsService = {
  // Create settlement
  async create(settlementData: Omit<Settlement, 'id' | 'createdAt'>) {
    const now = Timestamp.now();
    const settlement: Omit<Settlement, 'id'> = {
      ...settlementData,
      createdAt: now.toDate(),
      isSettled: false
    };

    const docRef = await addDoc(collection(db, COLLECTIONS.SETTLEMENTS), settlement);
    return { id: docRef.id, ...settlement };
  },

  // Get all settlements
  async getAll() {
    const q = query(collection(db, COLLECTIONS.SETTLEMENTS), orderBy('createdAt', 'desc'));
    const snapshot = await getDocs(q);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      settledAt: doc.data().settledAt?.toDate()
    })) as Settlement[];
  },

  // Mark settlement as settled
  async markAsSettled(id: string) {
    const settlementRef = doc(db, COLLECTIONS.SETTLEMENTS, id);
    await updateDoc(settlementRef, {
      isSettled: true,
      settledAt: Timestamp.now()
    });
  },

  // Real-time subscription to orders
  subscribeToOrders(callback: (orders: Order[]) => void) {
    try {
      const q = query(collection(db, COLLECTIONS.ORDERS), orderBy('createdAt', 'desc'));
      return onSnapshot(q, (snapshot) => {
        const orders = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as Order));
        callback(orders);
      });
    } catch (error) {
      console.warn('Firebase subscription not available:', error);
      // Return a dummy unsubscribe function
      return () => {};
    }
  },

  // Real-time subscription to user's orders
  subscribeToUserOrders(userId: string, callback: (orders: Order[]) => void) {
    try {
      const q = query(
        collection(db, COLLECTIONS.ORDERS),
        where('courierId', '==', userId),
        orderBy('createdAt', 'desc')
      );
      return onSnapshot(q, (snapshot) => {
        const orders = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as Order));
        callback(orders);
      });
    } catch (error) {
      console.warn('Firebase subscription not available:', error);
      return () => {};
    }
  }
};
