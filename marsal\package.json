{"name": "marsa", "version": "1.1.0", "description": "نظام إدارة التوصيل مرسال - تطبيق شامل لإدارة عمليات التوصيل والشحن", "author": "فريق تطوير مرسال", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "build:web": "node build-config.js && cp next.config.web.js next.config.js && npm run build", "build:mobile": "node build-config.js && cp next.config.mobile.js next.config.js && npm run build", "build:desktop": "node build-config.js && cp next.config.desktop.js next.config.js && npm run build", "build:all": "node build-all.js", "electron": "electron .", "electron:dev": "NODE_ENV=development electron .", "pack": "electron-builder --dir", "dist": "electron-builder", "dist:win": "electron-builder --win", "dist:mac": "electron-builder --mac", "dist:linux": "electron-builder --linux"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@supabase/supabase-js": "^2.50.3", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.9.1", "html2canvas": "^1.4.1", "lucide-react": "^0.525.0", "next": "15.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@capacitor/android": "^6.0.0", "@capacitor/cli": "^6.0.0", "@capacitor/core": "^6.0.0", "@capacitor/ios": "^6.0.0", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}, "main": "electron.js", "homepage": "./", "build": {"appId": "com.marsal.delivery", "productName": "مرسال - إدارة التوصيل", "directories": {"output": "dist/desktop"}, "files": ["out/**/*", "electron.js", "package.json"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "public/icon.ico"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "public/icon.icns"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "public/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}