(()=>{var e={};e.id=463,e.ids=[463],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8321:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>nT});var n,o,i,a=r(60687),l=r(43210),s=r.t(l,2),c=r(44493),u=r(29523),d=r(89667),f=r(54300),p=r(51215);function m(e,[t,r]){return Math.min(r,Math.max(t,e))}function h(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function v(e,t=[]){let r=[],n=()=>{let t=r.map(e=>l.createContext(e));return function(r){let n=r?.[e]||t;return l.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let o=l.createContext(n),i=r.length;r=[...r,n];let s=t=>{let{scope:r,children:n,...s}=t,c=r?.[e]?.[i]||o,u=l.useMemo(()=>s,Object.values(s));return(0,a.jsx)(c.Provider,{value:u,children:n})};return s.displayName=t+"Provider",[s,function(r,a){let s=a?.[e]?.[i]||o,c=l.useContext(s);if(c)return c;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}var g=r(98599),x=r(8730),y=new WeakMap;function w(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=b(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function b(e){return e!=e||0===e?0:Math.trunc(e)}var E=l.createContext(void 0),j=r(14163);function S(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}var C="dismissableLayer.update",N=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),R=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:n,onPointerDownOutside:i,onFocusOutside:s,onInteractOutside:c,onDismiss:u,...d}=e,f=l.useContext(N),[p,m]=l.useState(null),v=p?.ownerDocument??globalThis?.document,[,x]=l.useState({}),y=(0,g.s)(t,e=>m(e)),w=Array.from(f.layers),[b]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),E=w.indexOf(b),R=p?w.indexOf(p):-1,k=f.layersWithOutsidePointerEventsDisabled.size>0,P=R>=E,L=function(e,t=globalThis?.document){let r=S(e),n=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){T("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=n,t.addEventListener("click",o.current,{once:!0})):n()}else t.removeEventListener("click",o.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...f.branches].some(e=>e.contains(t));P&&!r&&(i?.(e),c?.(e),e.defaultPrevented||u?.())},v),M=function(e,t=globalThis?.document){let r=S(e),n=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!n.current&&T("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;![...f.branches].some(e=>e.contains(t))&&(s?.(e),c?.(e),e.defaultPrevented||u?.())},v);return!function(e,t=globalThis?.document){let r=S(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{R===f.layers.size-1&&(n?.(e),!e.defaultPrevented&&u&&(e.preventDefault(),u()))},v),l.useEffect(()=>{if(p)return r&&(0===f.layersWithOutsidePointerEventsDisabled.size&&(o=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(p)),f.layers.add(p),A(),()=>{r&&1===f.layersWithOutsidePointerEventsDisabled.size&&(v.body.style.pointerEvents=o)}},[p,v,r,f]),l.useEffect(()=>()=>{p&&(f.layers.delete(p),f.layersWithOutsidePointerEventsDisabled.delete(p),A())},[p,f]),l.useEffect(()=>{let e=()=>x({});return document.addEventListener(C,e),()=>document.removeEventListener(C,e)},[]),(0,a.jsx)(j.sG.div,{...d,ref:y,style:{pointerEvents:k?P?"auto":"none":void 0,...e.style},onFocusCapture:h(e.onFocusCapture,M.onFocusCapture),onBlurCapture:h(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:h(e.onPointerDownCapture,L.onPointerDownCapture)})});function A(){let e=new CustomEvent(C);document.dispatchEvent(e)}function T(e,t,r,{discrete:n}){let o=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,j.hO)(o,i):o.dispatchEvent(i)}R.displayName="DismissableLayer",l.forwardRef((e,t)=>{let r=l.useContext(N),n=l.useRef(null),o=(0,g.s)(t,n);return l.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,a.jsx)(j.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var k=0;function P(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var L="focusScope.autoFocusOnMount",M="focusScope.autoFocusOnUnmount",D={bubbles:!1,cancelable:!0},O=l.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[c,u]=l.useState(null),d=S(o),f=S(i),p=l.useRef(null),m=(0,g.s)(t,e=>u(e)),h=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(n){let e=function(e){if(h.paused||!c)return;let t=e.target;c.contains(t)?p.current=t:W(p.current,{select:!0})},t=function(e){if(h.paused||!c)return;let t=e.relatedTarget;null!==t&&(c.contains(t)||W(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&W(c)});return c&&r.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,c,h.paused]),l.useEffect(()=>{if(c){_.add(h);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(L,D);c.addEventListener(L,d),c.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(W(n,{select:t}),document.activeElement!==r)return}(I(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&W(c))}return()=>{c.removeEventListener(L,d),setTimeout(()=>{let t=new CustomEvent(M,D);c.addEventListener(M,f),c.dispatchEvent(t),t.defaultPrevented||W(e??document.body,{select:!0}),c.removeEventListener(M,f),_.remove(h)},0)}}},[c,d,f,h]);let v=l.useCallback(e=>{if(!r&&!n||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,i]=function(e){let t=I(e);return[F(t,e),F(t.reverse(),e)]}(t);n&&i?e.shiftKey||o!==i?e.shiftKey&&o===n&&(e.preventDefault(),r&&W(i,{select:!0})):(e.preventDefault(),r&&W(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,h.paused]);return(0,a.jsx)(j.sG.div,{tabIndex:-1,...s,ref:m,onKeyDown:v})});function I(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function F(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function W(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}O.displayName="FocusScope";var _=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=H(e,t)).unshift(t)},remove(t){e=H(e,t),e[0]?.resume()}}}();function H(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var B=globalThis?.document?l.useLayoutEffect:()=>{},q=s[" useId ".trim().toString()]||(()=>void 0),z=0;function $(e){let[t,r]=l.useState(q());return B(()=>{e||r(e=>e??String(z++))},[e]),e||(t?`radix-${t}`:"")}let G=["top","right","bottom","left"],V=Math.min,K=Math.max,X=Math.round,U=Math.floor,Y=e=>({x:e,y:e}),Z={left:"right",right:"left",bottom:"top",top:"bottom"},J={start:"end",end:"start"};function Q(e,t){return"function"==typeof e?e(t):e}function ee(e){return e.split("-")[0]}function et(e){return e.split("-")[1]}function er(e){return"x"===e?"y":"x"}function en(e){return"y"===e?"height":"width"}let eo=new Set(["top","bottom"]);function ei(e){return eo.has(ee(e))?"y":"x"}function ea(e){return e.replace(/start|end/g,e=>J[e])}let el=["left","right"],es=["right","left"],ec=["top","bottom"],eu=["bottom","top"];function ed(e){return e.replace(/left|right|bottom|top/g,e=>Z[e])}function ef(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ep(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function em(e,t,r){let n,{reference:o,floating:i}=e,a=ei(t),l=er(ei(t)),s=en(l),c=ee(t),u="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(c){case"top":n={x:d,y:o.y-i.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-i.width,y:f};break;default:n={x:o.x,y:o.y}}switch(et(t)){case"start":n[l]-=p*(r&&u?-1:1);break;case"end":n[l]+=p*(r&&u?-1:1)}return n}let eh=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:a}=r,l=i.filter(Boolean),s=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=em(c,n,s),f=n,p={},m=0;for(let r=0;r<l.length;r++){let{name:i,fn:h}=l[r],{x:v,y:g,data:x,reset:y}=await h({x:u,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});u=null!=v?v:u,d=null!=g?g:d,p={...p,[i]:{...p[i],...x}},y&&m<=50&&(m++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(c=!0===y.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:u,y:d}=em(c,f,s)),r=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}};async function ev(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:a,elements:l,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=Q(t,e),m=ef(p),h=l[f?"floating"===d?"reference":"floating":d],v=ep(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(h)))||r?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:u,strategy:s})),g="floating"===d?{x:n,y:o,width:a.floating.width,height:a.floating.height}:a.reference,x=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),y=await (null==i.isElement?void 0:i.isElement(x))&&await (null==i.getScale?void 0:i.getScale(x))||{x:1,y:1},w=ep(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:x,strategy:s}):g);return{top:(v.top-w.top+m.top)/y.y,bottom:(w.bottom-v.bottom+m.bottom)/y.y,left:(v.left-w.left+m.left)/y.x,right:(w.right-v.right+m.right)/y.x}}function eg(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ex(e){return G.some(t=>e[t]>=0)}let ey=new Set(["left","top"]);async function ew(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),a=ee(r),l=et(r),s="y"===ei(r),c=ey.has(a)?-1:1,u=i&&s?-1:1,d=Q(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof m&&(p="end"===l?-1*m:m),s?{x:p*u,y:f*c}:{x:f*c,y:p*u}}function eb(){return"undefined"!=typeof window}function eE(e){return eC(e)?(e.nodeName||"").toLowerCase():"#document"}function ej(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eS(e){var t;return null==(t=(eC(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eC(e){return!!eb()&&(e instanceof Node||e instanceof ej(e).Node)}function eN(e){return!!eb()&&(e instanceof Element||e instanceof ej(e).Element)}function eR(e){return!!eb()&&(e instanceof HTMLElement||e instanceof ej(e).HTMLElement)}function eA(e){return!!eb()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ej(e).ShadowRoot)}let eT=new Set(["inline","contents"]);function ek(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=eB(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!eT.has(o)}let eP=new Set(["table","td","th"]),eL=[":popover-open",":modal"];function eM(e){return eL.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eD=["transform","translate","scale","rotate","perspective"],eO=["transform","translate","scale","rotate","perspective","filter"],eI=["paint","layout","strict","content"];function eF(e){let t=eW(),r=eN(e)?eB(e):e;return eD.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||eO.some(e=>(r.willChange||"").includes(e))||eI.some(e=>(r.contain||"").includes(e))}function eW(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let e_=new Set(["html","body","#document"]);function eH(e){return e_.has(eE(e))}function eB(e){return ej(e).getComputedStyle(e)}function eq(e){return eN(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ez(e){if("html"===eE(e))return e;let t=e.assignedSlot||e.parentNode||eA(e)&&e.host||eS(e);return eA(t)?t.host:t}function e$(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=ez(t);return eH(r)?t.ownerDocument?t.ownerDocument.body:t.body:eR(r)&&ek(r)?r:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),a=ej(o);if(i){let e=eG(a);return t.concat(a,a.visualViewport||[],ek(o)?o:[],e&&r?e$(e):[])}return t.concat(o,e$(o,[],r))}function eG(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eV(e){let t=eB(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=eR(e),i=o?e.offsetWidth:r,a=o?e.offsetHeight:n,l=X(r)!==i||X(n)!==a;return l&&(r=i,n=a),{width:r,height:n,$:l}}function eK(e){return eN(e)?e:e.contextElement}function eX(e){let t=eK(e);if(!eR(t))return Y(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:i}=eV(t),a=(i?X(r.width):r.width)/n,l=(i?X(r.height):r.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let eU=Y(0);function eY(e){let t=ej(e);return eW()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eU}function eZ(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),a=eK(e),l=Y(1);t&&(n?eN(n)&&(l=eX(n)):l=eX(e));let s=(void 0===(o=r)&&(o=!1),n&&(!o||n===ej(a))&&o)?eY(a):Y(0),c=(i.left+s.x)/l.x,u=(i.top+s.y)/l.y,d=i.width/l.x,f=i.height/l.y;if(a){let e=ej(a),t=n&&eN(n)?ej(n):n,r=e,o=eG(r);for(;o&&n&&t!==r;){let e=eX(o),t=o.getBoundingClientRect(),n=eB(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,u*=e.y,d*=e.x,f*=e.y,c+=i,u+=a,o=eG(r=ej(o))}}return ep({width:d,height:f,x:c,y:u})}function eJ(e,t){let r=eq(e).scrollLeft;return t?t.left+r:eZ(eS(e)).left+r}function eQ(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:eJ(e,n)),y:n.top+t.scrollTop}}let e0=new Set(["absolute","fixed"]);function e1(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=ej(e),n=eS(e),o=r.visualViewport,i=n.clientWidth,a=n.clientHeight,l=0,s=0;if(o){i=o.width,a=o.height;let e=eW();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:i,height:a,x:l,y:s}}(e,r);else if("document"===t)n=function(e){let t=eS(e),r=eq(e),n=e.ownerDocument.body,o=K(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=K(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),a=-r.scrollLeft+eJ(e),l=-r.scrollTop;return"rtl"===eB(n).direction&&(a+=K(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:a,y:l}}(eS(e));else if(eN(t))n=function(e,t){let r=eZ(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=eR(e)?eX(e):Y(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:n*i.y}}(t,r);else{let r=eY(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return ep(n)}function e2(e){return"static"===eB(e).position}function e4(e,t){if(!eR(e)||"fixed"===eB(e).position)return null;if(t)return t(e);let r=e.offsetParent;return eS(e)===r&&(r=r.ownerDocument.body),r}function e3(e,t){var r;let n=ej(e);if(eM(e))return n;if(!eR(e)){let t=ez(e);for(;t&&!eH(t);){if(eN(t)&&!e2(t))return t;t=ez(t)}return n}let o=e4(e,t);for(;o&&(r=o,eP.has(eE(r)))&&e2(o);)o=e4(o,t);return o&&eH(o)&&e2(o)&&!eF(o)?n:o||function(e){let t=ez(e);for(;eR(t)&&!eH(t);){if(eF(t))return t;if(eM(t))break;t=ez(t)}return null}(e)||n}let e6=async function(e){let t=this.getOffsetParent||e3,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=eR(t),o=eS(t),i="fixed"===r,a=eZ(e,!0,i,t),l={scrollLeft:0,scrollTop:0},s=Y(0);if(n||!n&&!i)if(("body"!==eE(t)||ek(o))&&(l=eq(t)),n){let e=eZ(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eJ(o));i&&!n&&o&&(s.x=eJ(o));let c=!o||n||i?Y(0):eQ(o,l);return{x:a.left+l.scrollLeft-s.x-c.x,y:a.top+l.scrollTop-s.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},e8={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,a=eS(n),l=!!t&&eM(t.floating);if(n===a||l&&i)return r;let s={scrollLeft:0,scrollTop:0},c=Y(1),u=Y(0),d=eR(n);if((d||!d&&!i)&&(("body"!==eE(n)||ek(a))&&(s=eq(n)),eR(n))){let e=eZ(n);c=eX(n),u.x=e.x+n.clientLeft,u.y=e.y+n.clientTop}let f=!a||d||i?Y(0):eQ(a,s,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-s.scrollLeft*c.x+u.x+f.x,y:r.y*c.y-s.scrollTop*c.y+u.y+f.y}},getDocumentElement:eS,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,i=[..."clippingAncestors"===r?eM(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=e$(e,[],!1).filter(e=>eN(e)&&"body"!==eE(e)),o=null,i="fixed"===eB(e).position,a=i?ez(e):e;for(;eN(a)&&!eH(a);){let t=eB(a),r=eF(a);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&e0.has(o.position)||ek(a)&&!r&&function e(t,r){let n=ez(t);return!(n===r||!eN(n)||eH(n))&&("fixed"===eB(n).position||e(n,r))}(e,a))?n=n.filter(e=>e!==a):o=t,a=ez(a)}return t.set(e,n),n}(t,this._c):[].concat(r),n],a=i[0],l=i.reduce((e,r)=>{let n=e1(t,r,o);return e.top=K(n.top,e.top),e.right=V(n.right,e.right),e.bottom=V(n.bottom,e.bottom),e.left=K(n.left,e.left),e},e1(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:e3,getElementRects:e6,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=eV(e);return{width:t,height:r}},getScale:eX,isElement:eN,isRTL:function(e){return"rtl"===eB(e).direction}};function e5(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e9=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:i,platform:a,elements:l,middlewareData:s}=t,{element:c,padding:u=0}=Q(e,t)||{};if(null==c)return{};let d=ef(u),f={x:r,y:n},p=er(ei(o)),m=en(p),h=await a.getDimensions(c),v="y"===p,g=v?"clientHeight":"clientWidth",x=i.reference[m]+i.reference[p]-f[p]-i.floating[m],y=f[p]-i.reference[p],w=await (null==a.getOffsetParent?void 0:a.getOffsetParent(c)),b=w?w[g]:0;b&&await (null==a.isElement?void 0:a.isElement(w))||(b=l.floating[g]||i.floating[m]);let E=b/2-h[m]/2-1,j=V(d[v?"top":"left"],E),S=V(d[v?"bottom":"right"],E),C=b-h[m]-S,N=b/2-h[m]/2+(x/2-y/2),R=K(j,V(N,C)),A=!s.arrow&&null!=et(o)&&N!==R&&i.reference[m]/2-(N<j?j:S)-h[m]/2<0,T=A?N<j?N-j:N-C:0;return{[p]:f[p]+T,data:{[p]:R,centerOffset:N-R-T,...A&&{alignmentOffset:T}},reset:A}}}),e7=(e,t,r)=>{let n=new Map,o={platform:e8,...r},i={...o.platform,_c:n};return eh(e,t,{...o,platform:i})};var te="undefined"!=typeof document?l.useLayoutEffect:function(){};function tt(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!tt(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!tt(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function tr(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function tn(e,t){let r=tr(e);return Math.round(t*r)/r}function to(e){let t=l.useRef(e);return te(()=>{t.current=e}),t}let ti=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?e9({element:r.current,padding:n}).fn(t):{}:r?e9({element:r,padding:n}).fn(t):{}}}),ta=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:a,middlewareData:l}=t,s=await ew(t,e);return a===(null==(r=l.offset)?void 0:r.placement)&&null!=(n=l.arrow)&&n.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:a}}}}}(e),options:[e,t]}),tl=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...s}=Q(e,t),c={x:r,y:n},u=await ev(t,s),d=ei(ee(o)),f=er(d),p=c[f],m=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+u[e],n=p-u[t];p=K(r,V(p,n))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",r=m+u[e],n=m-u[t];m=K(r,V(m,n))}let h=l.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-r,y:h.y-n,enabled:{[f]:i,[d]:a}}}}}}(e),options:[e,t]}),ts=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:s=!0,crossAxis:c=!0}=Q(e,t),u={x:r,y:n},d=ei(o),f=er(d),p=u[f],m=u[d],h=Q(l,t),v="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(s){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,r=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>r&&(p=r)}if(c){var g,x;let e="y"===f?"width":"height",t=ey.has(ee(o)),r=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),n=i.reference[d]+i.reference[e]+(t?0:(null==(x=a.offset)?void 0:x[d])||0)-(t?v.crossAxis:0);m<r?m=r:m>n&&(m=n)}return{[f]:p,[d]:m}}}}(e),options:[e,t]}),tc=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,a;let{placement:l,middlewareData:s,rects:c,initialPlacement:u,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:x=!0,...y}=Q(e,t);if(null!=(r=s.arrow)&&r.alignmentOffset)return{};let w=ee(l),b=ei(u),E=ee(u)===u,j=await (null==d.isRTL?void 0:d.isRTL(f.floating)),S=h||(E||!x?[ed(u)]:function(e){let t=ed(e);return[ea(e),t,ea(t)]}(u)),C="none"!==g;!h&&C&&S.push(...function(e,t,r,n){let o=et(e),i=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?es:el;return t?el:es;case"left":case"right":return t?ec:eu;default:return[]}}(ee(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(ea)))),i}(u,x,g,j));let N=[u,...S],R=await ev(t,y),A=[],T=(null==(n=s.flip)?void 0:n.overflows)||[];if(p&&A.push(R[w]),m){let e=function(e,t,r){void 0===r&&(r=!1);let n=et(e),o=er(ei(e)),i=en(o),a="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=ed(a)),[a,ed(a)]}(l,c,j);A.push(R[e[0]],R[e[1]])}if(T=[...T,{placement:l,overflows:A}],!A.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=N[e];if(t&&("alignment"!==m||b===ei(t)||T.every(e=>e.overflows[0]>0&&ei(e.placement)===b)))return{data:{index:e,overflows:T},reset:{placement:t}};let r=null==(i=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(v){case"bestFit":{let e=null==(a=T.filter(e=>{if(C){let t=ei(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(r=e);break}case"initialPlacement":r=u}if(l!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),tu=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,i,{placement:a,rects:l,platform:s,elements:c}=t,{apply:u=()=>{},...d}=Q(e,t),f=await ev(t,d),p=ee(a),m=et(a),h="y"===ei(a),{width:v,height:g}=l.floating;"top"===p||"bottom"===p?(o=p,i=m===(await (null==s.isRTL?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===m?"top":"bottom");let x=g-f.top-f.bottom,y=v-f.left-f.right,w=V(g-f[o],x),b=V(v-f[i],y),E=!t.middlewareData.shift,j=w,S=b;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(S=y),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(j=x),E&&!m){let e=K(f.left,0),t=K(f.right,0),r=K(f.top,0),n=K(f.bottom,0);h?S=v-2*(0!==e||0!==t?e+t:K(f.left,f.right)):j=g-2*(0!==r||0!==n?r+n:K(f.top,f.bottom))}await u({...t,availableWidth:S,availableHeight:j});let C=await s.getDimensions(c.floating);return v!==C.width||g!==C.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),td=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=Q(e,t);switch(n){case"referenceHidden":{let e=eg(await ev(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ex(e)}}}case"escaped":{let e=eg(await ev(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:ex(e)}}}default:return{}}}}}(e),options:[e,t]}),tf=(e,t)=>({...ti(e),options:[e,t]});var tp=l.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,a.jsx)(j.sG.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,a.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tp.displayName="Arrow";var tm="Popper",[th,tv]=v(tm),[tg,tx]=th(tm),ty=e=>{let{__scopePopper:t,children:r}=e,[n,o]=l.useState(null);return(0,a.jsx)(tg,{scope:t,anchor:n,onAnchorChange:o,children:r})};ty.displayName=tm;var tw="PopperAnchor",tb=l.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...o}=e,i=tx(tw,r),s=l.useRef(null),c=(0,g.s)(t,s);return l.useEffect(()=>{i.onAnchorChange(n?.current||s.current)}),n?null:(0,a.jsx)(j.sG.div,{...o,ref:c})});tb.displayName=tw;var tE="PopperContent",[tj,tS]=th(tE),tC=l.forwardRef((e,t)=>{let{__scopePopper:r,side:n="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:c=0,avoidCollisions:u=!0,collisionBoundary:d=[],collisionPadding:f=0,sticky:m="partial",hideWhenDetached:h=!1,updatePositionStrategy:v="optimized",onPlaced:x,...y}=e,w=tx(tE,r),[b,E]=l.useState(null),C=(0,g.s)(t,e=>E(e)),[N,R]=l.useState(null),A=function(e){let[t,r]=l.useState(void 0);return B(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(N),T=A?.width??0,k=A?.height??0,P="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},L=Array.isArray(d)?d:[d],M=L.length>0,D={padding:P,boundary:L.filter(tT),altBoundary:M},{refs:O,floatingStyles:I,placement:F,isPositioned:W,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:o,elements:{reference:i,floating:a}={},transform:s=!0,whileElementsMounted:c,open:u}=e,[d,f]=l.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[m,h]=l.useState(n);tt(m,n)||h(n);let[v,g]=l.useState(null),[x,y]=l.useState(null),w=l.useCallback(e=>{e!==S.current&&(S.current=e,g(e))},[]),b=l.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),E=i||v,j=a||x,S=l.useRef(null),C=l.useRef(null),N=l.useRef(d),R=null!=c,A=to(c),T=to(o),k=to(u),P=l.useCallback(()=>{if(!S.current||!C.current)return;let e={placement:t,strategy:r,middleware:m};T.current&&(e.platform=T.current),e7(S.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==k.current};L.current&&!tt(N.current,t)&&(N.current=t,p.flushSync(()=>{f(t)}))})},[m,t,r,T,k]);te(()=>{!1===u&&N.current.isPositioned&&(N.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[u]);let L=l.useRef(!1);te(()=>(L.current=!0,()=>{L.current=!1}),[]),te(()=>{if(E&&(S.current=E),j&&(C.current=j),E&&j){if(A.current)return A.current(E,j,P);P()}},[E,j,P,A,R]);let M=l.useMemo(()=>({reference:S,floating:C,setReference:w,setFloating:b}),[w,b]),D=l.useMemo(()=>({reference:E,floating:j}),[E,j]),O=l.useMemo(()=>{let e={position:r,left:0,top:0};if(!D.floating)return e;let t=tn(D.floating,d.x),n=tn(D.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+n+"px)",...tr(D.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,s,D.floating,d.x,d.y]);return l.useMemo(()=>({...d,update:P,refs:M,elements:D,floatingStyles:O}),[d,P,M,D,O])}({strategy:"fixed",placement:n+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,u=eK(e),d=i||a?[...u?e$(u):[],...e$(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",r,{passive:!0}),a&&e.addEventListener("resize",r)});let f=u&&s?function(e,t){let r,n=null,o=eS(e);function i(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function a(l,s){void 0===l&&(l=!1),void 0===s&&(s=1),i();let c=e.getBoundingClientRect(),{left:u,top:d,width:f,height:p}=c;if(l||t(),!f||!p)return;let m=U(d),h=U(o.clientWidth-(u+f)),v={rootMargin:-m+"px "+-h+"px "+-U(o.clientHeight-(d+p))+"px "+-U(u)+"px",threshold:K(0,V(1,s))||1},g=!0;function x(t){let n=t[0].intersectionRatio;if(n!==s){if(!g)return a();n?a(!1,n):r=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==n||e5(c,e.getBoundingClientRect())||a(),g=!1}try{n=new IntersectionObserver(x,{...v,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(x,v)}n.observe(e)}(!0),i}(u,r):null,p=-1,m=null;l&&(m=new ResizeObserver(e=>{let[n]=e;n&&n.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=m)||e.observe(t)})),r()}),u&&!c&&m.observe(u),m.observe(t));let h=c?eZ(e):null;return c&&function t(){let n=eZ(e);h&&!e5(h,n)&&r(),h=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",r),a&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=m)||e.disconnect(),m=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===v}),elements:{reference:w.anchor},middleware:[ta({mainAxis:o+k,alignmentAxis:s}),u&&tl({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ts():void 0,...D}),u&&tc({...D}),tu({...D,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${r}px`),a.setProperty("--radix-popper-available-height",`${n}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),N&&tf({element:N,padding:c}),tk({arrowWidth:T,arrowHeight:k}),h&&td({strategy:"referenceHidden",...D})]}),[H,q]=tP(F),z=S(x);B(()=>{W&&z?.()},[W,z]);let $=_.arrow?.x,G=_.arrow?.y,X=_.arrow?.centerOffset!==0,[Y,Z]=l.useState();return B(()=>{b&&Z(window.getComputedStyle(b).zIndex)},[b]),(0,a.jsx)("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:W?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Y,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,a.jsx)(tj,{scope:r,placedSide:H,onArrowChange:R,arrowX:$,arrowY:G,shouldHideArrow:X,children:(0,a.jsx)(j.sG.div,{"data-side":H,"data-align":q,...y,ref:C,style:{...y.style,animation:W?void 0:"none"}})})})});tC.displayName=tE;var tN="PopperArrow",tR={top:"bottom",right:"left",bottom:"top",left:"right"},tA=l.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=tS(tN,r),i=tR[o.placedSide];return(0,a.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,a.jsx)(tp,{...n,ref:t,style:{...n.style,display:"block"}})})});function tT(e){return null!==e}tA.displayName=tN;var tk=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[s,c]=tP(r),u={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",m="";return"bottom"===s?(p=i?u:`${d}px`,m=`${-l}px`):"top"===s?(p=i?u:`${d}px`,m=`${n.floating.height+l}px`):"right"===s?(p=`${-l}px`,m=i?u:`${f}px`):"left"===s&&(p=`${n.floating.width+l}px`,m=i?u:`${f}px`),{data:{x:p,y:m}}}});function tP(e){let[t,r="center"]=e.split("-");return[t,r]}var tL=l.forwardRef((e,t)=>{let{container:r,...n}=e,[o,i]=l.useState(!1);B(()=>i(!0),[]);let s=r||o&&globalThis?.document?.body;return s?p.createPortal((0,a.jsx)(j.sG.div,{...n,ref:t}),s):null});tL.displayName="Portal";var tM=s[" useInsertionEffect ".trim().toString()]||B;function tD({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[o,i,a]=function({defaultProp:e,onChange:t}){let[r,n]=l.useState(e),o=l.useRef(r),i=l.useRef(t);return tM(()=>{i.current=t},[t]),l.useEffect(()=>{o.current!==r&&(i.current?.(r),o.current=r)},[r,o]),[r,n,i]}({defaultProp:t,onChange:r}),s=void 0!==e,c=s?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,n])}return[c,l.useCallback(t=>{if(s){let r="function"==typeof t?t(e):t;r!==e&&a.current?.(r)}else i(t)},[s,e,i,a])]}Symbol("RADIX:SYNC_STATE");var tO=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});l.forwardRef((e,t)=>(0,a.jsx)(j.sG.span,{...e,ref:t,style:{...tO,...e.style}})).displayName="VisuallyHidden";var tI=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tF=new WeakMap,tW=new WeakMap,t_={},tH=0,tB=function(e){return e&&(e.host||tB(e.parentNode))},tq=function(e,t,r,n){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=tB(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});t_[r]||(t_[r]=new WeakMap);var i=t_[r],a=[],l=new Set,s=new Set(o),c=function(e){!e||l.has(e)||(l.add(e),c(e.parentNode))};o.forEach(c);var u=function(e){!e||s.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))u(e);else try{var t=e.getAttribute(n),o=null!==t&&"false"!==t,s=(tF.get(e)||0)+1,c=(i.get(e)||0)+1;tF.set(e,s),i.set(e,c),a.push(e),1===s&&o&&tW.set(e,!0),1===c&&e.setAttribute(r,"true"),o||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return u(t),l.clear(),tH++,function(){a.forEach(function(e){var t=tF.get(e)-1,o=i.get(e)-1;tF.set(e,t),i.set(e,o),t||(tW.has(e)||e.removeAttribute(n),tW.delete(e)),o||e.removeAttribute(r)}),--tH||(tF=new WeakMap,tF=new WeakMap,tW=new WeakMap,t_={})}},tz=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=t||tI(e);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live], script"))),tq(n,o,r,"aria-hidden")):function(){return null}},t$=r(4363),tG="right-scroll-bar-position",tV="width-before-scroll-bar";function tK(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tX="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,tU=new WeakMap;function tY(e){return e}var tZ=function(e){void 0===e&&(e={});var t,r,n,o,i=(t=null,void 0===r&&(r=tY),n=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,o);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){o=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var i=function(){var r=t;t=[],r.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}});return i.options=(0,t$.Cl)({async:!0,ssr:!1},e),i}(),tJ=function(){},tQ=l.forwardRef(function(e,t){var r,n,o,i,a=l.useRef(null),s=l.useState({onScrollCapture:tJ,onWheelCapture:tJ,onTouchMoveCapture:tJ}),c=s[0],u=s[1],d=e.forwardProps,f=e.children,p=e.className,m=e.removeScrollBar,h=e.enabled,v=e.shards,g=e.sideCar,x=e.noRelative,y=e.noIsolation,w=e.inert,b=e.allowPinchZoom,E=e.as,j=e.gapMode,S=(0,t$.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=(r=[a,t],n=function(e){return r.forEach(function(t){return tK(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,i=o.facade,tX(function(){var e=tU.get(i);if(e){var t=new Set(e),n=new Set(r),o=i.current;t.forEach(function(e){n.has(e)||tK(e,null)}),n.forEach(function(e){t.has(e)||tK(e,o)})}tU.set(i,r)},[r]),i),N=(0,t$.Cl)((0,t$.Cl)({},S),c);return l.createElement(l.Fragment,null,h&&l.createElement(g,{sideCar:tZ,removeScrollBar:m,shards:v,noRelative:x,noIsolation:y,inert:w,setCallbacks:u,allowPinchZoom:!!b,lockRef:a,gapMode:j}),d?l.cloneElement(l.Children.only(f),(0,t$.Cl)((0,t$.Cl)({},N),{ref:C})):l.createElement(void 0===E?"div":E,(0,t$.Cl)({},N,{className:p,ref:C}),f))});tQ.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tQ.classNames={fullWidth:tV,zeroRight:tG};var t0=function(e){var t=e.sideCar,r=(0,t$.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return l.createElement(n,(0,t$.Cl)({},r))};t0.isSideCarExport=!0;var t1=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||r.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=n:o.appendChild(document.createTextNode(n)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},t2=function(){var e=t1();return function(t,r){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},t4=function(){var e=t2();return function(t){return e(t.styles,t.dynamic),null}},t3={left:0,top:0,right:0,gap:0},t6=function(e){return parseInt(e||"",10)||0},t8=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[t6(r),t6(n),t6(o)]},t5=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t3;var t=t8(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},t9=t4(),t7="data-scroll-locked",re=function(e,t,r,n){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(l,"px ").concat(n,";\n  }\n  body[").concat(t7,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(l,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tG," {\n    right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(tV," {\n    margin-right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(tG," .").concat(tG," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(tV," .").concat(tV," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(t7,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},rt=function(){var e=parseInt(document.body.getAttribute(t7)||"0",10);return isFinite(e)?e:0},rr=function(){l.useEffect(function(){return document.body.setAttribute(t7,(rt()+1).toString()),function(){var e=rt()-1;e<=0?document.body.removeAttribute(t7):document.body.setAttribute(t7,e.toString())}},[])},rn=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;rr();var i=l.useMemo(function(){return t5(o)},[o]);return l.createElement(t9,{styles:re(i,!t,o,r?"":"!important")})},ro=!1;if("undefined"!=typeof window)try{var ri=Object.defineProperty({},"passive",{get:function(){return ro=!0,!0}});window.addEventListener("test",ri,ri),window.removeEventListener("test",ri,ri)}catch(e){ro=!1}var ra=!!ro&&{passive:!1},rl=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},rs=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),rc(e,n)){var o=ru(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},rc=function(e,t){return"v"===e?rl(t,"overflowY"):rl(t,"overflowX")},ru=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},rd=function(e,t,r,n,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*n,s=r.target,c=t.contains(s),u=!1,d=l>0,f=0,p=0;do{if(!s)break;var m=ru(e,s),h=m[0],v=m[1]-m[2]-a*h;(h||v)&&rc(e,s)&&(f+=v,p+=h);var g=s.parentNode;s=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&l>f)?u=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(u=!0),u},rf=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},rp=function(e){return[e.deltaX,e.deltaY]},rm=function(e){return e&&"current"in e?e.current:e},rh=0,rv=[];let rg=(n=function(e){var t=l.useRef([]),r=l.useRef([0,0]),n=l.useRef(),o=l.useState(rh++)[0],i=l.useState(t4)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,t$.fX)([e.lockRef.current],(e.shards||[]).map(rm),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=rf(e),l=r.current,s="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],u=e.target,d=Math.abs(s)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=rs(d,u);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=rs(d,u)),!f)return!1;if(!n.current&&"changedTouches"in e&&(s||c)&&(n.current=o),!o)return!0;var p=n.current||o;return rd(p,t,e,"h"===p?s:c,!0)},[]),c=l.useCallback(function(e){if(rv.length&&rv[rv.length-1]===i){var r="deltaY"in e?rp(e):rf(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(a.current.shards||[]).map(rm).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),u=l.useCallback(function(e,r,n,o){var i={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){r.current=rf(e),n.current=void 0},[]),f=l.useCallback(function(t){u(t.type,rp(t),t.target,s(t,e.lockRef.current))},[]),p=l.useCallback(function(t){u(t.type,rf(t),t.target,s(t,e.lockRef.current))},[]);l.useEffect(function(){return rv.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,ra),document.addEventListener("touchmove",c,ra),document.addEventListener("touchstart",d,ra),function(){rv=rv.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,ra),document.removeEventListener("touchmove",c,ra),document.removeEventListener("touchstart",d,ra)}},[]);var m=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?l.createElement(rn,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tZ.useMedium(n),t0);var rx=l.forwardRef(function(e,t){return l.createElement(tQ,(0,t$.Cl)({},e,{ref:t,sideCar:rg}))});rx.classNames=tQ.classNames;var ry=[" ","Enter","ArrowUp","ArrowDown"],rw=[" ","Enter"],rb="Select",[rE,rj,rS]=function(e){let t=e+"CollectionProvider",[r,n]=v(t),[o,i]=r(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:r}=e,n=l.useRef(null),i=l.useRef(new Map).current;return(0,a.jsx)(o,{scope:t,itemMap:i,collectionRef:n,children:r})};s.displayName=t;let c=e+"CollectionSlot",u=(0,x.TL)(c),d=l.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=i(c,r),l=(0,g.s)(t,o.collectionRef);return(0,a.jsx)(u,{ref:l,children:n})});d.displayName=c;let f=e+"CollectionItemSlot",p="data-radix-collection-item",m=(0,x.TL)(f),h=l.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,s=l.useRef(null),c=(0,g.s)(t,s),u=i(f,r);return l.useEffect(()=>(u.itemMap.set(s,{ref:s,...o}),()=>void u.itemMap.delete(s))),(0,a.jsx)(m,{...{[p]:""},ref:c,children:n})});return h.displayName=f,[{Provider:s,Slot:d,ItemSlot:h},function(t){let r=i(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${p}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}(rb),[rC,rN]=v(rb,[rS,tv]),rR=tv(),[rA,rT]=rC(rb),[rk,rP]=rC(rb),rL=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:o,onOpenChange:i,value:s,defaultValue:c,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:m,required:h,form:v}=e,g=rR(t),[x,y]=l.useState(null),[w,b]=l.useState(null),[j,S]=l.useState(!1),C=function(e){let t=l.useContext(E);return e||t||"ltr"}(d),[N,R]=tD({prop:n,defaultProp:o??!1,onChange:i,caller:rb}),[A,T]=tD({prop:s,defaultProp:c,onChange:u,caller:rb}),k=l.useRef(null),P=!x||v||!!x.closest("form"),[L,M]=l.useState(new Set),D=Array.from(L).map(e=>e.props.value).join(";");return(0,a.jsx)(ty,{...g,children:(0,a.jsxs)(rA,{required:h,scope:t,trigger:x,onTriggerChange:y,valueNode:w,onValueNodeChange:b,valueNodeHasChildren:j,onValueNodeHasChildrenChange:S,contentId:$(),value:A,onValueChange:T,open:N,onOpenChange:R,dir:C,triggerPointerDownPosRef:k,disabled:m,children:[(0,a.jsx)(rE.Provider,{scope:t,children:(0,a.jsx)(rk,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{M(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{M(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),P?(0,a.jsxs)(nl,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:p,value:A,onChange:e=>T(e.target.value),disabled:m,form:v,children:[void 0===A?(0,a.jsx)("option",{value:""}):null,Array.from(L)]},D):null]})})};rL.displayName=rb;var rM="SelectTrigger",rD=l.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...o}=e,i=rR(r),s=rT(rM,r),c=s.disabled||n,u=(0,g.s)(t,s.onTriggerChange),d=rj(r),f=l.useRef("touch"),[p,m,v]=nc(e=>{let t=d().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=nu(t,e,r);void 0!==n&&s.onValueChange(n.value)}),x=e=>{c||(s.onOpenChange(!0),v()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,a.jsx)(tb,{asChild:!0,...i,children:(0,a.jsx)(j.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":ns(s.value)?"":void 0,...o,ref:u,onClick:h(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:h(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:h(o.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&ry.includes(e.key)&&(x(),e.preventDefault())})})})});rD.displayName=rM;var rO="SelectValue",rI=l.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:i,placeholder:l="",...s}=e,c=rT(rO,r),{onValueNodeHasChildrenChange:u}=c,d=void 0!==i,f=(0,g.s)(t,c.onValueNodeChange);return B(()=>{u(d)},[u,d]),(0,a.jsx)(j.sG.span,{...s,ref:f,style:{pointerEvents:"none"},children:ns(c.value)?(0,a.jsx)(a.Fragment,{children:l}):i})});rI.displayName=rO;var rF=l.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,a.jsx)(j.sG.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});rF.displayName="SelectIcon";var rW=e=>(0,a.jsx)(tL,{asChild:!0,...e});rW.displayName="SelectPortal";var r_="SelectContent",rH=l.forwardRef((e,t)=>{let r=rT(r_,e.__scopeSelect),[n,o]=l.useState();return(B(()=>{o(new DocumentFragment)},[]),r.open)?(0,a.jsx)(r$,{...e,ref:t}):n?p.createPortal((0,a.jsx)(rB,{scope:e.__scopeSelect,children:(0,a.jsx)(rE.Slot,{scope:e.__scopeSelect,children:(0,a.jsx)("div",{children:e.children})})}),n):null});rH.displayName=r_;var[rB,rq]=rC(r_),rz=(0,x.TL)("SelectContent.RemoveScroll"),r$=l.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:s,side:c,sideOffset:u,align:d,alignOffset:f,arrowPadding:p,collisionBoundary:m,collisionPadding:v,sticky:x,hideWhenDetached:y,avoidCollisions:w,...b}=e,E=rT(r_,r),[j,S]=l.useState(null),[C,N]=l.useState(null),A=(0,g.s)(t,e=>S(e)),[T,L]=l.useState(null),[M,D]=l.useState(null),I=rj(r),[F,W]=l.useState(!1),_=l.useRef(!1);l.useEffect(()=>{if(j)return tz(j)},[j]),l.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??P()),document.body.insertAdjacentElement("beforeend",e[1]??P()),k++,()=>{1===k&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),k--}},[]);let H=l.useCallback(e=>{let[t,...r]=I().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&C&&(C.scrollTop=0),r===n&&C&&(C.scrollTop=C.scrollHeight),r?.focus(),document.activeElement!==o))return},[I,C]),B=l.useCallback(()=>H([T,j]),[H,T,j]);l.useEffect(()=>{F&&B()},[F,B]);let{onOpenChange:q,triggerPointerDownPosRef:z}=E;l.useEffect(()=>{if(j){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(z.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():j.contains(r.target)||q(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[j,q,z]),l.useEffect(()=>{let e=()=>q(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[q]);let[$,G]=nc(e=>{let t=I().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=nu(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),V=l.useCallback((e,t,r)=>{let n=!_.current&&!r;(void 0!==E.value&&E.value===t||n)&&(L(e),n&&(_.current=!0))},[E.value]),K=l.useCallback(()=>j?.focus(),[j]),X=l.useCallback((e,t,r)=>{let n=!_.current&&!r;(void 0!==E.value&&E.value===t||n)&&D(e)},[E.value]),U="popper"===n?rV:rG,Y=U===rV?{side:c,sideOffset:u,align:d,alignOffset:f,arrowPadding:p,collisionBoundary:m,collisionPadding:v,sticky:x,hideWhenDetached:y,avoidCollisions:w}:{};return(0,a.jsx)(rB,{scope:r,content:j,viewport:C,onViewportChange:N,itemRefCallback:V,selectedItem:T,onItemLeave:K,itemTextRefCallback:X,focusSelectedItem:B,selectedItemText:M,position:n,isPositioned:F,searchRef:$,children:(0,a.jsx)(rx,{as:rz,allowPinchZoom:!0,children:(0,a.jsx)(O,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:h(o,e=>{E.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,a.jsx)(R,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,a.jsx)(U,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...b,...Y,onPlaced:()=>W(!0),ref:A,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:h(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=I().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>H(t)),e.preventDefault()}})})})})})})});r$.displayName="SelectContentImpl";var rG=l.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...o}=e,i=rT(r_,r),s=rq(r_,r),[c,u]=l.useState(null),[d,f]=l.useState(null),p=(0,g.s)(t,e=>f(e)),h=rj(r),v=l.useRef(!1),x=l.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:b,focusSelectedItem:E}=s,S=l.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&d&&y&&w&&b){let e=i.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),o=b.getBoundingClientRect();if("rtl"!==i.dir){let n=o.left-t.left,i=r.left-n,a=e.left-i,l=e.width+a,s=Math.max(l,t.width),u=m(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.left=u+"px"}else{let n=t.right-o.right,i=window.innerWidth-r.right-n,a=window.innerWidth-e.right-i,l=e.width+a,s=Math.max(l,t.width),u=m(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.right=u+"px"}let a=h(),l=window.innerHeight-20,s=y.scrollHeight,u=window.getComputedStyle(d),f=parseInt(u.borderTopWidth,10),p=parseInt(u.paddingTop,10),g=parseInt(u.borderBottomWidth,10),x=f+p+s+parseInt(u.paddingBottom,10)+g,E=Math.min(5*w.offsetHeight,x),j=window.getComputedStyle(y),S=parseInt(j.paddingTop,10),C=parseInt(j.paddingBottom,10),N=e.top+e.height/2-10,R=w.offsetHeight/2,A=f+p+(w.offsetTop+R);if(A<=N){let e=a.length>0&&w===a[a.length-1].ref.current;c.style.bottom="0px";let t=Math.max(l-N,R+(e?C:0)+(d.clientHeight-y.offsetTop-y.offsetHeight)+g);c.style.height=A+t+"px"}else{let e=a.length>0&&w===a[0].ref.current;c.style.top="0px";let t=Math.max(N,f+y.offsetTop+(e?S:0)+R);c.style.height=t+(x-A)+"px",y.scrollTop=A-N+y.offsetTop}c.style.margin="10px 0",c.style.minHeight=E+"px",c.style.maxHeight=l+"px",n?.(),requestAnimationFrame(()=>v.current=!0)}},[h,i.trigger,i.valueNode,c,d,y,w,b,i.dir,n]);B(()=>S(),[S]);let[C,N]=l.useState();B(()=>{d&&N(window.getComputedStyle(d).zIndex)},[d]);let R=l.useCallback(e=>{e&&!0===x.current&&(S(),E?.(),x.current=!1)},[S,E]);return(0,a.jsx)(rK,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:R,children:(0,a.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,a.jsx)(j.sG.div,{...o,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});rG.displayName="SelectItemAlignedPosition";var rV=l.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...i}=e,l=rR(r);return(0,a.jsx)(tC,{...l,...i,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});rV.displayName="SelectPopperPosition";var[rK,rX]=rC(r_,{}),rU="SelectViewport",rY=l.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...o}=e,i=rq(rU,r),s=rX(rU,r),c=(0,g.s)(t,i.onViewportChange),u=l.useRef(0);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,a.jsx)(rE.Slot,{scope:r,children:(0,a.jsx)(j.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:h(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if(n?.current&&r){let e=Math.abs(u.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let i=o+e,a=Math.min(n,i),l=i-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=l>0?l:0,r.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});rY.displayName=rU;var rZ="SelectGroup",[rJ,rQ]=rC(rZ);l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=$();return(0,a.jsx)(rJ,{scope:r,id:o,children:(0,a.jsx)(j.sG.div,{role:"group","aria-labelledby":o,...n,ref:t})})}).displayName=rZ;var r0="SelectLabel",r1=l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=rQ(r0,r);return(0,a.jsx)(j.sG.div,{id:o.id,...n,ref:t})});r1.displayName=r0;var r2="SelectItem",[r4,r3]=rC(r2),r6=l.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:o=!1,textValue:i,...s}=e,c=rT(r2,r),u=rq(r2,r),d=c.value===n,[f,p]=l.useState(i??""),[m,v]=l.useState(!1),x=(0,g.s)(t,e=>u.itemRefCallback?.(e,n,o)),y=$(),w=l.useRef("touch"),b=()=>{o||(c.onValueChange(n),c.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,a.jsx)(r4,{scope:r,value:n,disabled:o,textId:y,isSelected:d,onItemTextChange:l.useCallback(e=>{p(t=>t||(e?.textContent??"").trim())},[]),children:(0,a.jsx)(rE.ItemSlot,{scope:r,value:n,disabled:o,textValue:f,children:(0,a.jsx)(j.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":d&&m,"data-state":d?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:x,onFocus:h(s.onFocus,()=>v(!0)),onBlur:h(s.onBlur,()=>v(!1)),onClick:h(s.onClick,()=>{"mouse"!==w.current&&b()}),onPointerUp:h(s.onPointerUp,()=>{"mouse"===w.current&&b()}),onPointerDown:h(s.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:h(s.onPointerMove,e=>{w.current=e.pointerType,o?u.onItemLeave?.():"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:h(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:h(s.onKeyDown,e=>{(u.searchRef?.current===""||" "!==e.key)&&(rw.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});r6.displayName=r2;var r8="SelectItemText",r5=l.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,...i}=e,s=rT(r8,r),c=rq(r8,r),u=r3(r8,r),d=rP(r8,r),[f,m]=l.useState(null),h=(0,g.s)(t,e=>m(e),u.onItemTextChange,e=>c.itemTextRefCallback?.(e,u.value,u.disabled)),v=f?.textContent,x=l.useMemo(()=>(0,a.jsx)("option",{value:u.value,disabled:u.disabled,children:v},u.value),[u.disabled,u.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=d;return B(()=>(y(x),()=>w(x)),[y,w,x]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.sG.span,{id:u.textId,...i,ref:h}),u.isSelected&&s.valueNode&&!s.valueNodeHasChildren?p.createPortal(i.children,s.valueNode):null]})});r5.displayName=r8;var r9="SelectItemIndicator",r7=l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return r3(r9,r).isSelected?(0,a.jsx)(j.sG.span,{"aria-hidden":!0,...n,ref:t}):null});r7.displayName=r9;var ne="SelectScrollUpButton",nt=l.forwardRef((e,t)=>{let r=rq(ne,e.__scopeSelect),n=rX(ne,e.__scopeSelect),[o,i]=l.useState(!1),s=(0,g.s)(t,n.onScrollButtonChange);return B(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,a.jsx)(no,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});nt.displayName=ne;var nr="SelectScrollDownButton",nn=l.forwardRef((e,t)=>{let r=rq(nr,e.__scopeSelect),n=rX(nr,e.__scopeSelect),[o,i]=l.useState(!1),s=(0,g.s)(t,n.onScrollButtonChange);return B(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,a.jsx)(no,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});nn.displayName=nr;var no=l.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...o}=e,i=rq("SelectScrollButton",r),s=l.useRef(null),c=rj(r),u=l.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return l.useEffect(()=>()=>u(),[u]),B(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,a.jsx)(j.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:h(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(n,50))}),onPointerMove:h(o.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(n,50))}),onPointerLeave:h(o.onPointerLeave,()=>{u()})})}),ni=l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,a.jsx)(j.sG.div,{"aria-hidden":!0,...n,ref:t})});ni.displayName="SelectSeparator";var na="SelectArrow";l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=rR(r),i=rT(na,r),l=rq(na,r);return i.open&&"popper"===l.position?(0,a.jsx)(tA,{...o,...n,ref:t}):null}).displayName=na;var nl=l.forwardRef(({__scopeSelect:e,value:t,...r},n)=>{let o=l.useRef(null),i=(0,g.s)(n,o),s=function(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return l.useEffect(()=>{let e=o.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[s,t]),(0,a.jsx)(j.sG.select,{...r,style:{...tO,...r.style},ref:i,defaultValue:t})});function ns(e){return""===e||void 0===e}function nc(e){let t=S(e),r=l.useRef(""),n=l.useRef(0),o=l.useCallback(e=>{let o=r.current+e;t(o),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=l.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,o,i]}function nu(e,t,r){var n,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,l=(n=e,o=Math.max(a,0),n.map((e,t)=>n[(o+t)%n.length]));1===i.length&&(l=l.filter(e=>e!==r));let s=l.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==r?s:void 0}nl.displayName="SelectBubbleInput";var nd=r(78272);let nf=(0,r(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var np=r(13964),nm=r(4780);let nh=l.forwardRef(({className:e,children:t,...r},n)=>(0,a.jsxs)(rD,{ref:n,className:(0,nm.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,(0,a.jsx)(rF,{asChild:!0,children:(0,a.jsx)(nd.A,{className:"h-4 w-4 opacity-50"})})]}));nh.displayName=rD.displayName;let nv=l.forwardRef(({className:e,...t},r)=>(0,a.jsx)(nt,{ref:r,className:(0,nm.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(nf,{className:"h-4 w-4"})}));nv.displayName=nt.displayName;let ng=l.forwardRef(({className:e,...t},r)=>(0,a.jsx)(nn,{ref:r,className:(0,nm.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(nd.A,{className:"h-4 w-4"})}));ng.displayName=nn.displayName;let nx=l.forwardRef(({className:e,children:t,position:r="popper",...n},o)=>(0,a.jsx)(rW,{children:(0,a.jsxs)(rH,{ref:o,className:(0,nm.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,a.jsx)(nv,{}),(0,a.jsx)(rY,{className:(0,nm.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(ng,{})]})}));nx.displayName=rH.displayName,l.forwardRef(({className:e,...t},r)=>(0,a.jsx)(r1,{ref:r,className:(0,nm.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=r1.displayName;let ny=l.forwardRef(({className:e,children:t,...r},n)=>(0,a.jsxs)(r6,{ref:n,className:(0,nm.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r7,{children:(0,a.jsx)(np.A,{className:"h-4 w-4"})})}),(0,a.jsx)(r5,{children:t})]}));ny.displayName=r6.displayName,l.forwardRef(({className:e,...t},r)=>(0,a.jsx)(ni,{ref:r,className:(0,nm.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=ni.displayName;var nw=r(91821),nb=r(96834),nE=r(41312),nj=r(23026),nS=r(41862),nC=r(96474),nN=r(39903),nR=r(37730),nA=r(67958);function nT(){let{user:e}=(0,nA.A)(),[t,r]=(0,l.useState)([]),[n,o]=(0,l.useState)(!0),[i,s]=(0,l.useState)(!1),[p,m]=(0,l.useState)(!1),[h,v]=(0,l.useState)(""),[g,x]=(0,l.useState)(""),[y,w]=(0,l.useState)({username:"",email:"",name:"",phone:"",role:"courier",password:""}),b=e?.role==="manager",E=async()=>{try{o(!0);let e=await nN.firebaseAuthService.getAllUsers();r(e)}catch(e){console.error("Error loading users:",e),v("فشل في تحميل المستخدمين")}finally{o(!1)}},j=async t=>{if(t.preventDefault(),!y.username||!y.email||!y.name||!y.password)return void v("يرجى ملء جميع الحقول المطلوبة");s(!0),v(""),x("");try{let t=await nN.firebaseAuthService.createUser({...y,createdBy:e?.username||"unknown"});t.success?(x(`تم إنشاء المستخدم ${y.username} بنجاح`),w({username:"",email:"",name:"",phone:"",role:"courier",password:""}),m(!1),await E()):v(t.error||"فشل في إنشاء المستخدم")}catch(e){v(e.message||"حدث خطأ غير متوقع")}finally{s(!1)}},S=async(e,t)=>{try{let r=await nN.firebaseAuthService.updateUser(e,{isActive:!t});r.success?(x(`تم ${!t?"تفعيل":"تعطيل"} المستخدم بنجاح`),await E()):v(r.error||"فشل في تحديث حالة المستخدم")}catch(e){v(e.message||"حدث خطأ في تحديث المستخدم")}},C=e=>{let t={manager:{label:"مدير",color:"bg-red-100 text-red-800"},supervisor:{label:"متابع",color:"bg-blue-100 text-blue-800"},courier:{label:"مندوب",color:"bg-green-100 text-green-800"}}[e]||{label:e,color:"bg-gray-100 text-gray-800"};return(0,a.jsx)(nb.E,{className:t.color,children:t.label})};return b?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(nR.A,{}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center gap-2",children:[(0,a.jsx)(nE.A,{className:"h-6 w-6"}),"إدارة المستخدمين"]}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"إدارة حسابات المستخدمين في النظام"})]}),(0,a.jsxs)(u.$,{onClick:()=>m(!p),className:"flex items-center gap-2",children:[(0,a.jsx)(nj.A,{className:"h-4 w-4"}),"إضافة مستخدم جديد"]})]}),h&&(0,a.jsx)(nw.Fc,{variant:"destructive",className:"mb-4",children:(0,a.jsx)(nw.TN,{children:h})}),g&&(0,a.jsx)(nw.Fc,{className:"mb-4 border-green-200 bg-green-50",children:(0,a.jsx)(nw.TN,{className:"text-green-800",children:g})}),p&&(0,a.jsxs)(c.Zp,{className:"mb-6",children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)(c.ZB,{children:"إنشاء مستخدم جديد"}),(0,a.jsx)(c.BT,{children:"أدخل بيانات المستخدم الجديد"})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("form",{onSubmit:j,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.J,{htmlFor:"username",children:"اسم المستخدم *"}),(0,a.jsx)(d.p,{id:"username",value:y.username,onChange:e=>w({...y,username:e.target.value}),placeholder:"أدخل اسم المستخدم",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.J,{htmlFor:"email",children:"البريد الإلكتروني *"}),(0,a.jsx)(d.p,{id:"email",type:"email",value:y.email,onChange:e=>w({...y,email:e.target.value}),placeholder:"أدخل البريد الإلكتروني",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.J,{htmlFor:"name",children:"الاسم الكامل *"}),(0,a.jsx)(d.p,{id:"name",value:y.name,onChange:e=>w({...y,name:e.target.value}),placeholder:"أدخل الاسم الكامل",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.J,{htmlFor:"phone",children:"رقم الهاتف"}),(0,a.jsx)(d.p,{id:"phone",value:y.phone,onChange:e=>w({...y,phone:e.target.value}),placeholder:"أدخل رقم الهاتف"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.J,{htmlFor:"role",children:"الدور *"}),(0,a.jsxs)(rL,{value:y.role,onValueChange:e=>w({...y,role:e}),children:[(0,a.jsx)(nh,{children:(0,a.jsx)(rI,{placeholder:"اختر الدور"})}),(0,a.jsxs)(nx,{children:[(0,a.jsx)(ny,{value:"manager",children:"مدير"}),(0,a.jsx)(ny,{value:"supervisor",children:"متابع"}),(0,a.jsx)(ny,{value:"courier",children:"مندوب"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.J,{htmlFor:"password",children:"كلمة المرور *"}),(0,a.jsx)(d.p,{id:"password",type:"password",value:y.password,onChange:e=>w({...y,password:e.target.value}),placeholder:"أدخل كلمة المرور",required:!0})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(u.$,{type:"submit",disabled:i,children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(nS.A,{className:"mr-2 h-4 w-4 animate-spin"}),"جاري الإنشاء..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(nC.A,{className:"mr-2 h-4 w-4"}),"إنشاء المستخدم"]})}),(0,a.jsx)(u.$,{type:"button",variant:"outline",onClick:()=>m(!1),children:"إلغاء"})]})]})})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)(c.ZB,{children:"قائمة المستخدمين"}),(0,a.jsx)(c.BT,{children:"جميع المستخدمين المسجلين في النظام"})]}),(0,a.jsx)(c.Wu,{children:n?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)(nS.A,{className:"h-6 w-6 animate-spin mr-2"}),"جاري تحميل المستخدمين..."]}):0===t.length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"لا يوجد مستخدمين مسجلين"}):(0,a.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("h3",{className:"font-medium",children:e.name}),C(e.role),(0,a.jsx)(nb.E,{variant:e.isActive?"default":"secondary",children:e.isActive?"نشط":"معطل"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"اسم المستخدم:"})," ",e.username]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"البريد:"})," ",e.email]}),e.phone&&(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"الهاتف:"})," ",e.phone]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"تاريخ الإنشاء:"})," ",e.createdAt.toLocaleDateString("ar-SA")]}),e.lastLogin&&(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"آخر دخول:"})," ",e.lastLogin.toLocaleDateString("ar-SA")]})]})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>S(e.id,e.isActive),children:e.isActive?"تعطيل":"تفعيل"})})]},e.id))})})]})]})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(nR.A,{}),(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)(nw.Fc,{variant:"destructive",children:(0,a.jsx)(nw.TN,{children:"ليس لديك صلاحية للوصول إلى هذه الصفحة. هذه الصفحة مخصصة للمديرين فقط."})})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13964:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14163:(e,t,r)=>{"use strict";r.d(t,{hO:()=>s,sG:()=>l});var n=r(43210),o=r(51215),i=r(8730),a=r(60687),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?r:t,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},15485:(e,t,r)=>{Promise.resolve().then(r.bind(r,84723))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23026:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>u});var n=r(60687),o=r(43210),i=r(14163),a=o.forwardRef((e,t)=>(0,n.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var l=r(24224),s=r(4780);let c=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),u=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(a,{ref:r,className:(0,s.cn)(c(),e),...t}));u.displayName=a.displayName},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59453:(e,t,r)=>{Promise.resolve().then(r.bind(r,8321))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74917:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>f,tree:()=>c});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),l=r(30893),s={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);r.d(t,s);let c={children:["",{children:["users-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84723)),"E:\\Marsal\\marsal\\src\\app\\users-management\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["E:\\Marsal\\marsal\\src\\app\\users-management\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/users-management/page",pathname:"/users-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84723:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\users-management\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\users-management\\page.tsx","default")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var n=r(60687);r(43210);var o=r(4780);function i({className:e,type:t,...r}){return(0,n.jsx)("input",{type:t,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91645:e=>{"use strict";e.exports=require("net")},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>s,TN:()=>c});var n=r(60687),o=r(43210),i=r(24224),a=r(4780);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),s=o.forwardRef(({className:e,variant:t,...r},o)=>(0,n.jsx)("div",{ref:o,role:"alert",className:(0,a.cn)(l({variant:t}),e),...r}));s.displayName="Alert",o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("h5",{ref:r,className:(0,a.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let c=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("text-sm [&_p]:leading-relaxed",e),...t}));c.displayName="AlertDescription"},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,863,860,814,451,610,809],()=>r(74917));module.exports=n})();