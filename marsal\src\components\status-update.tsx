"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Package,
  TruckIcon,
  CheckCircle,
  XCircle,
  Clock,
  Camera,
  Upload,
  MapPin,
  AlertCircle,
  DollarSign
} from "lucide-react";
import { useAuth } from "@/components/auth-provider";
import { UserRole } from "@/types/roles";

interface StatusUpdateProps {
  orderId: string;
  currentStatus: string;
  onUpdate?: (newStatus: string, notes: string, image?: File) => void;
  onStatusUpdate?: (newStatus: string, notes: string, image?: File) => void;
  onClose: () => void;
}

const statusOptions = [
  {
    value: 'delivered',
    label: 'تم التسليم',
    icon: CheckCircle,
    color: 'bg-green-500',
    description: 'تم تسليم الطلب بنجاح',
    requiresReason: false
  },
  {
    value: 'returned_to_courier',
    label: 'راجع عند المندوب',
    icon: AlertCircle,
    color: 'bg-amber-500',
    description: 'الطلب راجع عند المندوب',
    requiresReason: true
  },
  {
    value: 'postponed',
    label: 'مؤجل',
    icon: Clock,
    color: 'bg-orange-500',
    description: 'تم تأجيل التسليم',
    requiresReason: true
  },
  {
    value: 'partial_delivery',
    label: 'تسليم جزئي',
    icon: Package,
    color: 'bg-cyan-500',
    description: 'تم تسليم جزء من الطلب',
    requiresReason: false
  },
  {
    value: 'price_change',
    label: 'تغيير سعر فقط',
    icon: DollarSign,
    color: 'bg-indigo-500',
    description: 'تغيير سعر الطلب فقط',
    requiresReason: false
  }
];

export default function StatusUpdate({ orderId, currentStatus, onUpdate, onStatusUpdate, onClose }: StatusUpdateProps) {
  const { user } = useAuth();
  const [selectedStatus, setSelectedStatus] = useState(currentStatus);
  const [notes, setNotes] = useState('');
  const [image, setImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [reason, setReason] = useState('');
  const [returnCount, setReturnCount] = useState('');
  const [newPrice, setNewPrice] = useState('');

  // Filter status options based on user role and current status
  const getAvailableStatusOptions = () => {
    let availableOptions = [...statusOptions];

    // If current status is delivered, restrict access based on role
    if (currentStatus === 'delivered') {
      // Only supervisors and managers can modify delivered orders
      if (user?.role === UserRole.SUPERVISOR || user?.role === UserRole.MANAGER) {
        availableOptions.unshift({
          value: 'out_for_delivery',
          label: 'إرجاع إلى قيد التوصيل',
          icon: TruckIcon,
          color: 'bg-indigo-500',
          description: 'إرجاع الطلب المسلم إلى حالة قيد التوصيل',
          requiresReason: true
        });
      } else {
        // Couriers cannot modify delivered orders
        return [];
      }
    }

    // If current status is returned, cancelled, or archived, restrict modifications
    if (['returned', 'cancelled', 'archived'].includes(currentStatus)) {
      // Only managers can modify these statuses
      if (user?.role !== UserRole.MANAGER) {
        return [];
      }
    }

    return availableOptions;
  };

  const availableStatusOptions = getAvailableStatusOptions();

  // Check if user can update this order
  const canUpdateOrder = availableStatusOptions.length > 0;
  const getRestrictionMessage = () => {
    if (currentStatus === 'delivered' && user?.role === UserRole.COURIER) {
      return 'لا يمكن للمندوب تحديث الطلبات التي تم تسليمها. يرجى التواصل مع المشرف أو المدير.';
    }
    if (['returned', 'cancelled', 'archived'].includes(currentStatus) && user?.role !== UserRole.MANAGER) {
      return 'لا يمكن تحديث هذا الطلب. يرجى التواصل مع المدير.';
    }
    return 'لا يمكن تحديث حالة هذا الطلب حالياً.';
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async () => {
    const selectedOption = statusOptions.find(option => option.value === selectedStatus);

    // Validation
    if (selectedStatus === currentStatus && !notes && !image) {
      alert('يرجى تغيير الحالة أو إضافة ملاحظات أو صورة');
      return;
    }

    if (selectedOption?.requiresReason && (!reason.trim() || !image)) {
      alert('هذه الحالة تتطلب إضافة سبب وصورة');
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      let finalNotes = selectedOption?.requiresReason ? `${reason}\n${notes}` : notes;

      // Add partial delivery details to notes
      if (selectedStatus === 'partial_delivery') {
        const partialDetails = [];
        if (returnCount) partialDetails.push(`عدد القطع الراجعة: ${returnCount}`);
        if (newPrice) partialDetails.push(`السعر الجديد: ${newPrice} دينار عراقي`);
        if (partialDetails.length > 0) {
          finalNotes = finalNotes ? `${finalNotes}\n${partialDetails.join('\n')}` : partialDetails.join('\n');
        }
      }

      const updateFunction = onUpdate || onStatusUpdate;
      if (updateFunction) {
        updateFunction(selectedStatus, finalNotes, image || undefined);
      }

      // Show success message
      alert('تم تحديث حالة الطلب بنجاح!');

      // Close modal without navigation
      onClose();
    } catch (error) {
      alert('حدث خطأ أثناء تحديث الحالة');
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentStatusOption = statusOptions.find(option => option.value === currentStatus);
  const selectedStatusOption = statusOptions.find(option => option.value === selectedStatus);

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4" dir="rtl">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white/95 backdrop-blur-sm border-0 shadow-2xl">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 border-b">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-bold text-gray-800 flex items-center gap-2">
              <Package className="h-6 w-6 text-blue-600" />
              تحديث حالة الطلب {orderId}
            </CardTitle>
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </Button>
          </div>
          <CardDescription className="text-gray-600">
            الحالة الحالية:
            <Badge className={`mr-2 ${currentStatusOption?.color} text-white`}>
              {currentStatusOption?.label}
            </Badge>
            {!canUpdateOrder && (
              <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-yellow-800 text-sm">
                  ⚠️ {getRestrictionMessage()}
                </p>
              </div>
            )}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="p-6 space-y-6">
          {canUpdateOrder ? (
            <>
              {/* Status Selection */}
              <div>
                <h3 className="text-lg font-semibold mb-4 text-gray-800">اختيار الحالة الجديدة</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {availableStatusOptions.map((option) => {
                const IconComponent = option.icon;
                const isSelected = selectedStatus === option.value;
                const isCurrent = currentStatus === option.value;
                
                return (
                  <button
                    key={option.value}
                    onClick={() => setSelectedStatus(option.value)}
                    disabled={isCurrent}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 text-right ${
                      isSelected 
                        ? 'border-blue-500 bg-blue-50 shadow-md' 
                        : isCurrent
                        ? 'border-gray-300 bg-gray-100 opacity-50 cursor-not-allowed'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${option.color}`}>
                        <IconComponent className="h-5 w-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-800">{option.label}</div>
                        <div className="text-sm text-gray-600">{option.description}</div>
                      </div>
                      {isSelected && (
                        <CheckCircle className="h-5 w-5 text-blue-500" />
                      )}
                      {isCurrent && (
                        <Badge variant="secondary" className="text-xs">حالية</Badge>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Reason (for specific statuses) */}
          {statusOptions.find(option => option.value === selectedStatus)?.requiresReason && (
            <div>
              <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-red-500" />
                السبب (مطلوب)
              </h3>
              <Textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="يرجى توضيح سبب هذه الحالة..."
                className="min-h-[80px] resize-none border-red-200 focus:ring-red-500 focus:border-red-500"
                required
              />
            </div>
          )}

          {/* Partial Delivery Fields */}
          {selectedStatus === 'partial_delivery' && (
            <div className="space-y-4 p-4 bg-cyan-50 rounded-lg border border-cyan-200">
              <h3 className="text-lg font-semibold text-cyan-800">تفاصيل التسليم الجزئي</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2 text-gray-700">عدد القطع الراجعة</label>
                  <input
                    type="number"
                    value={returnCount}
                    onChange={(e) => setReturnCount(e.target.value)}
                    placeholder="أدخل عدد القطع الراجعة"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                    min="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2 text-gray-700">السعر الجديد (دينار عراقي)</label>
                  <input
                    type="number"
                    value={newPrice}
                    onChange={(e) => setNewPrice(e.target.value)}
                    placeholder="أدخل السعر الجديد"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                    min="0"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-800">ملاحظات إضافية</h3>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="أضف ملاحظات حول تحديث الحالة..."
              className="min-h-[100px] resize-none"
            />
          </div>

          {/* Image Upload */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-800">إرفاق صورة</h3>
            <div className="space-y-3">
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById('image-upload')?.click()}
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  اختيار صورة
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    // In a real app, this would open camera
                    document.getElementById('image-upload')?.click();
                  }}
                  className="flex items-center gap-2"
                >
                  <Camera className="h-4 w-4" />
                  التقاط صورة
                </Button>
              </div>
              
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
              />
              
              {imagePreview && (
                <div className="relative">
                  <img
                    src={imagePreview}
                    alt="معاينة الصورة"
                    className="w-full max-w-xs h-48 object-cover rounded-lg border"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={() => {
                      setImage(null);
                      setImagePreview(null);
                    }}
                    className="absolute top-2 left-2"
                  >
                    حذف
                  </Button>
                </div>
              )}
            </div>
          </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4 border-t">
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      جاري التحديث...
                    </div>
                  ) : (
                    'تحديث الحالة'
                  )}
                </Button>
                <Button
                  onClick={onClose}
                  variant="outline"
                  disabled={isSubmitting}
                  className="flex-1"
                >
                  إلغاء
                </Button>
              </div>
            </>
          ) : (
            /* Restriction Message for unauthorized users */
            <div className="text-center py-8">
              <div className="mb-4">
                <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Package className="h-8 w-8 text-yellow-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  لا يمكن تحديث هذا الطلب
                </h3>
                <p className="text-gray-600 mb-6">
                  {getRestrictionMessage()}
                </p>
              </div>
              <Button
                onClick={onClose}
                variant="outline"
                className="w-full max-w-xs"
              >
                إغلاق
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
