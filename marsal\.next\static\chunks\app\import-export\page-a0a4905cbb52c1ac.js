(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4015],{39599:(e,r,s)=>{"use strict";s.d(r,{BC:()=>i,_m:()=>l});let t={manager:["orders","dispatch","returns","accounting","statistics","archive","users","import-export","notifications","settings"],supervisor:["orders","statistics","archive","users","notifications","settings"],courier:["orders","archive","notifications","statistics"]};function l(e,r){var s;return(null==(s=t[e])?void 0:s.includes(r))||!1}let a=[{id:"orders",href:"/orders",label:"إدارة الطلبات",description:"عرض ومتابعة وتعديل الطلبات",icon:"Package",color:"from-blue-500 to-blue-600",roles:["manager","supervisor","courier"]},{id:"dispatch",href:"/dispatch",label:"إسناد الطلبات",description:"توزيع الطلبات على المندوبين",icon:"TruckIcon",color:"from-green-500 to-emerald-600",roles:["manager"]},{id:"returns",href:"/returns",label:"إدارة الرواجع",description:"استلام الطلبات الراجعة",icon:"RotateCcw",color:"from-orange-500 to-amber-600",roles:["manager"]},{id:"accounting",href:"/accounting",label:"المحاسبة",description:"التسويات المالية مع المندوبين",icon:"Calculator",color:"from-purple-500 to-violet-600",roles:["manager"]},{id:"statistics",href:"/statistics",label:"الإحصائيات",description:"التقارير والإحصائيات التفصيلية",icon:"BarChart3",color:"from-cyan-500 to-blue-600",roles:["manager","supervisor"]},{id:"archive",href:"/archive",label:"الأرشيف",description:"السجلات المنتهية",icon:"Archive",color:"from-gray-500 to-slate-600",roles:["manager","supervisor","courier"]},{id:"users",href:"/users",label:"إدارة الموظفين",description:"إدارة حسابات المستخدمين",icon:"Users",color:"from-indigo-500 to-purple-600",roles:["manager","supervisor"]},{id:"import-export",href:"/import-export",label:"استيراد وتصدير",description:"عمليات مجمعة على الطلبات",icon:"Upload",color:"from-teal-500 to-cyan-600",roles:["manager"]},{id:"notifications",href:"/notifications",label:"الإشعارات",description:"التنبيهات والتحديثات",icon:"Bell",color:"from-yellow-500 to-orange-600",roles:["manager","supervisor","courier"]},{id:"settings",href:"/settings",label:"الإعدادات",description:"تخصيص التطبيق",icon:"Settings",color:"from-pink-500 to-rose-600",roles:["manager","supervisor","courier"]}];function i(e){return a.filter(r=>r.roles.includes(e))}},62523:(e,r,s)=>{"use strict";s.d(r,{p:()=>a});var t=s(95155);s(12115);var l=s(59434);function a(e){let{className:r,type:s,...a}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,l.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...a})}},63009:(e,r,s)=>{Promise.resolve().then(s.bind(s,92861))},64044:(e,r,s)=>{"use strict";s.d(r,{A:()=>x});var t=s(95155),l=s(32044),a=s(39599),i=s(35695),o=s(12115),n=s(66695),c=s(30285),d=s(1243),m=s(92138);function x(e){let{children:r,requiredSection:s,fallbackPath:x="/"}=e,{user:u,isAuthenticated:h}=(0,l.A)(),p=(0,i.useRouter)();return((0,o.useEffect)(()=>{if(!h)return void p.push("/login");(null==u?void 0:u.role)&&!(0,a._m)(u.role,s)&&p.push(x)},[u,h,s,x,p]),h&&u)?(0,a._m)(u.role,s)?(0,t.jsx)(t.Fragment,{children:r}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center p-4",dir:"rtl",children:(0,t.jsxs)(n.Zp,{className:"max-w-md w-full shadow-xl border-2 border-red-200",children:[(0,t.jsxs)(n.aR,{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-full shadow-lg mb-4 mx-auto",children:(0,t.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,t.jsx)(n.ZB,{className:"text-2xl text-red-600",children:"غير مصرح بالدخول"}),(0,t.jsx)(n.BT,{className:"text-lg text-red-500",children:"ليس لديك صلاحية للوصول إلى هذا القسم"})]}),(0,t.jsxs)(n.Wu,{className:"text-center space-y-4",children:[(0,t.jsxs)("p",{className:"text-gray-600 leading-relaxed",children:["عذراً، دورك الحالي (","manager"===u.role?"مدير":"supervisor"===u.role?"متابع":"مندوب",") لا يسمح بالوصول إلى هذا القسم."]}),(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-sm text-yellow-800",children:"إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع المدير لمراجعة صلاحياتك."})}),(0,t.jsxs)(c.$,{onClick:()=>p.push(x),className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:["العودة إلى الصفحة الرئيسية",(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"})]})]})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse",children:(0,t.jsx)("div",{className:"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"مرسال"}),(0,t.jsx)("p",{className:"text-gray-500",children:"جاري التحقق من الصلاحيات..."})]})})}},92861:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>v});var t=s(95155),l=s(12115),a=s(6874),i=s.n(a),o=s(66695),n=s(30285),c=s(62523),d=s(37192),m=s(64044),x=s(64261),u=s(57340),h=s(91788),p=s(29869),g=s(40646),b=s(62525),f=s(1243);let j=[{id:"albarq",name:"شركة البرق",color:"bg-blue-500",columns:{trackingNumber:"A",companyName:"C",customerPhone:"F",customerAddress:"G",amount:"H"}},{id:"almasro",name:"شركة المسرة",color:"bg-green-500",columns:{trackingNumber:"C",companyName:"D",customerPhone:"H",amount:"I"}}];function v(){let[e,r]=(0,l.useState)("import"),[s,a]=(0,l.useState)(""),[v,N]=(0,l.useState)(""),[w,y]=(0,l.useState)("all"),[k,A]=(0,l.useState)("all"),[C,S]=(0,l.useState)(""),[B,R]=(0,l.useState)(""),[E,_]=(0,l.useState)(""),[T,Z]=(0,l.useState)(!1),[P,M]=(0,l.useState)(null),[U,$]=(0,l.useState)(!1),[O,W]=(0,l.useState)(null),L=async()=>{if(!P)return void alert("يرجى اختيار ملف أولاً");if(!v)return void alert("يرجى اختيار الشركة أولاً");$(!0);try{let e={success:Math.floor(50*Math.random())+10,errors:["الصف 5: رقم الهاتف غير صحيح","الصف 12: المبلغ مطلوب","الصف 18: عنوان المستلم مطلوب"]};await new Promise(e=>setTimeout(e,2e3)),W(e),alert("تم استيراد ".concat(e.success," طلب بنجاح"))}catch(e){console.error("Error importing orders:",e),alert("حدث خطأ أثناء استيراد الطلبات")}finally{$(!1)}},I=async()=>{$(!0);try{await new Promise(e=>setTimeout(e,1e3));let e=new Blob(["رقم الوصل,اسم المرسل,اسم المستلم,هاتف المستلم,المبلغ,الحالة,تاريخ الإنشاء\nMRS001,أحمد محمد,فاطمة علي,07801234567,50000,تم التسليم,2024-01-15\nMRS002,سارة أحمد,محمد حسن,07901234567,75000,في الانتظار,2024-01-15"],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a"),s=URL.createObjectURL(e);r.setAttribute("href",s),r.setAttribute("download","orders_".concat(new Date().toISOString().split("T")[0],".csv")),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r),alert("تم تصدير الطلبات بنجاح")}catch(e){console.error("Error exporting orders:",e),alert("حدث خطأ أثناء تصدير الطلبات")}finally{$(!1)}},q=async()=>{if(confirm("هل أنت متأكد من حذف جميع الطلبات؟ هذا الإجراء لا يمكن التراجع عنه!")&&confirm("تأكيد نهائي: سيتم حذف جميع الطلبات نهائياً. هل تريد المتابعة؟")){$(!0);try{await new Promise(e=>setTimeout(e,2e3)),alert("تم حذف جميع الطلبات")}catch(e){console.error("Error deleting orders:",e),alert("حدث خطأ أثناء حذف الطلبات")}finally{$(!1)}}};return(0,t.jsxs)(m.A,{requiredSection:"import-export",children:[(0,t.jsx)(d.A,{}),(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 p-4 md:p-6 lg:p-8",dir:"rtl",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"text-center flex-1 space-y-4",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-lg",children:(0,t.jsx)(x.A,{className:"h-8 w-8 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"استيراد وتصدير البيانات"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2",children:"إدارة البيانات من وإلى شركات التوصيل المختلفة"})]})]}),(0,t.jsx)(i(),{href:"/",children:(0,t.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex items-center gap-2 bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-200",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),"الصفحة الرئيسية"]})})]}),(0,t.jsxs)("div",{className:"flex space-x-1 bg-white/80 backdrop-blur-sm p-1 rounded-xl shadow-lg w-fit",children:[(0,t.jsx)("button",{onClick:()=>r("import"),className:"px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ".concat("import"===e?"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"),children:"استيراد طلبات"}),(0,t.jsx)("button",{onClick:()=>r("export"),className:"px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ".concat("export"===e?"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"),children:"تصدير طلبات"}),(0,t.jsx)("button",{onClick:()=>r("delete"),className:"px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ".concat("delete"===e?"bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"),children:"حذف مجمع"})]}),"import"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(o.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-xl",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"flex items-center gap-2 text-gray-800",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-600"}),"تحميل النموذج"]}),(0,t.jsx)(o.BT,{className:"text-gray-600",children:"قم بتحميل نموذج Excel لتعبئة بيانات الطلبات"})]}),(0,t.jsx)(o.Wu,{children:(0,t.jsxs)(n.$,{onClick:()=>{let e=new Blob(["رقم الوصل,اسم المرسل,هاتف المرسل,عنوان المرسل,اسم المستلم,هاتف المستلم,عنوان المستلم,المبلغ,ملاحظات\nMRS001,أحمد محمد,07901234567,بغداد - الكرادة,فاطمة علي,07801234567,بغداد - الجادرية,50000,مثال\nMRS002,سارة أحمد,07701234567,بغداد - المنصور,محمد حسن,07601234567,بغداد - الكاظمية,75000,مثال آخر"],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a"),s=URL.createObjectURL(e);r.setAttribute("href",s),r.setAttribute("download","template_orders.csv"),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r)},className:"bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),"تحميل نموذج Excel"]})})]}),(0,t.jsxs)(o.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-xl",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"flex items-center gap-2 text-gray-800",children:[(0,t.jsx)(p.A,{className:"h-5 w-5 text-blue-600"}),"استيراد الطلبات"]}),(0,t.jsx)(o.BT,{className:"text-gray-600",children:"اختر ملف Excel يحتوي على بيانات الطلبات"})]}),(0,t.jsxs)(o.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اختيار الشركة"}),(0,t.jsxs)("select",{value:v,onChange:e=>N(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,t.jsx)("option",{value:"",children:"اختر الشركة"}),j.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),v&&(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,t.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"تحديد الأعمدة:"}),(0,t.jsx)("div",{className:"text-sm text-blue-700 space-y-1",children:(()=>{let e=j.find(e=>e.id===v);return e?(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsxs)("div",{children:["عمود ",e.columns.trackingNumber,": رقم الوصل"]}),(0,t.jsxs)("div",{children:["عمود ",e.columns.companyName,": اسم الشركة"]}),(0,t.jsxs)("div",{children:["عمود ",e.columns.customerPhone,": رقم الهاتف"]}),e.columns.customerAddress&&(0,t.jsxs)("div",{children:["عمود ",e.columns.customerAddress,": العنوان"]}),(0,t.jsxs)("div",{children:["عمود ",e.columns.amount,": المبلغ"]})]}):null})()})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اختيار الملف"}),(0,t.jsx)(c.p,{type:"file",accept:".xlsx,.xls,.csv",onChange:e=>{e.target.files&&e.target.files[0]&&(M(e.target.files[0]),W(null))},className:"w-full",disabled:!v}),P&&(0,t.jsxs)("p",{className:"text-sm text-muted-foreground mt-2",children:["الملف المختار: ",P.name]})]}),(0,t.jsxs)(n.$,{onClick:L,disabled:!P||!v||U,className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),U?"جاري الاستيراد...":"استيراد الطلبات"]}),O&&(0,t.jsxs)("div",{className:"mt-4 p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsx)("span",{className:"font-medium",children:"نتائج الاستيراد"})]}),(0,t.jsxs)("p",{className:"text-sm text-green-600 mb-2",children:["تم استيراد ",O.success," طلب بنجاح"]}),O.errors.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-red-600 mb-1",children:"أخطاء:"}),(0,t.jsx)("ul",{className:"text-xs text-red-600 space-y-1",children:O.errors.map((e,r)=>(0,t.jsxs)("li",{children:["• ",e]},r))})]})]})]})]})]}),"export"===e&&(0,t.jsxs)(o.Zp,{children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5"}),"تصدير الطلبات"]}),(0,t.jsx)(o.BT,{children:"تصدير جميع الطلبات إلى ملف Excel"})]}),(0,t.jsxs)(o.Wu,{className:"space-y-4",children:[(0,t.jsx)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,t.jsx)("p",{className:"text-sm text-blue-800",children:"سيتم تصدير جميع الطلبات الموجودة في النظام إلى ملف CSV يمكن فتحه في Excel"})}),(0,t.jsxs)(n.$,{onClick:I,disabled:U,className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),U?"جاري التصدير...":"تصدير جميع الطلبات"]})]})]}),"delete"===e&&(0,t.jsxs)(o.Zp,{className:"border-red-200",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,t.jsx)(b.A,{className:"h-5 w-5"}),"حذف جميع الطلبات"]}),(0,t.jsx)(o.BT,{children:"حذف جميع الطلبات من النظام نهائياً"})]}),(0,t.jsxs)(o.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5 text-red-600"}),(0,t.jsx)("span",{className:"font-medium text-red-800",children:"تحذير!"})]}),(0,t.jsx)("p",{className:"text-sm text-red-800",children:"هذا الإجراء سيحذف جميع الطلبات من النظام نهائياً ولا يمكن التراجع عنه. تأكد من عمل نسخة احتياطية قبل المتابعة."})]}),(0,t.jsxs)(n.$,{onClick:q,disabled:U,variant:"destructive",className:"flex items-center gap-2",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),U?"جاري الحذف...":"حذف جميع الطلبات"]})]})]})]})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[8541,1336,9948,4709,875,416,8779,622,2432,7979,1899,9744,4495,5138,6321,2652,5030,3719,9473,558,9401,6325,6077,4409,1124,9385,4927,37,2041,4979,8321,2900,3610,9988,2158,8058,2044,7192,7358],()=>r(63009)),_N_E=e.O()}]);