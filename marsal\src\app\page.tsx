"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Header from "@/components/header";
import DashboardStats from "@/components/dashboard-stats";
import QuickSearch from "@/components/quick-search";
import { useAuth } from "@/components/auth-provider";
import { statisticsService } from "@/lib/statistics";
import { Statistics } from "@/types";
import { getNavigationSections } from "@/lib/permissions";
import { APP_CONFIG } from "@/lib/config";
import { testFirebaseConnection } from "@/lib/firebase";
import Link from "next/link";
import {
  Package,
  TruckIcon,
  RotateCcw,
  Calculator,
  BarChart3,
  Archive,
  Users,
  Upload,
  Bell,
  Settings,
  Info
} from "lucide-react";

// Icon mapping
const iconMap = {
  Package,
  TruckIcon,
  RotateCcw,
  Calculator,
  BarChart3,
  Archive,
  Users,
  Upload,
  Bell,
  Settings,
  Info
};

export default function Home() {
  const { user } = useAuth();
  const [stats, setStats] = useState<Statistics | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const overallStats = await statisticsService.getOverallStats();
      const todayOrders = await statisticsService.getTodayStats();

      setStats({
        ...overallStats,
        todayOrders,
        activeCouriers: 12,
        completionRate: overallStats.totalOrders > 0
          ? Math.round((overallStats.deliveredOrders / overallStats.totalOrders) * 100)
          : 0
      });
    } catch (error) {
      console.error('Error loading stats:', error);
      setStats({
        totalOrders: 1234,
        deliveredOrders: 987,
        returnedOrders: 45,
        pendingOrders: 156,
        totalAmount: 125000000,
        totalCommission: 987000,
        todayOrders: 89,
        activeCouriers: 12,
        completionRate: 85
      });
    } finally {
      setLoading(false);
    }
  };

  const availableSections = user ? getNavigationSections(user.role) : [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 p-4 md:p-6 lg:p-8 animated-bg" dir="rtl">
      <Header />
      <div className="max-w-7xl mx-auto space-y-8">
        <div className="text-center space-y-6 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl blur-3xl"></div>
          <div className="relative">
            <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl shadow-2xl mb-6 shadow-glow animate-pulse">
              <Package className="h-12 w-12 text-white" />
            </div>
            <div>
              <h1 className="text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-4 animate-pulse">
                مرسال
              </h1>
              <p className="text-gray-600 dark:text-gray-300 text-xl max-w-3xl mx-auto leading-relaxed glass p-6 rounded-2xl">
                🚀 نظام إدارة التوصيل السريع والموثوق - حلول متطورة لإدارة الطلبات والتوصيل بأحدث التقنيات
              </p>
            </div>
          </div>
        </div>

        <QuickSearch />

        {/* Demo Mode Notice - تنبيه الوضع التجريبي */}
        {APP_CONFIG.demo.enabled && APP_CONFIG.demo.showDemoNotice && (
          <div className="mt-8">
            <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                      <Info className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-blue-900 mb-2">
                      🎯 الوضع التجريبي مُفعل
                    </h3>
                    <p className="text-blue-700 text-sm leading-relaxed">
                      أنت تستخدم النسخة التجريبية من نظام مرسال. جميع البيانات تجريبية ولن يتم حفظها بشكل دائم.
                      <br />
                      <strong>بيانات الدخول:</strong> manager / 123456 أو supervisor / 123456 أو courier / 123456
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {stats && (
          <div className="mt-8">
            <DashboardStats stats={stats} />
          </div>
        )}

        {user && availableSections.length > 0 ? (
          <div className="mt-12">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
                أقسام النظام
              </h2>
              <p className="text-gray-600 dark:text-gray-300">
                اختر القسم الذي تريد الوصول إليه
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {availableSections.map((section) => {
                const IconComponent = iconMap[section.icon as keyof typeof iconMap];
                return (
                  <Link key={section.id} href={section.href}>
                    <Card className="group card-hover cursor-pointer border-2 hover:border-blue-300 glass gpu-accelerated">
                      <CardHeader className="text-center pb-4">
                        <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ${section.color} shadow-lg mb-4 group-hover:scale-110 transition-transform duration-300 will-change-transform`}>
                          {IconComponent && <IconComponent className="h-8 w-8 text-white" />}
                        </div>
                        <CardTitle className="text-lg font-bold text-gray-800 dark:text-white group-hover:text-blue-600 transition-colors">
                          {section.label}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="text-center pt-0">
                        <CardDescription className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                          {section.description}
                        </CardDescription>
                      </CardContent>
                    </Card>
                  </Link>
                );
              })}
            </div>
          </div>
        ) : null}

        <div className="text-center mt-12">
          <Card className="max-w-2xl mx-auto glass border-2 border-blue-200">
            <CardHeader>
              <CardTitle className="text-2xl text-blue-600">
                مرحباً، {user?.name || 'مستخدم'}
              </CardTitle>
              <CardDescription className="text-lg">
                {user?.role === 'manager' ? 'مدير النظام' :
                 user?.role === 'supervisor' ? 'متابع' : 'مندوب'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                مرحباً بك في نظام مرسال لإدارة التوصيل. يمكنك استخدام القائمة الجانبية للتنقل بين أقسام النظام المختلفة.
              </p>
              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">معلومات سريعة:</h4>
                <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <div>📅 التاريخ: {new Date().toLocaleDateString('ar-SA')}</div>
                  <div>⏰ الوقت: {new Date().toLocaleTimeString('ar-SA')}</div>
                  <div>👤 المستخدم: {user?.name || 'غير محدد'}</div>
                  <div>🔑 الدور: {user?.role === 'manager' ? 'مدير' : user?.role === 'supervisor' ? 'متابع' : 'مندوب'}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
