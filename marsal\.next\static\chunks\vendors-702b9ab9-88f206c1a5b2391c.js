"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2041],{17391:(t,e,r)=>{r.d(e,{A:()=>f});var i=r(56595),n=r(79886),o=r(438);let f=function(){function t(e,r,i,n){this.image=e,this.height=e.getHeight(),this.width=e.getWidth(),null==r&&(r=t.INIT_SIZE),null==i&&(i=e.getWidth()/2|0),null==n&&(n=e.getHeight()/2|0);var f=r/2|0;if(this.leftInit=i-f,this.rightInit=i+f,this.upInit=n-f,this.downInit=n+f,this.upInit<0||this.leftInit<0||this.downInit>=this.height||this.rightInit>=this.width)throw new o.A}return t.prototype.detect=function(){for(var t=this.leftInit,e=this.rightInit,r=this.upInit,i=this.downInit,n=!1,f=!0,a=!1,l=!1,u=!1,s=!1,h=!1,c=this.width,g=this.height;f;){f=!1;for(var p=!0;(p||!l)&&e<c;)(p=this.containsBlackPoint(r,i,e,!1))?(e++,f=!0,l=!0):!l&&e++;if(e>=c){n=!0;break}for(var A=!0;(A||!u)&&i<g;)(A=this.containsBlackPoint(t,e,i,!0))?(i++,f=!0,u=!0):!u&&i++;if(i>=g){n=!0;break}for(var d=!0;(d||!s)&&t>=0;)(d=this.containsBlackPoint(r,i,t,!1))?(t--,f=!0,s=!0):!s&&t--;if(t<0){n=!0;break}for(var w=!0;(w||!h)&&r>=0;)(w=this.containsBlackPoint(t,e,r,!0))?(r--,f=!0,h=!0):!h&&r--;if(r<0){n=!0;break}f&&(a=!0)}if(!n&&a){for(var y=e-t,v=null,m=1;null===v&&m<y;m++)v=this.getBlackPointOnSegment(t,i-m,t+m,i);if(null==v)throw new o.A;for(var I=null,m=1;null===I&&m<y;m++)I=this.getBlackPointOnSegment(t,r+m,t+m,r);if(null==I)throw new o.A;for(var _=null,m=1;null===_&&m<y;m++)_=this.getBlackPointOnSegment(e,r+m,e-m,r);if(null==_)throw new o.A;for(var b=null,m=1;null===b&&m<y;m++)b=this.getBlackPointOnSegment(e,i-m,e-m,i);if(null==b)throw new o.A;return this.centerEdges(b,v,_,I)}throw new o.A},t.prototype.getBlackPointOnSegment=function(t,e,r,o){for(var f=n.A.round(n.A.distance(t,e,r,o)),a=(r-t)/f,l=(o-e)/f,u=this.image,s=0;s<f;s++){var h=n.A.round(t+s*a),c=n.A.round(e+s*l);if(u.get(h,c))return new i.A(h,c)}return null},t.prototype.centerEdges=function(e,r,n,o){var f=e.getX(),a=e.getY(),l=r.getX(),u=r.getY(),s=n.getX(),h=n.getY(),c=o.getX(),g=o.getY(),p=t.CORR;return f<this.width/2?[new i.A(c-p,g+p),new i.A(l+p,u+p),new i.A(s-p,h-p),new i.A(f+p,a-p)]:[new i.A(c+p,g+p),new i.A(l+p,u-p),new i.A(s-p,h+p),new i.A(f-p,a-p)]},t.prototype.containsBlackPoint=function(t,e,r,i){var n=this.image;if(i){for(var o=t;o<=e;o++)if(n.get(o,r))return!0}else for(var f=t;f<=e;f++)if(n.get(r,f))return!0;return!1},t.INIT_SIZE=10,t.CORR=1,t}()},29477:(t,e,r)=>{r.d(e,{A:()=>f});var i=r(34382),n=r(322),o=r(38988);let f=function(){function t(t){this.field=t,this.cachedGenerators=[],this.cachedGenerators.push(new i.A(t,Int32Array.from([1])))}return t.prototype.buildGenerator=function(t){var e=this.cachedGenerators;if(t>=e.length)for(var r=e[e.length-1],n=this.field,o=e.length;o<=t;o++){var f=r.multiply(new i.A(n,Int32Array.from([1,n.exp(o-1+n.getGeneratorBase())])));e.push(f),r=f}return e[t]},t.prototype.encode=function(t,e){if(0===e)throw new o.A("No error correction bytes");var r=t.length-e;if(r<=0)throw new o.A("No data bytes provided");var f=this.buildGenerator(e),a=new Int32Array(r);n.A.arraycopy(t,0,a,0,r);for(var l=new i.A(this.field,a),u=(l=l.multiplyByMonomial(e,1)).divide(f)[1].getCoefficients(),s=e-u.length,h=0;h<s;h++)t[r+h]=0;n.A.arraycopy(u,0,t,r+s,u.length)},t}()},33638:(t,e,r)=>{r.d(e,{A:()=>n});var i=r(38988);let n=function(){function t(){}return t.prototype.exp=function(t){return this.expTable[t]},t.prototype.log=function(t){if(0===t)throw new i.A;return this.logTable[t]},t.addOrSubtract=function(t,e){return t^e},t}()},34382:(t,e,r)=>{r.d(e,{A:()=>f});var i=r(33638),n=r(322),o=r(38988);let f=function(){function t(t,e){if(0===e.length)throw new o.A;this.field=t;var r=e.length;if(r>1&&0===e[0]){for(var i=1;i<r&&0===e[i];)i++;i===r?this.coefficients=Int32Array.from([0]):(this.coefficients=new Int32Array(r-i),n.A.arraycopy(e,i,this.coefficients,0,this.coefficients.length))}else this.coefficients=e}return t.prototype.getCoefficients=function(){return this.coefficients},t.prototype.getDegree=function(){return this.coefficients.length-1},t.prototype.isZero=function(){return 0===this.coefficients[0]},t.prototype.getCoefficient=function(t){return this.coefficients[this.coefficients.length-1-t]},t.prototype.evaluateAt=function(t){if(0===t)return this.getCoefficient(0);var e,r=this.coefficients;if(1===t){e=0;for(var n=0,o=r.length;n!==o;n++){var f=r[n];e=i.A.addOrSubtract(e,f)}return e}e=r[0];for(var a=r.length,l=this.field,n=1;n<a;n++)e=i.A.addOrSubtract(l.multiply(t,e),r[n]);return e},t.prototype.addOrSubtract=function(e){if(!this.field.equals(e.field))throw new o.A("GenericGFPolys do not have same GenericGF field");if(this.isZero())return e;if(e.isZero())return this;var r=this.coefficients,f=e.coefficients;if(r.length>f.length){var a=r;r=f,f=a}var l=new Int32Array(f.length),u=f.length-r.length;n.A.arraycopy(f,0,l,0,u);for(var s=u;s<f.length;s++)l[s]=i.A.addOrSubtract(r[s-u],f[s]);return new t(this.field,l)},t.prototype.multiply=function(e){if(!this.field.equals(e.field))throw new o.A("GenericGFPolys do not have same GenericGF field");if(this.isZero()||e.isZero())return this.field.getZero();for(var r=this.coefficients,n=r.length,f=e.coefficients,a=f.length,l=new Int32Array(n+a-1),u=this.field,s=0;s<n;s++)for(var h=r[s],c=0;c<a;c++)l[s+c]=i.A.addOrSubtract(l[s+c],u.multiply(h,f[c]));return new t(u,l)},t.prototype.multiplyScalar=function(e){if(0===e)return this.field.getZero();if(1===e)return this;for(var r=this.coefficients.length,i=this.field,n=new Int32Array(r),o=this.coefficients,f=0;f<r;f++)n[f]=i.multiply(o[f],e);return new t(i,n)},t.prototype.multiplyByMonomial=function(e,r){if(e<0)throw new o.A;if(0===r)return this.field.getZero();for(var i=this.coefficients,n=i.length,f=new Int32Array(n+e),a=this.field,l=0;l<n;l++)f[l]=a.multiply(i[l],r);return new t(a,f)},t.prototype.divide=function(t){if(!this.field.equals(t.field))throw new o.A("GenericGFPolys do not have same GenericGF field");if(t.isZero())throw new o.A("Divide by 0");for(var e=this.field,r=e.getZero(),i=this,n=t.getCoefficient(t.getDegree()),f=e.inverse(n);i.getDegree()>=t.getDegree()&&!i.isZero();){var a=i.getDegree()-t.getDegree(),l=e.multiply(i.getCoefficient(i.getDegree()),f),u=t.multiplyByMonomial(a,l),s=e.buildMonomial(a,l);r=r.addOrSubtract(s),i=i.addOrSubtract(u)}return[r,i]},t.prototype.toString=function(){for(var t="",e=this.getDegree();e>=0;e--){var r=this.getCoefficient(e);if(0!==r){if(r<0?(t+=" - ",r=-r):t.length>0&&(t+=" + "),0===e||1!==r){var i=this.field.log(r);0===i?t+="1":1===i?t+="a":(t+="a^",t+=i)}0!==e&&(1===e?t+="x":(t+="x^",t+=e))}}return t},t}()},55192:(t,e,r)=>{r.d(e,{A:()=>u});var i=r(34382),n=r(33638),o=r(63479),f=r(38988),a=r(73179),l=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function i(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(i.prototype=r.prototype,new i)}}();let u=function(t){function e(e,r,n){var o=t.call(this)||this;o.primitive=e,o.size=r,o.generatorBase=n;for(var f=new Int32Array(r),a=1,l=0;l<r;l++)f[l]=a,(a*=2)>=r&&(a^=e,a&=r-1);o.expTable=f;for(var u=new Int32Array(r),l=0;l<r-1;l++)u[f[l]]=l;return o.logTable=u,o.zero=new i.A(o,Int32Array.from([0])),o.one=new i.A(o,Int32Array.from([1])),o}return l(e,t),e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,e){if(t<0)throw new f.A;if(0===e)return this.zero;var r=new Int32Array(t+1);return r[0]=e,new i.A(this,r)},e.prototype.inverse=function(t){if(0===t)throw new a.A;return this.expTable[this.size-this.logTable[t]-1]},e.prototype.multiply=function(t,e){return 0===t||0===e?0:this.expTable[(this.logTable[t]+this.logTable[e])%(this.size-1)]},e.prototype.getSize=function(){return this.size},e.prototype.getGeneratorBase=function(){return this.generatorBase},e.prototype.toString=function(){return"GF(0x"+o.A.toHexString(this.primitive)+","+this.size+")"},e.prototype.equals=function(t){return t===this},e.AZTEC_DATA_12=new e(4201,4096,1),e.AZTEC_DATA_10=new e(1033,1024,1),e.AZTEC_DATA_6=new e(67,64,1),e.AZTEC_PARAM=new e(19,16,1),e.QR_CODE_FIELD_256=new e(285,256,0),e.DATA_MATRIX_FIELD_256=new e(301,256,1),e.AZTEC_DATA_8=e.DATA_MATRIX_FIELD_256,e.MAXICODE_FIELD_64=e.AZTEC_DATA_6,e}(n.A)},60109:(t,e,r)=>{r.d(e,{A:()=>a});var i=r(55192),n=r(34382),o=r(20738),f=r(39778);let a=function(){function t(t){this.field=t}return t.prototype.decode=function(t,e){for(var r=this.field,f=new n.A(r,t),a=new Int32Array(e),l=!0,u=0;u<e;u++){var s=f.evaluateAt(r.exp(u+r.getGeneratorBase()));a[a.length-1-u]=s,0!==s&&(l=!1)}if(!l)for(var h=new n.A(r,a),c=this.runEuclideanAlgorithm(r.buildMonomial(e,1),h,e),g=c[0],p=c[1],A=this.findErrorLocations(g),d=this.findErrorMagnitudes(p,A),u=0;u<A.length;u++){var w=t.length-1-r.log(A[u]);if(w<0)throw new o.A("Bad error location");t[w]=i.A.addOrSubtract(t[w],d[u])}},t.prototype.runEuclideanAlgorithm=function(t,e,r){if(t.getDegree()<e.getDegree()){var i=t;t=e,e=i}for(var n=this.field,a=t,l=e,u=n.getZero(),s=n.getOne();l.getDegree()>=(r/2|0);){var h=a,c=u;if(a=l,u=s,a.isZero())throw new o.A("r_{i-1} was zero");l=h;for(var g=n.getZero(),p=a.getCoefficient(a.getDegree()),A=n.inverse(p);l.getDegree()>=a.getDegree()&&!l.isZero();){var d=l.getDegree()-a.getDegree(),w=n.multiply(l.getCoefficient(l.getDegree()),A);g=g.addOrSubtract(n.buildMonomial(d,w)),l=l.addOrSubtract(a.multiplyByMonomial(d,w))}if(s=g.multiply(u).addOrSubtract(c),l.getDegree()>=a.getDegree())throw new f.A("Division algorithm failed to reduce polynomial?")}var y=s.getCoefficient(0);if(0===y)throw new o.A("sigmaTilde(0) was zero");var v=n.inverse(y);return[s.multiplyScalar(v),l.multiplyScalar(v)]},t.prototype.findErrorLocations=function(t){var e=t.getDegree();if(1===e)return Int32Array.from([t.getCoefficient(1)]);for(var r=new Int32Array(e),i=0,n=this.field,f=1;f<n.getSize()&&i<e;f++)0===t.evaluateAt(f)&&(r[i]=n.inverse(f),i++);if(i!==e)throw new o.A("Error locator degree does not match number of roots");return r},t.prototype.findErrorMagnitudes=function(t,e){for(var r=e.length,i=new Int32Array(r),n=this.field,o=0;o<r;o++){for(var f=n.inverse(e[o]),a=1,l=0;l<r;l++)if(o!==l){var u=n.multiply(e[l],f),s=(1&u)==0?1|u:-2&u;a=n.multiply(a,s)}i[o]=n.multiply(t.evaluateAt(f),n.inverse(a)),0!==n.getGeneratorBase()&&(i[o]=n.multiply(i[o],f))}return i},t}()},79886:(t,e,r)=>{r.d(e,{A:()=>i});let i=function(){function t(){}return t.round=function(t){return isNaN(t)?0:t<=Number.MIN_SAFE_INTEGER?Number.MIN_SAFE_INTEGER:t>=Number.MAX_SAFE_INTEGER?Number.MAX_SAFE_INTEGER:t+(t<0?-.5:.5)|0},t.distance=function(t,e,r,i){var n=t-r,o=e-i;return Math.sqrt(n*n+o*o)},t.sum=function(t){for(var e=0,r=0,i=t.length;r!==i;r++)e+=t[r];return e},t}()}}]);