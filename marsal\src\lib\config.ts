// Application configuration
export const APP_CONFIG = {
  name: "مرسال",
  description: "نظام إدارة عمليات التوصيل السريع",
  version: "1.1.0",
  
  // Colors
  colors: {
    primary: "#41a7ff",
    background: "#f0f3f5", 
    accent: "#b19cd9"
  },
  
  // Business rules
  business: {
    commissionPerOrder: 1000, // 1000 IQD per order
    currency: "د.ع",
    defaultOrderStatuses: [
      "pending",
      "assigned", 
      "picked_up",
      "in_transit",
      "delivered",
      "returned",
      "cancelled",
      "postponed"
    ]
  },
  
  // API endpoints (when backend is ready)
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001",
    endpoints: {
      orders: "/api/orders",
      users: "/api/users",
      couriers: "/api/couriers",
      settlements: "/api/settlements"
    }
  },
  
  // Firebase config - إعدادات قاعدة البيانات السحابية الحقيقية
  firebase: {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || "AIzaSyDemoKeyForMarsalDeliveryApp123456789",
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || "marsal-delivery-app.firebaseapp.com",
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "marsal-delivery-app",
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || "marsal-delivery-app.appspot.com",
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "123456789012",
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || "1:123456789012:web:abcdef123456789012345",
    measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || "G-XXXXXXXXXX"
  },
  
  // Features flags
  features: {
    enableNotifications: true,
    enableReports: true,
    enableBulkOperations: true,
    enableImageUpload: true
  },

  // Firebase Production mode - وضع الإنتاج مع Firebase حصرياً
  demo: {
    enabled: false, // تعطيل الوضع التجريبي - استخدام Firebase حصرياً
    autoLogin: false, // تعطيل تسجيل الدخول التلقائي
    defaultUser: null, // لا يوجد مستخدم افتراضي
    skipFirebase: false, // استخدام Firebase الحقيقي حصرياً
    showDemoNotice: false // إخفاء تنبيه الوضع التجريبي
  },

  // Company info
  company: {
    name: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',
    phone: '+*********** 4567',
    address: 'بغداد، العراق'
  },

  // Receipt settings - إعدادات الوصل
  receipt: {
    dimensions: {
      width: '110mm',
      height: '130mm'
    },
    companyName: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',
    showBarcode: true,
    showDate: true,
    priceFormat: 'en-US', // English format for price
    currency: 'IQD',
    fields: {
      trackingNumber: true,
      customerPhone: true,
      status: true,
      courierName: true,
      amount: true,
      barcode: true,
      date: true
    }
  }
};

// Status labels in Arabic
export const STATUS_LABELS = {
  pending: "في الانتظار",
  assigned: "مسند", 
  picked_up: "تم الاستلام",
  in_transit: "في الطريق",
  delivered: "تم التسليم",
  returned: "راجع",
  cancelled: "ملغي",
  postponed: "مؤجل"
} as const;

// Status colors for UI
export const STATUS_COLORS = {
  pending: "text-yellow-600 bg-yellow-100",
  assigned: "text-blue-600 bg-blue-100",
  picked_up: "text-purple-600 bg-purple-100", 
  in_transit: "text-orange-600 bg-orange-100",
  delivered: "text-green-600 bg-green-100",
  returned: "text-red-600 bg-red-100",
  cancelled: "text-gray-600 bg-gray-100",
  postponed: "text-gray-600 bg-gray-100"
} as const;

// User roles
export const USER_ROLES = {
  admin: "مدير",
  manager: "مدير فرع", 
  dispatcher: "موزع",
  courier: "مندوب",
  accountant: "محاسب",
  viewer: "مشاهد"
} as const;
