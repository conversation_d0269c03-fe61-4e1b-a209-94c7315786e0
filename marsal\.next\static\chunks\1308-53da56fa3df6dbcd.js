"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1308],{9086:(e,t,a)=>{a.d(t,{cO:()=>n,pv:()=>o,y7:()=>c});var r=a(35317),d=a(56104),s=a(59434),i=a(16641);let o={async create(e){try{let t=(0,s.y7)(),a=(0,r.collection)(d.db,"orders"),i=await (0,r.gS)(a,{trackingNumber:t,customerName:e.customerName,customerPhone:e.customerPhone,address:e.address,amount:e.amount,status:"pending",courierName:e.courierName||"",assignedTo:e.assignedTo||"",notes:e.notes||"",createdAt:(0,r.O5)(),updatedAt:(0,r.O5)(),createdBy:e.createdBy||"system"});return await this.addStatusHistory(i.id,"pending","تم إنشاء الطلب",e.createdBy||"system"),console.log("✅ تم إنشاء الطلب:",i.id),{id:i.id,trackingNumber:t,customerName:e.customerName,customerPhone:e.customerPhone,address:e.address,amount:e.amount,status:"pending",courierName:e.courierName||"",assignedTo:e.assignedTo||"",notes:e.notes||"",createdAt:new Date,updatedAt:new Date,statusHistory:[{status:"pending",timestamp:new Date,updatedBy:e.createdBy||"system",notes:"تم إنشاء الطلب"}]}}catch(e){throw console.error("Error creating order:",e),e}},async getAll(){try{let e=(0,r.collection)(d.db,"orders"),t=(0,r.P)(e,(0,r.My)("createdAt","desc")),a=await (0,r.getDocs)(t);return await Promise.all(a.docs.map(async e=>{var t,a,r;let d=e.data(),s=await this.getStatusHistory(e.id);return{id:e.id,trackingNumber:d.trackingNumber,customerName:d.customerName,customerPhone:d.customerPhone,address:d.address,amount:d.amount,status:d.status,courierName:d.courierName||"",assignedTo:d.assignedTo||"",notes:d.notes||"",createdAt:(null==(t=d.createdAt)?void 0:t.toDate())||new Date,updatedAt:(null==(a=d.updatedAt)?void 0:a.toDate())||new Date,deliveredAt:null==(r=d.deliveredAt)?void 0:r.toDate(),statusHistory:s}}))}catch(e){return console.error("Error getting orders:",e),i.Ys}},async getStatusHistory(e){try{let t=(0,r.collection)(d.db,"orders",e,"statusHistory"),a=(0,r.P)(t,(0,r.My)("timestamp","desc"));return(await (0,r.getDocs)(a)).docs.map(e=>{var t;let a=e.data();return{status:a.status,timestamp:(null==(t=a.timestamp)?void 0:t.toDate())||new Date,updatedBy:a.updatedBy,notes:a.notes||""}})}catch(e){return console.error("Error getting status history:",e),[]}},async addStatusHistory(e,t,a,s){try{let i=(0,r.collection)(d.db,"orders",e,"statusHistory");await (0,r.gS)(i,{status:t,notes:a,updatedBy:s,timestamp:(0,r.O5)()})}catch(e){console.error("Error adding status history:",e)}},async getById(e){try{var t,a,s;let i=(0,r.doc)(d.db,"orders",e),o=await (0,r.getDoc)(i);if(!o.exists())throw Error("Order not found");let n=o.data(),c=await this.getStatusHistory(e);return{id:o.id,trackingNumber:n.trackingNumber,customerName:n.customerName,customerPhone:n.customerPhone,address:n.address,amount:n.amount,status:n.status,courierName:n.courierName||"",assignedTo:n.assignedTo||"",notes:n.notes||"",createdAt:(null==(t=n.createdAt)?void 0:t.toDate())||new Date,updatedAt:(null==(a=n.updatedAt)?void 0:a.toDate())||new Date,deliveredAt:null==(s=n.deliveredAt)?void 0:s.toDate(),statusHistory:c}}catch(e){throw console.error("Error getting order by ID:",e),Error("Order not found")}},async search(e){try{return(await orderService.searchOrders(e)).map(e=>({id:e.id,trackingNumber:e.tracking_number,customerName:e.customer_name,customerPhone:e.customer_phone,address:e.address,amount:e.amount,status:e.status,courierName:e.courier_name,courierId:e.courier_id,notes:e.notes,createdAt:new Date(e.created_at),updatedAt:new Date(e.updated_at),deliveredAt:e.delivered_at?new Date(e.delivered_at):void 0,statusHistory:[{status:e.status,timestamp:new Date(e.updated_at),updatedBy:"system",notes:e.notes||""}]}))}catch(t){return console.warn("Search failed, using mock data:",t),i.Ys.filter(t=>t.trackingNumber.includes(e)||t.customerName.includes(e)||t.customerPhone.includes(e))}},async updateStatus(e,t,a,s,i){let o=(0,r.doc)(d.db,COLLECTIONS.ORDERS,e),n=r.Dc.now(),c={status:t,timestamp:n.toDate(),updatedBy:s,notes:a,image:i},u={status:t,updatedAt:n,statusHistory:[...(await this.getById(e)).statusHistory,c]};return"delivered"===t?u.deliveredAt=n:"returned"===t&&(u.returnedAt=n),await (0,r.mZ)(o,u),c},async getByStatus(e){let t=(0,r.P)((0,r.collection)(d.db,COLLECTIONS.ORDERS),(0,r._M)("status","==",e),(0,r.My)("createdAt","desc"));return(await (0,r.getDocs)(t)).docs.map(e=>{var t,a,r,d,s;return{id:e.id,...e.data(),createdAt:null==(t=e.data().createdAt)?void 0:t.toDate(),updatedAt:null==(a=e.data().updatedAt)?void 0:a.toDate(),deliveredAt:null==(r=e.data().deliveredAt)?void 0:r.toDate(),returnedAt:null==(d=e.data().returnedAt)?void 0:d.toDate(),statusHistory:null==(s=e.data().statusHistory)?void 0:s.map(e=>{var t;return{...e,timestamp:null==(t=e.timestamp)?void 0:t.toDate()}})}})},async getByCourier(e){let t=(0,r.P)((0,r.collection)(d.db,COLLECTIONS.ORDERS),(0,r._M)("assignedTo","==",e),(0,r.My)("createdAt","desc"));return(await (0,r.getDocs)(t)).docs.map(e=>{var t,a,r,d,s;return{id:e.id,...e.data(),createdAt:null==(t=e.data().createdAt)?void 0:t.toDate(),updatedAt:null==(a=e.data().updatedAt)?void 0:a.toDate(),deliveredAt:null==(r=e.data().deliveredAt)?void 0:r.toDate(),returnedAt:null==(d=e.data().returnedAt)?void 0:d.toDate(),statusHistory:null==(s=e.data().statusHistory)?void 0:s.map(e=>{var t;return{...e,timestamp:null==(t=e.timestamp)?void 0:t.toDate()}})}})},async assignToCourier(e,t,a){let s=writeBatch(d.db),i=r.Dc.now();for(let o of e){let e=(0,r.doc)(d.db,COLLECTIONS.ORDERS,o),n=await this.getById(o),c={status:"assigned",timestamp:i.toDate(),updatedBy:a,notes:"تم إسناد الطلب للمندوب"};s.update(e,{assignedTo:t,status:"assigned",updatedAt:i,statusHistory:[...n.statusHistory,c]})}await s.commit()},async delete(e){await (0,r.kd)((0,r.doc)(d.db,COLLECTIONS.ORDERS,e))}},n={async create(e){try{let t={username:e.username,name:e.name,phone:e.phone,role:e.role,password_hash:"$2b$10$example_hash_for_123456",is_active:!0,created_by:e.createdBy||"system"},a=await userService.createUser(t);return{id:a.id,username:a.username,name:a.name,phone:a.phone,role:a.role,isActive:a.is_active,createdAt:new Date(a.created_at),updatedAt:new Date(a.created_at),createdBy:a.created_by}}catch(e){throw console.error("Error creating user:",e),e}},async getAll(){try{return(await userService.getAllUsers()).map(e=>({id:e.id,username:e.username,name:e.name,phone:e.phone,role:e.role,isActive:e.is_active,createdAt:new Date(e.created_at),updatedAt:new Date(e.created_at),createdBy:e.created_by}))}catch(e){return console.warn("Error getting users, using mock data:",e),i.rB}},async getCouriers(){try{return(await userService.getUsersByRole("courier")).filter(e=>e.is_active).map(e=>({id:e.id,username:e.username,name:e.name,phone:e.phone,role:e.role,isActive:e.is_active,createdAt:new Date(e.created_at),updatedAt:new Date(e.created_at),createdBy:e.created_by}))}catch(e){return console.warn("Error getting couriers, using mock data:",e),i.rB.filter(e=>"courier"===e.role&&e.isActive)}},async getById(e){var t,a;let s=(0,r.doc)(d.db,COLLECTIONS.USERS,e),i=await (0,r.getDoc)(s);if(!i.exists())throw Error("User not found");let o=i.data();return{id:i.id,...o,createdAt:null==(t=o.createdAt)?void 0:t.toDate(),updatedAt:null==(a=o.updatedAt)?void 0:a.toDate()}},async update(e,t){let a=(0,r.doc)(d.db,COLLECTIONS.USERS,e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})},async delete(e){await (0,r.kd)((0,r.doc)(d.db,COLLECTIONS.USERS,e))}},c={async create(e){let t=r.Dc.now(),a={...e,createdAt:t.toDate(),isSettled:!1};return{id:(await (0,r.gS)((0,r.collection)(d.db,COLLECTIONS.SETTLEMENTS),a)).id,...a}},async getAll(){let e=(0,r.P)((0,r.collection)(d.db,COLLECTIONS.SETTLEMENTS),(0,r.My)("createdAt","desc"));return(await (0,r.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),createdAt:null==(t=e.data().createdAt)?void 0:t.toDate(),settledAt:null==(a=e.data().settledAt)?void 0:a.toDate()}})},async markAsSettled(e){let t=(0,r.doc)(d.db,COLLECTIONS.SETTLEMENTS,e);await (0,r.mZ)(t,{isSettled:!0,settledAt:r.Dc.now()})},subscribeToOrders(e){try{let t=(0,r.P)((0,r.collection)(d.db,COLLECTIONS.ORDERS),(0,r.My)("createdAt","desc"));return(0,r.aQ)(t,t=>{let a=t.docs.map(e=>({id:e.id,...e.data()}));e(a)})}catch(e){return console.warn("Firebase subscription not available:",e),()=>{}}},subscribeToUserOrders(e,t){try{let a=(0,r.P)((0,r.collection)(d.db,COLLECTIONS.ORDERS),(0,r._M)("courierId","==",e),(0,r.My)("createdAt","desc"));return(0,r.aQ)(a,e=>{let a=e.docs.map(e=>({id:e.id,...e.data()}));t(a)})}catch(e){return console.warn("Firebase subscription not available:",e),()=>{}}}}},16641:(e,t,a)=>{a.d(t,{Ys:()=>r,rB:()=>d});let r=[{id:"1",trackingNumber:"MRS001",senderName:"أحمد محمد",senderPhone:"07901234567",senderAddress:"بغداد - الكرادة - شارع الرشيد",recipientName:"فاطمة علي",recipientPhone:"07801234567",recipientAddress:"بغداد - الجادرية - قرب الجامعة",amount:5e4,status:"pending",notes:"يرجى التسليم في المساء",createdAt:new Date("2024-01-15T10:30:00"),updatedAt:new Date("2024-01-15T10:30:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-15T10:30:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"}]},{id:"2",trackingNumber:"MRS002",senderName:"سارة أحمد",senderPhone:"07701234567",senderAddress:"بغداد - المنصور - شارع الأميرات",recipientName:"محمد حسن",recipientPhone:"07601234567",recipientAddress:"بغداد - الكاظمية - شارع الإمام",amount:75e3,status:"assigned",assignedTo:"courier1",createdAt:new Date("2024-01-15T09:15:00"),updatedAt:new Date("2024-01-15T11:00:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-15T09:15:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"},{status:"assigned",timestamp:new Date("2024-01-15T11:00:00"),updatedBy:"dispatcher1",notes:"تم إسناد الطلب للمندوب"}]},{id:"3",trackingNumber:"MRS003",senderName:"خالد عبدالله",senderPhone:"07501234567",senderAddress:"بغداد - الدورة - شارع الصناعة",recipientName:"زينب محمد",recipientPhone:"07401234567",recipientAddress:"بغداد - الشعلة - قرب المجمع",amount:12e4,status:"delivered",assignedTo:"courier2",deliveredAt:new Date("2024-01-14T16:30:00"),createdAt:new Date("2024-01-14T08:00:00"),updatedAt:new Date("2024-01-14T16:30:00"),statusHistory:[{status:"pending",timestamp:new Date("2024-01-14T08:00:00"),updatedBy:"النظام",notes:"تم إنشاء الطلب"},{status:"assigned",timestamp:new Date("2024-01-14T09:00:00"),updatedBy:"dispatcher1",notes:"تم إسناد الطلب للمندوب"},{status:"delivered",timestamp:new Date("2024-01-14T16:30:00"),updatedBy:"courier2",notes:"تم تسليم الطلب بنجاح"}]}],d=[{id:"admin1",email:"<EMAIL>",name:"أحمد المدير",username:"admin",phone:"07901234567",role:"admin",isActive:!0,createdAt:new Date("2024-01-01T00:00:00"),updatedAt:new Date("2024-01-01T00:00:00")},{id:"courier1",email:"<EMAIL>",name:"علي حسين",username:"courier1",phone:"07801234567",role:"courier",isActive:!0,createdAt:new Date("2024-01-02T00:00:00"),updatedAt:new Date("2024-01-02T00:00:00")},{id:"courier2",email:"<EMAIL>",name:"حسام محمد",username:"courier2",phone:"07701234567",role:"courier",isActive:!0,createdAt:new Date("2024-01-03T00:00:00"),updatedAt:new Date("2024-01-03T00:00:00")},{id:"dispatcher1",email:"<EMAIL>",name:"سارة الموزعة",username:"dispatcher",phone:"07601234567",role:"dispatcher",isActive:!0,createdAt:new Date("2024-01-04T00:00:00"),updatedAt:new Date("2024-01-04T00:00:00")}]},62523:(e,t,a)=>{a.d(t,{p:()=>s});var r=a(95155);a(12115);var d=a(59434);function s(e){let{className:t,type:a,...s}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,d.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}}}]);