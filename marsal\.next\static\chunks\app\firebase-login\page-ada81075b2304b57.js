(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[894],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var a=r(5155);r(2115);var s=r(9708),n=r(2085),i=r(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:r,size:n,asChild:c=!1,...l}=e,o=c?s.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,i.cn)(d({variant:r,size:n,className:t})),...l})}},968:(e,t,r)=>{"use strict";r.d(t,{b:()=>d});var a=r(2115),s=r(3655),n=r(5155),i=a.forwardRef((e,t)=>(0,n.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var d=i},1154:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(5155);r(2115);var s=r(9434);function n(e){let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},2657:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3022:(e,t,r)=>{Promise.resolve().then(r.bind(r,7088))},3655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>c,sG:()=>d});var a=r(2115),s=r(7650),n=r(9708),i=r(5155),d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,n.TL)(`Primitive.${t}`),s=a.forwardRef((e,a)=>{let{asChild:s,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s?r:t,{...n,ref:a})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function c(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},4213:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},4449:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},5057:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var a=r(5155),s=r(2115),n=r(968),i=r(2085),d=r(9434);let c=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.b,{ref:t,className:(0,d.cn)(c(),r),...s})});l.displayName=n.b.displayName},5365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>c,TN:()=>l});var a=r(5155),s=r(2115),n=r(2085),i=r(9434);let d=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:s,...n}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,i.cn)(d({variant:s}),r),...n})});c.displayName="Alert",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",r),...s})}).displayName="AlertTitle";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",r),...s})});l.displayName="AlertDescription"},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},6517:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>n,aR:()=>i});var a=r(5155);r(2115);var s=r(9434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}},7088:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(5155),s=r(2115),n=r(5695),i=r(6695),d=r(285),c=r(2523),l=r(5057),o=r(5365),u=r(1154),m=r(6517),g=r(4449),p=r(4213),h=r(8749),f=r(2657),x=r(9345),v=r(6104);function b(){let[e,t]=(0,s.useState)(""),[r,b]=(0,s.useState)(""),[y,w]=(0,s.useState)(!1),[j,k]=(0,s.useState)(!1),[N,D]=(0,s.useState)(""),[A,M]=(0,s.useState)("checking"),z=(0,n.useRouter)();(0,s.useEffect)(()=>{S()},[]);let S=async()=>{try{M("checking");let e=await (0,v.testFirebaseConnection)();M(e.success?"connected":"disconnected"),e.success||D("فشل الاتصال بـ Firebase: ".concat(e.message))}catch(e){M("disconnected"),D("فشل في اختبار الاتصال بـ Firebase")}},C=async t=>{if(t.preventDefault(),"connected"!==A)return void D("يجب الاتصال بـ Firebase أولاً");if(!e.trim()||!r.trim())return void D("يرجى إدخال اسم المستخدم وكلمة المرور");k(!0),D("");try{console.log("\uD83D\uDD10 محاولة تسجيل الدخول مع Firebase...");let t=await x.firebaseAuthService.login(e.trim(),r);t.success&&t.user?(console.log("✅ تم تسجيل الدخول بنجاح:",t.user.name),localStorage.setItem("currentUser",JSON.stringify(t.user)),localStorage.setItem("isAuthenticated","true"),z.push("/")):D(t.error||"فشل في تسجيل الدخول")}catch(e){console.error("❌ خطأ في تسجيل الدخول:",e),D(e.message||"حدث خطأ غير متوقع")}finally{k(!1)}},F=async e=>{let r={azad95:{username:"azad95",password:"Azad@1995"},manager:{username:"manager",password:"123456"},supervisor:{username:"supervisor",password:"123456"},courier:{username:"courier",password:"123456"}};t(r[e].username),b(r[e].password),setTimeout(()=>{let e=document.querySelector("form");e&&e.dispatchEvent(new Event("submit",{cancelable:!0,bubbles:!0}))},100)};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-12 w-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4",children:(0,a.jsx)(p.A,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"تطبيق مرسال"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"تسجيل الدخول مع Firebase"})]}),(0,a.jsx)(i.Zp,{className:"".concat((()=>{switch(A){case"checking":return"border-yellow-200 bg-yellow-50";case"connected":return"border-green-200 bg-green-50";case"disconnected":return"border-red-200 bg-red-50"}})()),children:(0,a.jsxs)(i.Wu,{className:"pt-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(()=>{switch(A){case"checking":return(0,a.jsx)(u.A,{className:"h-4 w-4 animate-spin"});case"connected":return(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-600"});case"disconnected":return(0,a.jsx)(g.A,{className:"h-4 w-4 text-red-600"})}})(),(0,a.jsx)("span",{className:"text-sm font-medium",children:(()=>{switch(A){case"checking":return"جاري فحص الاتصال بـ Firebase...";case"connected":return"متصل بـ Firebase بنجاح";case"disconnected":return"غير متصل بـ Firebase"}})()})]}),"disconnected"===A&&(0,a.jsx)(d.$,{variant:"outline",size:"sm",onClick:S,className:"mt-2 w-full",children:"\uD83D\uDD04 إعادة المحاولة"})]})}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"تسجيل الدخول"}),(0,a.jsx)(i.BT,{children:"أدخل بيانات الدخول للوصول إلى التطبيق"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.J,{htmlFor:"username",children:"اسم المستخدم"}),(0,a.jsx)(c.p,{id:"username",type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"أدخل اسم المستخدم",disabled:j||"connected"!==A,className:"text-right"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.J,{htmlFor:"password",children:"كلمة المرور"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.p,{id:"password",type:y?"text":"password",value:r,onChange:e=>b(e.target.value),placeholder:"أدخل كلمة المرور",disabled:j||"connected"!==A,className:"text-right pr-10"}),(0,a.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>w(!y),disabled:j,children:y?(0,a.jsx)(h.A,{className:"h-4 w-4"}):(0,a.jsx)(f.A,{className:"h-4 w-4"})})]})]}),N&&(0,a.jsx)(o.Fc,{variant:"destructive",children:(0,a.jsx)(o.TN,{children:N})}),(0,a.jsx)(d.$,{type:"submit",className:"w-full",disabled:j||"connected"!==A,children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"جاري تسجيل الدخول..."]}):"تسجيل الدخول"})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{className:"text-sm",children:"تسجيل دخول سريع (للاختبار)"})}),(0,a.jsxs)(i.Wu,{className:"space-y-2",children:[(0,a.jsx)(d.$,{variant:"outline",className:"w-full justify-start bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200",onClick:()=>F("azad95"),disabled:j||"connected"!==A,children:"\uD83D\uDC68‍\uD83D\uDCBC أزاد - المدير الرئيسي (azad95 / Azad@1995)"}),(0,a.jsx)(d.$,{variant:"outline",className:"w-full justify-start",onClick:()=>F("manager"),disabled:j||"connected"!==A,children:"\uD83D\uDC51 مدير النظام (manager / 123456)"}),(0,a.jsx)(d.$,{variant:"outline",className:"w-full justify-start",onClick:()=>F("supervisor"),disabled:j||"connected"!==A,children:"\uD83D\uDC68‍\uD83D\uDCBC المتابع (supervisor / 123456)"}),(0,a.jsx)(d.$,{variant:"outline",className:"w-full justify-start",onClick:()=>F("courier"),disabled:j||"connected"!==A,children:"\uD83D\uDE9A المندوب (courier / 123456)"})]})]}),(0,a.jsxs)("div",{className:"text-center text-sm text-gray-500",children:[(0,a.jsx)("p",{children:"تطبيق مرسال - نظام إدارة التوصيل"}),(0,a.jsx)("p",{children:"مدعوم بـ Firebase"})]})]})})}},8749:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},9434:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>d,cn:()=>n,ps:()=>m,qY:()=>u,r6:()=>c,vv:()=>i,y7:()=>l,zC:()=>o});var a=r(2596),s=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}function i(e){return"".concat(e.toLocaleString("ar-IQ")," د.ع")}function d(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function c(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function l(){let e=Date.now().toString().slice(-6),t=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"".concat("MRS").concat(e).concat(t)}function o(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function u(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function m(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,811,455,134,568,345,441,684,358],()=>t(3022)),_N_E=e.O()}]);