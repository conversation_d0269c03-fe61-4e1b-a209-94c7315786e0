"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8058],{23843:(e,a,r)=>{r.d(a,{vQ:()=>t});let t={name:"مرسال",description:"نظام إدارة عمليات التوصيل السريع",version:"1.1.0",colors:{primary:"#41a7ff",background:"#f0f3f5",accent:"#b19cd9"},business:{commissionPerOrder:1e3,currency:"د.ع",defaultOrderStatuses:["pending","assigned","picked_up","in_transit","delivered","returned","cancelled","postponed"]},api:{baseUrl:"http://localhost:3001",endpoints:{orders:"/api/orders",users:"/api/users",couriers:"/api/couriers",settlements:"/api/settlements"}},firebase:{apiKey:"AIzaSyDUcdNPsyxpWHOw-vxUaw2kmnmv5SXWflY",authDomain:"marsal-delivery-app.firebaseapp.com",projectId:"marsal-delivery-app",storageBucket:"marsal-delivery-app.firebasestorage.app",messagingSenderId:"403753087327",appId:"1:403753087327:web:1ed573c427309db39686ea",measurementId:"G-PMLRQKR9NB"},features:{enableNotifications:!0,enableReports:!0,enableBulkOperations:!0,enableImageUpload:!0},demo:{enabled:!1,autoLogin:!1,defaultUser:null,skipFirebase:!1,showDemoNotice:!1},company:{name:"مكتب علي الشيباني للتوصيل السريع فرع الحي",phone:"+964 ************",address:"بغداد، العراق"},receipt:{dimensions:{width:"110mm",height:"130mm"},companyName:"مكتب علي الشيباني للتوصيل السريع فرع الحي",showBarcode:!0,showDate:!0,priceFormat:"en-US",currency:"IQD",fields:{trackingNumber:!0,customerPhone:!0,status:!0,courierName:!0,amount:!0,barcode:!0,date:!0}}}},56104:(e,a,r)=>{r.d(a,{db:()=>u,j2:()=>d,testFirebaseConnection:()=>m});var t=r(23915),s=r(35317),n=r(16203),o=r(90858),i=r(23843);let c={apiKey:i.vQ.firebase.apiKey||"AIzaSyDemoKeyForMarsalDeliveryApp123456789",authDomain:i.vQ.firebase.authDomain||"marsal-delivery.firebaseapp.com",projectId:i.vQ.firebase.projectId||"marsal-delivery-system",storageBucket:i.vQ.firebase.storageBucket||"marsal-delivery.appspot.com",messagingSenderId:i.vQ.firebase.messagingSenderId||"*********",appId:i.vQ.firebase.appId||"1:*********:web:abc123def456ghi789"},l=null,u=null,d=null;if(!i.vQ.demo.skipFirebase)try{l=(0,t.Wp)(c),u=(0,s.aU)(l),d=(0,n.xI)(l),(0,o.c7)(l)}catch(e){console.warn("Firebase initialization failed, running in demo mode:",e)}let m=async()=>{if(i.vQ.demo.skipFirebase||!u)return{success:!0,message:"تم الاتصال بقاعدة البيانات التجريبية بنجاح ✅ (وضع تجريبي)"};try{let{doc:e,getDoc:a,setDoc:t}=await Promise.resolve().then(r.bind(r,35317)),s=e(u,"system","connection_test");if(await t(s,{timestamp:new Date,status:"connected",app:"marsal-delivery"}),(await a(s)).exists())return{success:!0,message:"تم الاتصال بقاعدة البيانات السحابية بنجاح ✅"};return{success:!1,message:"تم الاتصال ولكن فشل في قراءة البيانات"}}catch(a){console.error("Firebase connection test failed:",a);let e="فشل في الاتصال بقاعدة البيانات السحابية";return a instanceof Error&&(a.message.includes("network")?e+=" - تحقق من الاتصال بالإنترنت":a.message.includes("permission")?e+=" - مشكلة في الصلاحيات":e+=": "+a.message),{success:!1,message:e}}}},59345:(e,a,r)=>{r.d(a,{firebaseAuthService:()=>i});var t=r(16203),s=r(56104),n=r(35317);let o=[{username:"azad95",email:"<EMAIL>",name:"أزاد - مدير النظام الرئيسي",phone:"07801234567",role:"manager",password:"Azad@1995"},{username:"manager",email:"<EMAIL>",name:"مدير النظام",phone:"07801234568",role:"manager",password:"123456"},{username:"supervisor",email:"<EMAIL>",name:"المشرف العام",phone:"07801234569",role:"supervisor",password:"123456"},{username:"courier",email:"<EMAIL>",name:"مندوب التوصيل",phone:"07801234570",role:"courier",password:"123456"}],i={async initializeDefaultUsers(){try{if(console.log("\uD83D\uDD27 إعداد المستخدمين الافتراضيين..."),s.j2&&s.db){for(let e of(console.log("\uD83D\uDD25 محاولة استخدام Firebase..."),o)){if(await this.getUserByUsername(e.username)){console.log("✅ المستخدم ".concat(e.username," موجود مسبقاً"));continue}try{let a=await (0,t.eJ)(s.j2,e.email,e.password);await (0,n.setDoc)((0,n.doc)(s.db,"users",a.user.uid),{username:e.username,email:e.email,name:e.name,phone:e.phone,role:e.role,isActive:!0,createdAt:(0,n.O5)(),updatedAt:(0,n.O5)(),createdBy:"system"}),console.log("✅ تم إنشاء المستخدم في Firebase: ".concat(e.username))}catch(a){if("auth/email-already-in-use"===a.code)console.log("⚠️ البريد الإلكتروني ".concat(e.email," مستخدم مسبقاً"));else throw console.error("❌ خطأ في إنشاء المستخدم ".concat(e.username,":"),a),a}}console.log("✅ تم إعداد المستخدمين في Firebase بنجاح");return}throw Error("Firebase غير متاح")}catch(e){console.error("❌ خطأ في إعداد المستخدمين مع Firebase:",e),console.log("\uD83D\uDCF1 التبديل إلى التخزين المحلي كنظام احتياطي"),this.initializeLocalUsers()}},async tryFirebaseSetup(){try{if(!s.j2||!s.db)return void console.log("⚠️ Firebase غير متاح");await s.j2.currentUser,console.log("✅ Firebase متاح ولكن قد يحتاج إعداد إضافي")}catch(e){console.warn("⚠️ Firebase غير مُعد بشكل صحيح:",e.message)}},initializeLocalUsers(){try{if(localStorage.getItem("marsal_users"))return void console.log("✅ المستخدمين موجودين في التخزين المحلي");let e=o.map((e,a)=>({id:"local_".concat(a+1),username:e.username,email:e.email,name:e.name,phone:e.phone,role:e.role,isActive:!0,createdAt:new Date,updatedAt:new Date,createdBy:"system",password:e.password}));localStorage.setItem("marsal_users",JSON.stringify(e)),console.log("✅ تم إنشاء المستخدمين في التخزين المحلي")}catch(e){console.error("❌ خطأ في إنشاء المستخدمين المحليين:",e)}},async login(e,a){try{if(console.log("\uD83D\uDD10 محاولة تسجيل الدخول للمستخدم:",e),!s.j2||!s.db)return console.log("\uD83D\uDCF1 Firebase غير متاح، استخدام التخزين المحلي"),await this.loginWithLocalStorage(e,a);try{let r=await this.loginWithFirebase(e,a);return console.log("✅ تم تسجيل الدخول مع Firebase بنجاح"),r}catch(r){if(console.warn("⚠️ فشل تسجيل الدخول مع Firebase:",r.message),"auth/network-request-failed"===r.code||r.message.includes("Firebase"))return console.log("\uD83D\uDCF1 التبديل إلى التخزين المحلي"),await this.loginWithLocalStorage(e,a);throw r}}catch(a){console.error("❌ خطأ في تسجيل الدخول:",a);let e="خطأ في تسجيل الدخول";switch(a.code){case"auth/user-not-found":e="المستخدم غير موجود";break;case"auth/wrong-password":e="كلمة المرور غير صحيحة";break;case"auth/invalid-email":e="البريد الإلكتروني غير صحيح";break;case"auth/user-disabled":e="الحساب معطل";break;case"auth/too-many-requests":e="محاولات كثيرة، يرجى المحاولة لاحقاً";break;default:e=a.message||"خطأ غير معروف"}return{success:!1,error:e}}},async loginWithFirebase(e,a){let r=await this.getUserByUsername(e);if(!r)throw Error("اسم المستخدم غير موجود");if(!r.isActive)throw Error("الحساب غير مفعل");let o=await (0,t.x9)(s.j2,r.email,a);return await (0,n.setDoc)((0,n.doc)(s.db,"users",o.user.uid),{lastLogin:(0,n.O5)()},{merge:!0}),console.log("✅ تم تسجيل الدخول مع Firebase بنجاح"),{success:!0,user:{...r,id:o.user.uid}}},async loginWithLocalStorage(e,a){let r=localStorage.getItem("marsal_users");if(!r)throw Error("لا يوجد مستخدمين مسجلين");let t=JSON.parse(r),s=t.find(a=>a.username===e);if(!s)throw Error("اسم المستخدم غير موجود");if(!s.isActive)throw Error("الحساب غير مفعل");if(s.password!==a)throw Error("كلمة المرور غير صحيحة");s.lastLogin=new Date;let n=t.map(e=>e.id===s.id?s:e);return localStorage.setItem("marsal_users",JSON.stringify(n)),console.log("✅ تم تسجيل الدخول مع التخزين المحلي بنجاح"),{success:!0,user:{id:s.id,username:s.username,email:s.email,name:s.name,phone:s.phone,role:s.role,isActive:s.isActive,createdAt:new Date(s.createdAt),updatedAt:new Date(s.updatedAt),createdBy:s.createdBy,lastLogin:s.lastLogin?new Date(s.lastLogin):void 0}}},async getUserByUsername(e){try{if(s.db)try{let o=(0,n.collection)(s.db,"users"),i=(0,n.P)(o,(0,n._M)("username","==",e)),c=await (0,n.getDocs)(i);if(!c.empty){var a,r,t;let e=c.docs[0],s=e.data();return{id:e.id,username:s.username,email:s.email,name:s.name,phone:s.phone,role:s.role,isActive:s.isActive,createdAt:(null==(a=s.createdAt)?void 0:a.toDate())||new Date,updatedAt:(null==(r=s.updatedAt)?void 0:r.toDate())||new Date,createdBy:s.createdBy,lastLogin:null==(t=s.lastLogin)?void 0:t.toDate()}}}catch(e){console.warn("⚠️ فشل البحث في Firebase، محاولة التخزين المحلي")}let o=localStorage.getItem("marsal_users");if(!o)return null;let i=JSON.parse(o).find(a=>a.username===e);if(!i)return null;return{id:i.id,username:i.username,email:i.email,name:i.name,phone:i.phone,role:i.role,isActive:i.isActive,createdAt:new Date(i.createdAt),updatedAt:new Date(i.updatedAt),createdBy:i.createdBy,lastLogin:i.lastLogin?new Date(i.lastLogin):void 0}}catch(e){return console.error("Error getting user by username:",e),null}},async getCurrentUser(){try{var e,a,r;let t=s.j2.currentUser;if(!t)return null;let o=await (0,n.getDoc)((0,n.doc)(s.db,"users",t.uid));if(!o.exists())return null;let i=o.data();return{id:o.id,username:i.username,email:i.email,name:i.name,phone:i.phone,role:i.role,isActive:i.isActive,createdAt:(null==(e=i.createdAt)?void 0:e.toDate())||new Date,updatedAt:(null==(a=i.updatedAt)?void 0:a.toDate())||new Date,createdBy:i.createdBy,lastLogin:null==(r=i.lastLogin)?void 0:r.toDate()}}catch(e){return console.error("Error getting current user:",e),null}},async logout(){try{await (0,t.CI)(s.j2),console.log("✅ تم تسجيل الخروج بنجاح")}catch(e){throw console.error("❌ خطأ في تسجيل الخروج:",e),e}},onAuthStateChanged(e){return(0,t.hg)(s.j2,async a=>{a?e(await this.getCurrentUser()):e(null)})},async createUser(e){try{console.log("\uD83D\uDC64 إنشاء مستخدم جديد:",e.username);let a=await (0,t.eJ)(s.j2,e.email,e.password);await (0,n.setDoc)((0,n.doc)(s.db,"users",a.user.uid),{username:e.username,email:e.email,name:e.name,phone:e.phone,role:e.role,isActive:!0,createdAt:(0,n.O5)(),updatedAt:(0,n.O5)(),createdBy:e.createdBy}),console.log("✅ تم إنشاء المستخدم بنجاح:",e.username);let r={id:a.user.uid,username:e.username,email:e.email,name:e.name,phone:e.phone,role:e.role,isActive:!0,createdAt:new Date,updatedAt:new Date,createdBy:e.createdBy};return{success:!0,user:r}}catch(a){console.error("❌ خطأ في إنشاء المستخدم:",a);let e="خطأ في إنشاء المستخدم";switch(a.code){case"auth/email-already-in-use":e="البريد الإلكتروني مستخدم مسبقاً";break;case"auth/weak-password":e="كلمة المرور ضعيفة";break;case"auth/invalid-email":e="البريد الإلكتروني غير صحيح";break;default:e=a.message||"خطأ غير معروف"}return{success:!1,error:e}}},async getAllUsers(){try{let e=(0,n.collection)(s.db,"users");return(await (0,n.getDocs)(e)).docs.map(e=>{var a,r,t;let s=e.data();return{id:e.id,username:s.username,email:s.email,name:s.name,phone:s.phone,role:s.role,isActive:s.isActive,createdAt:(null==(a=s.createdAt)?void 0:a.toDate())||new Date,updatedAt:(null==(r=s.updatedAt)?void 0:r.toDate())||new Date,createdBy:s.createdBy,lastLogin:null==(t=s.lastLogin)?void 0:t.toDate()}})}catch(e){return console.error("Error getting all users:",e),[]}},async updateUser(e,a){try{return await (0,n.setDoc)((0,n.doc)(s.db,"users",e),{...a,updatedAt:(0,n.O5)()},{merge:!0}),console.log("✅ تم تحديث المستخدم:",e),{success:!0}}catch(e){return console.error("❌ خطأ في تحديث المستخدم:",e),{success:!1,error:e.message||"خطأ في تحديث المستخدم"}}},async deleteUser(e){try{return await (0,n.kd)((0,n.doc)(s.db,"users",e)),await this.updateUser(e,{isActive:!1}),console.log("✅ تم حذف المستخدم:",e),{success:!0}}catch(e){return console.error("❌ خطأ في حذف المستخدم:",e),{success:!1,error:e.message||"خطأ في حذف المستخدم"}}},async checkConnection(){try{return s.j2.currentUser,console.log("✅ Firebase Auth متصل"),{connected:!0,message:"Firebase Auth متصل بنجاح"}}catch(e){return console.error("❌ Firebase Auth غير متصل:",e),{connected:!1,message:"خطأ في الاتصال: ".concat(e.message)}}}};i.initializeDefaultUsers().catch(console.error)},59434:(e,a,r)=>{r.d(a,{Yq:()=>i,cn:()=>n,ps:()=>m,qY:()=>d,r6:()=>c,vv:()=>o,y7:()=>l,zC:()=>u});var t=r(52596),s=r(39688);function n(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return(0,s.QP)((0,t.$)(a))}function o(e){return"".concat(e.toLocaleString("ar-IQ")," د.ع")}function i(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function c(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function l(){let e=Date.now().toString().slice(-6),a=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"".concat("MRS").concat(e).concat(a)}function u(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function d(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function m(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}}}]);