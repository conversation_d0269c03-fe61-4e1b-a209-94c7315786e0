(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[450],{1243:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1304:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},1482:(e,t,r)=>{Promise.resolve().then(r.bind(r,4003))},2138:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},3109:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},4003:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var s=r(5155),n=r(2115),a=r(6695),l=r(285),i=r(7192),d=r(6740),o=r(7340),c=r(3109),h=r(9946);let m=(0,h.A)("wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]]),x=(0,h.A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),g=(0,h.A)("receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z",key:"q3az6g"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17.5v-11",key:"1jc1ny"}]]);var p=r(7580),u=r(1007),b=r(7434),f=r(7924),j=r(646),N=r(7108),v=r(1304),y=r(6874),w=r.n(y),k=r(4044);function A(){var e,t;let[r,h]=(0,n.useState)("couriers"),[y,A]=(0,n.useState)(""),[S,L]=(0,n.useState)([]),[C,M]=(0,n.useState)(!1),[Z,B]=(0,n.useState)(""),I=[{id:"1",name:"أحمد محمد",phone:"07701234567"},{id:"2",name:"علي حسن",phone:"07801234567"},{id:"3",name:"محمد علي",phone:"***********"}],R=[{id:"1",trackingNumber:"MRS001",recipientName:"سارة أحمد",recipientPhone:"07701111111",amount:5e4,status:"delivered",courierId:"1",courierName:"أحمد محمد",deliveredAt:new Date("2024-01-15")},{id:"2",trackingNumber:"MRS002",recipientName:"فاطمة علي",recipientPhone:"07702222222",amount:75e3,status:"delivered",courierId:"1",courierName:"أحمد محمد",deliveredAt:new Date("2024-01-15")},{id:"3",trackingNumber:"MRS003",recipientName:"زينب محمد",recipientPhone:"07703333333",amount:1e5,status:"delivered",courierId:"2",courierName:"علي حسن",deliveredAt:new Date("2024-01-16")}],_=y?R.filter(e=>e.courierId===y):[],z=e=>{L(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},D=e=>{let t=I.find(t=>t.id===e),r=R.filter(t=>t.courierId===e),s=r.reduce((e,t)=>e+t.amount,0),n=1e3*r.length,a=s-n,l='\n      <html dir="rtl">\n        <head>\n          <title>كشف حساب المندوب - الأرشيف</title>\n          <style>\n            body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }\n            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }\n            .company-name { font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 10px; }\n            .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n            .table th, .table td { border: 1px solid #ddd; padding: 12px; text-align: right; }\n            .table th { background-color: #f8f9fa; font-weight: bold; }\n            .total { font-weight: bold; background-color: #e3f2fd; }\n            .summary { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-top: 20px; }\n            .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }\n          </style>\n        </head>\n        <body>\n          <div class="header">\n            <div class="company-name">مكتب علي الشيباني للتوصيل السريع</div>\n            <h2>كشف حساب المندوب - الأرشيف</h2>\n            <p><strong>التاريخ:</strong> '.concat(new Date().toLocaleDateString("ar-IQ"),"</p>\n            <p><strong>المندوب:</strong> ").concat((null==t?void 0:t.name)||"غير محدد","</p>\n            <p><strong>رقم الهاتف:</strong> ").concat((null==t?void 0:t.phone)||"غير محدد",'</p>\n          </div>\n          <table class="table">\n            <thead>\n              <tr>\n                <th>رقم الوصل</th>\n                <th>اسم المستلم</th>\n                <th>رقم الهاتف</th>\n                <th>المبلغ</th>\n                <th>العمولة</th>\n                <th>الصافي</th>\n                <th>تاريخ التسليم</th>\n              </tr>\n            </thead>\n            <tbody>\n              ').concat(r.map(e=>"\n                <tr>\n                  <td>".concat(e.trackingNumber,"</td>\n                  <td>").concat(e.recipientName,"</td>\n                  <td>").concat(e.recipientPhone,"</td>\n                  <td>").concat(e.amount.toLocaleString()," د.ع</td>\n                  <td>1,000 د.ع</td>\n                  <td>").concat((e.amount-1e3).toLocaleString()," د.ع</td>\n                  <td>").concat(e.deliveredAt.toLocaleDateString("ar-IQ"),"</td>\n                </tr>\n              ")).join(""),'\n              <tr class="total">\n                <td colspan="3"><strong>المجموع الكلي</strong></td>\n                <td><strong>').concat(s.toLocaleString()," د.ع</strong></td>\n                <td><strong>").concat(n.toLocaleString()," د.ع</strong></td>\n                <td><strong>").concat(a.toLocaleString(),' د.ع</strong></td>\n                <td></td>\n              </tr>\n            </tbody>\n          </table>\n          <div class="summary">\n            <h3>ملخص الحساب:</h3>\n            <p><strong>عدد الطلبات المسلمة:</strong> ').concat(r.length," طلب</p>\n            <p><strong>إجمالي المبالغ المحصلة:</strong> ").concat(s.toLocaleString()," دينار عراقي</p>\n            <p><strong>إجمالي العمولات:</strong> ").concat(n.toLocaleString()," دينار عراقي</p>\n            <p><strong>صافي المبلغ للشركة:</strong> ").concat(a.toLocaleString(),' دينار عراقي</p>\n          </div>\n          <div class="footer">\n            <p>تم إنشاء هذا الكشف بواسطة نظام مرسال لإدارة التوصيل</p>\n            <p>للاستفسار: ***********</p>\n          </div>\n        </body>\n      </html>\n    '),i=window.open("","_blank");i&&(i.document.write(l),i.document.close(),i.focus(),i.print(),i.close())},W=R.filter(e=>S.includes(e.id)),$=W.reduce((e,t)=>e+t.amount,0),q=1e3*W.length,H=$-q;return(0,s.jsxs)(k.A,{requiredSection:"accounting",children:[(0,s.jsx)(i.A,{}),(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 p-4 md:p-6 lg:p-8",dir:"rtl",children:[(0,s.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"text-center flex-1 space-y-4",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-lg",children:(0,s.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"المحاسبة المالية"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2 text-lg",children:"إدارة المحاسبة والتسويات المالية بطريقة احترافية"})]})]}),(0,s.jsx)(w(),{href:"/",children:(0,s.jsxs)(l.$,{variant:"outline",className:"flex items-center gap-2 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 text-blue-700 hover:from-blue-100 hover:to-indigo-100",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),"الصفحة الرئيسية"]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,s.jsx)(a.Zp,{className:"bg-gradient-to-r from-green-500 to-emerald-600 border-0 text-white shadow-xl",children:(0,s.jsx)(a.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-green-100 text-sm font-medium",children:"إجمالي المبيعات"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:"2,450,000 د.ع"})]}),(0,s.jsx)(c.A,{className:"h-8 w-8 text-green-100"})]})})}),(0,s.jsx)(a.Zp,{className:"bg-gradient-to-r from-blue-500 to-cyan-600 border-0 text-white shadow-xl",children:(0,s.jsx)(a.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-blue-100 text-sm font-medium",children:"عمولات المندوبين"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:"245,000 د.ع"})]}),(0,s.jsx)(m,{className:"h-8 w-8 text-blue-100"})]})})}),(0,s.jsx)(a.Zp,{className:"bg-gradient-to-r from-purple-500 to-pink-600 border-0 text-white shadow-xl",children:(0,s.jsx)(a.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-purple-100 text-sm font-medium",children:"صافي الأرباح"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:"2,205,000 د.ع"})]}),(0,s.jsx)(x,{className:"h-8 w-8 text-purple-100"})]})})}),(0,s.jsx)(a.Zp,{className:"bg-gradient-to-r from-orange-500 to-red-600 border-0 text-white shadow-xl",children:(0,s.jsx)(a.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-orange-100 text-sm font-medium",children:"طلبات محاسبة"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:"156"})]}),(0,s.jsx)(g,{className:"h-8 w-8 text-orange-100"})]})})})]}),(0,s.jsxs)("div",{className:"flex space-x-1 bg-white/50 backdrop-blur-sm p-1 rounded-xl border border-white/20 shadow-lg",children:[(0,s.jsx)("button",{onClick:()=>h("couriers"),className:"flex-1 py-3 px-6 rounded-lg font-medium transition-all duration-200 ".concat("couriers"===r?"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:text-gray-800 hover:bg-white/50"),children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-5 w-5"}),"محاسبة المندوبين"]})}),(0,s.jsx)("button",{onClick:()=>h("customers"),className:"flex-1 py-3 px-6 rounded-lg font-medium transition-all duration-200 ".concat("customers"===r?"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:text-gray-800 hover:bg-white/50"),children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(u.A,{className:"h-5 w-5"}),"محاسبة العملاء"]})}),(0,s.jsx)("button",{onClick:()=>h("archive"),className:"flex-1 py-3 px-6 rounded-lg font-medium transition-all duration-200 ".concat("archive"===r?"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:text-gray-800 hover:bg-white/50"),children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(b.A,{className:"h-5 w-5"}),"أرشيف الحسابات"]})})]}),"couriers"===r&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(a.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-xl",children:[(0,s.jsxs)(a.aR,{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-t-lg",children:[(0,s.jsxs)(a.ZB,{className:"flex items-center gap-2 text-gray-800",children:[(0,s.jsx)("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg",children:(0,s.jsx)(u.A,{className:"h-5 w-5 text-white"})}),"اختيار المندوب"]}),(0,s.jsx)(a.BT,{className:"text-gray-600",children:"اختر المندوب لعرض طلباته المسلمة والمستحقة للمحاسبة"})]}),(0,s.jsx)(a.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(f.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsxs)("select",{value:y,onChange:e=>A(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-200 rounded-xl bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-700",children:[(0,s.jsx)("option",{value:"",children:"اختر مندوب للمحاسبة"}),I.map(e=>(0,s.jsxs)("option",{value:e.id,children:[e.name," - ",e.phone]},e.id))]})]}),y&&(0,s.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-blue-50 rounded-lg",children:[(0,s.jsx)(j.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsxs)("span",{className:"text-blue-800 font-medium",children:["تم اختيار: ",null==(e=I.find(e=>e.id===y))?void 0:e.name]})]})]})})]}),y&&_.length>0&&(0,s.jsxs)(a.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-xl",children:[(0,s.jsx)(a.aR,{className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(a.ZB,{className:"flex items-center gap-2 text-gray-800",children:[(0,s.jsx)("div",{className:"p-2 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg",children:(0,s.jsx)(N.A,{className:"h-5 w-5 text-white"})}),"الطلبات المسلمة (",_.length,")"]}),(0,s.jsx)(l.$,{onClick:()=>{S.length===_.length?L([]):L(_.map(e=>e.id))},variant:"outline",size:"sm",className:"bg-white/50",children:S.length===_.length?"إلغاء تحديد الكل":"تحديد الكل"})]})}),(0,s.jsxs)(a.Wu,{className:"p-6",children:[(0,s.jsx)("div",{className:"space-y-3",children:_.map(e=>(0,s.jsx)("div",{onClick:()=>z(e.id),className:"p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ".concat(S.includes(e.id)?"border-blue-500 bg-blue-50 shadow-md":"border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm"),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"w-5 h-5 rounded-full border-2 flex items-center justify-center ".concat(S.includes(e.id)?"border-blue-500 bg-blue-500":"border-gray-300"),children:S.includes(e.id)&&(0,s.jsx)(j.A,{className:"h-3 w-3 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-semibold text-gray-800",children:e.trackingNumber}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.recipientName})]})]}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsxs)("p",{className:"font-bold text-green-600",children:[e.amount.toLocaleString()," د.ع"]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.deliveredAt.toLocaleDateString("ar-IQ")})]})]})},e.id))}),S.length>0&&(0,s.jsxs)("div",{className:"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:"ملخص المحاسبة"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"عدد الطلبات:"}),(0,s.jsx)("span",{className:"font-medium",children:S.length})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"إجمالي المبلغ:"}),(0,s.jsxs)("span",{className:"font-medium",children:[$.toLocaleString()," د.ع"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("span",{children:["العمولة (",S.length," \xd7 1000):"]}),(0,s.jsxs)("span",{className:"font-medium text-red-600",children:["-",q.toLocaleString()," د.ع"]})]}),(0,s.jsx)("hr",{className:"border-gray-300"}),(0,s.jsxs)("div",{className:"flex justify-between text-lg font-bold",children:[(0,s.jsx)("span",{children:"صافي المبلغ:"}),(0,s.jsxs)("span",{className:"text-green-600",children:[H.toLocaleString()," د.ع"]})]})]}),(0,s.jsxs)("div",{className:"flex gap-3 mt-4",children:[(0,s.jsxs)(l.$,{onClick:()=>{var e;let t='\n      <html dir="rtl">\n        <head>\n          <title>كشف حساب المندوب</title>\n          <style>\n            body { font-family: Arial, sans-serif; direction: rtl; }\n            .header { text-align: center; margin-bottom: 20px; }\n            .table { width: 100%; border-collapse: collapse; }\n            .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: right; }\n            .table th { background-color: #f2f2f2; }\n            .total { font-weight: bold; background-color: #e8f4fd; }\n          </style>\n        </head>\n        <body>\n          <div class="header">\n            <h1>كشف حساب المندوب</h1>\n            <p>التاريخ: '.concat(new Date().toLocaleDateString("ar-IQ"),"</p>\n            <p>المندوب: ").concat("all"===y?"جميع المندوبين":null==(e=I.find(e=>e.id===y))?void 0:e.name,'</p>\n          </div>\n          <table class="table">\n            <thead>\n              <tr>\n                <th>رقم الوصل</th>\n                <th>اسم المستلم</th>\n                <th>المبلغ</th>\n                <th>العمولة</th>\n                <th>الصافي</th>\n              </tr>\n            </thead>\n            <tbody>\n              ').concat(W.map(e=>"\n                <tr>\n                  <td>".concat(e.trackingNumber,"</td>\n                  <td>").concat(e.recipientName,"</td>\n                  <td>").concat(e.amount.toLocaleString()," د.ع</td>\n                  <td>1,000 د.ع</td>\n                  <td>").concat((e.amount-1e3).toLocaleString()," د.ع</td>\n                </tr>\n              ")).join(""),'\n              <tr class="total">\n                <td colspan="2">المجموع</td>\n                <td>').concat($.toLocaleString()," د.ع</td>\n                <td>").concat(q.toLocaleString()," د.ع</td>\n                <td>").concat(H.toLocaleString()," د.ع</td>\n              </tr>\n            </tbody>\n          </table>\n        </body>\n      </html>\n    "),r=window.open("","_blank");r&&(r.document.write(t),r.document.close(),r.print())},variant:"outline",className:"flex-1 flex items-center gap-2",children:[(0,s.jsx)(v.A,{className:"h-4 w-4"}),"طباعة الكشف"]}),(0,s.jsx)(l.$,{onClick:()=>M(!0),className:"flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700",children:"إنهاء المحاسبة"})]})]})]})]})]}),"archive"===r&&(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)(a.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-xl",children:[(0,s.jsxs)(a.aR,{className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg",children:[(0,s.jsxs)(a.ZB,{className:"flex items-center gap-2 text-gray-800",children:[(0,s.jsx)(b.A,{className:"h-6 w-6 text-green-600"}),"أرشيف الحسابات"]}),(0,s.jsx)(a.BT,{children:"اختر المندوب لعرض وطباعة كشف حسابه من الأرشيف"})]}),(0,s.jsx)(a.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اختيار المندوب"}),(0,s.jsxs)("select",{value:Z,onChange:e=>B(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"",children:"اختر المندوب"}),I.map(e=>(0,s.jsxs)("option",{value:e.id,children:[e.name," - ",e.phone]},e.id))]})]}),Z&&(0,s.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold",children:["كشف حساب: ",null==(t=I.find(e=>e.id===Z))?void 0:t.name]}),(0,s.jsxs)(l.$,{onClick:()=>D(Z),className:"flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700",children:[(0,s.jsx)(v.A,{className:"h-4 w-4"}),"طباعة الكشف"]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:(()=>{let e=R.filter(e=>e.courierId===Z),t=e.reduce((e,t)=>e+t.amount,0),r=1e3*e.length,n=t-r;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg text-center",children:[(0,s.jsx)("p",{className:"text-sm text-blue-600",children:"عدد الطلبات"}),(0,s.jsx)("p",{className:"text-xl font-bold text-blue-800",children:e.length})]}),(0,s.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg text-center",children:[(0,s.jsx)("p",{className:"text-sm text-green-600",children:"إجمالي المبالغ"}),(0,s.jsxs)("p",{className:"text-xl font-bold text-green-800",children:[t.toLocaleString()," د.ع"]})]}),(0,s.jsxs)("div",{className:"bg-orange-50 p-3 rounded-lg text-center",children:[(0,s.jsx)("p",{className:"text-sm text-orange-600",children:"العمولات"}),(0,s.jsxs)("p",{className:"text-xl font-bold text-orange-800",children:[r.toLocaleString()," د.ع"]})]}),(0,s.jsxs)("div",{className:"bg-purple-50 p-3 rounded-lg text-center",children:[(0,s.jsx)("p",{className:"text-sm text-purple-600",children:"الصافي للشركة"}),(0,s.jsxs)("p",{className:"text-xl font-bold text-purple-800",children:[n.toLocaleString()," د.ع"]})]})]})})()})]})]})})]})}),"customers"===r&&(0,s.jsx)(a.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-xl",children:(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"محاسبة العملاء"}),(0,s.jsx)(a.BT,{children:"قريباً..."})]})})]}),C&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,s.jsxs)(a.Zp,{className:"w-full max-w-md bg-white shadow-2xl",children:[(0,s.jsx)(a.aR,{children:(0,s.jsx)(a.ZB,{className:"text-center",children:"تأكيد المحاسبة"})}),(0,s.jsxs)(a.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)("p",{children:"هل أنت متأكد من إنهاء محاسبة هذه الطلبات؟"}),(0,s.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"عدد الطلبات:"})," ",S.length]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"صافي المبلغ:"})," ",H.toLocaleString()," د.ع"]})]})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)(l.$,{onClick:()=>M(!1),variant:"outline",className:"flex-1",children:"إلغاء"}),(0,s.jsx)(l.$,{onClick:()=>{M(!1),L([]),alert("تم إنهاء المحاسبة بنجاح!")},className:"flex-1 bg-green-600 hover:bg-green-700",children:"تأكيد"})]})]})]})})]})]})}},4044:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(5155),n=r(2044),a=r(9599),l=r(5695),i=r(2115),d=r(6695),o=r(285),c=r(1243),h=r(2138);function m(e){let{children:t,requiredSection:r,fallbackPath:m="/"}=e,{user:x,isAuthenticated:g}=(0,n.A)(),p=(0,l.useRouter)();return((0,i.useEffect)(()=>{if(!g)return void p.push("/login");(null==x?void 0:x.role)&&!(0,a._m)(x.role,r)&&p.push(m)},[x,g,r,m,p]),g&&x)?(0,a._m)(x.role,r)?(0,s.jsx)(s.Fragment,{children:t}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center p-4",dir:"rtl",children:(0,s.jsxs)(d.Zp,{className:"max-w-md w-full shadow-xl border-2 border-red-200",children:[(0,s.jsxs)(d.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-full shadow-lg mb-4 mx-auto",children:(0,s.jsx)(c.A,{className:"h-8 w-8 text-white"})}),(0,s.jsx)(d.ZB,{className:"text-2xl text-red-600",children:"غير مصرح بالدخول"}),(0,s.jsx)(d.BT,{className:"text-lg text-red-500",children:"ليس لديك صلاحية للوصول إلى هذا القسم"})]}),(0,s.jsxs)(d.Wu,{className:"text-center space-y-4",children:[(0,s.jsxs)("p",{className:"text-gray-600 leading-relaxed",children:["عذراً، دورك الحالي (","manager"===x.role?"مدير":"supervisor"===x.role?"متابع":"مندوب",") لا يسمح بالوصول إلى هذا القسم."]}),(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-sm text-yellow-800",children:"إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع المدير لمراجعة صلاحياتك."})}),(0,s.jsxs)(o.$,{onClick:()=>p.push(m),className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:["العودة إلى الصفحة الرئيسية",(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"})]})]})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse",children:(0,s.jsx)("div",{className:"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"مرسال"}),(0,s.jsx)("p",{className:"text-gray-500",children:"جاري التحقق من الصلاحيات..."})]})})}},7434:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7924:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9599:(e,t,r)=>{"use strict";r.d(t,{BC:()=>l,_m:()=>n});let s={manager:["orders","dispatch","returns","accounting","statistics","archive","users","import-export","notifications","settings"],supervisor:["orders","statistics","archive","users","notifications","settings"],courier:["orders","archive","notifications","statistics"]};function n(e,t){var r;return(null==(r=s[e])?void 0:r.includes(t))||!1}let a=[{id:"orders",href:"/orders",label:"إدارة الطلبات",description:"عرض ومتابعة وتعديل الطلبات",icon:"Package",color:"from-blue-500 to-blue-600",roles:["manager","supervisor","courier"]},{id:"dispatch",href:"/dispatch",label:"إسناد الطلبات",description:"توزيع الطلبات على المندوبين",icon:"TruckIcon",color:"from-green-500 to-emerald-600",roles:["manager"]},{id:"returns",href:"/returns",label:"إدارة الرواجع",description:"استلام الطلبات الراجعة",icon:"RotateCcw",color:"from-orange-500 to-amber-600",roles:["manager"]},{id:"accounting",href:"/accounting",label:"المحاسبة",description:"التسويات المالية مع المندوبين",icon:"Calculator",color:"from-purple-500 to-violet-600",roles:["manager"]},{id:"statistics",href:"/statistics",label:"الإحصائيات",description:"التقارير والإحصائيات التفصيلية",icon:"BarChart3",color:"from-cyan-500 to-blue-600",roles:["manager","supervisor"]},{id:"archive",href:"/archive",label:"الأرشيف",description:"السجلات المنتهية",icon:"Archive",color:"from-gray-500 to-slate-600",roles:["manager","supervisor","courier"]},{id:"users",href:"/users",label:"إدارة الموظفين",description:"إدارة حسابات المستخدمين",icon:"Users",color:"from-indigo-500 to-purple-600",roles:["manager","supervisor"]},{id:"import-export",href:"/import-export",label:"استيراد وتصدير",description:"عمليات مجمعة على الطلبات",icon:"Upload",color:"from-teal-500 to-cyan-600",roles:["manager"]},{id:"notifications",href:"/notifications",label:"الإشعارات",description:"التنبيهات والتحديثات",icon:"Bell",color:"from-yellow-500 to-orange-600",roles:["manager","supervisor","courier"]},{id:"settings",href:"/settings",label:"الإعدادات",description:"تخصيص التطبيق",icon:"Settings",color:"from-pink-500 to-rose-600",roles:["manager","supervisor","courier"]}];function l(e){return a.filter(t=>t.roles.includes(e))}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,811,455,134,568,874,647,533,345,44,192,441,684,358],()=>t(1482)),_N_E=e.O()}]);