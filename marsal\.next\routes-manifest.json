{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [{"page": "/orders/[id]", "regex": "^/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/orders/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/orders/[id]/edit", "regex": "^/orders/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/orders/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/orders/[id]/status", "regex": "^/orders/([^/]+?)/status(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/orders/(?<nxtPid>[^/]+?)/status(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/accounting", "regex": "^/accounting(?:/)?$", "routeKeys": {}, "namedRegex": "^/accounting(?:/)?$"}, {"page": "/archive", "regex": "^/archive(?:/)?$", "routeKeys": {}, "namedRegex": "^/archive(?:/)?$"}, {"page": "/dispatch", "regex": "^/dispatch(?:/)?$", "routeKeys": {}, "namedRegex": "^/dispatch(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/firebase-login", "regex": "^/firebase\\-login(?:/)?$", "routeKeys": {}, "namedRegex": "^/firebase\\-login(?:/)?$"}, {"page": "/firebase-setup", "regex": "^/firebase\\-setup(?:/)?$", "routeKeys": {}, "namedRegex": "^/firebase\\-setup(?:/)?$"}, {"page": "/import-export", "regex": "^/import\\-export(?:/)?$", "routeKeys": {}, "namedRegex": "^/import\\-export(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/notifications", "regex": "^/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/notifications(?:/)?$"}, {"page": "/orders", "regex": "^/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/orders(?:/)?$"}, {"page": "/orders/new", "regex": "^/orders/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/orders/new(?:/)?$"}, {"page": "/returns", "regex": "^/returns(?:/)?$", "routeKeys": {}, "namedRegex": "^/returns(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/statistics", "regex": "^/statistics(?:/)?$", "routeKeys": {}, "namedRegex": "^/statistics(?:/)?$"}, {"page": "/users", "regex": "^/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/users(?:/)?$"}, {"page": "/users/new", "regex": "^/users/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/new(?:/)?$"}, {"page": "/users-management", "regex": "^/users\\-management(?:/)?$", "routeKeys": {}, "namedRegex": "^/users\\-management(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}