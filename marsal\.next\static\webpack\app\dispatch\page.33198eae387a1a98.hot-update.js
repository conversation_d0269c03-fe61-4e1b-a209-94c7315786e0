"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dispatch/page",{

/***/ "(app-pages-browser)/./src/lib/firestore.ts":
/*!******************************!*\
  !*** ./src/lib/firestore.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ordersService: () => (/* binding */ ordersService),\n/* harmony export */   settlementsService: () => (/* binding */ settlementsService),\n/* harmony export */   usersService: () => (/* binding */ usersService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _mock_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mock-data */ \"(app-pages-browser)/./src/lib/mock-data.ts\");\n// Updated to use Supabase instead of Firebase\n\n\n\n// Orders Service - Updated to use Supabase\nconst ordersService = {\n    // Create new order\n    async create (orderData) {\n        const trackingNumber = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.generateTrackingNumber)();\n        const supabaseOrderData = {\n            tracking_number: trackingNumber,\n            customer_name: orderData.customerName,\n            customer_phone: orderData.customerPhone,\n            address: orderData.address,\n            amount: orderData.amount,\n            status: 'pending',\n            courier_name: orderData.courierName,\n            courier_id: orderData.courierId,\n            notes: orderData.notes\n        };\n        const order = await _supabase__WEBPACK_IMPORTED_MODULE_0__.orderService.createOrder(supabaseOrderData);\n        // Convert back to frontend format\n        return {\n            id: order.id,\n            trackingNumber: order.tracking_number,\n            customerName: order.customer_name,\n            customerPhone: order.customer_phone,\n            address: order.address,\n            amount: order.amount,\n            status: order.status,\n            courierName: order.courier_name,\n            courierId: order.courier_id,\n            notes: order.notes,\n            createdAt: new Date(order.created_at),\n            updatedAt: new Date(order.updated_at),\n            statusHistory: [\n                {\n                    status: 'pending',\n                    timestamp: new Date(order.created_at),\n                    updatedBy: 'system',\n                    notes: 'تم إنشاء الطلب'\n                }\n            ]\n        };\n    },\n    // Get all orders with pagination\n    async getAll () {\n        let pageSize = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, lastDoc = arguments.length > 1 ? arguments[1] : void 0;\n        try {\n            const supabaseOrders = await _supabase__WEBPACK_IMPORTED_MODULE_0__.orderService.getAllOrders();\n            // Convert from Supabase format to frontend format\n            const orders = supabaseOrders.map((order)=>({\n                    id: order.id,\n                    trackingNumber: order.tracking_number,\n                    customerName: order.customer_name,\n                    customerPhone: order.customer_phone,\n                    address: order.address,\n                    amount: order.amount,\n                    status: order.status,\n                    courierName: order.courier_name,\n                    courierId: order.courier_id,\n                    notes: order.notes,\n                    createdAt: new Date(order.created_at),\n                    updatedAt: new Date(order.updated_at),\n                    deliveredAt: order.delivered_at ? new Date(order.delivered_at) : undefined,\n                    statusHistory: [\n                        {\n                            status: order.status,\n                            timestamp: new Date(order.updated_at),\n                            updatedBy: 'system',\n                            notes: order.notes || ''\n                        }\n                    ]\n                }));\n            // Simple pagination simulation\n            const startIndex = lastDoc ? parseInt(lastDoc) : 0;\n            const endIndex = startIndex + pageSize;\n            const paginatedOrders = orders.slice(startIndex, endIndex);\n            return {\n                orders: paginatedOrders,\n                lastDoc: endIndex.toString(),\n                hasMore: endIndex < orders.length\n            };\n        } catch (error) {\n            console.warn('Supabase not available, using mock data:', error);\n            return {\n                orders: _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockOrders,\n                lastDoc: null,\n                hasMore: false\n            };\n        }\n    },\n    // Get order by ID\n    async getById (id) {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('orders').select('*').eq('id', id).single();\n            if (error || !data) {\n                throw new Error('Order not found');\n            }\n            return {\n                id: data.id,\n                trackingNumber: data.tracking_number,\n                customerName: data.customer_name,\n                customerPhone: data.customer_phone,\n                address: data.address,\n                amount: data.amount,\n                status: data.status,\n                courierName: data.courier_name,\n                courierId: data.courier_id,\n                notes: data.notes,\n                createdAt: new Date(data.created_at),\n                updatedAt: new Date(data.updated_at),\n                deliveredAt: data.delivered_at ? new Date(data.delivered_at) : undefined,\n                statusHistory: [\n                    {\n                        status: data.status,\n                        timestamp: new Date(data.updated_at),\n                        updatedBy: 'system',\n                        notes: data.notes || ''\n                    }\n                ]\n            };\n        } catch (error) {\n            console.error('Error getting order by ID:', error);\n            throw new Error('Order not found');\n        }\n    },\n    // Search orders\n    async search (searchTerm) {\n        try {\n            const supabaseOrders = await _supabase__WEBPACK_IMPORTED_MODULE_0__.orderService.searchOrders(searchTerm);\n            return supabaseOrders.map((order)=>({\n                    id: order.id,\n                    trackingNumber: order.tracking_number,\n                    customerName: order.customer_name,\n                    customerPhone: order.customer_phone,\n                    address: order.address,\n                    amount: order.amount,\n                    status: order.status,\n                    courierName: order.courier_name,\n                    courierId: order.courier_id,\n                    notes: order.notes,\n                    createdAt: new Date(order.created_at),\n                    updatedAt: new Date(order.updated_at),\n                    deliveredAt: order.delivered_at ? new Date(order.delivered_at) : undefined,\n                    statusHistory: [\n                        {\n                            status: order.status,\n                            timestamp: new Date(order.updated_at),\n                            updatedBy: 'system',\n                            notes: order.notes || ''\n                        }\n                    ]\n                }));\n        } catch (error) {\n            console.warn('Search failed, using mock data:', error);\n            return _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockOrders.filter((order)=>order.trackingNumber.includes(searchTerm) || order.customerName.includes(searchTerm) || order.customerPhone.includes(searchTerm));\n        }\n        const orders = new Map();\n        results.forEach((snapshot)=>{\n            snapshot.docs.forEach((doc1)=>{\n                if (!orders.has(doc1.id)) {\n                    var _data_createdAt, _data_updatedAt, _data_deliveredAt, _data_returnedAt, _data_statusHistory;\n                    const data = doc1.data();\n                    orders.set(doc1.id, {\n                        id: doc1.id,\n                        ...data,\n                        createdAt: (_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate(),\n                        updatedAt: (_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate(),\n                        deliveredAt: (_data_deliveredAt = data.deliveredAt) === null || _data_deliveredAt === void 0 ? void 0 : _data_deliveredAt.toDate(),\n                        returnedAt: (_data_returnedAt = data.returnedAt) === null || _data_returnedAt === void 0 ? void 0 : _data_returnedAt.toDate(),\n                        statusHistory: (_data_statusHistory = data.statusHistory) === null || _data_statusHistory === void 0 ? void 0 : _data_statusHistory.map((update)=>{\n                            var _update_timestamp;\n                            return {\n                                ...update,\n                                timestamp: (_update_timestamp = update.timestamp) === null || _update_timestamp === void 0 ? void 0 : _update_timestamp.toDate()\n                            };\n                        })\n                    });\n                }\n            });\n        });\n        return Array.from(orders.values());\n    },\n    // Update order status\n    async updateStatus (orderId, status, notes, updatedBy, image) {\n        const orderRef = doc(db, COLLECTIONS.ORDERS, orderId);\n        const now = Timestamp.now();\n        const statusUpdate = {\n            status: status,\n            timestamp: now.toDate(),\n            updatedBy,\n            notes,\n            image\n        };\n        const updateData = {\n            status,\n            updatedAt: now,\n            [\"statusHistory\"]: [\n                ...(await this.getById(orderId)).statusHistory,\n                statusUpdate\n            ]\n        };\n        if (status === 'delivered') {\n            updateData.deliveredAt = now;\n        } else if (status === 'returned') {\n            updateData.returnedAt = now;\n        }\n        await updateDoc(orderRef, updateData);\n        return statusUpdate;\n    },\n    // Get orders by status\n    async getByStatus (status) {\n        const q = query(collection(db, COLLECTIONS.ORDERS), where('status', '==', status), orderBy('createdAt', 'desc'));\n        const snapshot = await getDocs(q);\n        return snapshot.docs.map((doc1)=>{\n            var _doc_data_createdAt, _doc_data_updatedAt, _doc_data_deliveredAt, _doc_data_returnedAt, _doc_data_statusHistory;\n            return {\n                id: doc1.id,\n                ...doc1.data(),\n                createdAt: (_doc_data_createdAt = doc1.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate(),\n                updatedAt: (_doc_data_updatedAt = doc1.data().updatedAt) === null || _doc_data_updatedAt === void 0 ? void 0 : _doc_data_updatedAt.toDate(),\n                deliveredAt: (_doc_data_deliveredAt = doc1.data().deliveredAt) === null || _doc_data_deliveredAt === void 0 ? void 0 : _doc_data_deliveredAt.toDate(),\n                returnedAt: (_doc_data_returnedAt = doc1.data().returnedAt) === null || _doc_data_returnedAt === void 0 ? void 0 : _doc_data_returnedAt.toDate(),\n                statusHistory: (_doc_data_statusHistory = doc1.data().statusHistory) === null || _doc_data_statusHistory === void 0 ? void 0 : _doc_data_statusHistory.map((update)=>{\n                    var _update_timestamp;\n                    return {\n                        ...update,\n                        timestamp: (_update_timestamp = update.timestamp) === null || _update_timestamp === void 0 ? void 0 : _update_timestamp.toDate()\n                    };\n                })\n            };\n        });\n    },\n    // Get orders by courier\n    async getByCourier (courierId) {\n        const q = query(collection(db, COLLECTIONS.ORDERS), where('assignedTo', '==', courierId), orderBy('createdAt', 'desc'));\n        const snapshot = await getDocs(q);\n        return snapshot.docs.map((doc1)=>{\n            var _doc_data_createdAt, _doc_data_updatedAt, _doc_data_deliveredAt, _doc_data_returnedAt, _doc_data_statusHistory;\n            return {\n                id: doc1.id,\n                ...doc1.data(),\n                createdAt: (_doc_data_createdAt = doc1.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate(),\n                updatedAt: (_doc_data_updatedAt = doc1.data().updatedAt) === null || _doc_data_updatedAt === void 0 ? void 0 : _doc_data_updatedAt.toDate(),\n                deliveredAt: (_doc_data_deliveredAt = doc1.data().deliveredAt) === null || _doc_data_deliveredAt === void 0 ? void 0 : _doc_data_deliveredAt.toDate(),\n                returnedAt: (_doc_data_returnedAt = doc1.data().returnedAt) === null || _doc_data_returnedAt === void 0 ? void 0 : _doc_data_returnedAt.toDate(),\n                statusHistory: (_doc_data_statusHistory = doc1.data().statusHistory) === null || _doc_data_statusHistory === void 0 ? void 0 : _doc_data_statusHistory.map((update)=>{\n                    var _update_timestamp;\n                    return {\n                        ...update,\n                        timestamp: (_update_timestamp = update.timestamp) === null || _update_timestamp === void 0 ? void 0 : _update_timestamp.toDate()\n                    };\n                })\n            };\n        });\n    },\n    // Assign orders to courier\n    async assignToCourier (orderIds, courierId, assignedBy) {\n        const batch = writeBatch(db);\n        const now = Timestamp.now();\n        for (const orderId of orderIds){\n            const orderRef = doc(db, COLLECTIONS.ORDERS, orderId);\n            const order = await this.getById(orderId);\n            const statusUpdate = {\n                status: 'assigned',\n                timestamp: now.toDate(),\n                updatedBy: assignedBy,\n                notes: \"تم إسناد الطلب للمندوب\"\n            };\n            batch.update(orderRef, {\n                assignedTo: courierId,\n                status: 'assigned',\n                updatedAt: now,\n                statusHistory: [\n                    ...order.statusHistory,\n                    statusUpdate\n                ]\n            });\n        }\n        await batch.commit();\n    },\n    // Delete order\n    async delete (id) {\n        await deleteDoc(doc(db, COLLECTIONS.ORDERS, id));\n    }\n};\n// Users Service - Updated to use Supabase\nconst usersService = {\n    // Create user\n    async create (userData) {\n        try {\n            const supabaseUserData = {\n                username: userData.username,\n                name: userData.name,\n                phone: userData.phone,\n                role: userData.role,\n                password_hash: '$2b$10$example_hash_for_123456',\n                is_active: true,\n                created_by: userData.createdBy || 'system'\n            };\n            const user = await _supabase__WEBPACK_IMPORTED_MODULE_0__.userService.createUser(supabaseUserData);\n            return {\n                id: user.id,\n                username: user.username,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                isActive: user.is_active,\n                createdAt: new Date(user.created_at),\n                updatedAt: new Date(user.created_at),\n                createdBy: user.created_by\n            };\n        } catch (error) {\n            console.error('Error creating user:', error);\n            throw error;\n        }\n    },\n    // Get all users\n    async getAll () {\n        try {\n            const supabaseUsers = await _supabase__WEBPACK_IMPORTED_MODULE_0__.userService.getAllUsers();\n            return supabaseUsers.map((user)=>({\n                    id: user.id,\n                    username: user.username,\n                    name: user.name,\n                    phone: user.phone,\n                    role: user.role,\n                    isActive: user.is_active,\n                    createdAt: new Date(user.created_at),\n                    updatedAt: new Date(user.created_at),\n                    createdBy: user.created_by\n                }));\n        } catch (error) {\n            console.warn('Error getting users, using mock data:', error);\n            return _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockUsers;\n        }\n    },\n    // Get couriers only\n    async getCouriers () {\n        try {\n            const couriers = await _supabase__WEBPACK_IMPORTED_MODULE_0__.userService.getUsersByRole('courier');\n            return couriers.filter((user)=>user.is_active).map((user)=>({\n                    id: user.id,\n                    username: user.username,\n                    name: user.name,\n                    phone: user.phone,\n                    role: user.role,\n                    isActive: user.is_active,\n                    createdAt: new Date(user.created_at),\n                    updatedAt: new Date(user.created_at),\n                    createdBy: user.created_by\n                }));\n        } catch (error) {\n            console.warn('Error getting couriers, using mock data:', error);\n            return _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockUsers.filter((user)=>user.role === 'courier' && user.isActive);\n        }\n    },\n    // Get user by ID\n    async getById (id) {\n        var _data_createdAt, _data_updatedAt;\n        const docRef = doc(db, COLLECTIONS.USERS, id);\n        const docSnap = await getDoc(docRef);\n        if (!docSnap.exists()) {\n            throw new Error('User not found');\n        }\n        const data = docSnap.data();\n        return {\n            id: docSnap.id,\n            ...data,\n            createdAt: (_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate(),\n            updatedAt: (_data_updatedAt = data.updatedAt) === null || _data_updatedAt === void 0 ? void 0 : _data_updatedAt.toDate()\n        };\n    },\n    // Get user by ID - محدث لاستخدام Supabase والنظام الاحتياطي\n    async getUserById (id) {\n        try {\n            // محاولة استخدام Supabase أولاً\n            try {\n                const { supabase } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\"));\n                const { data, error } = await supabase.from('users').select('*').eq('id', id).single();\n                if (error && error.code !== 'PGRST116') {\n                    throw new Error(\"Supabase error: \".concat(error.message));\n                }\n                if (data) {\n                    return {\n                        id: data.id,\n                        email: data.email || '',\n                        name: data.name,\n                        username: data.username,\n                        phone: data.phone,\n                        role: data.role,\n                        isActive: data.is_active,\n                        createdAt: new Date(data.created_at),\n                        updatedAt: new Date(data.updated_at || data.created_at)\n                    };\n                }\n            } catch (supabaseError) {\n                console.warn('⚠️ فشل في الحصول على المستخدم من Supabase، استخدام النظام الاحتياطي:', supabaseError);\n            }\n            // استخدام البيانات التجريبية كنظام احتياطي\n            const { mockUsers } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./mock-data */ \"(app-pages-browser)/./src/lib/mock-data.ts\"));\n            const user = mockUsers.find((u)=>u.id === id);\n            return user || null;\n        } catch (error) {\n            console.error('❌ خطأ في الحصول على المستخدم:', error);\n            return null;\n        }\n    },\n    // Update user\n    async update (id, userData) {\n        const userRef = doc(db, COLLECTIONS.USERS, id);\n        await updateDoc(userRef, {\n            ...userData,\n            updatedAt: Timestamp.now()\n        });\n    },\n    // Delete user\n    async delete (id) {\n        await deleteDoc(doc(db, COLLECTIONS.USERS, id));\n    }\n};\n// Settlements Service\nconst settlementsService = {\n    // Create settlement\n    async create (settlementData) {\n        const now = Timestamp.now();\n        const settlement = {\n            ...settlementData,\n            createdAt: now.toDate(),\n            isSettled: false\n        };\n        const docRef = await addDoc(collection(db, COLLECTIONS.SETTLEMENTS), settlement);\n        return {\n            id: docRef.id,\n            ...settlement\n        };\n    },\n    // Get all settlements\n    async getAll () {\n        const q = query(collection(db, COLLECTIONS.SETTLEMENTS), orderBy('createdAt', 'desc'));\n        const snapshot = await getDocs(q);\n        return snapshot.docs.map((doc1)=>{\n            var _doc_data_createdAt, _doc_data_settledAt;\n            return {\n                id: doc1.id,\n                ...doc1.data(),\n                createdAt: (_doc_data_createdAt = doc1.data().createdAt) === null || _doc_data_createdAt === void 0 ? void 0 : _doc_data_createdAt.toDate(),\n                settledAt: (_doc_data_settledAt = doc1.data().settledAt) === null || _doc_data_settledAt === void 0 ? void 0 : _doc_data_settledAt.toDate()\n            };\n        });\n    },\n    // Mark settlement as settled\n    async markAsSettled (id) {\n        const settlementRef = doc(db, COLLECTIONS.SETTLEMENTS, id);\n        await updateDoc(settlementRef, {\n            isSettled: true,\n            settledAt: Timestamp.now()\n        });\n    },\n    // Real-time subscription to orders\n    subscribeToOrders (callback) {\n        try {\n            const q = query(collection(db, COLLECTIONS.ORDERS), orderBy('createdAt', 'desc'));\n            return onSnapshot(q, (snapshot)=>{\n                const orders = snapshot.docs.map((doc1)=>({\n                        id: doc1.id,\n                        ...doc1.data()\n                    }));\n                callback(orders);\n            });\n        } catch (error) {\n            console.warn('Firebase subscription not available:', error);\n            // Return a dummy unsubscribe function\n            return ()=>{};\n        }\n    },\n    // Real-time subscription to user's orders\n    subscribeToUserOrders (userId, callback) {\n        try {\n            const q = query(collection(db, COLLECTIONS.ORDERS), where('courierId', '==', userId), orderBy('createdAt', 'desc'));\n            return onSnapshot(q, (snapshot)=>{\n                const orders = snapshot.docs.map((doc1)=>({\n                        id: doc1.id,\n                        ...doc1.data()\n                    }));\n                callback(orders);\n            });\n        } catch (error) {\n            console.warn('Firebase subscription not available:', error);\n            return ()=>{};\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/firestore.ts\n"));

/***/ })

});