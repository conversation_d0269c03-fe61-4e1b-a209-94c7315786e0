"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { authService } from "@/lib/auth";
import { UserRole } from "@/types/roles";
import { Button } from "@/components/ui/button";
import Notifications from "@/components/notifications";
import {
  Home,
  Package,
  TruckIcon,
  RotateCcw,
  Calculator,
  Archive,
  Users,
  Upload,
  Bell,
  Settings,
  Menu,
  X,
  Sun,
  Moon,
  LogOut,
  User,
  ChevronDown,
  Palette,
  BarChart3,
  Warehouse,
  MessageSquare
} from "lucide-react";

// نظام التنقل حسب الأدوار
const getNavigationItems = (userRole: UserRole | null) => {
  if (!userRole) return [];

  const allItems = [
    { href: "/", label: "الرئيسية", icon: Home, section: "dashboard" },
    { href: "/orders", label: "إدارة الطلبات", icon: Package, section: "orders" },
    { href: "/dispatch", label: "إسناد الطلبات", icon: TruckIcon, section: "dispatch" },
    { href: "/returns", label: "إدارة الرواجع", icon: RotateCcw, section: "returns" },
    { href: "/accounting", label: "المحاسبة", icon: Calculator, section: "accounting" },
    { href: "/archive", label: "الأرشيف", icon: Archive, section: "archive" },
    { href: "/users", label: "إدارة الموظفين", icon: Users, section: "users" },
    { href: "/statistics", label: "الإحصائيات", icon: BarChart3, section: "statistics" },
    { href: "/warehouse", label: "المخزن", icon: Warehouse, section: "warehouse" },
    { href: "/import-export", label: "استيراد/تصدير", icon: Upload, section: "import-export" },
    { href: "/tickets", label: "التذاكر", icon: MessageSquare, section: "tickets" },
    { href: "/settings", label: "الإعدادات", icon: Settings, section: "settings" }
  ];

  // تصفية العناصر حسب الصلاحيات
  const accessibleSections = authService.getAccessibleSections();
  return allItems.filter(item => accessibleSections.includes(item.section));
};

export default function Header() {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  const router = useRouter();
  const [user, setUser] = useState(authService.getCurrentUser());

  useEffect(() => {
    const listener = (newUser: any) => setUser(newUser);
    authService.addListener(listener);
    return () => authService.removeListener(listener);
  }, []);

  const handleLogout = async () => {
    await authService.logout();
    router.push('/firebase-login');
  };

  const navigationItems = getNavigationItems(user?.role || null);

  // Don't show header on login page
  if (pathname === '/login') {
    return null;
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
              <Package className="h-6 w-6 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              مرسال
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {/* Home Button */}
            {pathname !== '/' && (
              <Link
                href="/"
                className="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 text-blue-700 hover:from-blue-100 hover:to-indigo-100"
              >
                <Home className="h-4 w-4" />
                <span>الرئيسية</span>
              </Link>
            )}

            {navigationItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                  }`}
                >
                  <item.icon className="h-4 w-4" />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setIsDarkMode(!isDarkMode);
                document.documentElement.classList.toggle('dark');
              }}
              className="hidden md:flex"
              title="تبديل الوضع"
            >
              {isDarkMode ? (
                <Sun className="h-4 w-4" />
              ) : (
                <Moon className="h-4 w-4" />
              )}
            </Button>

            {/* Notifications */}
            <Button
              variant="ghost"
              size="sm"
              className="hidden md:flex relative"
              onClick={() => setIsNotificationsOpen(true)}
            >
              <Bell className="h-4 w-4" />
              {/* Notification Badge */}
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                3
              </span>
            </Button>

            {/* User Menu */}
            {user && (
              <div className="relative">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2"
                >
                  <User className="h-4 w-4" />
                  <span className="hidden md:inline">{user.username}</span>
                  <ChevronDown className="h-3 w-3" />
                </Button>

                {/* User Dropdown */}
                {isUserMenuOpen && (
                  <div className="absolute left-0 mt-2 w-48 bg-background border rounded-lg shadow-lg py-1 z-50">
                    <div className="px-3 py-2 border-b">
                      <p className="text-sm font-medium">{user.username}</p>
                      <p className="text-xs text-muted-foreground">{user.role}</p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setIsDarkMode(!isDarkMode);
                        document.documentElement.classList.toggle('dark');
                      }}
                      className="w-full justify-start md:hidden"
                    >
                      {isDarkMode ? (
                        <>
                          <Sun className="h-4 w-4 ml-2" />
                          الوضع الفاتح
                        </>
                      ) : (
                        <>
                          <Moon className="h-4 w-4 ml-2" />
                          الوضع الداكن
                        </>
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleLogout}
                      className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <LogOut className="h-4 w-4 ml-2" />
                      تسجيل الخروج
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden"
            >
              {isMobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t py-4">
            <nav className="space-y-2">
              {navigationItems.map((item) => {
                const isActive = pathname === item.href;
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      isActive
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                    }`}
                  >
                    <item.icon className="h-4 w-4" />
                    <span>{item.label}</span>
                  </Link>
                );
              })}
            </nav>
          </div>
        )}
      </div>

      {/* Click outside to close user menu */}
      {isUserMenuOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsUserMenuOpen(false)}
        />
      )}

      {/* Notifications */}
      <Notifications
        isOpen={isNotificationsOpen}
        onClose={() => setIsNotificationsOpen(false)}
      />
    </header>
  );
}
