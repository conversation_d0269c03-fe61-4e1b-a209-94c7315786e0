"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-x.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleX)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m15 9-6 6\",\n            key: \"1uzhvr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 9 6 6\",\n            key: \"z0biqf\"\n        }\n    ]\n];\nconst CircleX = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-x\", __iconNode);\n //# sourceMappingURL=circle-x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/database.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Database)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"ellipse\",\n        {\n            cx: \"12\",\n            cy: \"5\",\n            rx: \"9\",\n            ry: \"3\",\n            key: \"msslwz\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 5V19A9 3 0 0 0 21 19V5\",\n            key: \"1wlel7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 12A9 3 0 0 0 21 12\",\n            key: \"mv7ke4\"\n        }\n    ]\n];\nconst Database = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"database\", __iconNode);\n //# sourceMappingURL=database.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/loader-circle.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ LoaderCircle)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 1 1-6.219-8.56\",\n            key: \"13zald\"\n        }\n    ]\n];\nconst LoaderCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"loader-circle\", __iconNode);\n //# sourceMappingURL=loader-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbG9hZGVyLWNpcmNsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxtQkFBdUI7SUFBQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsNkJBQStCO1lBQUEsSUFBSyxTQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhNUYsbUJBQWUsa0VBQWlCLGtCQUFpQixDQUFVIiwic291cmNlcyI6WyJFOlxcc3JjXFxpY29uc1xcbG9hZGVyLWNpcmNsZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwYXRoJywgeyBkOiAnTTIxIDEyYTkgOSAwIDEgMS02LjIxOS04LjU2Jywga2V5OiAnMTN6YWxkJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBMb2FkZXJDaXJjbGVcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1qRWdNVEpoT1NBNUlEQWdNU0F4TFRZdU1qRTVMVGd1TlRZaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2xvYWRlci1jaXJjbGVcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBMb2FkZXJDaXJjbGUgPSBjcmVhdGVMdWNpZGVJY29uKCdsb2FkZXItY2lyY2xlJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IExvYWRlckNpcmNsZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/header */ \"(app-pages-browser)/./src/components/header.tsx\");\n/* harmony import */ var _components_dashboard_stats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard-stats */ \"(app-pages-browser)/./src/components/dashboard-stats.tsx\");\n/* harmony import */ var _components_quick_search__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/quick-search */ \"(app-pages-browser)/./src/components/quick-search.tsx\");\n/* harmony import */ var _components_firebase_status__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/firebase-status */ \"(app-pages-browser)/./src/components/firebase-status.tsx\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth-provider */ \"(app-pages-browser)/./src/components/auth-provider.tsx\");\n/* harmony import */ var _lib_statistics__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/statistics */ \"(app-pages-browser)/./src/lib/statistics.ts\");\n/* harmony import */ var _lib_permissions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/permissions */ \"(app-pages-browser)/./src/lib/permissions.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Icon mapping\nconst iconMap = {\n    Package: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    TruckIcon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    RotateCcw: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    Calculator: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    BarChart3: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    Archive: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    Users: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    Upload: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    Bell: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    Settings: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n    Info: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"]\n};\nfunction Home() {\n    _s();\n    const { user } = (0,_components_auth_provider__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            loadStats();\n        }\n    }[\"Home.useEffect\"], []);\n    const loadStats = async ()=>{\n        try {\n            const overallStats = await _lib_statistics__WEBPACK_IMPORTED_MODULE_8__.statisticsService.getOverallStats();\n            const todayOrders = await _lib_statistics__WEBPACK_IMPORTED_MODULE_8__.statisticsService.getTodayStats();\n            setStats({\n                ...overallStats,\n                todayOrders,\n                activeCouriers: 12,\n                completionRate: overallStats.totalOrders > 0 ? Math.round(overallStats.deliveredOrders / overallStats.totalOrders * 100) : 0\n            });\n        } catch (error) {\n            console.error('Error loading stats:', error);\n            setStats({\n                totalOrders: 1234,\n                deliveredOrders: 987,\n                returnedOrders: 45,\n                pendingOrders: 156,\n                totalAmount: 125000000,\n                totalCommission: 987000,\n                todayOrders: 89,\n                activeCouriers: 12,\n                completionRate: 85\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const availableSections = user ? (0,_lib_permissions__WEBPACK_IMPORTED_MODULE_9__.getNavigationSections)(user.role) : [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 p-4 md:p-6 lg:p-8 animated-bg\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl blur-3xl\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl shadow-2xl mb-6 shadow-glow animate-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-12 w-12 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-4 animate-pulse\",\n                                                children: \"مرسال\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-300 text-xl max-w-3xl mx-auto leading-relaxed glass p-6 rounded-2xl\",\n                                                children: \"\\uD83D\\uDE80 نظام إدارة التوصيل السريع والموثوق - حلول متطورة لإدارة الطلبات والتوصيل بأحدث التقنيات\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_quick_search__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-md mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_firebase_status__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    _lib_config__WEBPACK_IMPORTED_MODULE_10__.APP_CONFIG.demo.enabled && _lib_config__WEBPACK_IMPORTED_MODULE_10__.APP_CONFIG.demo.showDemoNotice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-blue-900 mb-2\",\n                                                    children: \"\\uD83C\\uDFAF الوضع التجريبي مُفعل\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-700 text-sm leading-relaxed\",\n                                                    children: [\n                                                        \"أنت تستخدم النسخة التجريبية من نظام مرسال. جميع البيانات تجريبية ولن يتم حفظها بشكل دائم.\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"بيانات الدخول:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" manager / 123456 أو supervisor / 123456 أو courier / 123456\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this),\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            stats: stats\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    user && availableSections.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-gray-800 dark:text-white mb-2\",\n                                        children: \"أقسام النظام\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-300\",\n                                        children: \"اختر القسم الذي تريد الوصول إليه\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                children: availableSections.map((section)=>{\n                                    const IconComponent = iconMap[section.icon];\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                        href: section.href,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"group card-hover cursor-pointer border-2 hover:border-blue-300 glass gpu-accelerated\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    className: \"text-center pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r \".concat(section.color, \" shadow-lg mb-4 group-hover:scale-110 transition-transform duration-300 will-change-transform\"),\n                                                            children: IconComponent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"h-8 w-8 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg font-bold text-gray-800 dark:text-white group-hover:text-blue-600 transition-colors\",\n                                                            children: section.label\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"text-center pt-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                                        children: section.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, section.id, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this) : null,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"max-w-2xl mx-auto glass border-2 border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-2xl text-blue-600\",\n                                            children: [\n                                                \"مرحباً، \",\n                                                (user === null || user === void 0 ? void 0 : user.name) || 'مستخدم'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            className: \"text-lg\",\n                                            children: (user === null || user === void 0 ? void 0 : user.role) === 'manager' ? 'مدير النظام' : (user === null || user === void 0 ? void 0 : user.role) === 'supervisor' ? 'متابع' : 'مندوب'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                            children: \"مرحباً بك في نظام مرسال لإدارة التوصيل. يمكنك استخدام القائمة الجانبية للتنقل بين أقسام النظام المختلفة.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-blue-800 dark:text-blue-200 mb-2\",\n                                                    children: \"معلومات سريعة:\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-700 dark:text-blue-300 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"\\uD83D\\uDCC5 التاريخ: \",\n                                                                new Date().toLocaleDateString('ar-SA')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"⏰ الوقت: \",\n                                                                new Date().toLocaleTimeString('ar-SA')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"\\uD83D\\uDC64 المستخدم: \",\n                                                                (user === null || user === void 0 ? void 0 : user.name) || 'غير محدد'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"\\uD83D\\uDD11 الدور: \",\n                                                                (user === null || user === void 0 ? void 0 : user.role) === 'manager' ? 'مدير' : (user === null || user === void 0 ? void 0 : user.role) === 'supervisor' ? 'متابع' : 'مندوب'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"5oDpyjbHAsuLdsZd4zVcW7uZ598=\", false, function() {\n    return [\n        _components_auth_provider__WEBPACK_IMPORTED_MODULE_7__.useAuth\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/firebase-status.tsx":
/*!********************************************!*\
  !*** ./src/components/firebase-status.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FirebaseStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Loader2,Package,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Loader2,Package,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Loader2,Package,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Loader2,Package,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Loader2,Package,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Loader2,Package,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CheckCircle,Database,Loader2,Package,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _lib_firebase_setup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase-setup */ \"(app-pages-browser)/./src/lib/firebase-setup.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction FirebaseStatus() {\n    _s();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('checking');\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        users: 0,\n        orders: 0,\n        notifications: 0,\n        total: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FirebaseStatus.useEffect\": ()=>{\n            checkStatus();\n        }\n    }[\"FirebaseStatus.useEffect\"], []);\n    const checkStatus = async ()=>{\n        try {\n            setLoading(true);\n            // Check Firebase connection\n            const result = await (0,_lib_firebase_setup__WEBPACK_IMPORTED_MODULE_5__.checkFirebaseSetup)();\n            if (result.success) {\n                setStatus('connected');\n                // Get database statistics\n                const dbStats = await (0,_lib_firebase_setup__WEBPACK_IMPORTED_MODULE_5__.getDatabaseStats)();\n                setStats(dbStats);\n            } else {\n                setStatus('disconnected');\n            }\n        } catch (error) {\n            setStatus('disconnected');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getStatusIcon = ()=>{\n        if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-4 w-4 animate-spin text-yellow-600\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n            lineNumber: 43,\n            columnNumber: 25\n        }, this);\n        switch(status){\n            case 'connected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, this);\n            case 'disconnected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 animate-spin text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusText = ()=>{\n        if (loading) return 'جاري الفحص...';\n        switch(status){\n            case 'connected':\n                return 'متصل بـ Firebase';\n            case 'disconnected':\n                return 'غير متصل بـ Firebase';\n            default:\n                return 'جاري الفحص...';\n        }\n    };\n    const getStatusColor = ()=>{\n        if (loading) return 'border-yellow-200 bg-yellow-50';\n        switch(status){\n            case 'connected':\n                return 'border-green-200 bg-green-50';\n            case 'disconnected':\n                return 'border-red-200 bg-red-50';\n            default:\n                return 'border-yellow-200 bg-yellow-50';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"\".concat(getStatusColor()),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n            className: \"pt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                getStatusIcon(),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: getStatusText()\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        status === 'connected' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                            variant: \"default\",\n                            className: \"bg-green-100 text-green-800\",\n                            children: \"نشط\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this),\n                        status === 'disconnected' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                            variant: \"destructive\",\n                            children: \"غير متصل\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                status === 'connected' && stats.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-4 gap-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-6 w-6 text-blue-600 mx-auto mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-bold\",\n                                    children: stats.users\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: \"مستخدمين\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6 text-green-600 mx-auto mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-bold\",\n                                    children: stats.orders\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: \"طلبات\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-6 w-6 text-yellow-600 mx-auto mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-bold\",\n                                    children: stats.notifications\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: \"إشعارات\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-6 w-6 text-purple-600 mx-auto mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-bold\",\n                                    children: stats.total\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: \"إجمالي\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: [\n                        status === 'disconnected' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            onClick: ()=>window.open('/firebase-setup', '_blank'),\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                \"إعداد Firebase\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this),\n                        status === 'connected' && stats.total === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            size: \"sm\",\n                            onClick: ()=>window.open('/firebase-setup', '_blank'),\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this),\n                                \"إعداد البيانات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            onClick: checkStatus,\n                            disabled: loading,\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CheckCircle_Database_Loader2_Package_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this) : 'تحديث'\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\firebase-status.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(FirebaseStatus, \"grkVHjVxV8bS6V0o/gnuJTckQNE=\");\n_c = FirebaseStatus;\nvar _c;\n$RefreshReg$(_c, \"FirebaseStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/firebase-status.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/firebase-setup.ts":
/*!***********************************!*\
  !*** ./src/lib/firebase-setup.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFirebaseSetup: () => (/* binding */ checkFirebaseSetup),\n/* harmony export */   getDatabaseStats: () => (/* binding */ getDatabaseStats),\n/* harmony export */   setupFirebaseDatabase: () => (/* binding */ setupFirebaseDatabase)\n/* harmony export */ });\n/* harmony import */ var _firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firebase-auth */ \"(app-pages-browser)/./src/lib/firebase-auth.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n// Firebase setup and initialization script\n\n\n\n// Setup Firebase with all required data\nconst setupFirebaseDatabase = async ()=>{\n    try {\n        console.log('🔥 بدء إعداد قاعدة البيانات Firebase...');\n        // Step 1: Initialize default users\n        console.log('👥 إنشاء المستخدمين الافتراضيين...');\n        await _firebase_auth__WEBPACK_IMPORTED_MODULE_0__.firebaseAuthService.initializeDefaultUsers();\n        // Step 2: Create sample orders\n        console.log('📦 إنشاء طلبات تجريبية...');\n        await createSampleOrders();\n        // Step 3: Create sample notifications\n        console.log('🔔 إنشاء إشعارات تجريبية...');\n        await createSampleNotifications();\n        console.log('✅ تم إعداد قاعدة البيانات Firebase بنجاح!');\n        return {\n            success: true,\n            message: 'تم إعداد قاعدة البيانات بنجاح مع جميع البيانات المطلوبة'\n        };\n    } catch (error) {\n        console.error('❌ خطأ في إعداد قاعدة البيانات:', error);\n        return {\n            success: false,\n            message: \"فشل في إعداد قاعدة البيانات: \".concat(error.message)\n        };\n    }\n};\n// Create sample orders\nconst createSampleOrders = async ()=>{\n    if (!_firebase__WEBPACK_IMPORTED_MODULE_2__.db) {\n        throw new Error('Firebase غير متصل');\n    }\n    const sampleOrders = [\n        {\n            trackingNumber: 'ORDER_001',\n            customerName: 'أحمد محمد علي',\n            customerPhone: '07701234567',\n            address: 'بغداد - الكرادة - شارع الرشيد',\n            amount: 25000,\n            status: 'pending',\n            courierName: '',\n            assignedTo: '',\n            notes: 'طلب تجريبي - يرجى التوصيل صباحاً',\n            createdBy: 'system'\n        },\n        {\n            trackingNumber: 'ORDER_002',\n            customerName: 'فاطمة علي حسن',\n            customerPhone: '07701234568',\n            address: 'البصرة - المعقل - حي الجمهورية',\n            amount: 35000,\n            status: 'assigned',\n            courierName: 'مندوب التوصيل',\n            assignedTo: 'courier_1',\n            notes: 'طلب عاجل - يرجى التوصيل اليوم',\n            createdBy: 'manager'\n        },\n        {\n            trackingNumber: 'ORDER_003',\n            customerName: 'محمد حسين كريم',\n            customerPhone: '07701234569',\n            address: 'أربيل - عنكاوا - شارع الكنائس',\n            amount: 45000,\n            status: 'delivered',\n            courierName: 'مندوب التوصيل',\n            assignedTo: 'courier_1',\n            notes: 'تم التسليم بنجاح',\n            createdBy: 'supervisor',\n            deliveredAt: new Date()\n        },\n        {\n            trackingNumber: 'ORDER_004',\n            customerName: 'زينب أحمد محمود',\n            customerPhone: '07701234570',\n            address: 'النجف - المدينة القديمة - قرب الحرم',\n            amount: 30000,\n            status: 'returned',\n            courierName: 'مندوب التوصيل',\n            assignedTo: 'courier_1',\n            notes: 'تم الإرجاع - العنوان غير صحيح',\n            createdBy: 'supervisor'\n        },\n        {\n            trackingNumber: 'ORDER_005',\n            customerName: 'علي حسن جعفر',\n            customerPhone: '07701234571',\n            address: 'كربلاء - حي الحسين - شارع الإمام علي',\n            amount: 55000,\n            status: 'in_transit',\n            courierName: 'مندوب التوصيل',\n            assignedTo: 'courier_1',\n            notes: 'في الطريق للتسليم',\n            createdBy: 'manager'\n        }\n    ];\n    const ordersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'orders');\n    for (const orderData of sampleOrders){\n        try {\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)(ordersRef, {\n                ...orderData,\n                createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)(),\n                updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n            });\n            console.log(\"✅ تم إنشاء الطلب: \".concat(orderData.trackingNumber));\n        } catch (error) {\n            console.error(\"❌ خطأ في إنشاء الطلب \".concat(orderData.trackingNumber, \":\"), error);\n        }\n    }\n};\n// Create sample notifications\nconst createSampleNotifications = async ()=>{\n    if (!_firebase__WEBPACK_IMPORTED_MODULE_2__.db) {\n        throw new Error('Firebase غير متصل');\n    }\n    const sampleNotifications = [\n        {\n            title: 'طلب جديد',\n            message: 'تم إسناد طلب ORDER_002 إليك',\n            type: 'order_assigned',\n            userId: 'courier_1',\n            orderId: 'ORDER_002',\n            isRead: false\n        },\n        {\n            title: 'تحديث حالة الطلب',\n            message: 'تم تسليم الطلب ORDER_003 بنجاح',\n            type: 'status_changed',\n            userId: 'manager',\n            orderId: 'ORDER_003',\n            isRead: false\n        },\n        {\n            title: 'طلب مرتجع',\n            message: 'تم إرجاع الطلب ORDER_004 - العنوان غير صحيح',\n            type: 'order_returned',\n            userId: 'supervisor',\n            orderId: 'ORDER_004',\n            isRead: false\n        },\n        {\n            title: 'مستخدم جديد',\n            message: 'تم إنشاء حساب مندوب جديد',\n            type: 'user_created',\n            userId: 'manager',\n            isRead: true\n        },\n        {\n            title: 'تقرير يومي',\n            message: 'تم تسليم 5 طلبات اليوم بنجاح',\n            type: 'daily_report',\n            userId: 'manager',\n            isRead: false\n        }\n    ];\n    const notificationsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'notifications');\n    for (const notificationData of sampleNotifications){\n        try {\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)(notificationsRef, {\n                ...notificationData,\n                createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n            });\n            console.log(\"✅ تم إنشاء الإشعار: \".concat(notificationData.title));\n        } catch (error) {\n            console.error(\"❌ خطأ في إنشاء الإشعار:\", error);\n        }\n    }\n};\n// Check if Firebase is properly configured\nconst checkFirebaseSetup = async ()=>{\n    try {\n        // Test Firebase connection\n        const { testFirebaseConnection } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\"));\n        const connectionResult = await testFirebaseConnection();\n        if (!connectionResult.success) {\n            return {\n                success: false,\n                message: \"فشل الاتصال بـ Firebase: \".concat(connectionResult.message)\n            };\n        }\n        // Test authentication\n        const authResult = await _firebase_auth__WEBPACK_IMPORTED_MODULE_0__.firebaseAuthService.checkConnection();\n        if (!authResult.connected) {\n            return {\n                success: false,\n                message: \"فشل في نظام المصادقة: \".concat(authResult.message)\n            };\n        }\n        return {\n            success: true,\n            message: 'Firebase مُعد بشكل صحيح ومتصل'\n        };\n    } catch (error) {\n        return {\n            success: false,\n            message: \"خطأ في فحص Firebase: \".concat(error.message)\n        };\n    }\n};\n// Get database statistics\nconst getDatabaseStats = async ()=>{\n    try {\n        if (!_firebase__WEBPACK_IMPORTED_MODULE_2__.db) {\n            throw new Error('Firebase غير متصل');\n        }\n        const { getDocs, collection } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n        // Get users count\n        const usersSnapshot = await getDocs(collection(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'users'));\n        const usersCount = usersSnapshot.size;\n        // Get orders count\n        const ordersSnapshot = await getDocs(collection(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'orders'));\n        const ordersCount = ordersSnapshot.size;\n        // Get notifications count\n        const notificationsSnapshot = await getDocs(collection(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'notifications'));\n        const notificationsCount = notificationsSnapshot.size;\n        return {\n            users: usersCount,\n            orders: ordersCount,\n            notifications: notificationsCount,\n            total: usersCount + ordersCount + notificationsCount\n        };\n    } catch (error) {\n        console.error('خطأ في جلب إحصائيات قاعدة البيانات:', error);\n        return {\n            users: 0,\n            orders: 0,\n            notifications: 0,\n            total: 0\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvZmlyZWJhc2Utc2V0dXAudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUEsMkNBQTJDO0FBQ1c7QUFFbUI7QUFDekM7QUFFaEMsd0NBQXdDO0FBQ2pDLE1BQU1LLHdCQUF3QjtJQUNuQyxJQUFJO1FBQ0ZDLFFBQVFDLEdBQUcsQ0FBQztRQUVaLG1DQUFtQztRQUNuQ0QsUUFBUUMsR0FBRyxDQUFDO1FBQ1osTUFBTVAsK0RBQW1CQSxDQUFDUSxzQkFBc0I7UUFFaEQsK0JBQStCO1FBQy9CRixRQUFRQyxHQUFHLENBQUM7UUFDWixNQUFNRTtRQUVOLHNDQUFzQztRQUN0Q0gsUUFBUUMsR0FBRyxDQUFDO1FBQ1osTUFBTUc7UUFFTkosUUFBUUMsR0FBRyxDQUFDO1FBQ1osT0FBTztZQUNMSSxTQUFTO1lBQ1RDLFNBQVM7UUFDWDtJQUVGLEVBQUUsT0FBT0MsT0FBWTtRQUNuQlAsUUFBUU8sS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsT0FBTztZQUNMRixTQUFTO1lBQ1RDLFNBQVMsZ0NBQThDLE9BQWRDLE1BQU1ELE9BQU87UUFDeEQ7SUFDRjtBQUNGLEVBQUU7QUFFRix1QkFBdUI7QUFDdkIsTUFBTUgscUJBQXFCO0lBQ3pCLElBQUksQ0FBQ0wseUNBQUVBLEVBQUU7UUFDUCxNQUFNLElBQUlVLE1BQU07SUFDbEI7SUFFQSxNQUFNQyxlQUFlO1FBQ25CO1lBQ0VDLGdCQUFnQjtZQUNoQkMsY0FBYztZQUNkQyxlQUFlO1lBQ2ZDLFNBQVM7WUFDVEMsUUFBUTtZQUNSQyxRQUFRO1lBQ1JDLGFBQWE7WUFDYkMsWUFBWTtZQUNaQyxPQUFPO1lBQ1BDLFdBQVc7UUFDYjtRQUNBO1lBQ0VULGdCQUFnQjtZQUNoQkMsY0FBYztZQUNkQyxlQUFlO1lBQ2ZDLFNBQVM7WUFDVEMsUUFBUTtZQUNSQyxRQUFRO1lBQ1JDLGFBQWE7WUFDYkMsWUFBWTtZQUNaQyxPQUFPO1lBQ1BDLFdBQVc7UUFDYjtRQUNBO1lBQ0VULGdCQUFnQjtZQUNoQkMsY0FBYztZQUNkQyxlQUFlO1lBQ2ZDLFNBQVM7WUFDVEMsUUFBUTtZQUNSQyxRQUFRO1lBQ1JDLGFBQWE7WUFDYkMsWUFBWTtZQUNaQyxPQUFPO1lBQ1BDLFdBQVc7WUFDWEMsYUFBYSxJQUFJQztRQUNuQjtRQUNBO1lBQ0VYLGdCQUFnQjtZQUNoQkMsY0FBYztZQUNkQyxlQUFlO1lBQ2ZDLFNBQVM7WUFDVEMsUUFBUTtZQUNSQyxRQUFRO1lBQ1JDLGFBQWE7WUFDYkMsWUFBWTtZQUNaQyxPQUFPO1lBQ1BDLFdBQVc7UUFDYjtRQUNBO1lBQ0VULGdCQUFnQjtZQUNoQkMsY0FBYztZQUNkQyxlQUFlO1lBQ2ZDLFNBQVM7WUFDVEMsUUFBUTtZQUNSQyxRQUFRO1lBQ1JDLGFBQWE7WUFDYkMsWUFBWTtZQUNaQyxPQUFPO1lBQ1BDLFdBQVc7UUFDYjtLQUNEO0lBRUQsTUFBTUcsWUFBWTNCLDhEQUFVQSxDQUFDRyx5Q0FBRUEsRUFBRTtJQUVqQyxLQUFLLE1BQU15QixhQUFhZCxhQUFjO1FBQ3BDLElBQUk7WUFDRixNQUFNYiwwREFBTUEsQ0FBQzBCLFdBQVc7Z0JBQ3RCLEdBQUdDLFNBQVM7Z0JBQ1pDLFdBQVczQixtRUFBZUE7Z0JBQzFCNEIsV0FBVzVCLG1FQUFlQTtZQUM1QjtZQUNBRyxRQUFRQyxHQUFHLENBQUMscUJBQThDLE9BQXpCc0IsVUFBVWIsY0FBYztRQUMzRCxFQUFFLE9BQU9ILE9BQU87WUFDZFAsUUFBUU8sS0FBSyxDQUFDLHdCQUFpRCxPQUF6QmdCLFVBQVViLGNBQWMsRUFBQyxNQUFJSDtRQUNyRTtJQUNGO0FBQ0Y7QUFFQSw4QkFBOEI7QUFDOUIsTUFBTUgsNEJBQTRCO0lBQ2hDLElBQUksQ0FBQ04seUNBQUVBLEVBQUU7UUFDUCxNQUFNLElBQUlVLE1BQU07SUFDbEI7SUFFQSxNQUFNa0Isc0JBQXNCO1FBQzFCO1lBQ0VDLE9BQU87WUFDUHJCLFNBQVM7WUFDVHNCLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFFBQVE7UUFDVjtRQUNBO1lBQ0VKLE9BQU87WUFDUHJCLFNBQVM7WUFDVHNCLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFFBQVE7UUFDVjtRQUNBO1lBQ0VKLE9BQU87WUFDUHJCLFNBQVM7WUFDVHNCLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFFBQVE7UUFDVjtRQUNBO1lBQ0VKLE9BQU87WUFDUHJCLFNBQVM7WUFDVHNCLE1BQU07WUFDTkMsUUFBUTtZQUNSRSxRQUFRO1FBQ1Y7UUFDQTtZQUNFSixPQUFPO1lBQ1ByQixTQUFTO1lBQ1RzQixNQUFNO1lBQ05DLFFBQVE7WUFDUkUsUUFBUTtRQUNWO0tBQ0Q7SUFFRCxNQUFNQyxtQkFBbUJyQyw4REFBVUEsQ0FBQ0cseUNBQUVBLEVBQUU7SUFFeEMsS0FBSyxNQUFNbUMsb0JBQW9CUCxvQkFBcUI7UUFDbEQsSUFBSTtZQUNGLE1BQU05QiwwREFBTUEsQ0FBQ29DLGtCQUFrQjtnQkFDN0IsR0FBR0MsZ0JBQWdCO2dCQUNuQlQsV0FBVzNCLG1FQUFlQTtZQUM1QjtZQUNBRyxRQUFRQyxHQUFHLENBQUMsdUJBQThDLE9BQXZCZ0MsaUJBQWlCTixLQUFLO1FBQzNELEVBQUUsT0FBT3BCLE9BQU87WUFDZFAsUUFBUU8sS0FBSyxDQUFFLDJCQUEwQkE7UUFDM0M7SUFDRjtBQUNGO0FBRUEsMkNBQTJDO0FBQ3BDLE1BQU0yQixxQkFBcUI7SUFDaEMsSUFBSTtRQUNGLDJCQUEyQjtRQUMzQixNQUFNLEVBQUVDLHNCQUFzQixFQUFFLEdBQUcsTUFBTSxtSkFBb0I7UUFDN0QsTUFBTUMsbUJBQW1CLE1BQU1EO1FBRS9CLElBQUksQ0FBQ0MsaUJBQWlCL0IsT0FBTyxFQUFFO1lBQzdCLE9BQU87Z0JBQ0xBLFNBQVM7Z0JBQ1RDLFNBQVMsNEJBQXFELE9BQXpCOEIsaUJBQWlCOUIsT0FBTztZQUMvRDtRQUNGO1FBRUEsc0JBQXNCO1FBQ3RCLE1BQU0rQixhQUFhLE1BQU0zQywrREFBbUJBLENBQUM0QyxlQUFlO1FBQzVELElBQUksQ0FBQ0QsV0FBV0UsU0FBUyxFQUFFO1lBQ3pCLE9BQU87Z0JBQ0xsQyxTQUFTO2dCQUNUQyxTQUFTLHlCQUE0QyxPQUFuQitCLFdBQVcvQixPQUFPO1lBQ3REO1FBQ0Y7UUFFQSxPQUFPO1lBQ0xELFNBQVM7WUFDVEMsU0FBUztRQUNYO0lBRUYsRUFBRSxPQUFPQyxPQUFZO1FBQ25CLE9BQU87WUFDTEYsU0FBUztZQUNUQyxTQUFTLHdCQUFzQyxPQUFkQyxNQUFNRCxPQUFPO1FBQ2hEO0lBQ0Y7QUFDRixFQUFFO0FBRUYsMEJBQTBCO0FBQ25CLE1BQU1rQyxtQkFBbUI7SUFDOUIsSUFBSTtRQUNGLElBQUksQ0FBQzFDLHlDQUFFQSxFQUFFO1lBQ1AsTUFBTSxJQUFJVSxNQUFNO1FBQ2xCO1FBRUEsTUFBTSxFQUFFaUMsT0FBTyxFQUFFOUMsVUFBVSxFQUFFLEdBQUcsTUFBTSw2TEFBNEI7UUFFbEUsa0JBQWtCO1FBQ2xCLE1BQU0rQyxnQkFBZ0IsTUFBTUQsUUFBUTlDLFdBQVdHLHlDQUFFQSxFQUFFO1FBQ25ELE1BQU02QyxhQUFhRCxjQUFjRSxJQUFJO1FBRXJDLG1CQUFtQjtRQUNuQixNQUFNQyxpQkFBaUIsTUFBTUosUUFBUTlDLFdBQVdHLHlDQUFFQSxFQUFFO1FBQ3BELE1BQU1nRCxjQUFjRCxlQUFlRCxJQUFJO1FBRXZDLDBCQUEwQjtRQUMxQixNQUFNRyx3QkFBd0IsTUFBTU4sUUFBUTlDLFdBQVdHLHlDQUFFQSxFQUFFO1FBQzNELE1BQU1rRCxxQkFBcUJELHNCQUFzQkgsSUFBSTtRQUVyRCxPQUFPO1lBQ0xLLE9BQU9OO1lBQ1BPLFFBQVFKO1lBQ1JLLGVBQWVIO1lBQ2ZJLE9BQU9ULGFBQWFHLGNBQWNFO1FBQ3BDO0lBRUYsRUFBRSxPQUFPekMsT0FBTztRQUNkUCxRQUFRTyxLQUFLLENBQUMsdUNBQXVDQTtRQUNyRCxPQUFPO1lBQ0wwQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUkMsZUFBZTtZQUNmQyxPQUFPO1FBQ1Q7SUFDRjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIkU6XFxNYXJzYWxcXG1hcnNhbFxcc3JjXFxsaWJcXGZpcmViYXNlLXNldHVwLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZpcmViYXNlIHNldHVwIGFuZCBpbml0aWFsaXphdGlvbiBzY3JpcHRcbmltcG9ydCB7IGZpcmViYXNlQXV0aFNlcnZpY2UgfSBmcm9tICcuL2ZpcmViYXNlLWF1dGgnO1xuaW1wb3J0IHsgb3JkZXJzU2VydmljZSB9IGZyb20gJy4vZmlyZXN0b3JlJztcbmltcG9ydCB7IGNvbGxlY3Rpb24sIGFkZERvYywgc2VydmVyVGltZXN0YW1wIH0gZnJvbSAnZmlyZWJhc2UvZmlyZXN0b3JlJztcbmltcG9ydCB7IGRiIH0gZnJvbSAnLi9maXJlYmFzZSc7XG5cbi8vIFNldHVwIEZpcmViYXNlIHdpdGggYWxsIHJlcXVpcmVkIGRhdGFcbmV4cG9ydCBjb25zdCBzZXR1cEZpcmViYXNlRGF0YWJhc2UgPSBhc3luYyAoKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U6IHN0cmluZyB9PiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coJ/CflKUg2KjYr9ihINil2LnYr9in2K8g2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqIEZpcmViYXNlLi4uJyk7XG5cbiAgICAvLyBTdGVwIDE6IEluaXRpYWxpemUgZGVmYXVsdCB1c2Vyc1xuICAgIGNvbnNvbGUubG9nKCfwn5GlINil2YbYtNin2KEg2KfZhNmF2LPYqtiu2K/ZhdmK2YYg2KfZhNin2YHYqtix2KfYttmK2YrZhi4uLicpO1xuICAgIGF3YWl0IGZpcmViYXNlQXV0aFNlcnZpY2UuaW5pdGlhbGl6ZURlZmF1bHRVc2VycygpO1xuXG4gICAgLy8gU3RlcCAyOiBDcmVhdGUgc2FtcGxlIG9yZGVyc1xuICAgIGNvbnNvbGUubG9nKCfwn5OmINil2YbYtNin2KEg2LfZhNio2KfYqiDYqtis2LHZitio2YrYqS4uLicpO1xuICAgIGF3YWl0IGNyZWF0ZVNhbXBsZU9yZGVycygpO1xuXG4gICAgLy8gU3RlcCAzOiBDcmVhdGUgc2FtcGxlIG5vdGlmaWNhdGlvbnNcbiAgICBjb25zb2xlLmxvZygn8J+UlCDYpdmG2LTYp9ihINil2LTYudin2LHYp9iqINiq2KzYsdmK2KjZitipLi4uJyk7XG4gICAgYXdhaXQgY3JlYXRlU2FtcGxlTm90aWZpY2F0aW9ucygpO1xuXG4gICAgY29uc29sZS5sb2coJ+KchSDYqtmFINil2LnYr9in2K8g2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqIEZpcmViYXNlINio2YbYrNin2K0hJyk7XG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBtZXNzYWdlOiAn2KrZhSDYpdi52K/Yp9ivINmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqiDYqNmG2KzYp9itINmF2Lkg2KzZhdmK2Lkg2KfZhNio2YrYp9mG2KfYqiDYp9mE2YXYt9mE2YjYqNipJ1xuICAgIH07XG5cbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDYrti32KMg2YHZiiDYpdi52K/Yp9ivINmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqjonLCBlcnJvcik7XG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgbWVzc2FnZTogYNmB2LTZhCDZgdmKINil2LnYr9in2K8g2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqOiAke2Vycm9yLm1lc3NhZ2V9YFxuICAgIH07XG4gIH1cbn07XG5cbi8vIENyZWF0ZSBzYW1wbGUgb3JkZXJzXG5jb25zdCBjcmVhdGVTYW1wbGVPcmRlcnMgPSBhc3luYyAoKSA9PiB7XG4gIGlmICghZGIpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZpcmViYXNlINi62YrYsSDZhdiq2LXZhCcpO1xuICB9XG5cbiAgY29uc3Qgc2FtcGxlT3JkZXJzID0gW1xuICAgIHtcbiAgICAgIHRyYWNraW5nTnVtYmVyOiAnT1JERVJfMDAxJyxcbiAgICAgIGN1c3RvbWVyTmFtZTogJ9ij2K3ZhdivINmF2K3ZhdivINi52YTZiicsXG4gICAgICBjdXN0b21lclBob25lOiAnMDc3MDEyMzQ1NjcnLFxuICAgICAgYWRkcmVzczogJ9io2LrYr9in2K8gLSDYp9mE2YPYsdin2K/YqSAtINi02KfYsdi5INin2YTYsdi02YrYrycsXG4gICAgICBhbW91bnQ6IDI1MDAwLFxuICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICBjb3VyaWVyTmFtZTogJycsXG4gICAgICBhc3NpZ25lZFRvOiAnJyxcbiAgICAgIG5vdGVzOiAn2LfZhNioINiq2KzYsdmK2KjZiiAtINmK2LHYrNmJINin2YTYqtmI2LXZitmEINi12KjYp9it2KfZiycsXG4gICAgICBjcmVhdGVkQnk6ICdzeXN0ZW0nXG4gICAgfSxcbiAgICB7XG4gICAgICB0cmFja2luZ051bWJlcjogJ09SREVSXzAwMicsXG4gICAgICBjdXN0b21lck5hbWU6ICfZgdin2LfZhdipINi52YTZiiDYrdiz2YYnLFxuICAgICAgY3VzdG9tZXJQaG9uZTogJzA3NzAxMjM0NTY4JyxcbiAgICAgIGFkZHJlc3M6ICfYp9mE2KjYtdix2KkgLSDYp9mE2YXYudmC2YQgLSDYrdmKINin2YTYrNmF2YfZiNix2YrYqScsXG4gICAgICBhbW91bnQ6IDM1MDAwLFxuICAgICAgc3RhdHVzOiAnYXNzaWduZWQnLFxuICAgICAgY291cmllck5hbWU6ICfZhdmG2K/ZiNioINin2YTYqtmI2LXZitmEJyxcbiAgICAgIGFzc2lnbmVkVG86ICdjb3VyaWVyXzEnLFxuICAgICAgbm90ZXM6ICfYt9mE2Kgg2LnYp9is2YQgLSDZitix2KzZiSDYp9mE2KrZiNi12YrZhCDYp9mE2YrZiNmFJyxcbiAgICAgIGNyZWF0ZWRCeTogJ21hbmFnZXInXG4gICAgfSxcbiAgICB7XG4gICAgICB0cmFja2luZ051bWJlcjogJ09SREVSXzAwMycsXG4gICAgICBjdXN0b21lck5hbWU6ICfZhdit2YXYryDYrdiz2YrZhiDZg9ix2YrZhScsXG4gICAgICBjdXN0b21lclBob25lOiAnMDc3MDEyMzQ1NjknLFxuICAgICAgYWRkcmVzczogJ9ij2LHYqNmK2YQgLSDYudmG2YPYp9mI2KcgLSDYtNin2LHYuSDYp9mE2YPZhtin2KbYsycsXG4gICAgICBhbW91bnQ6IDQ1MDAwLFxuICAgICAgc3RhdHVzOiAnZGVsaXZlcmVkJyxcbiAgICAgIGNvdXJpZXJOYW1lOiAn2YXZhtiv2YjYqCDYp9mE2KrZiNi12YrZhCcsXG4gICAgICBhc3NpZ25lZFRvOiAnY291cmllcl8xJyxcbiAgICAgIG5vdGVzOiAn2KrZhSDYp9mE2KrYs9mE2YrZhSDYqNmG2KzYp9itJyxcbiAgICAgIGNyZWF0ZWRCeTogJ3N1cGVydmlzb3InLFxuICAgICAgZGVsaXZlcmVkQXQ6IG5ldyBEYXRlKClcbiAgICB9LFxuICAgIHtcbiAgICAgIHRyYWNraW5nTnVtYmVyOiAnT1JERVJfMDA0JyxcbiAgICAgIGN1c3RvbWVyTmFtZTogJ9iy2YrZhtioINij2K3ZhdivINmF2K3ZhdmI2K8nLFxuICAgICAgY3VzdG9tZXJQaG9uZTogJzA3NzAxMjM0NTcwJyxcbiAgICAgIGFkZHJlc3M6ICfYp9mE2YbYrNmBIC0g2KfZhNmF2K/ZitmG2Kkg2KfZhNmC2K/ZitmF2KkgLSDZgtix2Kgg2KfZhNit2LHZhScsXG4gICAgICBhbW91bnQ6IDMwMDAwLFxuICAgICAgc3RhdHVzOiAncmV0dXJuZWQnLFxuICAgICAgY291cmllck5hbWU6ICfZhdmG2K/ZiNioINin2YTYqtmI2LXZitmEJyxcbiAgICAgIGFzc2lnbmVkVG86ICdjb3VyaWVyXzEnLFxuICAgICAgbm90ZXM6ICfYqtmFINin2YTYpdix2KzYp9i5IC0g2KfZhNi52YbZiNin2YYg2LrZitixINi12K3ZititJyxcbiAgICAgIGNyZWF0ZWRCeTogJ3N1cGVydmlzb3InXG4gICAgfSxcbiAgICB7XG4gICAgICB0cmFja2luZ051bWJlcjogJ09SREVSXzAwNScsXG4gICAgICBjdXN0b21lck5hbWU6ICfYudmE2Yog2K3Ys9mGINis2LnZgdixJyxcbiAgICAgIGN1c3RvbWVyUGhvbmU6ICcwNzcwMTIzNDU3MScsXG4gICAgICBhZGRyZXNzOiAn2YPYsdio2YTYp9ihIC0g2K3ZiiDYp9mE2K3Ys9mK2YYgLSDYtNin2LHYuSDYp9mE2KXZhdin2YUg2LnZhNmKJyxcbiAgICAgIGFtb3VudDogNTUwMDAsXG4gICAgICBzdGF0dXM6ICdpbl90cmFuc2l0JyxcbiAgICAgIGNvdXJpZXJOYW1lOiAn2YXZhtiv2YjYqCDYp9mE2KrZiNi12YrZhCcsXG4gICAgICBhc3NpZ25lZFRvOiAnY291cmllcl8xJyxcbiAgICAgIG5vdGVzOiAn2YHZiiDYp9mE2LfYsdmK2YIg2YTZhNiq2LPZhNmK2YUnLFxuICAgICAgY3JlYXRlZEJ5OiAnbWFuYWdlcidcbiAgICB9XG4gIF07XG5cbiAgY29uc3Qgb3JkZXJzUmVmID0gY29sbGVjdGlvbihkYiwgJ29yZGVycycpO1xuXG4gIGZvciAoY29uc3Qgb3JkZXJEYXRhIG9mIHNhbXBsZU9yZGVycykge1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBhZGREb2Mob3JkZXJzUmVmLCB7XG4gICAgICAgIC4uLm9yZGVyRGF0YSxcbiAgICAgICAgY3JlYXRlZEF0OiBzZXJ2ZXJUaW1lc3RhbXAoKSxcbiAgICAgICAgdXBkYXRlZEF0OiBzZXJ2ZXJUaW1lc3RhbXAoKVxuICAgICAgfSk7XG4gICAgICBjb25zb2xlLmxvZyhg4pyFINiq2YUg2KXZhti02KfYoSDYp9mE2LfZhNioOiAke29yZGVyRGF0YS50cmFja2luZ051bWJlcn1gKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihg4p2MINiu2LfYoyDZgdmKINil2YbYtNin2KEg2KfZhNi32YTYqCAke29yZGVyRGF0YS50cmFja2luZ051bWJlcn06YCwgZXJyb3IpO1xuICAgIH1cbiAgfVxufTtcblxuLy8gQ3JlYXRlIHNhbXBsZSBub3RpZmljYXRpb25zXG5jb25zdCBjcmVhdGVTYW1wbGVOb3RpZmljYXRpb25zID0gYXN5bmMgKCkgPT4ge1xuICBpZiAoIWRiKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdGaXJlYmFzZSDYutmK2LEg2YXYqti12YQnKTtcbiAgfVxuXG4gIGNvbnN0IHNhbXBsZU5vdGlmaWNhdGlvbnMgPSBbXG4gICAge1xuICAgICAgdGl0bGU6ICfYt9mE2Kgg2KzYr9mK2K8nLFxuICAgICAgbWVzc2FnZTogJ9iq2YUg2KXYs9mG2KfYryDYt9mE2KggT1JERVJfMDAyINil2YTZitmDJyxcbiAgICAgIHR5cGU6ICdvcmRlcl9hc3NpZ25lZCcsXG4gICAgICB1c2VySWQ6ICdjb3VyaWVyXzEnLFxuICAgICAgb3JkZXJJZDogJ09SREVSXzAwMicsXG4gICAgICBpc1JlYWQ6IGZhbHNlXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ9iq2K3Yr9mK2Ksg2K3Yp9mE2Kkg2KfZhNi32YTYqCcsXG4gICAgICBtZXNzYWdlOiAn2KrZhSDYqtiz2YTZitmFINin2YTYt9mE2KggT1JERVJfMDAzINio2YbYrNin2K0nLFxuICAgICAgdHlwZTogJ3N0YXR1c19jaGFuZ2VkJyxcbiAgICAgIHVzZXJJZDogJ21hbmFnZXInLFxuICAgICAgb3JkZXJJZDogJ09SREVSXzAwMycsXG4gICAgICBpc1JlYWQ6IGZhbHNlXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ9i32YTYqCDZhdix2KrYrNi5JyxcbiAgICAgIG1lc3NhZ2U6ICfYqtmFINil2LHYrNin2Lkg2KfZhNi32YTYqCBPUkRFUl8wMDQgLSDYp9mE2LnZhtmI2KfZhiDYutmK2LEg2LXYrdmK2K0nLFxuICAgICAgdHlwZTogJ29yZGVyX3JldHVybmVkJyxcbiAgICAgIHVzZXJJZDogJ3N1cGVydmlzb3InLFxuICAgICAgb3JkZXJJZDogJ09SREVSXzAwNCcsXG4gICAgICBpc1JlYWQ6IGZhbHNlXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ9mF2LPYqtiu2K/ZhSDYrNiv2YrYrycsXG4gICAgICBtZXNzYWdlOiAn2KrZhSDYpdmG2LTYp9ihINit2LPYp9ioINmF2YbYr9mI2Kgg2KzYr9mK2K8nLFxuICAgICAgdHlwZTogJ3VzZXJfY3JlYXRlZCcsXG4gICAgICB1c2VySWQ6ICdtYW5hZ2VyJyxcbiAgICAgIGlzUmVhZDogdHJ1ZVxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICfYqtmC2LHZitixINmK2YjZhdmKJyxcbiAgICAgIG1lc3NhZ2U6ICfYqtmFINiq2LPZhNmK2YUgNSDYt9mE2KjYp9iqINin2YTZitmI2YUg2KjZhtis2KfYrScsXG4gICAgICB0eXBlOiAnZGFpbHlfcmVwb3J0JyxcbiAgICAgIHVzZXJJZDogJ21hbmFnZXInLFxuICAgICAgaXNSZWFkOiBmYWxzZVxuICAgIH1cbiAgXTtcblxuICBjb25zdCBub3RpZmljYXRpb25zUmVmID0gY29sbGVjdGlvbihkYiwgJ25vdGlmaWNhdGlvbnMnKTtcblxuICBmb3IgKGNvbnN0IG5vdGlmaWNhdGlvbkRhdGEgb2Ygc2FtcGxlTm90aWZpY2F0aW9ucykge1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBhZGREb2Mobm90aWZpY2F0aW9uc1JlZiwge1xuICAgICAgICAuLi5ub3RpZmljYXRpb25EYXRhLFxuICAgICAgICBjcmVhdGVkQXQ6IHNlcnZlclRpbWVzdGFtcCgpXG4gICAgICB9KTtcbiAgICAgIGNvbnNvbGUubG9nKGDinIUg2KrZhSDYpdmG2LTYp9ihINin2YTYpdi02LnYp9ixOiAke25vdGlmaWNhdGlvbkRhdGEudGl0bGV9YCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYOKdjCDYrti32KMg2YHZiiDYpdmG2LTYp9ihINin2YTYpdi02LnYp9ixOmAsIGVycm9yKTtcbiAgICB9XG4gIH1cbn07XG5cbi8vIENoZWNrIGlmIEZpcmViYXNlIGlzIHByb3Blcmx5IGNvbmZpZ3VyZWRcbmV4cG9ydCBjb25zdCBjaGVja0ZpcmViYXNlU2V0dXAgPSBhc3luYyAoKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U6IHN0cmluZyB9PiA9PiB7XG4gIHRyeSB7XG4gICAgLy8gVGVzdCBGaXJlYmFzZSBjb25uZWN0aW9uXG4gICAgY29uc3QgeyB0ZXN0RmlyZWJhc2VDb25uZWN0aW9uIH0gPSBhd2FpdCBpbXBvcnQoJy4vZmlyZWJhc2UnKTtcbiAgICBjb25zdCBjb25uZWN0aW9uUmVzdWx0ID0gYXdhaXQgdGVzdEZpcmViYXNlQ29ubmVjdGlvbigpO1xuICAgIFxuICAgIGlmICghY29ubmVjdGlvblJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgbWVzc2FnZTogYNmB2LTZhCDYp9mE2KfYqti12KfZhCDYqNmAIEZpcmViYXNlOiAke2Nvbm5lY3Rpb25SZXN1bHQubWVzc2FnZX1gXG4gICAgICB9O1xuICAgIH1cblxuICAgIC8vIFRlc3QgYXV0aGVudGljYXRpb25cbiAgICBjb25zdCBhdXRoUmVzdWx0ID0gYXdhaXQgZmlyZWJhc2VBdXRoU2VydmljZS5jaGVja0Nvbm5lY3Rpb24oKTtcbiAgICBpZiAoIWF1dGhSZXN1bHQuY29ubmVjdGVkKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgbWVzc2FnZTogYNmB2LTZhCDZgdmKINmG2LjYp9mFINin2YTZhdi12KfYr9mC2Kk6ICR7YXV0aFJlc3VsdC5tZXNzYWdlfWBcbiAgICAgIH07XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBtZXNzYWdlOiAnRmlyZWJhc2Ug2YXZj9i52K8g2KjYtNmD2YQg2LXYrdmK2K0g2YjZhdiq2LXZhCdcbiAgICB9O1xuXG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBtZXNzYWdlOiBg2K7Yt9ijINmB2Yog2YHYrdi1IEZpcmViYXNlOiAke2Vycm9yLm1lc3NhZ2V9YFxuICAgIH07XG4gIH1cbn07XG5cbi8vIEdldCBkYXRhYmFzZSBzdGF0aXN0aWNzXG5leHBvcnQgY29uc3QgZ2V0RGF0YWJhc2VTdGF0cyA9IGFzeW5jICgpID0+IHtcbiAgdHJ5IHtcbiAgICBpZiAoIWRiKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZpcmViYXNlINi62YrYsSDZhdiq2LXZhCcpO1xuICAgIH1cblxuICAgIGNvbnN0IHsgZ2V0RG9jcywgY29sbGVjdGlvbiB9ID0gYXdhaXQgaW1wb3J0KCdmaXJlYmFzZS9maXJlc3RvcmUnKTtcblxuICAgIC8vIEdldCB1c2VycyBjb3VudFxuICAgIGNvbnN0IHVzZXJzU25hcHNob3QgPSBhd2FpdCBnZXREb2NzKGNvbGxlY3Rpb24oZGIsICd1c2VycycpKTtcbiAgICBjb25zdCB1c2Vyc0NvdW50ID0gdXNlcnNTbmFwc2hvdC5zaXplO1xuXG4gICAgLy8gR2V0IG9yZGVycyBjb3VudFxuICAgIGNvbnN0IG9yZGVyc1NuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhjb2xsZWN0aW9uKGRiLCAnb3JkZXJzJykpO1xuICAgIGNvbnN0IG9yZGVyc0NvdW50ID0gb3JkZXJzU25hcHNob3Quc2l6ZTtcblxuICAgIC8vIEdldCBub3RpZmljYXRpb25zIGNvdW50XG4gICAgY29uc3Qgbm90aWZpY2F0aW9uc1NuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhjb2xsZWN0aW9uKGRiLCAnbm90aWZpY2F0aW9ucycpKTtcbiAgICBjb25zdCBub3RpZmljYXRpb25zQ291bnQgPSBub3RpZmljYXRpb25zU25hcHNob3Quc2l6ZTtcblxuICAgIHJldHVybiB7XG4gICAgICB1c2VyczogdXNlcnNDb3VudCxcbiAgICAgIG9yZGVyczogb3JkZXJzQ291bnQsXG4gICAgICBub3RpZmljYXRpb25zOiBub3RpZmljYXRpb25zQ291bnQsXG4gICAgICB0b3RhbDogdXNlcnNDb3VudCArIG9yZGVyc0NvdW50ICsgbm90aWZpY2F0aW9uc0NvdW50XG4gICAgfTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINis2YTYqCDYpdit2LXYp9im2YrYp9iqINmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqjonLCBlcnJvcik7XG4gICAgcmV0dXJuIHtcbiAgICAgIHVzZXJzOiAwLFxuICAgICAgb3JkZXJzOiAwLFxuICAgICAgbm90aWZpY2F0aW9uczogMCxcbiAgICAgIHRvdGFsOiAwXG4gICAgfTtcbiAgfVxufTtcbiJdLCJuYW1lcyI6WyJmaXJlYmFzZUF1dGhTZXJ2aWNlIiwiY29sbGVjdGlvbiIsImFkZERvYyIsInNlcnZlclRpbWVzdGFtcCIsImRiIiwic2V0dXBGaXJlYmFzZURhdGFiYXNlIiwiY29uc29sZSIsImxvZyIsImluaXRpYWxpemVEZWZhdWx0VXNlcnMiLCJjcmVhdGVTYW1wbGVPcmRlcnMiLCJjcmVhdGVTYW1wbGVOb3RpZmljYXRpb25zIiwic3VjY2VzcyIsIm1lc3NhZ2UiLCJlcnJvciIsIkVycm9yIiwic2FtcGxlT3JkZXJzIiwidHJhY2tpbmdOdW1iZXIiLCJjdXN0b21lck5hbWUiLCJjdXN0b21lclBob25lIiwiYWRkcmVzcyIsImFtb3VudCIsInN0YXR1cyIsImNvdXJpZXJOYW1lIiwiYXNzaWduZWRUbyIsIm5vdGVzIiwiY3JlYXRlZEJ5IiwiZGVsaXZlcmVkQXQiLCJEYXRlIiwib3JkZXJzUmVmIiwib3JkZXJEYXRhIiwiY3JlYXRlZEF0IiwidXBkYXRlZEF0Iiwic2FtcGxlTm90aWZpY2F0aW9ucyIsInRpdGxlIiwidHlwZSIsInVzZXJJZCIsIm9yZGVySWQiLCJpc1JlYWQiLCJub3RpZmljYXRpb25zUmVmIiwibm90aWZpY2F0aW9uRGF0YSIsImNoZWNrRmlyZWJhc2VTZXR1cCIsInRlc3RGaXJlYmFzZUNvbm5lY3Rpb24iLCJjb25uZWN0aW9uUmVzdWx0IiwiYXV0aFJlc3VsdCIsImNoZWNrQ29ubmVjdGlvbiIsImNvbm5lY3RlZCIsImdldERhdGFiYXNlU3RhdHMiLCJnZXREb2NzIiwidXNlcnNTbmFwc2hvdCIsInVzZXJzQ291bnQiLCJzaXplIiwib3JkZXJzU25hcHNob3QiLCJvcmRlcnNDb3VudCIsIm5vdGlmaWNhdGlvbnNTbmFwc2hvdCIsIm5vdGlmaWNhdGlvbnNDb3VudCIsInVzZXJzIiwib3JkZXJzIiwibm90aWZpY2F0aW9ucyIsInRvdGFsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/firebase-setup.ts\n"));

/***/ })

});