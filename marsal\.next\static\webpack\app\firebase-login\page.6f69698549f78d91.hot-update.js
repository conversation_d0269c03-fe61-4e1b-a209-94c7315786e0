"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/firebase-login/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   STATUS_COLORS: () => (/* binding */ STATUS_COLORS),\n/* harmony export */   STATUS_LABELS: () => (/* binding */ STATUS_LABELS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Application configuration\nconst APP_CONFIG = {\n    name: \"مرسال\",\n    description: \"نظام إدارة عمليات التوصيل السريع\",\n    version: \"1.1.0\",\n    // Colors\n    colors: {\n        primary: \"#41a7ff\",\n        background: \"#f0f3f5\",\n        accent: \"#b19cd9\"\n    },\n    // Business rules\n    business: {\n        commissionPerOrder: 1000,\n        currency: \"د.ع\",\n        defaultOrderStatuses: [\n            \"pending\",\n            \"assigned\",\n            \"picked_up\",\n            \"in_transit\",\n            \"delivered\",\n            \"returned\",\n            \"cancelled\",\n            \"postponed\"\n        ]\n    },\n    // API endpoints (when backend is ready)\n    api: {\n        baseUrl: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\",\n        endpoints: {\n            orders: \"/api/orders\",\n            users: \"/api/users\",\n            couriers: \"/api/couriers\",\n            settlements: \"/api/settlements\"\n        }\n    },\n    // Firebase config - إعدادات قاعدة البيانات السحابية الحقيقية\n    firebase: {\n        apiKey: \"AIzaSyDemoKeyForMarsalDeliveryApp123456789\" || 0,\n        authDomain: \"marsal-delivery-app.firebaseapp.com\" || 0,\n        projectId: \"marsal-delivery-app\" || 0,\n        storageBucket: \"marsal-delivery-app.appspot.com\" || 0,\n        messagingSenderId: \"123456789012\" || 0,\n        appId: \"1:123456789012:web:abcdef123456789012345\" || 0,\n        measurementId: \"G-XXXXXXXXXX\" || 0\n    },\n    // Features flags\n    features: {\n        enableNotifications: true,\n        enableReports: true,\n        enableBulkOperations: true,\n        enableImageUpload: true\n    },\n    // Firebase Production mode - وضع الإنتاج مع Firebase حصرياً\n    demo: {\n        enabled: false,\n        autoLogin: false,\n        defaultUser: null,\n        skipFirebase: false,\n        showDemoNotice: false // إخفاء تنبيه الوضع التجريبي\n    },\n    // Company info\n    company: {\n        name: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        phone: '+*********** 4567',\n        address: 'بغداد، العراق'\n    },\n    // Receipt settings - إعدادات الوصل\n    receipt: {\n        dimensions: {\n            width: '110mm',\n            height: '130mm'\n        },\n        companyName: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        showBarcode: true,\n        showDate: true,\n        priceFormat: 'en-US',\n        currency: 'IQD',\n        fields: {\n            trackingNumber: true,\n            customerPhone: true,\n            status: true,\n            courierName: true,\n            amount: true,\n            barcode: true,\n            date: true\n        }\n    }\n};\n// Status labels in Arabic\nconst STATUS_LABELS = {\n    pending: \"في الانتظار\",\n    assigned: \"مسند\",\n    picked_up: \"تم الاستلام\",\n    in_transit: \"في الطريق\",\n    delivered: \"تم التسليم\",\n    returned: \"راجع\",\n    cancelled: \"ملغي\",\n    postponed: \"مؤجل\"\n};\n// Status colors for UI\nconst STATUS_COLORS = {\n    pending: \"text-yellow-600 bg-yellow-100\",\n    assigned: \"text-blue-600 bg-blue-100\",\n    picked_up: \"text-purple-600 bg-purple-100\",\n    in_transit: \"text-orange-600 bg-orange-100\",\n    delivered: \"text-green-600 bg-green-100\",\n    returned: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    postponed: \"text-gray-600 bg-gray-100\"\n};\n// User roles\nconst USER_ROLES = {\n    admin: \"مدير\",\n    manager: \"مدير فرع\",\n    dispatcher: \"موزع\",\n    courier: \"مندوب\",\n    accountant: \"محاسب\",\n    viewer: \"مشاهد\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});