(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[171],{285:(e,r,s)=>{"use strict";s.d(r,{$:()=>l});var t=s(5155);s(2115);var n=s(9708),a=s(2085),i=s(9434);let d=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:s,size:a,asChild:l=!1,...c}=e,o=l?n.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,i.cn)(d({variant:s,size:a,className:r})),...c})}},2138:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2523:(e,r,s)=>{"use strict";s.d(r,{p:()=>a});var t=s(5155);s(2115);var n=s(9434);function a(e){let{className:r,type:s,...a}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...a})}},4116:(e,r,s)=>{Promise.resolve().then(s.bind(s,9353))},4229:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},6695:(e,r,s)=>{"use strict";s.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>a,aR:()=>i});var t=s(5155);s(2115);var n=s(9434);function a(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...s})}function i(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...s})}function d(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",r),...s})}function l(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",r),...s})}function c(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",r),...s})}},7108:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},9353:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>m});var t=s(5155),n=s(2115),a=s(6695),i=s(285),d=s(2523),l=s(7108),c=s(2138),o=s(4229),u=s(6874),x=s.n(u);function m(){let[e,r]=(0,n.useState)({senderName:"",senderPhone:"",senderAddress:"",recipientName:"",recipientPhone:"",recipientAddress:"",amount:"",notes:""}),s=e=>{let{name:s,value:t}=e.target;r(e=>({...e,[s]:t}))};return(0,t.jsx)("div",{className:"min-h-screen bg-background p-4 md:p-6 lg:p-8",dir:"rtl",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-primary flex items-center gap-2",children:[(0,t.jsx)(l.A,{className:"h-8 w-8"}),"طلب جديد"]}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2",children:"إضافة طلب توصيل جديد إلى النظام"})]}),(0,t.jsx)(x(),{href:"/orders",children:(0,t.jsxs)(i.$,{variant:"outline",className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4"}),"العودة للطلبات"]})})]}),(0,t.jsxs)("form",{onSubmit:r=>{r.preventDefault(),console.log("Order data:",e),alert("تم إضافة الطلب بنجاح!")},className:"space-y-6",children:[(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"بيانات المرسل"}),(0,t.jsx)(a.BT,{children:"معلومات الشخص أو الشركة المرسلة"})]}),(0,t.jsxs)(a.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اسم المرسل *"}),(0,t.jsx)(d.p,{name:"senderName",value:e.senderName,onChange:s,placeholder:"أدخل اسم المرسل",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"هاتف المرسل *"}),(0,t.jsx)(d.p,{name:"senderPhone",value:e.senderPhone,onChange:s,placeholder:"07xxxxxxxxx",required:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"عنوان المرسل *"}),(0,t.jsx)(d.p,{name:"senderAddress",value:e.senderAddress,onChange:s,placeholder:"العنوان الكامل للمرسل",required:!0})]})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"بيانات المستلم"}),(0,t.jsx)(a.BT,{children:"معلومات الشخص المستلم للطلب"})]}),(0,t.jsxs)(a.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اسم المستلم *"}),(0,t.jsx)(d.p,{name:"recipientName",value:e.recipientName,onChange:s,placeholder:"أدخل اسم المستلم",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"هاتف المستلم *"}),(0,t.jsx)(d.p,{name:"recipientPhone",value:e.recipientPhone,onChange:s,placeholder:"07xxxxxxxxx",required:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"عنوان المستلم *"}),(0,t.jsx)(d.p,{name:"recipientAddress",value:e.recipientAddress,onChange:s,placeholder:"العنوان الكامل للمستلم",required:!0})]})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"تفاصيل الطلب"}),(0,t.jsx)(a.BT,{children:"المبلغ والملاحظات الإضافية"})]}),(0,t.jsxs)(a.Wu,{className:"space-y-4",children:[(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"المبلغ (دينار عراقي) *"}),(0,t.jsx)(d.p,{name:"amount",type:"number",value:e.amount,onChange:s,placeholder:"0",required:!0})]})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"ملاحظات"}),(0,t.jsx)("textarea",{name:"notes",value:e.notes,onChange:s,placeholder:"أي ملاحظات إضافية...",className:"w-full p-3 border border-border rounded-md bg-background resize-none",rows:3})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,t.jsx)(x(),{href:"/orders",children:(0,t.jsx)(i.$,{variant:"outline",children:"إلغاء"})}),(0,t.jsxs)(i.$,{type:"submit",className:"flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),"حفظ الطلب"]})]})]})]})})}},9434:(e,r,s)=>{"use strict";s.d(r,{Yq:()=>d,cn:()=>a,ps:()=>x,qY:()=>u,r6:()=>l,vv:()=>i,y7:()=>c,zC:()=>o});var t=s(2596),n=s(9688);function a(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,n.QP)((0,t.$)(r))}function i(e){return"".concat(e.toLocaleString("ar-IQ")," د.ع")}function d(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function l(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function c(){let e=Date.now().toString().slice(-6),r=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"".concat("MRS").concat(e).concat(r)}function o(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function u(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function x(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}}},e=>{var r=r=>e(e.s=r);e.O(0,[455,874,441,684,358],()=>r(4116)),_N_E=e.O()}]);