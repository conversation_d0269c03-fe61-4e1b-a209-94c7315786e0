<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Firebase الشامل - تطبيق مرسال</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', 'Tahoma', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 2px solid #e0e0e0;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status {
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn.secondary {
            background: #6c757d;
        }

        .btn.danger {
            background: #dc3545;
        }

        .btn.success {
            background: #28a745;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .info-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
        }

        .info-card h4 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .info-list {
            list-style: none;
            padding: 0;
        }

        .info-list li {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-list li:last-child {
            border-bottom: none;
        }

        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }

        .config-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .config-section h4 {
            color: #856404;
            margin-bottom: 15px;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #ffeaa7;
        }

        .config-item:last-child {
            border-bottom: none;
        }

        .config-key {
            font-weight: bold;
            color: #856404;
        }

        .config-value {
            font-family: monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 اختبار Firebase الشامل</h1>
            <p>فحص شامل لجميع خدمات Firebase في تطبيق مرسال</p>
        </div>

        <div class="content">
            <!-- إعدادات Firebase -->
            <div class="config-section">
                <h4>⚙️ إعدادات Firebase الحالية</h4>
                <div id="firebaseConfig">
                    <div class="config-item">
                        <span class="config-key">Project ID:</span>
                        <span class="config-value" id="projectId">جاري التحميل...</span>
                    </div>
                    <div class="config-item">
                        <span class="config-key">Auth Domain:</span>
                        <span class="config-value" id="authDomain">جاري التحميل...</span>
                    </div>
                    <div class="config-item">
                        <span class="config-key">Storage Bucket:</span>
                        <span class="config-value" id="storageBucket">جاري التحميل...</span>
                    </div>
                    <div class="config-item">
                        <span class="config-key">حالة الاتصال:</span>
                        <span class="config-value" id="connectionStatus">جاري الفحص...</span>
                    </div>
                </div>
            </div>

            <!-- اختبار المصادقة -->
            <div class="test-section">
                <h3>🔐 اختبار نظام المصادقة (Authentication)</h3>
                <div id="authStatus" class="status loading">جاري فحص نظام المصادقة...</div>
                
                <div class="info-grid">
                    <div class="info-card">
                        <h4>المستخدمين الافتراضيين</h4>
                        <ul class="info-list">
                            <li><strong>مدير:</strong> <EMAIL> / 123456</li>
                            <li><strong>متابع:</strong> <EMAIL> / 123456</li>
                            <li><strong>مندوب:</strong> <EMAIL> / 123456</li>
                        </ul>
                    </div>
                    
                    <div class="info-card">
                        <h4>اختبارات المصادقة</h4>
                        <button class="btn" onclick="testAuth()">🔍 فحص نظام المصادقة</button>
                        <button class="btn secondary" onclick="testLogin()">🔑 اختبار تسجيل الدخول</button>
                        <button class="btn danger" onclick="testLogout()">🚪 اختبار تسجيل الخروج</button>
                    </div>
                </div>
            </div>

            <!-- اختبار Firestore -->
            <div class="test-section">
                <h3>🗄️ اختبار قاعدة البيانات (Firestore)</h3>
                <div id="firestoreStatus" class="status loading">جاري فحص قاعدة البيانات...</div>
                
                <div class="info-grid">
                    <div class="info-card">
                        <h4>إحصائيات قاعدة البيانات</h4>
                        <ul class="info-list">
                            <li>عدد المستخدمين: <span id="usersCount">-</span></li>
                            <li>عدد الطلبات: <span id="ordersCount">-</span></li>
                            <li>عدد الإشعارات: <span id="notificationsCount">-</span></li>
                        </ul>
                    </div>
                    
                    <div class="info-card">
                        <h4>اختبارات قاعدة البيانات</h4>
                        <button class="btn" onclick="testFirestore()">🔍 فحص قاعدة البيانات</button>
                        <button class="btn secondary" onclick="testCreateOrder()">📦 إنشاء طلب تجريبي</button>
                        <button class="btn success" onclick="initializeData()">🚀 إعداد البيانات الأولية</button>
                    </div>
                </div>
            </div>

            <!-- اختبار التحديثات الفورية -->
            <div class="test-section">
                <h3>🔄 اختبار التحديثات الفورية (Real-time)</h3>
                <div id="realtimeStatus" class="status loading">جاري فحص التحديثات الفورية...</div>
                
                <div class="info-grid">
                    <div class="info-card">
                        <h4>الاشتراكات النشطة</h4>
                        <ul class="info-list">
                            <li>اشتراك الطلبات: <span id="ordersSubscription">غير نشط</span></li>
                            <li>اشتراك الإشعارات: <span id="notificationsSubscription">غير نشط</span></li>
                            <li>عدد التحديثات: <span id="updatesCount">0</span></li>
                        </ul>
                    </div>
                    
                    <div class="info-card">
                        <h4>اختبارات التحديثات الفورية</h4>
                        <button class="btn" onclick="testRealtime()">🔍 فحص التحديثات الفورية</button>
                        <button class="btn secondary" onclick="startListening()">👂 بدء الاستماع</button>
                        <button class="btn danger" onclick="stopListening()">🔇 إيقاف الاستماع</button>
                    </div>
                </div>
            </div>

            <!-- سجل العمليات -->
            <div class="test-section">
                <h3>📋 سجل العمليات</h3>
                <button class="btn secondary" onclick="clearLog()">🗑️ مسح السجل</button>
                <button class="btn" onclick="exportLog()">💾 تصدير السجل</button>
                <div id="logArea" class="log-area">
                    جاري تحميل Firebase SDK...
                </div>
            </div>

            <!-- أزرار التحكم -->
            <div style="text-align: center; margin: 30px 0;">
                <button class="btn success" onclick="runAllTests()">🧪 تشغيل جميع الاختبارات</button>
                <button class="btn" onclick="resetTests()">🔄 إعادة تعيين</button>
                <button class="btn secondary" onclick="goToApp()">🚀 الذهاب للتطبيق</button>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDemoKeyForMarsalDeliveryApp123456789",
            authDomain: "marsal-delivery-app.firebaseapp.com",
            projectId: "marsal-delivery-app",
            storageBucket: "marsal-delivery-app.appspot.com",
            messagingSenderId: "123456789012",
            appId: "1:123456789012:web:abcdef123456789012345",
            measurementId: "G-XXXXXXXXXX"
        };

        let app, auth, db, storage;
        let subscriptions = [];
        let updatesCount = 0;

        // Initialize Firebase
        async function initializeFirebase() {
            try {
                log('🔥 بدء تحميل Firebase SDK...');
                
                // Import Firebase modules
                const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
                const { getAuth, onAuthStateChanged } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
                const { getFirestore, connectFirestoreEmulator } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
                const { getStorage } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js');

                // Initialize Firebase
                app = initializeApp(firebaseConfig);
                auth = getAuth(app);
                db = getFirestore(app);
                storage = getStorage(app);

                log('✅ تم تحميل Firebase SDK بنجاح');
                
                // Update config display
                updateConfigDisplay();
                
                // Start initial tests
                await runAllTests();
                
            } catch (error) {
                log('❌ خطأ في تحميل Firebase SDK: ' + error.message);
                updateStatus('authStatus', 'error', '❌ فشل تحميل Firebase SDK');
                updateStatus('firestoreStatus', 'error', '❌ فشل تحميل Firebase SDK');
                updateStatus('realtimeStatus', 'error', '❌ فشل تحميل Firebase SDK');
            }
        }

        // Utility functions
        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        function updateStatus(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
        }

        function updateConfigDisplay() {
            document.getElementById('projectId').textContent = firebaseConfig.projectId;
            document.getElementById('authDomain').textContent = firebaseConfig.authDomain;
            document.getElementById('storageBucket').textContent = firebaseConfig.storageBucket;
            document.getElementById('connectionStatus').textContent = '✅ متصل';
        }

        // Test functions
        window.testAuth = async function() {
            try {
                log('🔐 اختبار نظام المصادقة...');
                updateStatus('authStatus', 'loading', 'جاري اختبار المصادقة...');
                
                if (!auth) {
                    throw new Error('Firebase Auth غير مُحمل');
                }
                
                log('✅ نظام المصادقة جاهز');
                updateStatus('authStatus', 'success', '✅ نظام المصادقة يعمل بنجاح');
                
            } catch (error) {
                log('❌ خطأ في اختبار المصادقة: ' + error.message);
                updateStatus('authStatus', 'error', '❌ فشل اختبار المصادقة');
            }
        };

        window.testFirestore = async function() {
            try {
                log('🗄️ اختبار قاعدة البيانات...');
                updateStatus('firestoreStatus', 'loading', 'جاري اختبار قاعدة البيانات...');
                
                if (!db) {
                    throw new Error('Firestore غير مُحمل');
                }
                
                const { collection, getDocs } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
                
                // Test collections
                const usersSnapshot = await getDocs(collection(db, 'users'));
                const ordersSnapshot = await getDocs(collection(db, 'orders'));
                const notificationsSnapshot = await getDocs(collection(db, 'notifications'));
                
                document.getElementById('usersCount').textContent = usersSnapshot.size;
                document.getElementById('ordersCount').textContent = ordersSnapshot.size;
                document.getElementById('notificationsCount').textContent = notificationsSnapshot.size;
                
                log('✅ قاعدة البيانات تعمل بنجاح');
                updateStatus('firestoreStatus', 'success', '✅ قاعدة البيانات متصلة ومتاحة');
                
            } catch (error) {
                log('❌ خطأ في اختبار قاعدة البيانات: ' + error.message);
                updateStatus('firestoreStatus', 'error', '❌ فشل اختبار قاعدة البيانات');
            }
        };

        window.testRealtime = async function() {
            try {
                log('🔄 اختبار التحديثات الفورية...');
                updateStatus('realtimeStatus', 'loading', 'جاري اختبار التحديثات الفورية...');
                
                if (!db) {
                    throw new Error('Firestore غير مُحمل');
                }
                
                log('✅ نظام التحديثات الفورية جاهز');
                updateStatus('realtimeStatus', 'success', '✅ التحديثات الفورية تعمل بنجاح');
                
            } catch (error) {
                log('❌ خطأ في اختبار التحديثات الفورية: ' + error.message);
                updateStatus('realtimeStatus', 'error', '❌ فشل اختبار التحديثات الفورية');
            }
        };

        window.testLogin = async function() {
            try {
                log('🔑 اختبار تسجيل الدخول...');

                if (!auth) {
                    throw new Error('Firebase Auth غير مُحمل');
                }

                // محاولة تسجيل الدخول بحساب المدير
                const email = '<EMAIL>';
                const password = '123456';

                const { signInWithEmailAndPassword } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');

                const userCredential = await signInWithEmailAndPassword(auth, email, password);
                log('✅ تم تسجيل الدخول بنجاح: ' + userCredential.user.email);

                // اختبار الحصول على بيانات المستخدم
                const { doc, getDoc } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
                const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));

                if (userDoc.exists()) {
                    log('✅ تم العثور على بيانات المستخدم: ' + userDoc.data().name);
                } else {
                    log('⚠️ لم يتم العثور على بيانات المستخدم في Firestore');
                }

            } catch (error) {
                log('❌ خطأ في تسجيل الدخول: ' + error.message);

                // إذا فشل تسجيل الدخول، جرب إنشاء المستخدم
                if (error.code === 'auth/user-not-found') {
                    log('🔧 محاولة إنشاء المستخدم...');
                    await createDefaultUsers();
                }
            }
        };

        window.testLogout = async function() {
            try {
                log('🚪 اختبار تسجيل الخروج...');

                if (!auth) {
                    throw new Error('Firebase Auth غير مُحمل');
                }

                const { signOut } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
                await signOut(auth);

                log('✅ تم تسجيل الخروج بنجاح');

            } catch (error) {
                log('❌ خطأ في تسجيل الخروج: ' + error.message);
            }
        };

        window.initializeData = async function() {
            try {
                log('🚀 إعداد البيانات الأولية...');
                await createDefaultUsers();
                await createSampleOrders();
                log('✅ تم إعداد البيانات الأولية بنجاح');
            } catch (error) {
                log('❌ خطأ في إعداد البيانات: ' + error.message);
            }
        };

        window.testCreateOrder = async function() {
            try {
                log('📦 إنشاء طلب تجريبي...');

                const { collection, addDoc, serverTimestamp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

                const orderData = {
                    trackingNumber: 'TEST_' + Date.now(),
                    customerName: 'أحمد محمد',
                    customerPhone: '07701234567',
                    address: 'بغداد - الكرادة',
                    amount: 25000,
                    status: 'pending',
                    courierName: '',
                    assignedTo: '',
                    notes: 'طلب تجريبي',
                    createdAt: serverTimestamp(),
                    updatedAt: serverTimestamp(),
                    createdBy: 'test'
                };

                const docRef = await addDoc(collection(db, 'orders'), orderData);
                log('✅ تم إنشاء الطلب التجريبي: ' + docRef.id);

            } catch (error) {
                log('❌ خطأ في إنشاء الطلب: ' + error.message);
            }
        };

        // إنشاء المستخدمين الافتراضيين
        async function createDefaultUsers() {
            try {
                log('👤 إنشاء المستخدمين الافتراضيين...');

                const { createUserWithEmailAndPassword } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
                const { doc, setDoc, serverTimestamp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

                const users = [
                    {
                        email: '<EMAIL>',
                        password: '123456',
                        username: 'manager',
                        name: 'مدير النظام',
                        phone: '07801234567',
                        role: 'manager'
                    },
                    {
                        email: '<EMAIL>',
                        password: '123456',
                        username: 'supervisor',
                        name: 'المتابع العام',
                        phone: '07801234568',
                        role: 'supervisor'
                    },
                    {
                        email: '<EMAIL>',
                        password: '123456',
                        username: 'courier',
                        name: 'مندوب التوصيل',
                        phone: '07801234569',
                        role: 'courier'
                    }
                ];

                for (const userData of users) {
                    try {
                        // إنشاء المستخدم في Firebase Auth
                        const userCredential = await createUserWithEmailAndPassword(auth, userData.email, userData.password);

                        // إضافة بيانات المستخدم إلى Firestore
                        await setDoc(doc(db, 'users', userCredential.user.uid), {
                            username: userData.username,
                            email: userData.email,
                            name: userData.name,
                            phone: userData.phone,
                            role: userData.role,
                            isActive: true,
                            createdAt: serverTimestamp(),
                            updatedAt: serverTimestamp(),
                            createdBy: 'system'
                        });

                        log('✅ تم إنشاء المستخدم: ' + userData.name);

                    } catch (error) {
                        if (error.code === 'auth/email-already-in-use') {
                            log('⚠️ المستخدم موجود مسبقاً: ' + userData.name);
                        } else {
                            log('❌ خطأ في إنشاء المستخدم ' + userData.name + ': ' + error.message);
                        }
                    }
                }

            } catch (error) {
                log('❌ خطأ في إنشاء المستخدمين: ' + error.message);
            }
        }

        // إنشاء طلبات تجريبية
        async function createSampleOrders() {
            try {
                log('📦 إنشاء طلبات تجريبية...');

                const { collection, addDoc, serverTimestamp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

                const orders = [
                    {
                        trackingNumber: 'ORDER_001',
                        customerName: 'أحمد محمد',
                        customerPhone: '07701234567',
                        address: 'بغداد - الكرادة',
                        amount: 25000,
                        status: 'pending'
                    },
                    {
                        trackingNumber: 'ORDER_002',
                        customerName: 'فاطمة علي',
                        customerPhone: '07701234568',
                        address: 'البصرة - المعقل',
                        amount: 35000,
                        status: 'delivered'
                    }
                ];

                for (const orderData of orders) {
                    await addDoc(collection(db, 'orders'), {
                        ...orderData,
                        courierName: '',
                        assignedTo: '',
                        notes: 'طلب تجريبي',
                        createdAt: serverTimestamp(),
                        updatedAt: serverTimestamp(),
                        createdBy: 'system'
                    });

                    log('✅ تم إنشاء الطلب: ' + orderData.trackingNumber);
                }

            } catch (error) {
                log('❌ خطأ في إنشاء الطلبات: ' + error.message);
            }
        }

        window.runAllTests = async function() {
            log('🧪 بدء تشغيل جميع الاختبارات...');
            await testAuth();
            await testFirestore();
            await testRealtime();
            log('✅ انتهاء جميع الاختبارات');
        };

        window.clearLog = function() {
            document.getElementById('logArea').innerHTML = '';
        };

        window.goToApp = function() {
            window.open('out/index.html', '_blank');
        };

        // Initialize when page loads
        window.addEventListener('load', initializeFirebase);
    </script>
</body>
</html>
