"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8321],{3411:(r,t,e)=>{e.d(t,{A:()=>h});var n=e(25969),o=e(79417),a=e(71534),i=e(438),f=e(69071),c=e(56595),u=e(1933),A=e(322),d=e(22152),l=function(){var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(t,e)};return function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),s=function(r){var t="function"==typeof Symbol&&Symbol.iterator,e=t&&r[t],n=0;if(e)return e.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};let h=function(r){function t(){var t=null!==r&&r.apply(this,arguments)||this;return t.narrowLineWidth=-1,t}return l(t,r),t.prototype.decodeRow=function(r,e,i){var A,d,l=this.decodeStart(e),h=this.decodeEnd(e),y=new u.A;t.decodeMiddle(e,l[1],h[0],y);var _=y.toString(),w=null;null!=i&&(w=i.get(o.A.ALLOWED_LENGTHS)),null==w&&(w=t.DEFAULT_ALLOWED_LENGTHS);var p=_.length,C=!1,E=0;try{for(var v=s(w),I=v.next();!I.done;I=v.next()){var g=I.value;if(p===g){C=!0;break}g>E&&(E=g)}}catch(r){A={error:r}}finally{try{I&&!I.done&&(d=v.return)&&d.call(v)}finally{if(A)throw A.error}}if(!C&&p>E&&(C=!0),!C)throw new a.A;var S=[new c.A(l[1],r),new c.A(h[0],r)];return new f.A(_,null,0,S,n.A.ITF,new Date().getTime())},t.decodeMiddle=function(r,e,n,o){var a=new Int32Array(10),i=new Int32Array(5),f=new Int32Array(5);for(a.fill(0),i.fill(0),f.fill(0);e<n;){d.A.recordPattern(r,e,a);for(var c=0;c<5;c++){var u=2*c;i[c]=a[u],f[c]=a[u+1]}var A=t.decodeDigit(i);o.append(A.toString()),A=this.decodeDigit(f),o.append(A.toString()),a.forEach(function(r){e+=r})}},t.prototype.decodeStart=function(r){var e=t.skipWhiteSpace(r),n=t.findGuardPattern(r,e,t.START_PATTERN);return this.narrowLineWidth=(n[1]-n[0])/4,this.validateQuietZone(r,n[0]),n},t.prototype.validateQuietZone=function(r,t){var e=10*this.narrowLineWidth;e=e<t?e:t;for(var n=t-1;e>0&&n>=0&&!r.get(n);n--)e--;if(0!==e)throw new i.A},t.skipWhiteSpace=function(r){var t=r.getSize(),e=r.getNextSet(0);if(e===t)throw new i.A;return e},t.prototype.decodeEnd=function(r){r.reverse();try{var e=t.skipWhiteSpace(r),n=void 0;try{n=t.findGuardPattern(r,e,t.END_PATTERN_REVERSED[0])}catch(o){o instanceof i.A&&(n=t.findGuardPattern(r,e,t.END_PATTERN_REVERSED[1]))}this.validateQuietZone(r,n[0]);var o=n[0];return n[0]=r.getSize()-n[1],n[1]=r.getSize()-o,n}finally{r.reverse()}},t.findGuardPattern=function(r,e,n){var o=n.length,a=new Int32Array(o),f=r.getSize(),c=!1,u=0,l=e;a.fill(0);for(var s=e;s<f;s++)if(r.get(s)!==c)a[u]++;else{if(u===o-1){if(d.A.patternMatchVariance(a,n,t.MAX_INDIVIDUAL_VARIANCE)<t.MAX_AVG_VARIANCE)return[l,s];l+=a[0]+a[1],A.A.arraycopy(a,2,a,0,u-1),a[u-1]=0,a[u]=0,u--}else u++;a[u]=1,c=!c}throw new i.A},t.decodeDigit=function(r){for(var e=t.MAX_AVG_VARIANCE,n=-1,o=t.PATTERNS.length,a=0;a<o;a++){var f=t.PATTERNS[a],c=d.A.patternMatchVariance(r,f,t.MAX_INDIVIDUAL_VARIANCE);c<e?(e=c,n=a):c===e&&(n=-1)}if(n>=0)return n%10;throw new i.A},t.PATTERNS=[Int32Array.from([1,1,2,2,1]),Int32Array.from([2,1,1,1,2]),Int32Array.from([1,2,1,1,2]),Int32Array.from([2,2,1,1,1]),Int32Array.from([1,1,2,1,2]),Int32Array.from([2,1,2,1,1]),Int32Array.from([1,2,2,1,1]),Int32Array.from([1,1,1,2,2]),Int32Array.from([2,1,1,2,1]),Int32Array.from([1,2,1,2,1]),Int32Array.from([1,1,3,3,1]),Int32Array.from([3,1,1,1,3]),Int32Array.from([1,3,1,1,3]),Int32Array.from([3,3,1,1,1]),Int32Array.from([1,1,3,1,3]),Int32Array.from([3,1,3,1,1]),Int32Array.from([1,3,3,1,1]),Int32Array.from([1,1,1,3,3]),Int32Array.from([3,1,1,3,1]),Int32Array.from([1,3,1,3,1])],t.MAX_AVG_VARIANCE=.38,t.MAX_INDIVIDUAL_VARIANCE=.5,t.DEFAULT_ALLOWED_LENGTHS=[6,8,10,12,14],t.START_PATTERN=Int32Array.from([1,1,1,1]),t.END_PATTERN_REVERSED=[Int32Array.from([1,1,2]),Int32Array.from([1,1,3])],t}(d.A)},10692:(r,t,e)=>{e.d(t,{A:()=>N});var n=e(25969),o=e(79417),a=e(438),i=e(45332),f=e(65649),c=e(73977),u=e(3411),A=e(69071),d=e(22152),l=e(28822),s=e(49001),h=function(){var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(t,e)};return function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),y=function(r){var t="function"==typeof Symbol&&Symbol.iterator,e=t&&r[t],n=0;if(e)return e.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},_=function(r){function t(){var t=r.call(this)||this;return t.decodeMiddleCounters=Int32Array.from([0,0,0,0]),t}return h(t,r),t.prototype.decodeMiddle=function(r,t,e){var n,o,a,i,f=this.decodeMiddleCounters;f[0]=0,f[1]=0,f[2]=0,f[3]=0;for(var c=r.getSize(),u=t[1],A=0;A<4&&u<c;A++){var d=s.A.decodeDigit(r,f,u,s.A.L_PATTERNS);e+=String.fromCharCode(48+d);try{for(var l=(n=void 0,y(f)),h=l.next();!h.done;h=l.next()){var _=h.value;u+=_}}catch(r){n={error:r}}finally{try{h&&!h.done&&(o=l.return)&&o.call(l)}finally{if(n)throw n.error}}}u=s.A.findGuardPattern(r,u,!0,s.A.MIDDLE_PATTERN,new Int32Array(s.A.MIDDLE_PATTERN.length).fill(0))[1];for(var A=0;A<4&&u<c;A++){var d=s.A.decodeDigit(r,f,u,s.A.L_PATTERNS);e+=String.fromCharCode(48+d);try{for(var w=(a=void 0,y(f)),p=w.next();!p.done;p=w.next()){var _=p.value;u+=_}}catch(r){a={error:r}}finally{try{p&&!p.done&&(i=w.return)&&i.call(w)}finally{if(a)throw a.error}}}return{rowOffset:u,resultString:e}},t.prototype.getBarcodeFormat=function(){return n.A.EAN_8},t}(s.A),w=function(){var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(t,e)};return function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),p=function(r){function t(){var t=null!==r&&r.apply(this,arguments)||this;return t.ean13Reader=new l.A,t}return w(t,r),t.prototype.getBarcodeFormat=function(){return n.A.UPC_A},t.prototype.decode=function(r,t){return this.maybeReturnResult(this.ean13Reader.decode(r))},t.prototype.decodeRow=function(r,t,e){return this.maybeReturnResult(this.ean13Reader.decodeRow(r,t,e))},t.prototype.decodeMiddle=function(r,t,e){return this.ean13Reader.decodeMiddle(r,t,e)},t.prototype.maybeReturnResult=function(r){var t=r.getText();if("0"===t.charAt(0)){var e=new A.A(t.substring(1),null,null,r.getResultPoints(),n.A.UPC_A);return null!=r.getResultMetadata()&&e.putAllMetadata(r.getResultMetadata()),e}throw new a.A},t.prototype.reset=function(){this.ean13Reader.reset()},t}(s.A),C=e(1933),E=function(){var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(t,e)};return function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),v=function(r){var t="function"==typeof Symbol&&Symbol.iterator,e=t&&r[t],n=0;if(e)return e.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},I=function(r){function t(){var t=r.call(this)||this;return t.decodeMiddleCounters=new Int32Array(4),t}return E(t,r),t.prototype.decodeMiddle=function(r,e,n){var o,a,i=this.decodeMiddleCounters.map(function(r){return r});i[0]=0,i[1]=0,i[2]=0,i[3]=0;for(var f=r.getSize(),c=e[1],u=0,A=0;A<6&&c<f;A++){var d=t.decodeDigit(r,i,c,t.L_AND_G_PATTERNS);n+=String.fromCharCode(48+d%10);try{for(var l=(o=void 0,v(i)),s=l.next();!s.done;s=l.next()){var h=s.value;c+=h}}catch(r){o={error:r}}finally{try{s&&!s.done&&(a=l.return)&&a.call(l)}finally{if(o)throw o.error}}d>=10&&(u|=1<<5-A)}return t.determineNumSysAndCheckDigit(new C.A(n),u),c},t.prototype.decodeEnd=function(r,e){return t.findGuardPatternWithoutCounters(r,e,!0,t.MIDDLE_END_PATTERN)},t.prototype.checkChecksum=function(r){return s.A.checkChecksum(t.convertUPCEtoUPCA(r))},t.determineNumSysAndCheckDigit=function(r,t){for(var e=0;e<=1;e++)for(var n=0;n<10;n++)if(t===this.NUMSYS_AND_CHECK_DIGIT_PATTERNS[e][n]){r.insert(0,"0"+e),r.append("0"+n);return}throw a.A.getNotFoundInstance()},t.prototype.getBarcodeFormat=function(){return n.A.UPC_E},t.convertUPCEtoUPCA=function(r){var t=r.slice(1,7).split("").map(function(r){return r.charCodeAt(0)}),e=new C.A;e.append(r.charAt(0));var n=t[5];switch(n){case 0:case 1:case 2:e.appendChars(t,0,2),e.append(n),e.append("0000"),e.appendChars(t,2,3);break;case 3:e.appendChars(t,0,3),e.append("00000"),e.appendChars(t,3,2);break;case 4:e.appendChars(t,0,4),e.append("00000"),e.append(t[4]);break;default:e.appendChars(t,0,5),e.append("0000"),e.append(n)}return r.length>=8&&e.append(r.charAt(7)),e.toString()},t.MIDDLE_END_PATTERN=Int32Array.from([1,1,1,1,1,1]),t.NUMSYS_AND_CHECK_DIGIT_PATTERNS=[Int32Array.from([56,52,50,49,44,38,35,42,41,37]),Int32Array.from([7,11,13,14,19,25,28,21,22,1])],t}(s.A),g=function(){var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(t,e)};return function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),S=function(r){var t="function"==typeof Symbol&&Symbol.iterator,e=t&&r[t],n=0;if(e)return e.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},m=function(r){function t(t){var e=r.call(this)||this,a=null==t?null:t.get(o.A.POSSIBLE_FORMATS),i=[];return null!=a&&(a.indexOf(n.A.EAN_13)>-1&&i.push(new l.A),a.indexOf(n.A.UPC_A)>-1&&i.push(new p),a.indexOf(n.A.EAN_8)>-1&&i.push(new _),a.indexOf(n.A.UPC_E)>-1&&i.push(new I)),0===i.length&&(i.push(new l.A),i.push(new p),i.push(new _),i.push(new I)),e.readers=i,e}return g(t,r),t.prototype.decodeRow=function(r,t,e){var i,f;try{for(var c=S(this.readers),u=c.next();!u.done;u=c.next()){var d=u.value;try{var l=d.decodeRow(r,t,e),s=l.getBarcodeFormat()===n.A.EAN_13&&"0"===l.getText().charAt(0),h=null==e?null:e.get(o.A.POSSIBLE_FORMATS),y=null==h||h.includes(n.A.UPC_A);if(s&&y){var _=l.getRawBytes(),w=new A.A(l.getText().substring(1),_,_?_.length:null,l.getResultPoints(),n.A.UPC_A);return w.putAllMetadata(l.getResultMetadata()),w}return l}catch(r){}}}catch(r){i={error:r}}finally{try{u&&!u.done&&(f=c.return)&&f.call(c)}finally{if(i)throw i.error}}throw new a.A},t.prototype.reset=function(){var r,t;try{for(var e=S(this.readers),n=e.next();!n.done;n=e.next())n.value.reset()}catch(t){r={error:t}}finally{try{n&&!n.done&&(t=e.return)&&t.call(e)}finally{if(r)throw r.error}}},t}(d.A),R=e(64476),O=e(2605),T=e(10659),D=function(){var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(t,e)};return function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}();let N=function(r){function t(t){var e=r.call(this)||this;e.readers=[];var a=t?t.get(o.A.POSSIBLE_FORMATS):null,A=t&&void 0!==t.get(o.A.ASSUME_CODE_39_CHECK_DIGIT),d=t&&void 0!==t.get(o.A.ENABLE_CODE_39_EXTENDED_MODE);return a&&((a.includes(n.A.EAN_13)||a.includes(n.A.UPC_A)||a.includes(n.A.EAN_8)||a.includes(n.A.UPC_E))&&e.readers.push(new m(t)),a.includes(n.A.CODE_39)&&e.readers.push(new f.A(A,d)),a.includes(n.A.CODE_93)&&e.readers.push(new c.A),a.includes(n.A.CODE_128)&&e.readers.push(new i.A),a.includes(n.A.ITF)&&e.readers.push(new u.A),a.includes(n.A.CODABAR)&&e.readers.push(new R.A),a.includes(n.A.RSS_14)&&e.readers.push(new T.A),a.includes(n.A.RSS_EXPANDED)&&(console.warn("RSS Expanded reader IS NOT ready for production yet! use at your own risk."),e.readers.push(new O.A))),0===e.readers.length&&(e.readers.push(new m(t)),e.readers.push(new f.A),e.readers.push(new c.A),e.readers.push(new m(t)),e.readers.push(new i.A),e.readers.push(new u.A),e.readers.push(new T.A)),e}return D(t,r),t.prototype.decodeRow=function(r,t,e){for(var n=0;n<this.readers.length;n++)try{return this.readers[n].decodeRow(r,t,e)}catch(r){}throw new a.A},t.prototype.reset=function(){this.readers.forEach(function(r){return r.reset()})},t}(d.A)},22152:(r,t,e)=>{e.d(t,{A:()=>c});var n=e(22868),o=e(79417),a=e(43358),i=e(56595),f=e(438);let c=function(){function r(){}return r.prototype.decode=function(r,t){try{return this.doDecode(r,t)}catch(s){if(t&&!0===t.get(o.A.TRY_HARDER)&&r.isRotateSupported()){var e=r.rotateCounterClockwise(),n=this.doDecode(e,t),c=n.getResultMetadata(),u=270;null!==c&&!0===c.get(a.A.ORIENTATION)&&(u+=c.get(a.A.ORIENTATION)%360),n.putMetadata(a.A.ORIENTATION,u);var A=n.getResultPoints();if(null!==A)for(var d=e.getHeight(),l=0;l<A.length;l++)A[l]=new i.A(d-A[l].getY()-1,A[l].getX());return n}throw new f.A}},r.prototype.reset=function(){},r.prototype.doDecode=function(r,t){var e,c=r.getWidth(),u=r.getHeight(),A=new n.A(c),d=t&&!0===t.get(o.A.TRY_HARDER),l=Math.max(1,u>>(d?8:5));e=d?u:15;for(var s=Math.trunc(u/2),h=0;h<e;h++){var y=Math.trunc((h+1)/2),_=s+l*((1&h)==0?y:-y);if(_<0||_>=u)break;try{A=r.getBlackRow(_,A)}catch(r){continue}for(var w=this,p=0;p<2;p++){var C=function(r){if(1===r&&(A.reverse(),t&&!0===t.get(o.A.NEED_RESULT_POINT_CALLBACK))){var e=new Map;t.forEach(function(r,t){return e.set(t,r)}),e.delete(o.A.NEED_RESULT_POINT_CALLBACK),t=e}try{var n=w.decodeRow(_,A,t);if(1===r){n.putMetadata(a.A.ORIENTATION,180);var f=n.getResultPoints();null!==f&&(f[0]=new i.A(c-f[0].getX()-1,f[0].getY()),f[1]=new i.A(c-f[1].getX()-1,f[1].getY()))}return{value:n}}catch(r){}}(p);if("object"==typeof C)return C.value}}throw new f.A},r.recordPattern=function(r,t,e){for(var n=e.length,o=0;o<n;o++)e[o]=0;var a=r.getSize();if(t>=a)throw new f.A;for(var i=!r.get(t),c=0,u=t;u<a;){if(r.get(u)!==i)e[c]++;else if(++c===n)break;else e[c]=1,i=!i;u++}if(c!==n&&(c!==n-1||u!==a))throw new f.A},r.recordPatternInReverse=function(t,e,n){for(var o=n.length,a=t.get(e);e>0&&o>=0;)t.get(--e)!==a&&(o--,a=!a);if(o>=0)throw new f.A;r.recordPattern(t,e+1,n)},r.patternMatchVariance=function(r,t,e){for(var n=r.length,o=0,a=0,i=0;i<n;i++)o+=r[i],a+=t[i];if(o<a)return Number.POSITIVE_INFINITY;var f=o/a;e*=f;for(var c=0,u=0;u<n;u++){var A=r[u],d=t[u]*f,l=A>d?A-d:d-A;if(l>e)return Number.POSITIVE_INFINITY;c+=l}return c/o},r}()},28822:(r,t,e)=>{e.d(t,{A:()=>c});var n=e(25969),o=e(49001),a=e(438),i=function(){var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(t,e)};return function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),f=function(r){var t="function"==typeof Symbol&&Symbol.iterator,e=t&&r[t],n=0;if(e)return e.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};let c=function(r){function t(){var t=r.call(this)||this;return t.decodeMiddleCounters=Int32Array.from([0,0,0,0]),t}return i(t,r),t.prototype.decodeMiddle=function(r,e,n){var a,i,c,u,A=this.decodeMiddleCounters;A[0]=0,A[1]=0,A[2]=0,A[3]=0;for(var d=r.getSize(),l=e[1],s=0,h=0;h<6&&l<d;h++){var y=o.A.decodeDigit(r,A,l,o.A.L_AND_G_PATTERNS);n+=String.fromCharCode(48+y%10);try{for(var _=(a=void 0,f(A)),w=_.next();!w.done;w=_.next()){var p=w.value;l+=p}}catch(r){a={error:r}}finally{try{w&&!w.done&&(i=_.return)&&i.call(_)}finally{if(a)throw a.error}}y>=10&&(s|=1<<5-h)}n=t.determineFirstDigit(n,s),l=o.A.findGuardPattern(r,l,!0,o.A.MIDDLE_PATTERN,new Int32Array(o.A.MIDDLE_PATTERN.length).fill(0))[1];for(var h=0;h<6&&l<d;h++){var y=o.A.decodeDigit(r,A,l,o.A.L_PATTERNS);n+=String.fromCharCode(48+y);try{for(var C=(c=void 0,f(A)),E=C.next();!E.done;E=C.next()){var p=E.value;l+=p}}catch(r){c={error:r}}finally{try{E&&!E.done&&(u=C.return)&&u.call(C)}finally{if(c)throw c.error}}}return{rowOffset:l,resultString:n}},t.prototype.getBarcodeFormat=function(){return n.A.EAN_13},t.determineFirstDigit=function(r,t){for(var e=0;e<10;e++)if(t===this.FIRST_DIGIT_ENCODINGS[e])return r=String.fromCharCode(48+e)+r;throw new a.A},t.FIRST_DIGIT_ENCODINGS=[0,11,13,14,19,25,28,21,22,26],t}(o.A)},45332:(r,t,e)=>{e.d(t,{A:()=>l});var n=e(25969),o=e(66950),a=e(79417),i=e(71534),f=e(438),c=e(69071),u=e(56595),A=e(22152),d=function(){var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(t,e)};return function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}();let l=function(r){function t(){return null!==r&&r.apply(this,arguments)||this}return d(t,r),t.findStartPattern=function(r){for(var e=r.getSize(),n=r.getNextSet(0),o=0,a=Int32Array.from([0,0,0,0,0,0]),i=n,c=!1,u=n;u<e;u++)if(r.get(u)!==c)a[o]++;else{if(5===o){for(var d=t.MAX_AVG_VARIANCE,l=-1,s=t.CODE_START_A;s<=t.CODE_START_C;s++){var h=A.A.patternMatchVariance(a,t.CODE_PATTERNS[s],t.MAX_INDIVIDUAL_VARIANCE);h<d&&(d=h,l=s)}if(l>=0&&r.isRange(Math.max(0,i-(u-i)/2),i,!1))return Int32Array.from([i,u,l]);i+=a[0]+a[1],(a=a.slice(2,a.length))[o-1]=0,a[o]=0,o--}else o++;a[o]=1,c=!c}throw new f.A},t.decodeCode=function(r,e,n){A.A.recordPattern(r,n,e);for(var o=t.MAX_AVG_VARIANCE,a=-1,i=0;i<t.CODE_PATTERNS.length;i++){var c=t.CODE_PATTERNS[i],u=this.patternMatchVariance(e,c,t.MAX_INDIVIDUAL_VARIANCE);u<o&&(o=u,a=i)}if(a>=0)return a;throw new f.A},t.prototype.decodeRow=function(r,e,A){var d,l=A&&!0===A.get(a.A.ASSUME_GS1),s=t.findStartPattern(e),h=s[2],y=0,_=new Uint8Array(20);switch(_[y++]=h,h){case t.CODE_START_A:d=t.CODE_CODE_A;break;case t.CODE_START_B:d=t.CODE_CODE_B;break;case t.CODE_START_C:d=t.CODE_CODE_C;break;default:throw new i.A}for(var w=!1,p=!1,C="",E=s[0],v=s[1],I=Int32Array.from([0,0,0,0,0,0]),g=0,S=0,m=h,R=0,O=!0,T=!1,D=!1;!w;){var N=p;switch(p=!1,g=S,S=t.decodeCode(e,I,v),_[y++]=S,S!==t.CODE_STOP&&(O=!0),S!==t.CODE_STOP&&(m+=++R*S),E=v,v+=I.reduce(function(r,t){return r+t},0),S){case t.CODE_START_A:case t.CODE_START_B:case t.CODE_START_C:throw new i.A}switch(d){case t.CODE_CODE_A:if(S<64)D===T?C+=String.fromCharCode(32+S):C+=String.fromCharCode(32+S+128),D=!1;else if(S<96)D===T?C+=String.fromCharCode(S-64):C+=String.fromCharCode(S+64),D=!1;else switch(S!==t.CODE_STOP&&(O=!1),S){case t.CODE_FNC_1:l&&(0===C.length?C+="]C1":C+="\x1d");break;case t.CODE_FNC_2:case t.CODE_FNC_3:break;case t.CODE_FNC_4_A:!T&&D?(T=!0,D=!1):T&&D?(T=!1,D=!1):D=!0;break;case t.CODE_SHIFT:p=!0,d=t.CODE_CODE_B;break;case t.CODE_CODE_B:d=t.CODE_CODE_B;break;case t.CODE_CODE_C:d=t.CODE_CODE_C;break;case t.CODE_STOP:w=!0}break;case t.CODE_CODE_B:if(S<96)D===T?C+=String.fromCharCode(32+S):C+=String.fromCharCode(32+S+128),D=!1;else switch(S!==t.CODE_STOP&&(O=!1),S){case t.CODE_FNC_1:l&&(0===C.length?C+="]C1":C+="\x1d");break;case t.CODE_FNC_2:case t.CODE_FNC_3:break;case t.CODE_FNC_4_B:!T&&D?(T=!0,D=!1):T&&D?(T=!1,D=!1):D=!0;break;case t.CODE_SHIFT:p=!0,d=t.CODE_CODE_A;break;case t.CODE_CODE_A:d=t.CODE_CODE_A;break;case t.CODE_CODE_C:d=t.CODE_CODE_C;break;case t.CODE_STOP:w=!0}break;case t.CODE_CODE_C:if(S<100)S<10&&(C+="0"),C+=S;else switch(S!==t.CODE_STOP&&(O=!1),S){case t.CODE_FNC_1:l&&(0===C.length?C+="]C1":C+="\x1d");break;case t.CODE_CODE_A:d=t.CODE_CODE_A;break;case t.CODE_CODE_B:d=t.CODE_CODE_B;break;case t.CODE_STOP:w=!0}}N&&(d=d===t.CODE_CODE_A?t.CODE_CODE_B:t.CODE_CODE_A)}var P=v-E;if(v=e.getNextUnset(v),!e.isRange(v,Math.min(e.getSize(),v+(v-E)/2),!1))throw new f.A;if((m-=R*g)%103!==g)throw new o.A;var b=C.length;if(0===b)throw new f.A;b>0&&O&&(C=d===t.CODE_CODE_C?C.substring(0,b-2):C.substring(0,b-1));for(var x=(s[1]+s[0])/2,M=E+P/2,k=_.length,L=new Uint8Array(k),G=0;G<k;G++)L[G]=_[G];var B=[new u.A(x,r),new u.A(M,r)];return new c.A(C,L,0,B,n.A.CODE_128,new Date().getTime())},t.CODE_PATTERNS=[Int32Array.from([2,1,2,2,2,2]),Int32Array.from([2,2,2,1,2,2]),Int32Array.from([2,2,2,2,2,1]),Int32Array.from([1,2,1,2,2,3]),Int32Array.from([1,2,1,3,2,2]),Int32Array.from([1,3,1,2,2,2]),Int32Array.from([1,2,2,2,1,3]),Int32Array.from([1,2,2,3,1,2]),Int32Array.from([1,3,2,2,1,2]),Int32Array.from([2,2,1,2,1,3]),Int32Array.from([2,2,1,3,1,2]),Int32Array.from([2,3,1,2,1,2]),Int32Array.from([1,1,2,2,3,2]),Int32Array.from([1,2,2,1,3,2]),Int32Array.from([1,2,2,2,3,1]),Int32Array.from([1,1,3,2,2,2]),Int32Array.from([1,2,3,1,2,2]),Int32Array.from([1,2,3,2,2,1]),Int32Array.from([2,2,3,2,1,1]),Int32Array.from([2,2,1,1,3,2]),Int32Array.from([2,2,1,2,3,1]),Int32Array.from([2,1,3,2,1,2]),Int32Array.from([2,2,3,1,1,2]),Int32Array.from([3,1,2,1,3,1]),Int32Array.from([3,1,1,2,2,2]),Int32Array.from([3,2,1,1,2,2]),Int32Array.from([3,2,1,2,2,1]),Int32Array.from([3,1,2,2,1,2]),Int32Array.from([3,2,2,1,1,2]),Int32Array.from([3,2,2,2,1,1]),Int32Array.from([2,1,2,1,2,3]),Int32Array.from([2,1,2,3,2,1]),Int32Array.from([2,3,2,1,2,1]),Int32Array.from([1,1,1,3,2,3]),Int32Array.from([1,3,1,1,2,3]),Int32Array.from([1,3,1,3,2,1]),Int32Array.from([1,1,2,3,1,3]),Int32Array.from([1,3,2,1,1,3]),Int32Array.from([1,3,2,3,1,1]),Int32Array.from([2,1,1,3,1,3]),Int32Array.from([2,3,1,1,1,3]),Int32Array.from([2,3,1,3,1,1]),Int32Array.from([1,1,2,1,3,3]),Int32Array.from([1,1,2,3,3,1]),Int32Array.from([1,3,2,1,3,1]),Int32Array.from([1,1,3,1,2,3]),Int32Array.from([1,1,3,3,2,1]),Int32Array.from([1,3,3,1,2,1]),Int32Array.from([3,1,3,1,2,1]),Int32Array.from([2,1,1,3,3,1]),Int32Array.from([2,3,1,1,3,1]),Int32Array.from([2,1,3,1,1,3]),Int32Array.from([2,1,3,3,1,1]),Int32Array.from([2,1,3,1,3,1]),Int32Array.from([3,1,1,1,2,3]),Int32Array.from([3,1,1,3,2,1]),Int32Array.from([3,3,1,1,2,1]),Int32Array.from([3,1,2,1,1,3]),Int32Array.from([3,1,2,3,1,1]),Int32Array.from([3,3,2,1,1,1]),Int32Array.from([3,1,4,1,1,1]),Int32Array.from([2,2,1,4,1,1]),Int32Array.from([4,3,1,1,1,1]),Int32Array.from([1,1,1,2,2,4]),Int32Array.from([1,1,1,4,2,2]),Int32Array.from([1,2,1,1,2,4]),Int32Array.from([1,2,1,4,2,1]),Int32Array.from([1,4,1,1,2,2]),Int32Array.from([1,4,1,2,2,1]),Int32Array.from([1,1,2,2,1,4]),Int32Array.from([1,1,2,4,1,2]),Int32Array.from([1,2,2,1,1,4]),Int32Array.from([1,2,2,4,1,1]),Int32Array.from([1,4,2,1,1,2]),Int32Array.from([1,4,2,2,1,1]),Int32Array.from([2,4,1,2,1,1]),Int32Array.from([2,2,1,1,1,4]),Int32Array.from([4,1,3,1,1,1]),Int32Array.from([2,4,1,1,1,2]),Int32Array.from([1,3,4,1,1,1]),Int32Array.from([1,1,1,2,4,2]),Int32Array.from([1,2,1,1,4,2]),Int32Array.from([1,2,1,2,4,1]),Int32Array.from([1,1,4,2,1,2]),Int32Array.from([1,2,4,1,1,2]),Int32Array.from([1,2,4,2,1,1]),Int32Array.from([4,1,1,2,1,2]),Int32Array.from([4,2,1,1,1,2]),Int32Array.from([4,2,1,2,1,1]),Int32Array.from([2,1,2,1,4,1]),Int32Array.from([2,1,4,1,2,1]),Int32Array.from([4,1,2,1,2,1]),Int32Array.from([1,1,1,1,4,3]),Int32Array.from([1,1,1,3,4,1]),Int32Array.from([1,3,1,1,4,1]),Int32Array.from([1,1,4,1,1,3]),Int32Array.from([1,1,4,3,1,1]),Int32Array.from([4,1,1,1,1,3]),Int32Array.from([4,1,1,3,1,1]),Int32Array.from([1,1,3,1,4,1]),Int32Array.from([1,1,4,1,3,1]),Int32Array.from([3,1,1,1,4,1]),Int32Array.from([4,1,1,1,3,1]),Int32Array.from([2,1,1,4,1,2]),Int32Array.from([2,1,1,2,1,4]),Int32Array.from([2,1,1,2,3,2]),Int32Array.from([2,3,3,1,1,1,2])],t.MAX_AVG_VARIANCE=.25,t.MAX_INDIVIDUAL_VARIANCE=.7,t.CODE_SHIFT=98,t.CODE_CODE_C=99,t.CODE_CODE_B=100,t.CODE_CODE_A=101,t.CODE_FNC_1=102,t.CODE_FNC_2=97,t.CODE_FNC_3=96,t.CODE_FNC_4_A=101,t.CODE_FNC_4_B=100,t.CODE_START_A=103,t.CODE_START_B=104,t.CODE_START_C=105,t.CODE_STOP=106,t}(A.A)},49001:(r,t,e)=>{e.d(t,{A:()=>p});var n=e(25969),o=e(79417),a=e(69071),i=e(43358),f=e(56595),c=e(59272),u=e(438),A=function(r){var t="function"==typeof Symbol&&Symbol.iterator,e=t&&r[t],n=0;if(e)return e.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},d=function(){function r(){this.CHECK_DIGIT_ENCODINGS=[24,20,18,17,12,6,3,10,9,5],this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}return r.prototype.decodeRow=function(t,e,o){var i=this.decodeRowStringBuffer,c=this.decodeMiddle(e,o,i),u=i.toString(),A=r.parseExtensionString(u),d=[new f.A((o[0]+o[1])/2,t),new f.A(c,t)],l=new a.A(u,null,0,d,n.A.UPC_EAN_EXTENSION,new Date().getTime());return null!=A&&l.putAllMetadata(A),l},r.prototype.decodeMiddle=function(t,e,n){var o,a,i=this.decodeMiddleCounters;i[0]=0,i[1]=0,i[2]=0,i[3]=0;for(var f=t.getSize(),d=e[1],l=0,s=0;s<5&&d<f;s++){var h=c.A.decodeDigit(t,i,d,c.A.L_AND_G_PATTERNS);n+=String.fromCharCode(48+h%10);try{for(var y=(o=void 0,A(i)),_=y.next();!_.done;_=y.next()){var w=_.value;d+=w}}catch(r){o={error:r}}finally{try{_&&!_.done&&(a=y.return)&&a.call(y)}finally{if(o)throw o.error}}h>=10&&(l|=1<<4-s),4!==s&&(d=t.getNextSet(d),d=t.getNextUnset(d))}if(5!==n.length)throw new u.A;var p=this.determineCheckDigit(l);if(r.extensionChecksum(n.toString())!==p)throw new u.A;return d},r.extensionChecksum=function(r){for(var t=r.length,e=0,n=t-2;n>=0;n-=2)e+=r.charAt(n).charCodeAt(0)-48;e*=3;for(var n=t-1;n>=0;n-=2)e+=r.charAt(n).charCodeAt(0)-48;return(e*=3)%10},r.prototype.determineCheckDigit=function(r){for(var t=0;t<10;t++)if(r===this.CHECK_DIGIT_ENCODINGS[t])return t;throw new u.A},r.parseExtensionString=function(t){if(5!==t.length)return null;var e=r.parseExtension5String(t);return null==e?null:new Map([[i.A.SUGGESTED_PRICE,e]])},r.parseExtension5String=function(r){switch(r.charAt(0)){case"0":t="\xa3";break;case"5":t="$";break;case"9":switch(r){case"90000":return null;case"99991":return"0.00";case"99990":return"Used"}t="";break;default:t=""}var t,e=parseInt(r.substring(1)),n=(e/100).toString(),o=e%100;return t+n+"."+(o<10?"0"+o:o.toString())},r}(),l=function(r){var t="function"==typeof Symbol&&Symbol.iterator,e=t&&r[t],n=0;if(e)return e.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},s=function(){function r(){this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}return r.prototype.decodeRow=function(t,e,o){var i=this.decodeRowStringBuffer,c=this.decodeMiddle(e,o,i),u=i.toString(),A=r.parseExtensionString(u),d=[new f.A((o[0]+o[1])/2,t),new f.A(c,t)],l=new a.A(u,null,0,d,n.A.UPC_EAN_EXTENSION,new Date().getTime());return null!=A&&l.putAllMetadata(A),l},r.prototype.decodeMiddle=function(r,t,e){var n,o,a=this.decodeMiddleCounters;a[0]=0,a[1]=0,a[2]=0,a[3]=0;for(var i=r.getSize(),f=t[1],A=0,d=0;d<2&&f<i;d++){var s=c.A.decodeDigit(r,a,f,c.A.L_AND_G_PATTERNS);e+=String.fromCharCode(48+s%10);try{for(var h=(n=void 0,l(a)),y=h.next();!y.done;y=h.next()){var _=y.value;f+=_}}catch(r){n={error:r}}finally{try{y&&!y.done&&(o=h.return)&&o.call(h)}finally{if(n)throw n.error}}s>=10&&(A|=1<<1-d),1!==d&&(f=r.getNextSet(f),f=r.getNextUnset(f))}if(2!==e.length||parseInt(e.toString())%4!==A)throw new u.A;return f},r.parseExtensionString=function(r){return 2!==r.length?null:new Map([[i.A.ISSUE_NUMBER,parseInt(r)]])},r}(),h=function(){function r(){}return r.decodeRow=function(r,t,e){var n=c.A.findGuardPattern(t,e,!1,this.EXTENSION_START_PATTERN,new Int32Array(this.EXTENSION_START_PATTERN.length).fill(0));try{return new d().decodeRow(r,t,n)}catch(e){return new s().decodeRow(r,t,n)}},r.EXTENSION_START_PATTERN=Int32Array.from([1,1,2]),r}(),y=e(71534),_=e(66950),w=function(){var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(t,e)};return function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}();let p=function(r){function t(){var e=r.call(this)||this;e.decodeRowStringBuffer="",t.L_AND_G_PATTERNS=t.L_PATTERNS.map(function(r){return Int32Array.from(r)});for(var n=10;n<20;n++){for(var o=t.L_PATTERNS[n-10],a=new Int32Array(o.length),i=0;i<o.length;i++)a[i]=o[o.length-i-1];t.L_AND_G_PATTERNS[n]=a}return e}return w(t,r),t.prototype.decodeRow=function(r,e,c){var A=t.findStartGuardPattern(e),d=null==c?null:c.get(o.A.NEED_RESULT_POINT_CALLBACK);if(null!=d){var l=new f.A((A[0]+A[1])/2,r);d.foundPossibleResultPoint(l)}var s=this.decodeMiddle(e,A,this.decodeRowStringBuffer),w=s.rowOffset,p=s.resultString;if(null!=d){var C=new f.A(w,r);d.foundPossibleResultPoint(C)}var E=t.decodeEnd(e,w);if(null!=d){var v=new f.A((E[0]+E[1])/2,r);d.foundPossibleResultPoint(v)}var I=E[1],g=I+(I-E[0]);if(g>=e.getSize()||!e.isRange(I,g,!1))throw new u.A;var S=p.toString();if(S.length<8)throw new y.A;if(!t.checkChecksum(S))throw new _.A;var m=(A[1]+A[0])/2,R=(E[1]+E[0])/2,O=this.getBarcodeFormat(),T=[new f.A(m,r),new f.A(R,r)],D=new a.A(S,null,0,T,O,new Date().getTime()),N=0;try{var P=h.decodeRow(r,e,E[1]);D.putMetadata(i.A.UPC_EAN_EXTENSION,P.getText()),D.putAllMetadata(P.getResultMetadata()),D.addResultPoints(P.getResultPoints()),N=P.getText().length}catch(r){}var b=null==c?null:c.get(o.A.ALLOWED_EAN_EXTENSIONS);if(null!=b){var x=!1;for(var M in b)if(N.toString()===M){x=!0;break}if(!x)throw new u.A}return O===n.A.EAN_13||n.A.UPC_A,D},t.checkChecksum=function(r){return t.checkStandardUPCEANChecksum(r)},t.checkStandardUPCEANChecksum=function(r){var e=r.length;if(0===e)return!1;var n=parseInt(r.charAt(e-1),10);return t.getStandardUPCEANChecksum(r.substring(0,e-1))===n},t.getStandardUPCEANChecksum=function(r){for(var t=r.length,e=0,n=t-1;n>=0;n-=2){var o=r.charAt(n).charCodeAt(0)-48;if(o<0||o>9)throw new y.A;e+=o}e*=3;for(var n=t-2;n>=0;n-=2){var o=r.charAt(n).charCodeAt(0)-48;if(o<0||o>9)throw new y.A;e+=o}return(1e3-e)%10},t.decodeEnd=function(r,e){return t.findGuardPattern(r,e,!1,t.START_END_PATTERN,new Int32Array(t.START_END_PATTERN.length).fill(0))},t}(c.A)},64476:(r,t,e)=>{e.d(t,{A:()=>u});var n=e(25969),o=e(438),a=e(22152),i=e(69071),f=e(56595),c=function(){var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(t,e)};return function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}();let u=function(r){function t(){var t=null!==r&&r.apply(this,arguments)||this;return t.CODA_BAR_CHAR_SET={nnnnnww:"0",nnnnwwn:"1",nnnwnnw:"2",wwnnnnn:"3",nnwnnwn:"4",wnnnnwn:"5",nwnnnnw:"6",nwnnwnn:"7",nwwnnnn:"8",wnnwnnn:"9",nnnwwnn:"-",nnwwnnn:"$",wnnnwnw:":",wnwnnnw:"/",wnwnwnn:".",nnwwwww:"+",nnwwnwn:"A",nwnwnnw:"B",nnnwnww:"C",nnnwwwn:"D"},t}return c(t,r),t.prototype.decodeRow=function(r,t,e){var a=this.getValidRowData(t);if(!a)throw new o.A;var c=this.codaBarDecodeRow(a.row);if(!c)throw new o.A;return new i.A(c,null,0,[new f.A(a.left,r),new f.A(a.right,r)],n.A.CODABAR,new Date().getTime())},t.prototype.getValidRowData=function(r){var t=r.toArray(),e=t.indexOf(!0);if(-1===e)return null;var n=t.lastIndexOf(!0);if(n<=e)return null;t=t.slice(e,n+1);for(var o=[],a=t[0],i=1,f=1;f<t.length;f++)t[f]===a?i++:(a=t[f],o.push(i),i=1);return(o.push(i),o.length<23&&(o.length+1)%8!=0)?null:{row:o,left:e,right:n}},t.prototype.codaBarDecodeRow=function(r){for(var t=[],e=Math.ceil(r.reduce(function(r,t){return(r+t)/2},0));r.length>0;){var n=r.splice(0,8).splice(0,7).map(function(r){return r<e?"n":"w"}).join("");if(void 0===this.CODA_BAR_CHAR_SET[n])return null;t.push(this.CODA_BAR_CHAR_SET[n])}var o=t.join("");return this.validCodaBarString(o)?o:null},t.prototype.validCodaBarString=function(r){return/^[A-D].{1,}[A-D]$/.test(r)},t}(a.A)},65649:(r,t,e)=>{e.d(t,{A:()=>l});var n=e(25969),o=e(66950),a=e(71534),i=e(438),f=e(22152),c=e(69071),u=e(56595),A=function(){var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(t,e)};return function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),d=function(r){var t="function"==typeof Symbol&&Symbol.iterator,e=t&&r[t],n=0;if(e)return e.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};let l=function(r){function t(t,e){void 0===t&&(t=!1),void 0===e&&(e=!1);var n=r.call(this)||this;return n.usingCheckDigit=t,n.extendedMode=e,n.decodeRowResult="",n.counters=new Int32Array(9),n}return A(t,r),t.prototype.decodeRow=function(r,e,a){var f,A,l,s,h,y,_,w=this.counters;w.fill(0),this.decodeRowResult="";var p=t.findAsteriskPattern(e,w),C=e.getNextSet(p[1]),E=e.getSize();do{t.recordPattern(e,C,w);var v=t.toNarrowWidePattern(w);if(v<0)throw new i.A;h=t.patternToChar(v),this.decodeRowResult+=h,y=C;try{for(var I=(f=void 0,d(w)),g=I.next();!g.done;g=I.next()){var S=g.value;C+=S}}catch(r){f={error:r}}finally{try{g&&!g.done&&(A=I.return)&&A.call(I)}finally{if(f)throw f.error}}C=e.getNextSet(C)}while("*"!==h);this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var m=0;try{for(var R=d(w),O=R.next();!O.done;O=R.next()){var S=O.value;m+=S}}catch(r){l={error:r}}finally{try{O&&!O.done&&(s=R.return)&&s.call(R)}finally{if(l)throw l.error}}var T=C-y-m;if(C!==E&&2*T<m)throw new i.A;if(this.usingCheckDigit){for(var D=this.decodeRowResult.length-1,N=0,P=0;P<D;P++)N+=t.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(P));if(this.decodeRowResult.charAt(D)!==t.ALPHABET_STRING.charAt(N%43))throw new o.A;this.decodeRowResult=this.decodeRowResult.substring(0,D)}if(0===this.decodeRowResult.length)throw new i.A;_=this.extendedMode?t.decodeExtended(this.decodeRowResult):this.decodeRowResult;var b=(p[1]+p[0])/2,x=y+m/2;return new c.A(_,null,0,[new u.A(b,r),new u.A(x,r)],n.A.CODE_39,new Date().getTime())},t.findAsteriskPattern=function(r,e){for(var n=r.getSize(),o=r.getNextSet(0),a=0,f=o,c=!1,u=e.length,A=o;A<n;A++)if(r.get(A)!==c)e[a]++;else{if(a===u-1){if(this.toNarrowWidePattern(e)===t.ASTERISK_ENCODING&&r.isRange(Math.max(0,f-Math.floor((A-f)/2)),f,!1))return[f,A];f+=e[0]+e[1],e.copyWithin(0,2,2+a-1),e[a-1]=0,e[a]=0,a--}else a++;e[a]=1,c=!c}throw new i.A},t.toNarrowWidePattern=function(r){var t,e,n,o=r.length,a=0;do{var i=0x7fffffff;try{for(var f=(t=void 0,d(r)),c=f.next();!c.done;c=f.next()){var u=c.value;u<i&&u>a&&(i=u)}}catch(r){t={error:r}}finally{try{c&&!c.done&&(e=f.return)&&e.call(f)}finally{if(t)throw t.error}}a=i,n=0;for(var A=0,l=0,s=0;s<o;s++){var u=r[s];u>a&&(l|=1<<o-1-s,n++,A+=u)}if(3===n){for(var s=0;s<o&&n>0;s++){var u=r[s];if(u>a&&(n--,2*u>=A))return -1}return l}}while(n>3);return -1},t.patternToChar=function(r){for(var e=0;e<t.CHARACTER_ENCODINGS.length;e++)if(t.CHARACTER_ENCODINGS[e]===r)return t.ALPHABET_STRING.charAt(e);if(r===t.ASTERISK_ENCODING)return"*";throw new i.A},t.decodeExtended=function(r){for(var t=r.length,e="",n=0;n<t;n++){var o=r.charAt(n);if("+"===o||"$"===o||"%"===o||"/"===o){var i=r.charAt(n+1),f="\0";switch(o){case"+":if(i>="A"&&i<="Z")f=String.fromCharCode(i.charCodeAt(0)+32);else throw new a.A;break;case"$":if(i>="A"&&i<="Z")f=String.fromCharCode(i.charCodeAt(0)-64);else throw new a.A;break;case"%":if(i>="A"&&i<="E")f=String.fromCharCode(i.charCodeAt(0)-38);else if(i>="F"&&i<="J")f=String.fromCharCode(i.charCodeAt(0)-11);else if(i>="K"&&i<="O")f=String.fromCharCode(i.charCodeAt(0)+16);else if(i>="P"&&i<="T")f=String.fromCharCode(i.charCodeAt(0)+43);else if("U"===i)f="\0";else if("V"===i)f="@";else if("W"===i)f="`";else if("X"===i||"Y"===i||"Z"===i)f="";else throw new a.A;break;case"/":if(i>="A"&&i<="O")f=String.fromCharCode(i.charCodeAt(0)-32);else if("Z"===i)f=":";else throw new a.A}e+=f,n++}else e+=o}return e},t.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%",t.CHARACTER_ENCODINGS=[52,289,97,352,49,304,112,37,292,100,265,73,328,25,280,88,13,268,76,28,259,67,322,19,274,82,7,262,70,22,385,193,448,145,400,208,133,388,196,168,162,138,42],t.ASTERISK_ENCODING=148,t}(f.A)},73977:(r,t,e)=>{e.d(t,{A:()=>l});var n=e(25969),o=e(66950),a=e(71534),i=e(438),f=e(22152),c=e(69071),u=e(56595),A=function(){var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(t,e)};return function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}}(),d=function(r){var t="function"==typeof Symbol&&Symbol.iterator,e=t&&r[t],n=0;if(e)return e.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};let l=function(r){function t(){var t=r.call(this)||this;return t.decodeRowResult="",t.counters=new Int32Array(6),t}return A(t,r),t.prototype.decodeRow=function(r,e,o){var a,f,A,l,s,h,y=this.findAsteriskPattern(e),_=e.getNextSet(y[1]),w=e.getSize(),p=this.counters;p.fill(0),this.decodeRowResult="";do{t.recordPattern(e,_,p);var C=this.toPattern(p);if(C<0)throw new i.A;s=this.patternToChar(C),this.decodeRowResult+=s,h=_;try{for(var E=(a=void 0,d(p)),v=E.next();!v.done;v=E.next()){var I=v.value;_+=I}}catch(r){a={error:r}}finally{try{v&&!v.done&&(f=E.return)&&f.call(E)}finally{if(a)throw a.error}}_=e.getNextSet(_)}while("*"!==s);this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var g=0;try{for(var S=d(p),m=S.next();!m.done;m=S.next()){var I=m.value;g+=I}}catch(r){A={error:r}}finally{try{m&&!m.done&&(l=S.return)&&l.call(S)}finally{if(A)throw A.error}}if(_===w||!e.get(_)||this.decodeRowResult.length<2)throw new i.A;this.checkChecksums(this.decodeRowResult),this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-2);var R=this.decodeExtended(this.decodeRowResult),O=(y[1]+y[0])/2,T=h+g/2;return new c.A(R,null,0,[new u.A(O,r),new u.A(T,r)],n.A.CODE_93,new Date().getTime())},t.prototype.findAsteriskPattern=function(r){var e=r.getSize(),n=r.getNextSet(0);this.counters.fill(0);for(var o=this.counters,a=n,f=!1,c=o.length,u=0,A=n;A<e;A++)if(r.get(A)!==f)o[u]++;else{if(u===c-1){if(this.toPattern(o)===t.ASTERISK_ENCODING)return new Int32Array([a,A]);a+=o[0]+o[1],o.copyWithin(0,2,2+u-1),o[u-1]=0,o[u]=0,u--}else u++;o[u]=1,f=!f}throw new i.A},t.prototype.toPattern=function(r){var t,e,n=0;try{for(var o=d(r),a=o.next();!a.done;a=o.next()){var i=a.value;n+=i}}catch(r){t={error:r}}finally{try{a&&!a.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}for(var f=0,c=r.length,u=0;u<c;u++){var A=Math.round(9*r[u]/n);if(A<1||A>4)return -1;if((1&u)==0)for(var l=0;l<A;l++)f=f<<1|1;else f<<=A}return f},t.prototype.patternToChar=function(r){for(var e=0;e<t.CHARACTER_ENCODINGS.length;e++)if(t.CHARACTER_ENCODINGS[e]===r)return t.ALPHABET_STRING.charAt(e);throw new i.A},t.prototype.decodeExtended=function(r){for(var t=r.length,e="",n=0;n<t;n++){var o=r.charAt(n);if(o>="a"&&o<="d"){if(n>=t-1)throw new a.A;var i=r.charAt(n+1),f="\0";switch(o){case"d":if(i>="A"&&i<="Z")f=String.fromCharCode(i.charCodeAt(0)+32);else throw new a.A;break;case"a":if(i>="A"&&i<="Z")f=String.fromCharCode(i.charCodeAt(0)-64);else throw new a.A;break;case"b":if(i>="A"&&i<="E")f=String.fromCharCode(i.charCodeAt(0)-38);else if(i>="F"&&i<="J")f=String.fromCharCode(i.charCodeAt(0)-11);else if(i>="K"&&i<="O")f=String.fromCharCode(i.charCodeAt(0)+16);else if(i>="P"&&i<="T")f=String.fromCharCode(i.charCodeAt(0)+43);else if("U"===i)f="\0";else if("V"===i)f="@";else if("W"===i)f="`";else if(i>="X"&&i<="Z")f="";else throw new a.A;break;case"c":if(i>="A"&&i<="O")f=String.fromCharCode(i.charCodeAt(0)-32);else if("Z"===i)f=":";else throw new a.A}e+=f,n++}else e+=o}return e},t.prototype.checkChecksums=function(r){var t=r.length;this.checkOneChecksum(r,t-2,20),this.checkOneChecksum(r,t-1,15)},t.prototype.checkOneChecksum=function(r,e,n){for(var a=1,i=0,f=e-1;f>=0;f--)i+=a*t.ALPHABET_STRING.indexOf(r.charAt(f)),++a>n&&(a=1);if(r.charAt(e)!==t.ALPHABET_STRING[i%47])throw new o.A},t.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%abcd*",t.CHARACTER_ENCODINGS=[276,328,324,322,296,292,290,336,274,266,424,420,418,404,402,394,360,356,354,308,282,344,332,326,300,278,436,434,428,422,406,410,364,358,310,314,302,468,466,458,366,374,430,294,474,470,306,350],t.ASTERISK_ENCODING=t.CHARACTER_ENCODINGS[47],t}(f.A)}}]);