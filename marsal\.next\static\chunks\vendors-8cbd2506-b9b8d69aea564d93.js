"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9473],{6101:(e,t,n)=>{n.d(t,{s:()=>l,t:()=>i});var r=n(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function l(...e){return r.useCallback(i(...e),e)}},14232:(e,t,n)=>{e.exports=n(42167)},28228:(e,t)=>{var n=Symbol.for("react.transitional.element");function r(e,t,r){var o=null;if(void 0!==r&&(o=""+r),void 0!==t.key&&(o=""+t.key),"key"in t)for(var i in r={},t)"key"!==i&&(r[i]=t[i]);else r=t;return{$$typeof:n,type:e,key:o,ref:void 0!==(t=r.ref)?t:null,props:r}}t.Fragment=Symbol.for("react.fragment"),t.jsx=r,t.jsxs=r},37876:(e,t,n)=>{e.exports=n(28228)},40968:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(12115),o=n(63655),i=n(95155),l=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},42167:(e,t,n)=>{var r=n(65364),o=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),l=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),c=Symbol.for("react.context"),d=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),m=Symbol.iterator,h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},y=Object.assign,g={};function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function w(){}function x(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},w.prototype=b.prototype;var E=x.prototype=new w;E.constructor=x,y(E,b.prototype),E.isPureReactComponent=!0;var S=Array.isArray,C={H:null,A:null,T:null,S:null,V:null},_=Object.prototype.hasOwnProperty;function R(e,t,n,r,i,l){return{$$typeof:o,type:e,key:t,ref:void 0!==(n=l.ref)?n:null,props:l}}function T(e){return"object"==typeof e&&null!==e&&e.$$typeof===o}var k=/\/+/g;function P(e,t){var n,r;return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,r={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,function(e){return r[e]})):t.toString(36)}function L(){}function N(e,t,n){if(null==e)return e;var r=[],l=0;return!function e(t,n,r,l,a){var u,s,c,d=typeof t;("undefined"===d||"boolean"===d)&&(t=null);var f=!1;if(null===t)f=!0;else switch(d){case"bigint":case"string":case"number":f=!0;break;case"object":switch(t.$$typeof){case o:case i:f=!0;break;case v:return e((f=t._init)(t._payload),n,r,l,a)}}if(f)return a=a(t),f=""===l?"."+P(t,0):l,S(a)?(r="",null!=f&&(r=f.replace(k,"$&/")+"/"),e(a,n,r,"",function(e){return e})):null!=a&&(T(a)&&(u=a,s=r+(null==a.key||t&&t.key===a.key?"":(""+a.key).replace(k,"$&/")+"/")+f,a=R(u.type,s,void 0,void 0,void 0,u.props)),n.push(a)),1;f=0;var p=""===l?".":l+":";if(S(t))for(var h=0;h<t.length;h++)d=p+P(l=t[h],h),f+=e(l,n,r,d,a);else if("function"==typeof(h=null===(c=t)||"object"!=typeof c?null:"function"==typeof(c=m&&c[m]||c["@@iterator"])?c:null))for(t=h.call(t),h=0;!(l=t.next()).done;)d=p+P(l=l.value,h++),f+=e(l,n,r,d,a);else if("object"===d){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(L,L):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),n,r,l,a);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===(n=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":n)+"). If you meant to render a collection of children, use an array instead.")}return f}(e,r,"","",function(e){return t.call(n,e,l++)}),r}function j(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var O="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof r&&"function"==typeof r.emit)return void r.emit("uncaughtException",e);console.error(e)};function A(){}t.Children={map:N,forEach:function(e,t,n){N(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return N(e,function(){t++}),t},toArray:function(e){return N(e,function(e){return e})||[]},only:function(e){if(!T(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=b,t.Fragment=l,t.Profiler=u,t.PureComponent=x,t.StrictMode=a,t.Suspense=f,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=C,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return C.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var r=y({},e.props),o=e.key,i=void 0;if(null!=t)for(l in void 0!==t.ref&&(i=void 0),void 0!==t.key&&(o=""+t.key),t)_.call(t,l)&&"key"!==l&&"__self"!==l&&"__source"!==l&&("ref"!==l||void 0!==t.ref)&&(r[l]=t[l]);var l=arguments.length-2;if(1===l)r.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return R(e.type,o,void 0,void 0,i,r)},t.createContext=function(e){return(e={$$typeof:c,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:s,_context:e},e},t.createElement=function(e,t,n){var r,o={},i=null;if(null!=t)for(r in void 0!==t.key&&(i=""+t.key),t)_.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(o[r]=t[r]);var l=arguments.length-2;if(1===l)o.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps)void 0===o[r]&&(o[r]=l[r]);return R(e,i,void 0,void 0,null,o)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:d,render:e}},t.isValidElement=T,t.lazy=function(e){return{$$typeof:v,_payload:{_status:-1,_result:e},_init:j}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=C.T,n={};C.T=n;try{var r=e(),o=C.S;null!==o&&o(n,r),"object"==typeof r&&null!==r&&"function"==typeof r.then&&r.then(A,O)}catch(e){O(e)}finally{C.T=t}},t.unstable_useCacheRefresh=function(){return C.H.useCacheRefresh()},t.use=function(e){return C.H.use(e)},t.useActionState=function(e,t,n){return C.H.useActionState(e,t,n)},t.useCallback=function(e,t){return C.H.useCallback(e,t)},t.useContext=function(e){return C.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return C.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=C.H;if("function"==typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return C.H.useId()},t.useImperativeHandle=function(e,t,n){return C.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return C.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return C.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return C.H.useMemo(e,t)},t.useOptimistic=function(e,t){return C.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return C.H.useReducer(e,t,n)},t.useRef=function(e){return C.H.useRef(e)},t.useState=function(e){return C.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return C.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return C.H.useTransition()},t.version="19.1.0"},45919:(e,t)=>{function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,o=e[r];if(0<i(o,t))e[r]=t,e[n]=o,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,o=e.length,l=o>>>1;r<l;){var a=2*(r+1)-1,u=e[a],s=a+1,c=e[s];if(0>i(u,n))s<o&&0>i(c,u)?(e[r]=c,e[s]=n,r=s):(e[r]=u,e[a]=n,r=a);else if(s<o&&0>i(c,n))e[r]=c,e[s]=n,r=s;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var l,a=performance;t.unstable_now=function(){return a.now()}}else{var u=Date,s=u.now();t.unstable_now=function(){return u.now()-s}}var c=[],d=[],f=1,p=null,v=3,m=!1,h=!1,y=!1,g=!1,b="function"==typeof setTimeout?setTimeout:null,w="function"==typeof clearTimeout?clearTimeout:null,x="undefined"!=typeof setImmediate?setImmediate:null;function E(e){for(var t=r(d);null!==t;){if(null===t.callback)o(d);else if(t.startTime<=e)o(d),t.sortIndex=t.expirationTime,n(c,t);else break;t=r(d)}}function S(e){if(y=!1,E(e),!h)if(null!==r(c))h=!0,C||(C=!0,l());else{var t=r(d);null!==t&&j(S,t.startTime-e)}}var C=!1,_=-1,R=5,T=-1;function k(){return!!g||!(t.unstable_now()-T<R)}function P(){if(g=!1,C){var e=t.unstable_now();T=e;var n=!0;try{e:{h=!1,y&&(y=!1,w(_),_=-1),m=!0;var i=v;try{t:{for(E(e),p=r(c);null!==p&&!(p.expirationTime>e&&k());){var a=p.callback;if("function"==typeof a){p.callback=null,v=p.priorityLevel;var u=a(p.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof u){p.callback=u,E(e),n=!0;break t}p===r(c)&&o(c),E(e)}else o(c);p=r(c)}if(null!==p)n=!0;else{var s=r(d);null!==s&&j(S,s.startTime-e),n=!1}}break e}finally{p=null,v=i,m=!1}}}finally{n?l():C=!1}}}if("function"==typeof x)l=function(){x(P)};else if("undefined"!=typeof MessageChannel){var L=new MessageChannel,N=L.port2;L.port1.onmessage=P,l=function(){N.postMessage(null)}}else l=function(){b(P,0)};function j(e,n){_=b(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):R=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return v},t.unstable_next=function(e){switch(v){case 1:case 2:case 3:var t=3;break;default:t=v}var n=v;v=t;try{return e()}finally{v=n}},t.unstable_requestPaint=function(){g=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=v;v=e;try{return t()}finally{v=n}},t.unstable_scheduleCallback=function(e,o,i){var a=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?a+i:a,e){case 1:var u=-1;break;case 2:u=250;break;case 5:u=0x3fffffff;break;case 4:u=1e4;break;default:u=5e3}return u=i+u,e={id:f++,callback:o,priorityLevel:e,startTime:i,expirationTime:u,sortIndex:-1},i>a?(e.sortIndex=i,n(d,e),null===r(c)&&e===r(d)&&(y?(w(_),_=-1):y=!0,j(S,i-a))):(e.sortIndex=u,n(c,e),h||m||(h=!0,C||(C=!0,l()))),e},t.unstable_shouldYield=k,t.unstable_wrapCallback=function(e){var t=v;return function(){var n=v;v=t;try{return e.apply(this,arguments)}finally{v=n}}}},58901:(e,t,n)=>{n.d(t,{UC:()=>tq,YJ:()=>tJ,In:()=>tY,q7:()=>t0,VF:()=>t2,p4:()=>t1,JU:()=>tQ,ZL:()=>tX,bL:()=>tV,wn:()=>t9,PP:()=>t5,wv:()=>t4,l9:()=>tK,WT:()=>tz,LM:()=>tZ});var r,o,i=n(12115),l=n.t(i,2),a=n(47650);function u(e,[t,n]){return Math.min(n,Math.max(t,e))}function s(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var c=n(94971),d=n(95920),f=n(86266),p=n(95155);function v(e,t=[]){let n=[],r=()=>{let t=n.map(e=>i.createContext(e));return function(n){let r=n?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=i.createContext(r),l=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[l]||o,s=i.useMemo(()=>a,Object.values(a));return(0,p.jsx)(u.Provider,{value:s,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[l]||o,s=i.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var m=n(6101),h=n(99708),y=new WeakMap;function g(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=b(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function b(e){return e!=e||0===e?0:Math.trunc(e)}r=new WeakMap;var w=i.createContext(void 0),x=n(63655);function E(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}var S="dismissableLayer.update",C=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),_=i.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:c,onInteractOutside:d,onDismiss:f,...v}=e,h=i.useContext(C),[y,g]=i.useState(null),b=null!=(r=null==y?void 0:y.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,w]=i.useState({}),_=(0,m.s)(t,e=>g(e)),k=Array.from(h.layers),[P]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),L=k.indexOf(P),N=y?k.indexOf(y):-1,j=h.layersWithOutsidePointerEventsDisabled.size>0,O=N>=L,A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=E(e),o=i.useRef(!1),l=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){T("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...h.branches].some(e=>e.contains(t));O&&!n&&(null==u||u(e),null==d||d(e),e.defaultPrevented||null==f||f())},b),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=E(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&T("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...h.branches].some(e=>e.contains(t))&&(null==c||c(e),null==d||d(e),e.defaultPrevented||null==f||f())},b);return!function(e,t=globalThis?.document){let n=E(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===h.layers.size-1&&(null==a||a(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},b),i.useEffect(()=>{if(y)return l&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(o=b.body.style.pointerEvents,b.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(y)),h.layers.add(y),R(),()=>{l&&1===h.layersWithOutsidePointerEventsDisabled.size&&(b.body.style.pointerEvents=o)}},[y,b,l,h]),i.useEffect(()=>()=>{y&&(h.layers.delete(y),h.layersWithOutsidePointerEventsDisabled.delete(y),R())},[y,h]),i.useEffect(()=>{let e=()=>w({});return document.addEventListener(S,e),()=>document.removeEventListener(S,e)},[]),(0,p.jsx)(x.sG.div,{...v,ref:_,style:{pointerEvents:j?O?"auto":"none":void 0,...e.style},onFocusCapture:s(e.onFocusCapture,D.onFocusCapture),onBlurCapture:s(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:s(e.onPointerDownCapture,A.onPointerDownCapture)})});function R(){let e=new CustomEvent(S);document.dispatchEvent(e)}function T(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,x.hO)(i,l):i.dispatchEvent(l)}_.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(C),r=i.useRef(null),o=(0,m.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,p.jsx)(x.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var k=0;function P(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var L="focusScope.autoFocusOnMount",N="focusScope.autoFocusOnUnmount",j={bubbles:!1,cancelable:!0},O=i.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:l,...a}=e,[u,s]=i.useState(null),c=E(o),d=E(l),f=i.useRef(null),v=(0,m.s)(t,e=>s(e)),h=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let e=function(e){if(h.paused||!u)return;let t=e.target;u.contains(t)?f.current=t:I(f.current,{select:!0})},t=function(e){if(h.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||I(f.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&I(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,h.paused]),i.useEffect(()=>{if(u){M.add(h);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(L,j);u.addEventListener(L,c),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(I(r,{select:t}),document.activeElement!==n)return}(A(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&I(u))}return()=>{u.removeEventListener(L,c),setTimeout(()=>{let t=new CustomEvent(N,j);u.addEventListener(N,d),u.dispatchEvent(t),t.defaultPrevented||I(null!=e?e:document.body,{select:!0}),u.removeEventListener(N,d),M.remove(h)},0)}}},[u,c,d,h]);let y=i.useCallback(e=>{if(!n&&!r||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=A(e);return[D(t,e),D(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&I(i,{select:!0})):(e.preventDefault(),n&&I(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,h.paused]);return(0,p.jsx)(x.sG.div,{tabIndex:-1,...a,ref:v,onKeyDown:y})});function A(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function D(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function I(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}O.displayName="FocusScope";var M=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=H(e,t)).unshift(t)},remove(t){var n;null==(n=(e=H(e,t))[0])||n.resume()}}}();function H(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var F=globalThis?.document?i.useLayoutEffect:()=>{},B=l[" useId ".trim().toString()]||(()=>void 0),W=0;function U(e){let[t,n]=i.useState(B());return F(()=>{e||n(e=>e??String(W++))},[e]),e||(t?`radix-${t}`:"")}var $=n(84945),G=n(22475),V=i.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,p.jsx)(x.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,p.jsx)("polygon",{points:"0,0 30,0 15,10"})})});V.displayName="Arrow";var K="Popper",[z,Y]=v(K),[X,q]=z(K),Z=e=>{let{__scopePopper:t,children:n}=e,[r,o]=i.useState(null);return(0,p.jsx)(X,{scope:t,anchor:r,onAnchorChange:o,children:n})};Z.displayName=K;var J="PopperAnchor",Q=i.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,l=q(J,n),a=i.useRef(null),u=(0,m.s)(t,a);return i.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,p.jsx)(x.sG.div,{...o,ref:u})});Q.displayName=J;var ee="PopperContent",[et,en]=z(ee),er=i.forwardRef((e,t)=>{var n,r,o,l,a,u,s,c;let{__scopePopper:d,side:f="bottom",sideOffset:v=0,align:h="center",alignOffset:y=0,arrowPadding:g=0,avoidCollisions:b=!0,collisionBoundary:w=[],collisionPadding:S=0,sticky:C="partial",hideWhenDetached:_=!1,updatePositionStrategy:R="optimized",onPlaced:T,...k}=e,P=q(ee,d),[L,N]=i.useState(null),j=(0,m.s)(t,e=>N(e)),[O,A]=i.useState(null),D=function(e){let[t,n]=i.useState(void 0);return F(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(O),I=null!=(s=null==D?void 0:D.width)?s:0,M=null!=(c=null==D?void 0:D.height)?c:0,H="number"==typeof S?S:{top:0,right:0,bottom:0,left:0,...S},B=Array.isArray(w)?w:[w],W=B.length>0,U={padding:H,boundary:B.filter(ea),altBoundary:W},{refs:V,floatingStyles:K,placement:z,isPositioned:Y,middlewareData:X}=(0,$.we)({strategy:"fixed",placement:f+("center"!==h?"-"+h:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,G.ll)(...t,{animationFrame:"always"===R})},elements:{reference:P.anchor},middleware:[(0,$.cY)({mainAxis:v+M,alignmentAxis:y}),b&&(0,$.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?(0,$.ER)():void 0,...U}),b&&(0,$.UU)({...U}),(0,$.Ej)({...U,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),O&&(0,$.UE)({element:O,padding:g}),eu({arrowWidth:I,arrowHeight:M}),_&&(0,$.jD)({strategy:"referenceHidden",...U})]}),[Z,J]=es(z),Q=E(T);F(()=>{Y&&(null==Q||Q())},[Y,Q]);let en=null==(n=X.arrow)?void 0:n.x,er=null==(r=X.arrow)?void 0:r.y,eo=(null==(o=X.arrow)?void 0:o.centerOffset)!==0,[ei,el]=i.useState();return F(()=>{L&&el(window.getComputedStyle(L).zIndex)},[L]),(0,p.jsx)("div",{ref:V.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:Y?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ei,"--radix-popper-transform-origin":[null==(l=X.transformOrigin)?void 0:l.x,null==(a=X.transformOrigin)?void 0:a.y].join(" "),...(null==(u=X.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,p.jsx)(et,{scope:d,placedSide:Z,onArrowChange:A,arrowX:en,arrowY:er,shouldHideArrow:eo,children:(0,p.jsx)(x.sG.div,{"data-side":Z,"data-align":J,...k,ref:j,style:{...k.style,animation:Y?void 0:"none"}})})})});er.displayName=ee;var eo="PopperArrow",ei={top:"bottom",right:"left",bottom:"top",left:"right"},el=i.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=en(eo,n),i=ei[o.placedSide];return(0,p.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,p.jsx)(V,{...r,ref:t,style:{...r.style,display:"block"}})})});function ea(e){return null!==e}el.displayName=eo;var eu=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,c=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,v]=es(a),m={start:"0%",center:"50%",end:"100%"}[v],h=(null!=(i=null==(r=s.arrow)?void 0:r.x)?i:0)+d/2,y=(null!=(l=null==(o=s.arrow)?void 0:o.y)?l:0)+f/2,g="",b="";return"bottom"===p?(g=c?m:"".concat(h,"px"),b="".concat(-f,"px")):"top"===p?(g=c?m:"".concat(h,"px"),b="".concat(u.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),b=c?m:"".concat(y,"px")):"left"===p&&(g="".concat(u.floating.width+f,"px"),b=c?m:"".concat(y,"px")),{data:{x:g,y:b}}}});function es(e){let[t,n="center"]=e.split("-");return[t,n]}var ec=i.forwardRef((e,t)=>{var n,r;let{container:o,...l}=e,[u,s]=i.useState(!1);F(()=>s(!0),[]);let c=o||u&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?a.createPortal((0,p.jsx)(x.sG.div,{...l,ref:t}),c):null});ec.displayName="Portal";var ed=l[" useInsertionEffect ".trim().toString()]||F;function ef({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=i.useState(e),o=i.useRef(n),l=i.useRef(t);return ed(()=>{l.current=t},[t]),i.useEffect(()=>{o.current!==n&&(l.current?.(n),o.current=n)},[n,o]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,i.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[u,e,l,a])]}Symbol("RADIX:SYNC_STATE");var ep=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});i.forwardRef((e,t)=>(0,p.jsx)(x.sG.span,{...e,ref:t,style:{...ep,...e.style}})).displayName="VisuallyHidden";var ev=n(38168),em=n(39249),eh="right-scroll-bar-position",ey="width-before-scroll-bar",eg=n(70464),eb=(0,n(37548).f)(),ew=function(){},ex=i.forwardRef(function(e,t){var n=i.useRef(null),r=i.useState({onScrollCapture:ew,onWheelCapture:ew,onTouchMoveCapture:ew}),o=r[0],l=r[1],a=e.forwardProps,u=e.children,s=e.className,c=e.removeScrollBar,d=e.enabled,f=e.shards,p=e.sideCar,v=e.noRelative,m=e.noIsolation,h=e.inert,y=e.allowPinchZoom,g=e.as,b=e.gapMode,w=(0,em.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=(0,eg.S)([n,t]),E=(0,em.Cl)((0,em.Cl)({},w),o);return i.createElement(i.Fragment,null,d&&i.createElement(p,{sideCar:eb,removeScrollBar:c,shards:f,noRelative:v,noIsolation:m,inert:h,setCallbacks:l,allowPinchZoom:!!y,lockRef:n,gapMode:b}),a?i.cloneElement(i.Children.only(u),(0,em.Cl)((0,em.Cl)({},E),{ref:x})):i.createElement(void 0===g?"div":g,(0,em.Cl)({},E,{className:s,ref:x}),u))});ex.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},ex.classNames={fullWidth:ey,zeroRight:eh};var eE=n(50514),eS=n(41050),eC=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=(0,eS.m)();return t&&e.setAttribute("nonce",t),e}())){var r,o;(r=t).styleSheet?r.styleSheet.cssText=n:r.appendChild(document.createTextNode(n)),o=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(o)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},e_=function(){var e=eC();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},eR=function(){var e=e_();return function(t){return e(t.styles,t.dynamic),null}},eT={left:0,top:0,right:0,gap:0},ek=function(e){return parseInt(e||"",10)||0},eP=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[ek(n),ek(r),ek(o)]},eL=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return eT;var t=eP(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},eN=eR(),ej="data-scroll-locked",eO=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(ej,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(eh," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(ey," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(eh," .").concat(eh," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(ey," .").concat(ey," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(ej,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},eA=function(){var e=parseInt(document.body.getAttribute(ej)||"0",10);return isFinite(e)?e:0},eD=function(){i.useEffect(function(){return document.body.setAttribute(ej,(eA()+1).toString()),function(){var e=eA()-1;e<=0?document.body.removeAttribute(ej):document.body.setAttribute(ej,e.toString())}},[])},eI=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;eD();var l=i.useMemo(function(){return eL(o)},[o]);return i.createElement(eN,{styles:eO(l,!t,o,n?"":"!important")})},eM=!1;if("undefined"!=typeof window)try{var eH=Object.defineProperty({},"passive",{get:function(){return eM=!0,!0}});window.addEventListener("test",eH,eH),window.removeEventListener("test",eH,eH)}catch(e){eM=!1}var eF=!!eM&&{passive:!1},eB=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},eW=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),eU(e,r)){var o=e$(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},eU=function(e,t){return"v"===e?eB(t,"overflowY"):eB(t,"overflowX")},e$=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eG=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,s=t.contains(u),c=!1,d=a>0,f=0,p=0;do{if(!u)break;var v=e$(e,u),m=v[0],h=v[1]-v[2]-l*m;(m||h)&&eU(e,u)&&(f+=h,p+=m);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},eV=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},eK=function(e){return[e.deltaX,e.deltaY]},ez=function(e){return e&&"current"in e?e.current:e},eY=0,eX=[];let eq=(0,eE.m)(eb,function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(eY++)[0],l=i.useState(eR)[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,em.fX)([e.lockRef.current],(e.shards||[]).map(ez),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=eV(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],s="deltaY"in e?e.deltaY:l[1]-i[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=eW(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=eW(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return eG(p,t,e,"h"===p?u:s,!0)},[]),s=i.useCallback(function(e){if(eX.length&&eX[eX.length-1]===l){var n="deltaY"in e?eK(e):eV(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(ez).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=i.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=i.useCallback(function(e){n.current=eV(e),r.current=void 0},[]),f=i.useCallback(function(t){c(t.type,eK(t),t.target,u(t,e.lockRef.current))},[]),p=i.useCallback(function(t){c(t.type,eV(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return eX.push(l),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,eF),document.addEventListener("touchmove",s,eF),document.addEventListener("touchstart",d,eF),function(){eX=eX.filter(function(e){return e!==l}),document.removeEventListener("wheel",s,eF),document.removeEventListener("touchmove",s,eF),document.removeEventListener("touchstart",d,eF)}},[]);var v=e.removeScrollBar,m=e.inert;return i.createElement(i.Fragment,null,m?i.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?i.createElement(eI,{noRelative:e.noRelative,gapMode:e.gapMode}):null)});var eZ=i.forwardRef(function(e,t){return i.createElement(ex,(0,em.Cl)({},e,{ref:t,sideCar:eq}))});eZ.classNames=ex.classNames;var eJ=[" ","Enter","ArrowUp","ArrowDown"],eQ=[" ","Enter"],e0="Select",[e1,e2,e5]=function(e){let t=e+"CollectionProvider",[n,r]=v(t),[o,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=i.useRef(null),l=i.useRef(new Map).current;return(0,p.jsx)(o,{scope:t,itemMap:l,collectionRef:r,children:n})};a.displayName=t;let u=e+"CollectionSlot",s=(0,h.TL)(u),c=i.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=l(u,n),i=(0,m.s)(t,o.collectionRef);return(0,p.jsx)(s,{ref:i,children:r})});c.displayName=u;let d=e+"CollectionItemSlot",f="data-radix-collection-item",y=(0,h.TL)(d),g=i.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,a=i.useRef(null),u=(0,m.s)(t,a),s=l(d,n);return i.useEffect(()=>(s.itemMap.set(a,{ref:a,...o}),()=>void s.itemMap.delete(a))),(0,p.jsx)(y,{...{[f]:""},ref:u,children:r})});return g.displayName=d,[{Provider:a,Slot:c,ItemSlot:g},function(t){let n=l(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(f,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(e0),[e9,e4]=v(e0,[e5,Y]),e3=Y(),[e6,e8]=e9(e0),[e7,te]=e9(e0),tt=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:l,value:a,defaultValue:u,onValueChange:s,dir:c,name:d,autoComplete:f,disabled:v,required:m,form:h}=e,y=e3(t),[g,b]=i.useState(null),[x,E]=i.useState(null),[S,C]=i.useState(!1),_=function(e){let t=i.useContext(w);return e||t||"ltr"}(c),[R,T]=ef({prop:r,defaultProp:null!=o&&o,onChange:l,caller:e0}),[k,P]=ef({prop:a,defaultProp:u,onChange:s,caller:e0}),L=i.useRef(null),N=!g||h||!!g.closest("form"),[j,O]=i.useState(new Set),A=Array.from(j).map(e=>e.props.value).join(";");return(0,p.jsx)(Z,{...y,children:(0,p.jsxs)(e6,{required:m,scope:t,trigger:g,onTriggerChange:b,valueNode:x,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:U(),value:k,onValueChange:P,open:R,onOpenChange:T,dir:_,triggerPointerDownPosRef:L,disabled:v,children:[(0,p.jsx)(e1.Provider,{scope:t,children:(0,p.jsx)(e7,{scope:e.__scopeSelect,onNativeOptionAdd:i.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:i.useCallback(e=>{O(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),N?(0,p.jsxs)(tW,{"aria-hidden":!0,required:m,tabIndex:-1,name:d,autoComplete:f,value:k,onChange:e=>P(e.target.value),disabled:v,form:h,children:[void 0===k?(0,p.jsx)("option",{value:""}):null,Array.from(j)]},A):null]})})};tt.displayName=e0;var tn="SelectTrigger",tr=i.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,l=e3(n),a=e8(tn,n),u=a.disabled||r,c=(0,m.s)(t,a.onTriggerChange),d=e2(n),f=i.useRef("touch"),[v,h,y]=t$(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===a.value),r=tG(t,e,n);void 0!==r&&a.onValueChange(r.value)}),g=e=>{u||(a.onOpenChange(!0),y()),e&&(a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,p.jsx)(Q,{asChild:!0,...l,children:(0,p.jsx)(x.sG.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":tU(a.value)?"":void 0,...o,ref:c,onClick:s(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&g(e)}),onPointerDown:s(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:s(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&eJ.includes(e.key)&&(g(),e.preventDefault())})})})});tr.displayName=tn;var to="SelectValue",ti=i.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=e8(to,n),{onValueNodeHasChildrenChange:s}=u,c=void 0!==i,d=(0,m.s)(t,u.onValueNodeChange);return F(()=>{s(c)},[s,c]),(0,p.jsx)(x.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:tU(u.value)?(0,p.jsx)(p.Fragment,{children:l}):i})});ti.displayName=to;var tl=i.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,p.jsx)(x.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tl.displayName="SelectIcon";var ta=e=>(0,p.jsx)(ec,{asChild:!0,...e});ta.displayName="SelectPortal";var tu="SelectContent",ts=i.forwardRef((e,t)=>{let n=e8(tu,e.__scopeSelect),[r,o]=i.useState();return(F(()=>{o(new DocumentFragment)},[]),n.open)?(0,p.jsx)(tp,{...e,ref:t}):r?a.createPortal((0,p.jsx)(tc,{scope:e.__scopeSelect,children:(0,p.jsx)(e1.Slot,{scope:e.__scopeSelect,children:(0,p.jsx)("div",{children:e.children})})}),r):null});ts.displayName=tu;var[tc,td]=e9(tu),tf=(0,h.TL)("SelectContent.RemoveScroll"),tp=i.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:l,onPointerDownOutside:a,side:u,sideOffset:c,align:d,alignOffset:f,arrowPadding:v,collisionBoundary:h,collisionPadding:y,sticky:g,hideWhenDetached:b,avoidCollisions:w,...x}=e,E=e8(tu,n),[S,C]=i.useState(null),[R,T]=i.useState(null),L=(0,m.s)(t,e=>C(e)),[N,j]=i.useState(null),[A,D]=i.useState(null),I=e2(n),[M,H]=i.useState(!1),F=i.useRef(!1);i.useEffect(()=>{if(S)return(0,ev.Eq)(S)},[S]),i.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:P()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:P()),k++,()=>{1===k&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),k--}},[]);let B=i.useCallback(e=>{let[t,...n]=I().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&R&&(R.scrollTop=0),n===r&&R&&(R.scrollTop=R.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[I,R]),W=i.useCallback(()=>B([N,S]),[B,N,S]);i.useEffect(()=>{M&&W()},[M,W]);let{onOpenChange:U,triggerPointerDownPosRef:$}=E;i.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=$.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(i=null==(r=$.current)?void 0:r.y)?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||U(!1),document.removeEventListener("pointermove",t),$.current=null};return null!==$.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,U,$]),i.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[G,V]=t$(e=>{let t=I().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=tG(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),K=i.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==E.value&&E.value===t||r)&&(j(e),r&&(F.current=!0))},[E.value]),z=i.useCallback(()=>null==S?void 0:S.focus(),[S]),Y=i.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==E.value&&E.value===t||r)&&D(e)},[E.value]),X="popper"===r?tm:tv,q=X===tm?{side:u,sideOffset:c,align:d,alignOffset:f,arrowPadding:v,collisionBoundary:h,collisionPadding:y,sticky:g,hideWhenDetached:b,avoidCollisions:w}:{};return(0,p.jsx)(tc,{scope:n,content:S,viewport:R,onViewportChange:T,itemRefCallback:K,selectedItem:N,onItemLeave:z,itemTextRefCallback:Y,focusSelectedItem:W,selectedItemText:A,position:r,isPositioned:M,searchRef:G,children:(0,p.jsx)(eZ,{as:tf,allowPinchZoom:!0,children:(0,p.jsx)(O,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:s(o,e=>{var t;null==(t=E.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,p.jsx)(_,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,p.jsx)(X,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...x,...q,onPlaced:()=>H(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:s(x.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||V(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=I().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});tp.displayName="SelectContentImpl";var tv=i.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,l=e8(tu,n),a=td(tu,n),[s,c]=i.useState(null),[d,f]=i.useState(null),v=(0,m.s)(t,e=>f(e)),h=e2(n),y=i.useRef(!1),g=i.useRef(!0),{viewport:b,selectedItem:w,selectedItemText:E,focusSelectedItem:S}=a,C=i.useCallback(()=>{if(l.trigger&&l.valueNode&&s&&d&&b&&w&&E){let e=l.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),o=E.getBoundingClientRect();if("rtl"!==l.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,a=e.width+l,c=Math.max(a,t.width),d=u(i,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=a+"px",s.style.left=d+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,a=e.width+l,c=Math.max(a,t.width),d=u(i,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=a+"px",s.style.right=d+"px"}let i=h(),a=window.innerHeight-20,c=b.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),v=parseInt(f.paddingTop,10),m=parseInt(f.borderBottomWidth,10),g=p+v+c+parseInt(f.paddingBottom,10)+m,x=Math.min(5*w.offsetHeight,g),S=window.getComputedStyle(b),C=parseInt(S.paddingTop,10),_=parseInt(S.paddingBottom,10),R=e.top+e.height/2-10,T=w.offsetHeight/2,k=p+v+(w.offsetTop+T);if(k<=R){let e=i.length>0&&w===i[i.length-1].ref.current;s.style.bottom="0px";let t=Math.max(a-R,T+(e?_:0)+(d.clientHeight-b.offsetTop-b.offsetHeight)+m);s.style.height=k+t+"px"}else{let e=i.length>0&&w===i[0].ref.current;s.style.top="0px";let t=Math.max(R,p+b.offsetTop+(e?C:0)+T);s.style.height=t+(g-k)+"px",b.scrollTop=k-R+b.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=x+"px",s.style.maxHeight=a+"px",null==r||r(),requestAnimationFrame(()=>y.current=!0)}},[h,l.trigger,l.valueNode,s,d,b,w,E,l.dir,r]);F(()=>C(),[C]);let[_,R]=i.useState();F(()=>{d&&R(window.getComputedStyle(d).zIndex)},[d]);let T=i.useCallback(e=>{e&&!0===g.current&&(C(),null==S||S(),g.current=!1)},[C,S]);return(0,p.jsx)(th,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:y,onScrollButtonChange:T,children:(0,p.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:_},children:(0,p.jsx)(x.sG.div,{...o,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});tv.displayName="SelectItemAlignedPosition";var tm=i.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=e3(n);return(0,p.jsx)(er,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tm.displayName="SelectPopperPosition";var[th,ty]=e9(tu,{}),tg="SelectViewport",tb=i.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,l=td(tg,n),a=ty(tg,n),u=(0,m.s)(t,l.onViewportChange),c=i.useRef(0);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,p.jsx)(e1.Slot,{scope:n,children:(0,p.jsx)(x.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:s(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=a;if((null==r?void 0:r.current)&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});tb.displayName=tg;var tw="SelectGroup",[tx,tE]=e9(tw),tS=i.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=U();return(0,p.jsx)(tx,{scope:n,id:o,children:(0,p.jsx)(x.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});tS.displayName=tw;var tC="SelectLabel",t_=i.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tE(tC,n);return(0,p.jsx)(x.sG.div,{id:o.id,...r,ref:t})});t_.displayName=tC;var tR="SelectItem",[tT,tk]=e9(tR),tP=i.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:l,...a}=e,u=e8(tR,n),c=td(tR,n),d=u.value===r,[f,v]=i.useState(null!=l?l:""),[h,y]=i.useState(!1),g=(0,m.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,r,o)}),b=U(),w=i.useRef("touch"),E=()=>{o||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,p.jsx)(tT,{scope:n,value:r,disabled:o,textId:b,isSelected:d,onItemTextChange:i.useCallback(e=>{v(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,p.jsx)(e1.ItemSlot,{scope:n,value:r,disabled:o,textValue:f,children:(0,p.jsx)(x.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":h?"":void 0,"aria-selected":d&&h,"data-state":d?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...a,ref:g,onFocus:s(a.onFocus,()=>y(!0)),onBlur:s(a.onBlur,()=>y(!1)),onClick:s(a.onClick,()=>{"mouse"!==w.current&&E()}),onPointerUp:s(a.onPointerUp,()=>{"mouse"===w.current&&E()}),onPointerDown:s(a.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:s(a.onPointerMove,e=>{if(w.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:s(a.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:s(a.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(eQ.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});tP.displayName=tR;var tL="SelectItemText",tN=i.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...l}=e,u=e8(tL,n),s=td(tL,n),c=tk(tL,n),d=te(tL,n),[f,v]=i.useState(null),h=(0,m.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null==(t=s.itemTextRefCallback)?void 0:t.call(s,e,c.value,c.disabled)}),y=null==f?void 0:f.textContent,g=i.useMemo(()=>(0,p.jsx)("option",{value:c.value,disabled:c.disabled,children:y},c.value),[c.disabled,c.value,y]),{onNativeOptionAdd:b,onNativeOptionRemove:w}=d;return F(()=>(b(g),()=>w(g)),[b,w,g]),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(x.sG.span,{id:c.textId,...l,ref:h}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?a.createPortal(l.children,u.valueNode):null]})});tN.displayName=tL;var tj="SelectItemIndicator",tO=i.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return tk(tj,n).isSelected?(0,p.jsx)(x.sG.span,{"aria-hidden":!0,...r,ref:t}):null});tO.displayName=tj;var tA="SelectScrollUpButton",tD=i.forwardRef((e,t)=>{let n=td(tA,e.__scopeSelect),r=ty(tA,e.__scopeSelect),[o,l]=i.useState(!1),a=(0,m.s)(t,r.onScrollButtonChange);return F(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,p.jsx)(tH,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});tD.displayName=tA;var tI="SelectScrollDownButton",tM=i.forwardRef((e,t)=>{let n=td(tI,e.__scopeSelect),r=ty(tI,e.__scopeSelect),[o,l]=i.useState(!1),a=(0,m.s)(t,r.onScrollButtonChange);return F(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,p.jsx)(tH,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});tM.displayName=tI;var tH=i.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,l=td("SelectScrollButton",n),a=i.useRef(null),u=e2(n),c=i.useCallback(()=>{null!==a.current&&(window.clearInterval(a.current),a.current=null)},[]);return i.useEffect(()=>()=>c(),[c]),F(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,p.jsx)(x.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:s(o.onPointerDown,()=>{null===a.current&&(a.current=window.setInterval(r,50))}),onPointerMove:s(o.onPointerMove,()=>{var e;null==(e=l.onItemLeave)||e.call(l),null===a.current&&(a.current=window.setInterval(r,50))}),onPointerLeave:s(o.onPointerLeave,()=>{c()})})}),tF=i.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,p.jsx)(x.sG.div,{"aria-hidden":!0,...r,ref:t})});tF.displayName="SelectSeparator";var tB="SelectArrow";i.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=e3(n),i=e8(tB,n),l=td(tB,n);return i.open&&"popper"===l.position?(0,p.jsx)(el,{...o,...r,ref:t}):null}).displayName=tB;var tW=i.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...o}=e,l=i.useRef(null),a=(0,m.s)(t,l),u=function(e){let t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return i.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[u,r]),(0,p.jsx)(x.sG.select,{...o,style:{...ep,...o.style},ref:a,defaultValue:r})});function tU(e){return""===e||void 0===e}function t$(e){let t=E(e),n=i.useRef(""),r=i.useRef(0),o=i.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),l=i.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return i.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,l]}function tG(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}tW.displayName="SelectBubbleInput";var tV=tt,tK=tr,tz=ti,tY=tl,tX=ta,tq=ts,tZ=tb,tJ=tS,tQ=t_,t0=tP,t1=tN,t2=tO,t5=tD,t9=tM,t4=tF},62786:(e,t,n)=>{e.exports=n(45919)},63655:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(12115),o=n(47650),i=n(99708),l=n(95155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},78944:(e,t,n)=>{!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(24279)},84655:(e,t,n)=>{var r=n(14232);function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(){}var l={d:{f:i,r:function(){throw Error(o(522))},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null},a=Symbol.for("react.portal"),u=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function s(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(o(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:a,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=u.T,n=l.p;try{if(u.T=null,l.p=2,e)return e()}finally{u.T=t,l.p=n,l.d.f()}},t.preconnect=function(e,t){"string"==typeof e&&(t=t?"string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:null,l.d.C(e,t))},t.prefetchDNS=function(e){"string"==typeof e&&l.d.D(e)},t.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var n=t.as,r=s(n,t.crossOrigin),o="string"==typeof t.integrity?t.integrity:void 0,i="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?l.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:o,fetchPriority:i}):"script"===n&&l.d.X(e,{crossOrigin:r,integrity:o,fetchPriority:i,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=s(t.as,t.crossOrigin);l.d.M(e,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&l.d.M(e)},t.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var n=t.as,r=s(n,t.crossOrigin);l.d.L(e,n,{crossOrigin:r,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"==typeof e)if(t){var n=s(t.as,t.crossOrigin);l.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else l.d.m(e)},t.requestFormReset=function(e){l.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return u.H.useFormState(e,t,n)},t.useFormStatus=function(){return u.H.useHostTransitionStatus()},t.version="19.1.0"},98477:(e,t,n)=>{!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(84655)},99708:(e,t,n)=>{n.d(t,{DX:()=>a,TL:()=>l});var r=n(12115),o=n(6101),i=n(95155);function l(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var l;let e,a,u=(l=n,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(s.ref=t?(0,o.t)(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...l}=e,a=r.Children.toArray(o),u=a.find(s);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var a=l("Slot"),u=Symbol("radix.slottable");function s(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}}}]);