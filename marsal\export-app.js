#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  appName: 'Marsal-Delivery-App',
  version: '1.0.0',
  outputDir: path.join(process.env.USERPROFILE || process.env.HOME, 'Desktop', 'Marsal-Exports'),
  sourceDir: process.cwd(),
  excludePatterns: [
    'node_modules',
    '.next',
    '.git',
    'dist',
    'build',
    '*.log',
    '.env.local',
    '.DS_Store',
    'Thumbs.db'
  ]
};

console.log('🚀 بدء تصدير تطبيق مرسال...\n');

// Create output directory
function createOutputDirectory() {
  console.log('📁 إنشاء مجلد التصدير...');
  
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
    console.log(`✅ تم إنشاء المجلد: ${CONFIG.outputDir}`);
  } else {
    console.log(`✅ المجلد موجود: ${CONFIG.outputDir}`);
  }
}

// Copy source files
function copySourceFiles() {
  console.log('\n📋 نسخ ملفات المصدر...');
  
  const sourceExportDir = path.join(CONFIG.outputDir, 'source-code');
  
  if (fs.existsSync(sourceExportDir)) {
    console.log('🗑️ حذف النسخة السابقة...');
    fs.rmSync(sourceExportDir, { recursive: true, force: true });
  }
  
  fs.mkdirSync(sourceExportDir, { recursive: true });
  
  // Copy files recursively
  copyDirectory(CONFIG.sourceDir, sourceExportDir);
  
  console.log(`✅ تم نسخ الملفات إلى: ${sourceExportDir}`);
}

// Copy directory recursively with exclusions
function copyDirectory(src, dest) {
  const items = fs.readdirSync(src);
  
  for (const item of items) {
    const srcPath = path.join(src, item);
    const destPath = path.join(dest, item);
    
    // Check if item should be excluded
    if (shouldExclude(item, srcPath)) {
      continue;
    }
    
    const stat = fs.statSync(srcPath);
    
    if (stat.isDirectory()) {
      fs.mkdirSync(destPath, { recursive: true });
      copyDirectory(srcPath, destPath);
    } else {
      try {
        fs.copyFileSync(srcPath, destPath);
      } catch (error) {
        console.warn(`⚠️ تخطي ملف: ${item} (${error.message})`);
      }
    }
  }
}

// Check if file/directory should be excluded
function shouldExclude(name, fullPath) {
  return CONFIG.excludePatterns.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(name);
    }
    return name === pattern || fullPath.includes(pattern);
  });
}

// Build production version
function buildProduction() {
  console.log('\n🔨 بناء النسخة الإنتاجية...');
  
  try {
    console.log('📦 تثبيت التبعيات...');
    execSync('npm install', { stdio: 'inherit', cwd: CONFIG.sourceDir });
    
    console.log('🏗️ بناء التطبيق...');
    execSync('npm run build', { stdio: 'inherit', cwd: CONFIG.sourceDir });
    
    // Copy build files
    const buildDir = path.join(CONFIG.sourceDir, '.next');
    const exportBuildDir = path.join(CONFIG.outputDir, 'production-build');
    
    if (fs.existsSync(buildDir)) {
      if (fs.existsSync(exportBuildDir)) {
        fs.rmSync(exportBuildDir, { recursive: true, force: true });
      }
      
      fs.mkdirSync(exportBuildDir, { recursive: true });
      copyDirectory(buildDir, exportBuildDir);
      
      // Copy package.json and other necessary files
      const necessaryFiles = ['package.json', 'package-lock.json', 'next.config.js', 'tailwind.config.js'];
      for (const file of necessaryFiles) {
        const srcFile = path.join(CONFIG.sourceDir, file);
        const destFile = path.join(exportBuildDir, file);
        if (fs.existsSync(srcFile)) {
          fs.copyFileSync(srcFile, destFile);
        }
      }
      
      console.log(`✅ تم نسخ البناء الإنتاجي إلى: ${exportBuildDir}`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في البناء:', error.message);
  }
}

// Create deployment package
function createDeploymentPackage() {
  console.log('\n📦 إنشاء حزمة النشر...');
  
  const deploymentDir = path.join(CONFIG.outputDir, 'deployment-package');
  
  if (fs.existsSync(deploymentDir)) {
    fs.rmSync(deploymentDir, { recursive: true, force: true });
  }
  
  fs.mkdirSync(deploymentDir, { recursive: true });
  
  // Copy essential files for deployment
  const essentialFiles = [
    'package.json',
    'package-lock.json',
    'next.config.js',
    'tailwind.config.js',
    'tsconfig.json',
    'postcss.config.js'
  ];
  
  for (const file of essentialFiles) {
    const srcFile = path.join(CONFIG.sourceDir, file);
    const destFile = path.join(deploymentDir, file);
    if (fs.existsSync(srcFile)) {
      fs.copyFileSync(srcFile, destFile);
    }
  }
  
  // Copy src directory
  const srcDir = path.join(CONFIG.sourceDir, 'src');
  const destSrcDir = path.join(deploymentDir, 'src');
  if (fs.existsSync(srcDir)) {
    copyDirectory(srcDir, destSrcDir);
  }
  
  // Copy public directory
  const publicDir = path.join(CONFIG.sourceDir, 'public');
  const destPublicDir = path.join(deploymentDir, 'public');
  if (fs.existsSync(publicDir)) {
    copyDirectory(publicDir, destPublicDir);
  }
  
  // Create deployment instructions
  const deploymentInstructions = `# تعليمات نشر تطبيق مرسال

## متطلبات النشر:
- Node.js 18 أو أحدث
- npm أو yarn

## خطوات النشر:

### 1. تثبيت التبعيات:
\`\`\`bash
npm install
\`\`\`

### 2. إعداد متغيرات البيئة:
انسخ ملف \`.env.example\` إلى \`.env.local\` وأضف إعدادات Firebase الخاصة بك.

### 3. بناء التطبيق:
\`\`\`bash
npm run build
\`\`\`

### 4. تشغيل التطبيق:
\`\`\`bash
npm start
\`\`\`

## النشر على Vercel:
\`\`\`bash
npm install -g vercel
vercel --prod
\`\`\`

## النشر على Netlify:
\`\`\`bash
npm run build
# ارفع مجلد .next إلى Netlify
\`\`\`

## إعدادات Firebase:
تأكد من إعداد Firebase Console:
1. إنشاء مشروع جديد
2. تفعيل Authentication (Email/Password)
3. تفعيل Firestore Database
4. نسخ إعدادات Firebase إلى .env.local

## الدعم:
للمساعدة، راجع الملفات التوثيقية في مجلد docs/
`;
  
  fs.writeFileSync(path.join(deploymentDir, 'DEPLOYMENT.md'), deploymentInstructions, 'utf8');
  
  console.log(`✅ تم إنشاء حزمة النشر في: ${deploymentDir}`);
}

// Create documentation
function createDocumentation() {
  console.log('\n📚 إنشاء التوثيق...');
  
  const docsDir = path.join(CONFIG.outputDir, 'documentation');
  
  if (fs.existsSync(docsDir)) {
    fs.rmSync(docsDir, { recursive: true, force: true });
  }
  
  fs.mkdirSync(docsDir, { recursive: true });
  
  // Copy documentation files
  const docFiles = fs.readdirSync(CONFIG.sourceDir).filter(file => 
    file.endsWith('.md') || file.startsWith('دليل_') || file.startsWith('تقرير_')
  );
  
  for (const file of docFiles) {
    const srcFile = path.join(CONFIG.sourceDir, file);
    const destFile = path.join(docsDir, file);
    if (fs.existsSync(srcFile)) {
      fs.copyFileSync(srcFile, destFile);
    }
  }
  
  console.log(`✅ تم نسخ التوثيق إلى: ${docsDir}`);
}

// Create export summary
function createExportSummary() {
  console.log('\n📋 إنشاء ملخص التصدير...');
  
  const summary = `# ملخص تصدير تطبيق مرسال

## معلومات التصدير:
- **اسم التطبيق**: ${CONFIG.appName}
- **الإصدار**: ${CONFIG.version}
- **تاريخ التصدير**: ${new Date().toLocaleString('ar-SA')}
- **مجلد التصدير**: ${CONFIG.outputDir}

## محتويات التصدير:

### 1. source-code/
الكود المصدري الكامل للتطبيق

### 2. production-build/
النسخة المبنية والجاهزة للنشر

### 3. deployment-package/
حزمة النشر مع الملفات الأساسية فقط

### 4. documentation/
جميع ملفات التوثيق والأدلة

## طرق التشغيل:

### التطوير:
\`\`\`bash
cd source-code
npm install
npm run dev
\`\`\`

### الإنتاج:
\`\`\`bash
cd deployment-package
npm install
npm run build
npm start
\`\`\`

## الميزات المتوفرة:
- ✅ نظام إدارة التوصيل الشامل
- ✅ قاعدة بيانات Firebase
- ✅ نظام المصادقة والصلاحيات
- ✅ واجهة مستخدم متجاوبة
- ✅ تحديثات فورية
- ✅ دعم متعدد المنصات

## بيانات الدخول الافتراضية:
- **المدير الرئيسي**: azad95 / Azad@1995
- **مدير النظام**: manager / 123456
- **المتابع**: supervisor / 123456
- **المندوب**: courier / 123456

## الدعم والمساعدة:
راجع ملفات التوثيق في مجلد documentation/

---
تم إنشاء هذا التصدير تلقائياً بواسطة سكريبت التصدير
`;
  
  fs.writeFileSync(path.join(CONFIG.outputDir, 'README.md'), summary, 'utf8');
  
  console.log(`✅ تم إنشاء ملخص التصدير في: ${CONFIG.outputDir}/README.md`);
}

// Main export function
function exportApp() {
  try {
    console.log(`📱 تصدير تطبيق: ${CONFIG.appName} v${CONFIG.version}`);
    console.log(`📂 المجلد المصدر: ${CONFIG.sourceDir}`);
    console.log(`📁 مجلد التصدير: ${CONFIG.outputDir}\n`);
    
    createOutputDirectory();
    copySourceFiles();
    buildProduction();
    createDeploymentPackage();
    createDocumentation();
    createExportSummary();
    
    console.log('\n🎉 تم تصدير التطبيق بنجاح!');
    console.log(`📁 يمكنك العثور على الملفات في: ${CONFIG.outputDir}`);
    console.log('\n📋 محتويات التصدير:');
    console.log('   📂 source-code/ - الكود المصدري الكامل');
    console.log('   📂 production-build/ - النسخة المبنية');
    console.log('   📂 deployment-package/ - حزمة النشر');
    console.log('   📂 documentation/ - التوثيق والأدلة');
    console.log('   📄 README.md - ملخص التصدير');
    
  } catch (error) {
    console.error('\n❌ خطأ في التصدير:', error.message);
    process.exit(1);
  }
}

// Run export
exportApp();
