"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   STATUS_COLORS: () => (/* binding */ STATUS_COLORS),\n/* harmony export */   STATUS_LABELS: () => (/* binding */ STATUS_LABELS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Application configuration\nconst APP_CONFIG = {\n    name: \"مرسال\",\n    description: \"نظام إدارة عمليات التوصيل السريع\",\n    version: \"1.1.0\",\n    // Colors\n    colors: {\n        primary: \"#41a7ff\",\n        background: \"#f0f3f5\",\n        accent: \"#b19cd9\"\n    },\n    // Business rules\n    business: {\n        commissionPerOrder: 1000,\n        currency: \"د.ع\",\n        defaultOrderStatuses: [\n            \"pending\",\n            \"assigned\",\n            \"picked_up\",\n            \"in_transit\",\n            \"delivered\",\n            \"returned\",\n            \"cancelled\",\n            \"postponed\"\n        ]\n    },\n    // API endpoints (when backend is ready)\n    api: {\n        baseUrl: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\",\n        endpoints: {\n            orders: \"/api/orders\",\n            users: \"/api/users\",\n            couriers: \"/api/couriers\",\n            settlements: \"/api/settlements\"\n        }\n    },\n    // Firebase config keys (to be replaced with actual values)\n    firebase: {\n        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\n    },\n    // Features flags\n    features: {\n        enableNotifications: true,\n        enableReports: true,\n        enableBulkOperations: true,\n        enableImageUpload: true\n    },\n    // Database mode settings - إعدادات قاعدة البيانات (ربط دائم بالسحابية)\n    database: {\n        mode: 'cloud',\n        cloudEnabled: true,\n        localFallback: true,\n        autoSync: true,\n        syncInterval: 60000,\n        forceCloud: true,\n        retryAttempts: 5,\n        retryDelay: 2000 // تأخير بين المحاولات (2 ثانية)\n    },\n    // Demo mode settings - إعدادات الوضع التجريبي (مع ربط دائم بالسحابية)\n    demo: {\n        enabled: false,\n        autoLogin: false,\n        defaultUser: 'manager',\n        skipFirebase: true,\n        showDemoNotice: false,\n        cloudOnly: true // استخدام قاعدة البيانات السحابية فقط\n    },\n    // Company info\n    company: {\n        name: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        phone: '+964 ************',\n        address: 'بغداد، العراق'\n    },\n    // Receipt settings - إعدادات الوصل\n    receipt: {\n        dimensions: {\n            width: '110mm',\n            height: '130mm'\n        },\n        companyName: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        showBarcode: true,\n        showDate: true,\n        priceFormat: 'en-US',\n        currency: 'IQD',\n        fields: {\n            trackingNumber: true,\n            customerPhone: true,\n            status: true,\n            courierName: true,\n            amount: true,\n            barcode: true,\n            date: true\n        }\n    }\n};\n// Status labels in Arabic\nconst STATUS_LABELS = {\n    pending: \"في الانتظار\",\n    assigned: \"مسند\",\n    picked_up: \"تم الاستلام\",\n    in_transit: \"في الطريق\",\n    delivered: \"تم التسليم\",\n    returned: \"راجع\",\n    cancelled: \"ملغي\",\n    postponed: \"مؤجل\"\n};\n// Status colors for UI\nconst STATUS_COLORS = {\n    pending: \"text-yellow-600 bg-yellow-100\",\n    assigned: \"text-blue-600 bg-blue-100\",\n    picked_up: \"text-purple-600 bg-purple-100\",\n    in_transit: \"text-orange-600 bg-orange-100\",\n    delivered: \"text-green-600 bg-green-100\",\n    returned: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    postponed: \"text-gray-600 bg-gray-100\"\n};\n// User roles\nconst USER_ROLES = {\n    admin: \"مدير\",\n    manager: \"مدير فرع\",\n    dispatcher: \"موزع\",\n    courier: \"مندوب\",\n    accountant: \"محاسب\",\n    viewer: \"مشاهد\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});