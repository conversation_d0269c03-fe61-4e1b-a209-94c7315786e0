/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/archive/page";
exports.ids = ["app/archive/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Farchive%2Fpage&page=%2Farchive%2Fpage&appPaths=%2Farchive%2Fpage&pagePath=private-next-app-dir%2Farchive%2Fpage.tsx&appDir=E%3A%5CMarsal%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMarsal%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Farchive%2Fpage&page=%2Farchive%2Fpage&appPaths=%2Farchive%2Fpage&pagePath=private-next-app-dir%2Farchive%2Fpage.tsx&appDir=E%3A%5CMarsal%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMarsal%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/archive/page.tsx */ \"(rsc)/./src/app/archive/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'archive',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/archive/page\",\n        pathname: \"/archive\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Farchive%2Fpage&page=%2Farchive%2Fpage&appPaths=%2Farchive%2Fpage&pagePath=private-next-app-dir%2Farchive%2Fpage.tsx&appDir=E%3A%5CMarsal%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMarsal%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth-provider.tsx */ \"(rsc)/./src/components/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNNYXJzYWwlNUMlNUNtYXJzYWwlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTJDJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtaW50ZXIlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDTWFyc2FsJTVDJTVDbWFyc2FsJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q01hcnNhbCU1QyU1Q21hcnNhbCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0xBQTZIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJFOlxcXFxNYXJzYWxcXFxcbWFyc2FsXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Carchive%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Carchive%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/archive/page.tsx */ \"(rsc)/./src/app/archive/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNNYXJzYWwlNUMlNUNtYXJzYWwlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhcmNoaXZlJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUFvRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcTWFyc2FsXFxcXG1hcnNhbFxcXFxzcmNcXFxcYXBwXFxcXGFyY2hpdmVcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Carchive%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFxNYXJzYWxcXG1hcnNhbFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/archive/page.tsx":
/*!**********************************!*\
  !*** ./src/app/archive/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Marsal\\marsal\\src\\app\\archive\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a7c038f9bc65\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxcTWFyc2FsXFxtYXJzYWxcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE3YzAzOGY5YmM2NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   generateViewport: () => (/* binding */ generateViewport),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth-provider */ \"(rsc)/./src/components/auth-provider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"مرسال - نظام إدارة التوصيل\",\n    description: \"نظام إدارة عمليات شركات التوصيل السريع\",\n    manifest: \"/manifest.json\"\n};\nfunction generateViewport() {\n    return {\n        width: 'device-width',\n        initialScale: 1,\n        maximumScale: 5,\n        userScalable: true,\n        themeColor: '#3B82F6',\n        colorScheme: 'light dark',\n        viewportFit: 'cover'\n    };\n}\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased h-full w-full overflow-x-hidden`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_provider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen w-full max-w-full overflow-x-hidden\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/auth-provider.tsx":
/*!******************************************!*\
  !*** ./src/components/auth-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Marsal\\marsal\\src\\components\\auth-provider.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Marsal\\marsal\\src\\components\\auth-provider.tsx",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth-provider.tsx */ \"(ssr)/./src/components/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNNYXJzYWwlNUMlNUNtYXJzYWwlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTJDJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtaW50ZXIlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDTWFyc2FsJTVDJTVDbWFyc2FsJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q01hcnNhbCU1QyU1Q21hcnNhbCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0xBQTZIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJFOlxcXFxNYXJzYWxcXFxcbWFyc2FsXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Carchive%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Carchive%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/archive/page.tsx */ \"(ssr)/./src/app/archive/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNNYXJzYWwlNUMlNUNtYXJzYWwlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhcmNoaXZlJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUFvRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcTWFyc2FsXFxcXG1hcnNhbFxcXFxzcmNcXFxcYXBwXFxcXGFyY2hpdmVcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CMarsal%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Carchive%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/archive/page.tsx":
/*!**********************************!*\
  !*** ./src/app/archive/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArchivePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowLeft,Download,Eye,FileText,Package,Printer,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowLeft,Download,Eye,FileText,Package,Printer,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowLeft,Download,Eye,FileText,Package,Printer,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowLeft,Download,Eye,FileText,Package,Printer,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowLeft,Download,Eye,FileText,Package,Printer,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowLeft,Download,Eye,FileText,Package,Printer,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowLeft,Download,Eye,FileText,Package,Printer,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,ArrowLeft,Download,Eye,FileText,Package,Printer,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _lib_firestore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firestore */ \"(ssr)/./src/lib/firestore.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth-provider */ \"(ssr)/./src/components/auth-provider.tsx\");\n/* harmony import */ var _components_print_receipt__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/print-receipt */ \"(ssr)/./src/components/print-receipt.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction ArchivePage() {\n    const { user } = (0,_components_auth_provider__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('orders');\n    const [archivedOrders, setArchivedOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [settlements, setSettlements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [couriers, setCouriers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [courierFilter, setCourierFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ArchivePage.useEffect\": ()=>{\n            loadData();\n        }\n    }[\"ArchivePage.useEffect\"], [\n        activeTab\n    ]);\n    const handlePrintArchiveStatement = ()=>{\n        const reportDate = new Date().toLocaleDateString('ar-IQ');\n        const reportTime = new Date().toLocaleTimeString('ar-IQ');\n        const printContent = `\n      <!DOCTYPE html>\n      <html dir=\"rtl\" lang=\"ar\">\n      <head>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>كشف الأرشيف - مرسال</title>\n        <style>\n          body {\n            font-family: Arial, sans-serif;\n            direction: rtl;\n            margin: 20px;\n            color: #333;\n          }\n          .header {\n            text-align: center;\n            margin-bottom: 30px;\n            border-bottom: 3px solid #2563eb;\n            padding-bottom: 20px;\n          }\n          .company-name {\n            font-size: 28px;\n            font-weight: bold;\n            color: #2563eb;\n            margin-bottom: 10px;\n          }\n          .report-title {\n            font-size: 20px;\n            color: #374151;\n            margin-bottom: 10px;\n          }\n          .report-info {\n            font-size: 14px;\n            color: #6b7280;\n          }\n          .table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 20px;\n          }\n          .table th, .table td {\n            border: 1px solid #ddd;\n            padding: 12px;\n            text-align: right;\n          }\n          .table th {\n            background-color: #f8f9fa;\n            font-weight: bold;\n          }\n          .summary {\n            background-color: #f5f5f5;\n            padding: 15px;\n            border-radius: 5px;\n            margin-top: 20px;\n          }\n          .footer {\n            text-align: center;\n            margin-top: 30px;\n            font-size: 12px;\n            color: #666;\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"header\">\n          <div class=\"company-name\">مكتب علي الشيباني للتوصيل السريع</div>\n          <div class=\"report-title\">كشف الأرشيف</div>\n          <div class=\"report-info\">\n            <p>تاريخ الكشف: ${reportDate} - ${reportTime}</p>\n          </div>\n        </div>\n\n        <table class=\"table\">\n          <thead>\n            <tr>\n              <th>رقم الوصل</th>\n              <th>اسم المستلم</th>\n              <th>رقم الهاتف</th>\n              <th>المبلغ</th>\n              <th>الحالة</th>\n              <th>تاريخ الإنجاز</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${archivedOrders.map((order)=>`\n              <tr>\n                <td>${order.trackingNumber}</td>\n                <td>${order.recipientName}</td>\n                <td>${order.recipientPhone}</td>\n                <td>${order.amount.toLocaleString()} د.ع</td>\n                <td>${order.status === 'delivered' ? 'تم التسليم' : order.status === 'returned' ? 'مرتجع' : 'ملغي'}</td>\n                <td>${order.updatedAt?.toLocaleDateString('ar-IQ') || 'غير محدد'}</td>\n              </tr>\n            `).join('')}\n          </tbody>\n        </table>\n\n        <div class=\"summary\">\n          <h3>ملخص الأرشيف:</h3>\n          <p><strong>إجمالي الطلبات المؤرشفة:</strong> ${archivedOrders.length} طلب</p>\n          <p><strong>الطلبات المسلمة:</strong> ${archivedOrders.filter((o)=>o.status === 'delivered').length} طلب</p>\n          <p><strong>الطلبات المرتجعة:</strong> ${archivedOrders.filter((o)=>o.status === 'returned').length} طلب</p>\n          <p><strong>الطلبات الملغية:</strong> ${archivedOrders.filter((o)=>o.status === 'cancelled').length} طلب</p>\n          <p><strong>إجمالي المبالغ:</strong> ${archivedOrders.reduce((sum, order)=>sum + order.amount, 0).toLocaleString()} دينار عراقي</p>\n        </div>\n\n        <div class=\"footer\">\n          <p>تم إنشاء هذا الكشف بواسطة نظام مرسال لإدارة التوصيل</p>\n        </div>\n      </body>\n      </html>\n    `;\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write(printContent);\n            printWindow.document.close();\n            printWindow.focus();\n            printWindow.print();\n            printWindow.close();\n        }\n    };\n    const loadData = async ()=>{\n        setLoading(true);\n        try {\n            if (activeTab === 'orders') {\n                // Load archived orders (delivered, returned, cancelled)\n                let allOrders = [];\n                if (user?.role === 'courier') {\n                    // For couriers, only show their own orders\n                    const courierOrders = await _lib_firestore__WEBPACK_IMPORTED_MODULE_5__.ordersService.getByCourier(user.id);\n                    allOrders = courierOrders.filter((order)=>[\n                            'delivered',\n                            'returned',\n                            'cancelled'\n                        ].includes(order.status));\n                } else {\n                    // For managers and supervisors, show all orders\n                    const [delivered, returned, cancelled] = await Promise.all([\n                        _lib_firestore__WEBPACK_IMPORTED_MODULE_5__.ordersService.getByStatus('delivered'),\n                        _lib_firestore__WEBPACK_IMPORTED_MODULE_5__.ordersService.getByStatus('returned'),\n                        _lib_firestore__WEBPACK_IMPORTED_MODULE_5__.ordersService.getByStatus('cancelled')\n                    ]);\n                    allOrders = [\n                        ...delivered,\n                        ...returned,\n                        ...cancelled\n                    ];\n                }\n                setArchivedOrders(allOrders);\n                // Load couriers for filtering (only for non-courier users)\n                if (user?.role !== 'courier') {\n                    const couriersList = await _lib_firestore__WEBPACK_IMPORTED_MODULE_5__.usersService.getCouriers();\n                    setCouriers(couriersList);\n                }\n            } else {\n                // Load settlements\n                const settlementsList = await _lib_firestore__WEBPACK_IMPORTED_MODULE_5__.settlementsService.getAll();\n                setSettlements(settlementsList);\n            }\n        } catch (error) {\n            console.error('Error loading archive data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredOrders = archivedOrders.filter((order)=>{\n        const matchesSearch = order.trackingNumber.toLowerCase().includes(searchTerm.toLowerCase()) || order.recipientName.toLowerCase().includes(searchTerm.toLowerCase()) || order.recipientPhone.includes(searchTerm);\n        const matchesStatus = statusFilter === \"all\" || order.status === statusFilter;\n        const matchesCourier = courierFilter === \"all\" || order.assignedTo === courierFilter;\n        // إذا كان المستخدم مندوب، أظهر فقط الطلبات المسندة إليه\n        const matchesUserRole = user?.role !== 'courier' || order.assignedTo === user?.username;\n        return matchesSearch && matchesStatus && matchesCourier && matchesUserRole;\n    });\n    const filteredSettlements = settlements.filter((settlement)=>settlement.courierName.toLowerCase().includes(searchTerm.toLowerCase()));\n    const downloadOrderReceipt = (order)=>{\n        // This would generate and download a PDF receipt\n        alert(`تحميل وصل الطلب ${order.trackingNumber}`);\n    };\n    const downloadSettlement = (settlement)=>{\n        // This would generate and download a settlement PDF\n        alert(`تحميل كشف التسوية للمندوب ${settlement.courierName}`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 animated-bg p-6\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"flex items-center gap-2 glass\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this),\n                                \"العودة للرئيسية\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-gray-500 to-gray-700 rounded-3xl shadow-2xl mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-8 w-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent mb-2\",\n                            children: \"الأرشيف\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-300 text-lg\",\n                            children: \"السجلات المنتهية والمؤرشفة\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1 bg-muted p-1 rounded-lg w-fit\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab('orders'),\n                            className: `px-4 py-2 rounded-md text-sm font-medium transition-colors ${activeTab === 'orders' ? 'bg-background text-foreground shadow-sm' : 'text-muted-foreground hover:text-foreground'}`,\n                            children: \"أرشيف الطلبات\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab('settlements'),\n                            className: `px-4 py-2 rounded-md text-sm font-medium transition-colors ${activeTab === 'settlements' ? 'bg-background text-foreground shadow-sm' : 'text-muted-foreground hover:text-foreground'}`,\n                            children: \"أرشيف المحاسبات\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"البحث والفلترة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 flex-wrap\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-[200px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            placeholder: activeTab === 'orders' ? \"البحث برقم الوصل أو اسم المستلم...\" : \"البحث باسم المندوب...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === 'orders' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: statusFilter,\n                                                onChange: (e)=>setStatusFilter(e.target.value),\n                                                className: \"px-3 py-2 border rounded-md bg-background\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"جميع الحالات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"delivered\",\n                                                        children: \"تم التسليم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"returned\",\n                                                        children: \"راجع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"cancelled\",\n                                                        children: \"ملغي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this),\n                                            user?.role !== 'courier' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: courierFilter,\n                                                onChange: (e)=>setCourierFilter(e.target.value),\n                                                className: \"px-3 py-2 border rounded-md bg-background\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"جميع المندوبين\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    couriers.map((courier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: courier.id,\n                                                            children: courier.name\n                                                        }, courier.id, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    activeTab === 'orders' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handlePrintArchiveStatement,\n                                        className: \"flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"طباعة الكشف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, this),\n                activeTab === 'orders' ? /* Orders Archive */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"أرشيف الطلبات (\",\n                                        filteredOrders.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"الطلبات المسلمة والراجعة والملغية\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-muted-foreground\",\n                                        children: \"جاري التحميل...\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 17\n                            }, this) : filteredOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"لا توجد طلبات مؤرشفة\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: filteredOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 border border-border rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: order.trackingNumber\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getStatusColor)(order.status)}`,\n                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getStatusLabel)(order.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: order.recipientName\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: order.recipientPhone\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: [\n                                                                \"المندوب: \",\n                                                                couriers.find((c)=>c.id === order.assignedTo)?.name || 'غير محدد'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground mt-1\",\n                                                            children: [\n                                                                \"تاريخ الإنشاء: \",\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(order.createdAt)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        order.deliveredAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                \"تاريخ التسليم: \",\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(order.deliveredAt)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        order.returnedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                \"تاريخ الإرجاع: \",\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(order.returnedAt)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-semibold\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(order.amount)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 mr-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                                            href: `/orders/${order.id}`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_print_receipt__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            order: order,\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            showDownload: true\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, order.id, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 11\n                }, this) : /* Settlements Archive */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"أرشيف كشوفات التسوية (\",\n                                        filteredSettlements.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"جميع كشوفات التسوية المنشأة\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-muted-foreground\",\n                                        children: \"جاري التحميل...\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 17\n                            }, this) : filteredSettlements.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"لا توجد كشوفات تسوية\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: filteredSettlements.map((settlement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 border border-border rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: settlement.courierName\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${settlement.isSettled ? 'text-green-600 bg-green-100' : 'text-orange-600 bg-orange-100'}`,\n                                                                    children: settlement.isSettled ? 'مسدد' : 'غير مسدد'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: [\n                                                                \"عدد الطلبات: \",\n                                                                settlement.orders.length\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                \"تاريخ الإنشاء: \",\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(settlement.createdAt)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        settlement.settledAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                \"تاريخ التسديد: \",\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(settlement.settledAt)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(settlement.netAmount)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                \"إجمالي: \",\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(settlement.totalAmount)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                \"عمولة: \",\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(settlement.commission)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 mr-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>downloadSettlement(settlement),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_ArrowLeft_Download_Eye_FileText_Package_Printer_Search_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, settlement.id, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n                    lineNumber: 420,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\archive\\\\page.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/archive/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth-provider.tsx":
/*!******************************************!*\
  !*** ./src/components/auth-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check if user is logged in on app start\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": async ()=>{\n                    try {\n                        // Demo mode auto-login - تسجيل دخول تلقائي في الوضع التجريبي\n                        if (_lib_config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.demo.enabled && _lib_config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.demo.autoLogin) {\n                            try {\n                                console.log('Starting demo auto-login...');\n                                // استخدام timeout لتجنب التعليق\n                                const loginPromise = _lib_auth__WEBPACK_IMPORTED_MODULE_3__.authService.login({\n                                    username: _lib_config__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.demo.defaultUser,\n                                    password: '123456'\n                                });\n                                const timeoutPromise = new Promise({\n                                    \"AuthProvider.useEffect.checkAuth\": (_, reject)=>setTimeout({\n                                            \"AuthProvider.useEffect.checkAuth\": ()=>reject(new Error('Login timeout'))\n                                        }[\"AuthProvider.useEffect.checkAuth\"], 3000)\n                                }[\"AuthProvider.useEffect.checkAuth\"]);\n                                const demoUser = await Promise.race([\n                                    loginPromise,\n                                    timeoutPromise\n                                ]);\n                                console.log('Demo user logged in:', demoUser);\n                                setUser(demoUser);\n                                setIsAuthenticated(true);\n                                if (false) {}\n                                setLoading(false);\n                                return;\n                            } catch (error) {\n                                console.error('Demo auto-login failed:', error);\n                                // في حالة فشل التسجيل التلقائي، استخدم النظام الاحتياطي\n                                console.log('Using fallback authentication...');\n                                // إنشاء مستخدم تجريبي مباشرة\n                                const fallbackUser = {\n                                    id: 'manager_fallback',\n                                    username: 'manager',\n                                    name: 'مدير النظام',\n                                    phone: '07901234567',\n                                    role: 'manager',\n                                    permissions: [],\n                                    locationId: 'main_center',\n                                    location: {\n                                        id: 'main_center',\n                                        name: 'المركز العام',\n                                        type: 'company'\n                                    },\n                                    createdBy: 'system',\n                                    createdAt: new Date(),\n                                    isActive: true,\n                                    accessToken: 'fallback_token',\n                                    refreshToken: 'fallback_refresh'\n                                };\n                                setUser(fallbackUser);\n                                setIsAuthenticated(true);\n                                if (false) {}\n                                setLoading(false);\n                                return;\n                            }\n                        }\n                        if (false) {}\n                    } catch (error) {\n                        console.error('Error checking authentication:', error);\n                        // Clear invalid data\n                        if (false) {}\n                    } finally{\n                        // Always set loading to false after a short delay to ensure UI updates\n                        setTimeout({\n                            \"AuthProvider.useEffect.checkAuth\": ()=>{\n                                setLoading(false);\n                            }\n                        }[\"AuthProvider.useEffect.checkAuth\"], 300);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (username, password)=>{\n        try {\n            const userData = await _lib_auth__WEBPACK_IMPORTED_MODULE_3__.authService.login({\n                username,\n                password\n            });\n            // Update state\n            setUser(userData);\n            setIsAuthenticated(true);\n            // Store in localStorage\n            if (false) {}\n            return true;\n        } catch (error) {\n            console.error('Login error:', error);\n            return false;\n        }\n    };\n    const logout = ()=>{\n        try {\n            // Use authService logout\n            _lib_auth__WEBPACK_IMPORTED_MODULE_3__.authService.logout();\n            // Update state\n            setUser(null);\n            setIsAuthenticated(false);\n            // Clear localStorage\n            if (false) {}\n            // Redirect to login\n            router.push('/login');\n        } catch (error) {\n            console.error('Logout error:', error);\n            // Force redirect even if there's an error\n            router.push('/login');\n        }\n    };\n    const value = {\n        user,\n        isAuthenticated,\n        login,\n        logout,\n        loading\n    };\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-700 mb-2\",\n                        children: \"مرسال\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"جاري التحقق من بيانات الدخول...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n            lineNumber: 206,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/print-receipt.tsx":
/*!******************************************!*\
  !*** ./src/components/print-receipt.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PrintReceipt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Printer!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Printer!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _receipt_template__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./receipt-template */ \"(ssr)/./src/components/receipt-template.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction PrintReceipt({ order, variant = \"default\", size = \"default\", showDownload = false }) {\n    const receiptRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handlePrint = ()=>{\n        if (receiptRef.current) {\n            const printWindow = window.open('', '_blank');\n            if (printWindow) {\n                printWindow.document.write(`\n          <!DOCTYPE html>\n          <html>\n            <head>\n              <title>وصل رقم ${order.trackingNumber}</title>\n              <style>\n                @page {\n                  size: 110mm 130mm;\n                  margin: 0;\n                }\n                body {\n                  margin: 0;\n                  padding: 0;\n                  font-family: 'Arial', 'Tahoma', sans-serif;\n                  direction: rtl;\n                  -webkit-print-color-adjust: exact;\n                  print-color-adjust: exact;\n                }\n                .receipt-template {\n                  width: 110mm !important;\n                  height: 130mm !important;\n                  margin: 0 !important;\n                  padding: 5mm !important;\n                  box-sizing: border-box !important;\n                  font-size: 11px !important;\n                  line-height: 1.3 !important;\n                  color: #000 !important;\n                  background: white !important;\n                  border: 2px solid #000 !important;\n                  direction: rtl !important;\n                }\n                .receipt-template * {\n                  -webkit-print-color-adjust: exact !important;\n                  print-color-adjust: exact !important;\n                }\n                @media print {\n                  body {\n                    margin: 0;\n                    -webkit-print-color-adjust: exact;\n                    print-color-adjust: exact;\n                  }\n                  .receipt-template {\n                    width: 110mm !important;\n                    height: 130mm !important;\n                    margin: 0 !important;\n                    page-break-after: avoid;\n                    -webkit-print-color-adjust: exact !important;\n                    print-color-adjust: exact !important;\n                  }\n                }\n              </style>\n            </head>\n            <body>\n              ${receiptRef.current.outerHTML}\n            </body>\n          </html>\n        `);\n                printWindow.document.close();\n                printWindow.focus();\n                printWindow.print();\n                printWindow.close();\n            }\n        }\n    };\n    const handleDownload = ()=>{\n        // For now, just open print dialog as fallback\n        // TODO: Implement proper image download functionality\n        handlePrint();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    left: '-9999px',\n                    top: '-9999px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_receipt_template__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    ref: receiptRef,\n                    order: order\n                }, void 0, false, {\n                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\print-receipt.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\print-receipt.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: handlePrint,\n                        variant: variant,\n                        size: size,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\print-receipt.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            \"طباعة الوصل\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\print-receipt.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    showDownload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: handleDownload,\n                        variant: \"outline\",\n                        size: size,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\print-receipt.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            \"تحميل\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\print-receipt.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\print-receipt.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\print-receipt.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/print-receipt.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/receipt-template.tsx":
/*!*********************************************!*\
  !*** ./src/components/receipt-template.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ReceiptTemplate = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ order }, ref)=>{\n    // Generate barcode data (simple implementation)\n    const generateBarcode = (trackingNumber)=>{\n        // This is a simple barcode representation based on tracking number\n        // In a real app, you'd use a proper barcode library\n        const barcodePattern = trackingNumber.split('').map(()=>'|||').join(' ');\n        return `||||| ${barcodePattern} |||||`;\n    };\n    const getStatusText = (status)=>{\n        const statusMap = {\n            pending: \"في الانتظار\",\n            assigned: \"مسند للمندوب\",\n            \"out-for-delivery\": \"خارج للتوصيل\",\n            delivered: \"تم التسليم\",\n            returned: \"راجع للمرسل\",\n            cancelled: \"ملغي\",\n            postponed: \"مؤجل\"\n        };\n        return statusMap[status] || status;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: \"receipt-template\",\n        style: {\n            width: \"110mm\",\n            height: \"130mm\",\n            padding: \"5mm\",\n            fontFamily: \"'Arial', 'Tahoma', sans-serif\",\n            fontSize: \"11px\",\n            lineHeight: \"1.3\",\n            color: \"#000\",\n            backgroundColor: \"#fff\",\n            border: \"2px solid #000\",\n            boxSizing: \"border-box\",\n            direction: \"rtl\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"10px\",\n                    borderBottom: \"2px solid #000\",\n                    paddingBottom: \"6px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: \"13px\",\n                            fontWeight: \"bold\",\n                            margin: \"0 0 2px 0\",\n                            color: \"#000\",\n                            letterSpacing: \"0.3px\"\n                        },\n                        children: \"مكتب علي الشيباني للتوصيل السريع فرع الحي\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            margin: \"0\",\n                            fontSize: \"8px\",\n                            color: \"#666\"\n                        },\n                        children: \"خدمة توصيل سريعة وموثوقة\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"10px\",\n                    border: \"2px solid #000\",\n                    padding: \"6px\",\n                    backgroundColor: \"#f0f8ff\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"9px\",\n                            color: \"#666\",\n                            marginBottom: \"2px\"\n                        },\n                        children: \"رقم الوصل\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"18px\",\n                            fontWeight: \"bold\",\n                            color: \"#000\",\n                            letterSpacing: \"1px\"\n                        },\n                        children: order.trackingNumber\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"6px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"رقم هاتف الزبون:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"12px\",\n                            color: \"#0066cc\",\n                            direction: \"ltr\"\n                        },\n                        children: order.recipientPhone\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"6px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"الحالة:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"11px\",\n                            color: order.status === \"delivered\" ? \"#28a745\" : \"#ffc107\",\n                            padding: \"2px 6px\",\n                            borderRadius: \"3px\",\n                            backgroundColor: order.status === \"delivered\" ? \"#d4edda\" : \"#fff3cd\"\n                        },\n                        children: getStatusText(order.status)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"8px\",\n                    padding: \"4px 6px\",\n                    border: \"1px solid #ccc\",\n                    backgroundColor: \"#f9f9f9\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"10px\"\n                        },\n                        children: \"اسم المندوب:\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            fontSize: \"11px\",\n                            color: \"#333\"\n                        },\n                        children: order.assignedTo || \"غير محدد\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"10px\",\n                    border: \"2px solid #000\",\n                    padding: \"8px\",\n                    backgroundColor: \"#fff9e6\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"9px\",\n                            color: \"#666\",\n                            marginBottom: \"2px\"\n                        },\n                        children: \"المبلغ المطلوب\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"16px\",\n                            fontWeight: \"bold\",\n                            color: \"#000\",\n                            letterSpacing: \"1px\",\n                            direction: \"ltr\"\n                        },\n                        children: [\n                            order.amount.toLocaleString('en-US'),\n                            \" IQD\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 160,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"8px\",\n                    border: \"1px solid #000\",\n                    padding: \"6px\",\n                    backgroundColor: \"#fff\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"8px\",\n                            color: \"#666\",\n                            marginBottom: \"3px\"\n                        },\n                        children: \"الباركود\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontFamily: \"monospace\",\n                            fontSize: \"10px\",\n                            letterSpacing: \"1px\",\n                            color: \"#000\",\n                            backgroundColor: \"#fff\",\n                            padding: \"3px\"\n                        },\n                        children: generateBarcode(order.trackingNumber)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"8px\",\n                            color: \"#666\",\n                            marginTop: \"2px\"\n                        },\n                        children: order.trackingNumber\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 180,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    fontSize: \"8px\",\n                    color: \"#666\",\n                    borderTop: \"1px solid #ccc\",\n                    paddingTop: \"4px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"شكراً لاختياركم خدماتنا\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"2px\"\n                        },\n                        children: [\n                            \"التاريخ: \",\n                            new Date().toLocaleDateString('ar-IQ')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\receipt-template.tsx\",\n        lineNumber: 34,\n        columnNumber: 7\n    }, undefined);\n});\nReceiptTemplate.displayName = \"ReceiptTemplate\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReceiptTemplate);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9yZWNlaXB0LXRlbXBsYXRlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFbUM7QUFPbkMsTUFBTUMsZ0NBQWtCRCxpREFBVUEsQ0FDaEMsQ0FBQyxFQUFFRSxLQUFLLEVBQUUsRUFBRUM7SUFDVixnREFBZ0Q7SUFDaEQsTUFBTUMsa0JBQWtCLENBQUNDO1FBQ3ZCLG1FQUFtRTtRQUNuRSxvREFBb0Q7UUFDcEQsTUFBTUMsaUJBQWlCRCxlQUFlRSxLQUFLLENBQUMsSUFBSUMsR0FBRyxDQUFDLElBQU0sT0FBT0MsSUFBSSxDQUFDO1FBQ3RFLE9BQU8sQ0FBQyxNQUFNLEVBQUVILGVBQWUsTUFBTSxDQUFDO0lBQ3hDO0lBRUEsTUFBTUksZ0JBQWdCLENBQUNDO1FBQ3JCLE1BQU1DLFlBQXVDO1lBQzNDQyxTQUFTO1lBQ1RDLFVBQVU7WUFDVixvQkFBb0I7WUFDcEJDLFdBQVc7WUFDWEMsVUFBVTtZQUNWQyxXQUFXO1lBQ1hDLFdBQVc7UUFDYjtRQUNBLE9BQU9OLFNBQVMsQ0FBQ0QsT0FBTyxJQUFJQTtJQUM5QjtJQUVBLHFCQUNFLDhEQUFDUTtRQUNDaEIsS0FBS0E7UUFDTGlCLFdBQVU7UUFDVkMsT0FBTztZQUNMQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxZQUFZO1lBQ1pDLFVBQVU7WUFDVkMsWUFBWTtZQUNaQyxPQUFPO1lBQ1BDLGlCQUFpQjtZQUNqQkMsUUFBUTtZQUNSQyxXQUFXO1lBQ1hDLFdBQVc7UUFDYjs7MEJBR0EsOERBQUNiO2dCQUFJRSxPQUFPO29CQUNWWSxXQUFXO29CQUNYQyxjQUFjO29CQUNkQyxjQUFjO29CQUNkQyxlQUFlO2dCQUNqQjs7a0NBQ0UsOERBQUNDO3dCQUFHaEIsT0FBTzs0QkFDVEssVUFBVTs0QkFDVlksWUFBWTs0QkFDWkMsUUFBUTs0QkFDUlgsT0FBTzs0QkFDUFksZUFBZTt3QkFDakI7a0NBQUc7Ozs7OztrQ0FHSCw4REFBQ0M7d0JBQUVwQixPQUFPOzRCQUNSa0IsUUFBUTs0QkFDUmIsVUFBVTs0QkFDVkUsT0FBTzt3QkFDVDtrQ0FBRzs7Ozs7Ozs7Ozs7OzBCQU1MLDhEQUFDVDtnQkFBSUUsT0FBTztvQkFDVlksV0FBVztvQkFDWEMsY0FBYztvQkFDZEosUUFBUTtvQkFDUk4sU0FBUztvQkFDVEssaUJBQWlCO2dCQUNuQjs7a0NBQ0UsOERBQUNWO3dCQUFJRSxPQUFPOzRCQUFFSyxVQUFVOzRCQUFPRSxPQUFPOzRCQUFRTSxjQUFjO3dCQUFNO2tDQUFHOzs7Ozs7a0NBQ3JFLDhEQUFDZjt3QkFBSUUsT0FBTzs0QkFDVkssVUFBVTs0QkFDVlksWUFBWTs0QkFDWlYsT0FBTzs0QkFDUFksZUFBZTt3QkFDakI7a0NBQ0d0QyxNQUFNRyxjQUFjOzs7Ozs7Ozs7Ozs7MEJBS3pCLDhEQUFDYztnQkFBSUUsT0FBTztvQkFDVnFCLFNBQVM7b0JBQ1RDLGdCQUFnQjtvQkFDaEJDLFlBQVk7b0JBQ1pWLGNBQWM7b0JBQ2RWLFNBQVM7b0JBQ1RNLFFBQVE7b0JBQ1JELGlCQUFpQjtnQkFDbkI7O2tDQUNFLDhEQUFDZ0I7d0JBQUt4QixPQUFPOzRCQUFFaUIsWUFBWTs0QkFBUVosVUFBVTt3QkFBTztrQ0FBRzs7Ozs7O2tDQUN2RCw4REFBQ21CO3dCQUFLeEIsT0FBTzs0QkFDWGlCLFlBQVk7NEJBQ1paLFVBQVU7NEJBQ1ZFLE9BQU87NEJBQ1BJLFdBQVc7d0JBQ2I7a0NBQ0c5QixNQUFNNEMsY0FBYzs7Ozs7Ozs7Ozs7OzBCQUt6Qiw4REFBQzNCO2dCQUFJRSxPQUFPO29CQUNWcUIsU0FBUztvQkFDVEMsZ0JBQWdCO29CQUNoQkMsWUFBWTtvQkFDWlYsY0FBYztvQkFDZFYsU0FBUztvQkFDVE0sUUFBUTtvQkFDUkQsaUJBQWlCO2dCQUNuQjs7a0NBQ0UsOERBQUNnQjt3QkFBS3hCLE9BQU87NEJBQUVpQixZQUFZOzRCQUFRWixVQUFVO3dCQUFPO2tDQUFHOzs7Ozs7a0NBQ3ZELDhEQUFDbUI7d0JBQUt4QixPQUFPOzRCQUNYaUIsWUFBWTs0QkFDWlosVUFBVTs0QkFDVkUsT0FBTzFCLE1BQU1TLE1BQU0sS0FBSyxjQUFjLFlBQVk7NEJBQ2xEYSxTQUFTOzRCQUNUdUIsY0FBYzs0QkFDZGxCLGlCQUFpQjNCLE1BQU1TLE1BQU0sS0FBSyxjQUFjLFlBQVk7d0JBQzlEO2tDQUNHRCxjQUFjUixNQUFNUyxNQUFNOzs7Ozs7Ozs7Ozs7MEJBSy9CLDhEQUFDUTtnQkFBSUUsT0FBTztvQkFDVnFCLFNBQVM7b0JBQ1RDLGdCQUFnQjtvQkFDaEJDLFlBQVk7b0JBQ1pWLGNBQWM7b0JBQ2RWLFNBQVM7b0JBQ1RNLFFBQVE7b0JBQ1JELGlCQUFpQjtnQkFDbkI7O2tDQUNFLDhEQUFDZ0I7d0JBQUt4QixPQUFPOzRCQUFFaUIsWUFBWTs0QkFBUVosVUFBVTt3QkFBTztrQ0FBRzs7Ozs7O2tDQUN2RCw4REFBQ21CO3dCQUFLeEIsT0FBTzs0QkFDWGlCLFlBQVk7NEJBQ1paLFVBQVU7NEJBQ1ZFLE9BQU87d0JBQ1Q7a0NBQ0cxQixNQUFNOEMsVUFBVSxJQUFJOzs7Ozs7Ozs7Ozs7MEJBS3pCLDhEQUFDN0I7Z0JBQUlFLE9BQU87b0JBQ1ZZLFdBQVc7b0JBQ1hDLGNBQWM7b0JBQ2RKLFFBQVE7b0JBQ1JOLFNBQVM7b0JBQ1RLLGlCQUFpQjtnQkFDbkI7O2tDQUNFLDhEQUFDVjt3QkFBSUUsT0FBTzs0QkFBRUssVUFBVTs0QkFBT0UsT0FBTzs0QkFBUU0sY0FBYzt3QkFBTTtrQ0FBRzs7Ozs7O2tDQUNyRSw4REFBQ2Y7d0JBQUlFLE9BQU87NEJBQ1ZLLFVBQVU7NEJBQ1ZZLFlBQVk7NEJBQ1pWLE9BQU87NEJBQ1BZLGVBQWU7NEJBQ2ZSLFdBQVc7d0JBQ2I7OzRCQUNHOUIsTUFBTStDLE1BQU0sQ0FBQ0MsY0FBYyxDQUFDOzRCQUFTOzs7Ozs7Ozs7Ozs7OzBCQUsxQyw4REFBQy9CO2dCQUFJRSxPQUFPO29CQUNWWSxXQUFXO29CQUNYQyxjQUFjO29CQUNkSixRQUFRO29CQUNSTixTQUFTO29CQUNUSyxpQkFBaUI7Z0JBQ25COztrQ0FDRSw4REFBQ1Y7d0JBQUlFLE9BQU87NEJBQUVLLFVBQVU7NEJBQU9FLE9BQU87NEJBQVFNLGNBQWM7d0JBQU07a0NBQUc7Ozs7OztrQ0FDckUsOERBQUNmO3dCQUFJRSxPQUFPOzRCQUNWSSxZQUFZOzRCQUNaQyxVQUFVOzRCQUNWYyxlQUFlOzRCQUNmWixPQUFPOzRCQUNQQyxpQkFBaUI7NEJBQ2pCTCxTQUFTO3dCQUNYO2tDQUNHcEIsZ0JBQWdCRixNQUFNRyxjQUFjOzs7Ozs7a0NBRXZDLDhEQUFDYzt3QkFBSUUsT0FBTzs0QkFBRUssVUFBVTs0QkFBT0UsT0FBTzs0QkFBUXVCLFdBQVc7d0JBQU07a0NBQzVEakQsTUFBTUcsY0FBYzs7Ozs7Ozs7Ozs7OzBCQUt6Qiw4REFBQ2M7Z0JBQUlFLE9BQU87b0JBQ1ZZLFdBQVc7b0JBQ1hQLFVBQVU7b0JBQ1ZFLE9BQU87b0JBQ1B3QixXQUFXO29CQUNYQyxZQUFZO2dCQUNkOztrQ0FDRSw4REFBQ2xDO2tDQUFJOzs7Ozs7a0NBQ0wsOERBQUNBO3dCQUFJRSxPQUFPOzRCQUFFOEIsV0FBVzt3QkFBTTs7NEJBQUc7NEJBQ3RCLElBQUlHLE9BQU9DLGtCQUFrQixDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS2xEO0FBR0Z0RCxnQkFBZ0J1RCxXQUFXLEdBQUc7QUFFOUIsaUVBQWV2RCxlQUFlQSxFQUFDIiwic291cmNlcyI6WyJFOlxcTWFyc2FsXFxtYXJzYWxcXHNyY1xcY29tcG9uZW50c1xccmVjZWlwdC10ZW1wbGF0ZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IGZvcndhcmRSZWYgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IE9yZGVyIH0gZnJvbSBcIkAvdHlwZXMvb3JkZXJcIjtcblxuaW50ZXJmYWNlIFJlY2VpcHRUZW1wbGF0ZVByb3BzIHtcbiAgb3JkZXI6IE9yZGVyO1xufVxuXG5jb25zdCBSZWNlaXB0VGVtcGxhdGUgPSBmb3J3YXJkUmVmPEhUTUxEaXZFbGVtZW50LCBSZWNlaXB0VGVtcGxhdGVQcm9wcz4oXG4gICh7IG9yZGVyIH0sIHJlZikgPT4ge1xuICAgIC8vIEdlbmVyYXRlIGJhcmNvZGUgZGF0YSAoc2ltcGxlIGltcGxlbWVudGF0aW9uKVxuICAgIGNvbnN0IGdlbmVyYXRlQmFyY29kZSA9ICh0cmFja2luZ051bWJlcjogc3RyaW5nKSA9PiB7XG4gICAgICAvLyBUaGlzIGlzIGEgc2ltcGxlIGJhcmNvZGUgcmVwcmVzZW50YXRpb24gYmFzZWQgb24gdHJhY2tpbmcgbnVtYmVyXG4gICAgICAvLyBJbiBhIHJlYWwgYXBwLCB5b3UnZCB1c2UgYSBwcm9wZXIgYmFyY29kZSBsaWJyYXJ5XG4gICAgICBjb25zdCBiYXJjb2RlUGF0dGVybiA9IHRyYWNraW5nTnVtYmVyLnNwbGl0KCcnKS5tYXAoKCkgPT4gJ3x8fCcpLmpvaW4oJyAnKTtcbiAgICAgIHJldHVybiBgfHx8fHwgJHtiYXJjb2RlUGF0dGVybn0gfHx8fHxgO1xuICAgIH07XG5cbiAgICBjb25zdCBnZXRTdGF0dXNUZXh0ID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgICBjb25zdCBzdGF0dXNNYXA6IHsgW2tleTogc3RyaW5nXTogc3RyaW5nIH0gPSB7XG4gICAgICAgIHBlbmRpbmc6IFwi2YHZiiDYp9mE2KfZhtiq2LjYp9ixXCIsXG4gICAgICAgIGFzc2lnbmVkOiBcItmF2LPZhtivINmE2YTZhdmG2K/ZiNioXCIsXG4gICAgICAgIFwib3V0LWZvci1kZWxpdmVyeVwiOiBcItiu2KfYsdisINmE2YTYqtmI2LXZitmEXCIsXG4gICAgICAgIGRlbGl2ZXJlZDogXCLYqtmFINin2YTYqtiz2YTZitmFXCIsXG4gICAgICAgIHJldHVybmVkOiBcItix2KfYrNi5INmE2YTZhdix2LPZhFwiLFxuICAgICAgICBjYW5jZWxsZWQ6IFwi2YXZhNi62YpcIixcbiAgICAgICAgcG9zdHBvbmVkOiBcItmF2KTYrNmEXCJcbiAgICAgIH07XG4gICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgc3RhdHVzO1xuICAgIH07XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdlxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgY2xhc3NOYW1lPVwicmVjZWlwdC10ZW1wbGF0ZVwiXG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgd2lkdGg6IFwiMTEwbW1cIixcbiAgICAgICAgICBoZWlnaHQ6IFwiMTMwbW1cIixcbiAgICAgICAgICBwYWRkaW5nOiBcIjVtbVwiLFxuICAgICAgICAgIGZvbnRGYW1pbHk6IFwiJ0FyaWFsJywgJ1RhaG9tYScsIHNhbnMtc2VyaWZcIixcbiAgICAgICAgICBmb250U2l6ZTogXCIxMXB4XCIsXG4gICAgICAgICAgbGluZUhlaWdodDogXCIxLjNcIixcbiAgICAgICAgICBjb2xvcjogXCIjMDAwXCIsXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcIiNmZmZcIixcbiAgICAgICAgICBib3JkZXI6IFwiMnB4IHNvbGlkICMwMDBcIixcbiAgICAgICAgICBib3hTaXppbmc6IFwiYm9yZGVyLWJveFwiLFxuICAgICAgICAgIGRpcmVjdGlvbjogXCJydGxcIlxuICAgICAgICB9fVxuICAgICAgPlxuICAgICAgICB7LyogQ29tcGFueSBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICB0ZXh0QWxpZ246IFwiY2VudGVyXCIsXG4gICAgICAgICAgbWFyZ2luQm90dG9tOiBcIjEwcHhcIixcbiAgICAgICAgICBib3JkZXJCb3R0b206IFwiMnB4IHNvbGlkICMwMDBcIixcbiAgICAgICAgICBwYWRkaW5nQm90dG9tOiBcIjZweFwiXG4gICAgICAgIH19PlxuICAgICAgICAgIDxoMSBzdHlsZT17e1xuICAgICAgICAgICAgZm9udFNpemU6IFwiMTNweFwiLFxuICAgICAgICAgICAgZm9udFdlaWdodDogXCJib2xkXCIsXG4gICAgICAgICAgICBtYXJnaW46IFwiMCAwIDJweCAwXCIsXG4gICAgICAgICAgICBjb2xvcjogXCIjMDAwXCIsXG4gICAgICAgICAgICBsZXR0ZXJTcGFjaW5nOiBcIjAuM3B4XCJcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgINmF2YPYqtioINi52YTZiiDYp9mE2LTZitio2KfZhtmKINmE2YTYqtmI2LXZitmEINin2YTYs9ix2YrYuSDZgdix2Lkg2KfZhNit2YpcbiAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDxwIHN0eWxlPXt7XG4gICAgICAgICAgICBtYXJnaW46IFwiMFwiLFxuICAgICAgICAgICAgZm9udFNpemU6IFwiOHB4XCIsXG4gICAgICAgICAgICBjb2xvcjogXCIjNjY2XCJcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgINiu2K/ZhdipINiq2YjYtdmK2YQg2LPYsdmK2LnYqSDZiNmF2YjYq9mI2YLYqVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFJlY2VpcHQgTnVtYmVyIFNlY3Rpb24gKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICB0ZXh0QWxpZ246IFwiY2VudGVyXCIsXG4gICAgICAgICAgbWFyZ2luQm90dG9tOiBcIjEwcHhcIixcbiAgICAgICAgICBib3JkZXI6IFwiMnB4IHNvbGlkICMwMDBcIixcbiAgICAgICAgICBwYWRkaW5nOiBcIjZweFwiLFxuICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCIjZjBmOGZmXCJcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogXCI5cHhcIiwgY29sb3I6IFwiIzY2NlwiLCBtYXJnaW5Cb3R0b206IFwiMnB4XCIgfX0+2LHZgtmFINin2YTZiNi12YQ8L2Rpdj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBmb250U2l6ZTogXCIxOHB4XCIsXG4gICAgICAgICAgICBmb250V2VpZ2h0OiBcImJvbGRcIixcbiAgICAgICAgICAgIGNvbG9yOiBcIiMwMDBcIixcbiAgICAgICAgICAgIGxldHRlclNwYWNpbmc6IFwiMXB4XCJcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIHtvcmRlci50cmFja2luZ051bWJlcn1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEN1c3RvbWVyIFBob25lICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXG4gICAgICAgICAganVzdGlmeUNvbnRlbnQ6IFwic3BhY2UtYmV0d2VlblwiLFxuICAgICAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXG4gICAgICAgICAgbWFyZ2luQm90dG9tOiBcIjZweFwiLFxuICAgICAgICAgIHBhZGRpbmc6IFwiNHB4IDZweFwiLFxuICAgICAgICAgIGJvcmRlcjogXCIxcHggc29saWQgI2NjY1wiLFxuICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCIjZjlmOWY5XCJcbiAgICAgICAgfX0+XG4gICAgICAgICAgPHNwYW4gc3R5bGU9e3sgZm9udFdlaWdodDogXCJib2xkXCIsIGZvbnRTaXplOiBcIjEwcHhcIiB9fT7YsdmC2YUg2YfYp9iq2YEg2KfZhNiy2KjZiNmGOjwvc3Bhbj5cbiAgICAgICAgICA8c3BhbiBzdHlsZT17e1xuICAgICAgICAgICAgZm9udFdlaWdodDogXCJib2xkXCIsXG4gICAgICAgICAgICBmb250U2l6ZTogXCIxMnB4XCIsXG4gICAgICAgICAgICBjb2xvcjogXCIjMDA2NmNjXCIsXG4gICAgICAgICAgICBkaXJlY3Rpb246IFwibHRyXCJcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIHtvcmRlci5yZWNpcGllbnRQaG9uZX1cbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTdGF0dXMgKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogXCJzcGFjZS1iZXR3ZWVuXCIsXG4gICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcbiAgICAgICAgICBtYXJnaW5Cb3R0b206IFwiNnB4XCIsXG4gICAgICAgICAgcGFkZGluZzogXCI0cHggNnB4XCIsXG4gICAgICAgICAgYm9yZGVyOiBcIjFweCBzb2xpZCAjY2NjXCIsXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcIiNmOWY5ZjlcIlxuICAgICAgICB9fT5cbiAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250V2VpZ2h0OiBcImJvbGRcIiwgZm9udFNpemU6IFwiMTBweFwiIH19Ptin2YTYrdin2YTYqTo8L3NwYW4+XG4gICAgICAgICAgPHNwYW4gc3R5bGU9e3tcbiAgICAgICAgICAgIGZvbnRXZWlnaHQ6IFwiYm9sZFwiLFxuICAgICAgICAgICAgZm9udFNpemU6IFwiMTFweFwiLFxuICAgICAgICAgICAgY29sb3I6IG9yZGVyLnN0YXR1cyA9PT0gXCJkZWxpdmVyZWRcIiA/IFwiIzI4YTc0NVwiIDogXCIjZmZjMTA3XCIsXG4gICAgICAgICAgICBwYWRkaW5nOiBcIjJweCA2cHhcIixcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogXCIzcHhcIixcbiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogb3JkZXIuc3RhdHVzID09PSBcImRlbGl2ZXJlZFwiID8gXCIjZDRlZGRhXCIgOiBcIiNmZmYzY2RcIlxuICAgICAgICAgIH19PlxuICAgICAgICAgICAge2dldFN0YXR1c1RleHQob3JkZXIuc3RhdHVzKX1cbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDb3VyaWVyIE5hbWUgKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogXCJzcGFjZS1iZXR3ZWVuXCIsXG4gICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcbiAgICAgICAgICBtYXJnaW5Cb3R0b206IFwiOHB4XCIsXG4gICAgICAgICAgcGFkZGluZzogXCI0cHggNnB4XCIsXG4gICAgICAgICAgYm9yZGVyOiBcIjFweCBzb2xpZCAjY2NjXCIsXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcIiNmOWY5ZjlcIlxuICAgICAgICB9fT5cbiAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250V2VpZ2h0OiBcImJvbGRcIiwgZm9udFNpemU6IFwiMTBweFwiIH19Ptin2LPZhSDYp9mE2YXZhtiv2YjYqDo8L3NwYW4+XG4gICAgICAgICAgPHNwYW4gc3R5bGU9e3tcbiAgICAgICAgICAgIGZvbnRXZWlnaHQ6IFwiYm9sZFwiLFxuICAgICAgICAgICAgZm9udFNpemU6IFwiMTFweFwiLFxuICAgICAgICAgICAgY29sb3I6IFwiIzMzM1wiXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICB7b3JkZXIuYXNzaWduZWRUbyB8fCBcIti62YrYsSDZhdit2K/Yr1wifVxuICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFByaWNlIFNlY3Rpb24gKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICB0ZXh0QWxpZ246IFwiY2VudGVyXCIsXG4gICAgICAgICAgbWFyZ2luQm90dG9tOiBcIjEwcHhcIixcbiAgICAgICAgICBib3JkZXI6IFwiMnB4IHNvbGlkICMwMDBcIixcbiAgICAgICAgICBwYWRkaW5nOiBcIjhweFwiLFxuICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCIjZmZmOWU2XCJcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogXCI5cHhcIiwgY29sb3I6IFwiIzY2NlwiLCBtYXJnaW5Cb3R0b206IFwiMnB4XCIgfX0+2KfZhNmF2KjZhNi6INin2YTZhdi32YTZiNioPC9kaXY+XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgZm9udFNpemU6IFwiMTZweFwiLFxuICAgICAgICAgICAgZm9udFdlaWdodDogXCJib2xkXCIsXG4gICAgICAgICAgICBjb2xvcjogXCIjMDAwXCIsXG4gICAgICAgICAgICBsZXR0ZXJTcGFjaW5nOiBcIjFweFwiLFxuICAgICAgICAgICAgZGlyZWN0aW9uOiBcImx0clwiXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICB7b3JkZXIuYW1vdW50LnRvTG9jYWxlU3RyaW5nKCdlbi1VUycpfSBJUURcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEJhcmNvZGUgU2VjdGlvbiAqL31cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIHRleHRBbGlnbjogXCJjZW50ZXJcIixcbiAgICAgICAgICBtYXJnaW5Cb3R0b206IFwiOHB4XCIsXG4gICAgICAgICAgYm9yZGVyOiBcIjFweCBzb2xpZCAjMDAwXCIsXG4gICAgICAgICAgcGFkZGluZzogXCI2cHhcIixcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwiI2ZmZlwiXG4gICAgICAgIH19PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZm9udFNpemU6IFwiOHB4XCIsIGNvbG9yOiBcIiM2NjZcIiwgbWFyZ2luQm90dG9tOiBcIjNweFwiIH19Ptin2YTYqNin2LHZg9mI2K88L2Rpdj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBmb250RmFtaWx5OiBcIm1vbm9zcGFjZVwiLFxuICAgICAgICAgICAgZm9udFNpemU6IFwiMTBweFwiLFxuICAgICAgICAgICAgbGV0dGVyU3BhY2luZzogXCIxcHhcIixcbiAgICAgICAgICAgIGNvbG9yOiBcIiMwMDBcIixcbiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCIjZmZmXCIsXG4gICAgICAgICAgICBwYWRkaW5nOiBcIjNweFwiXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICB7Z2VuZXJhdGVCYXJjb2RlKG9yZGVyLnRyYWNraW5nTnVtYmVyKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiBcIjhweFwiLCBjb2xvcjogXCIjNjY2XCIsIG1hcmdpblRvcDogXCIycHhcIiB9fT5cbiAgICAgICAgICAgIHtvcmRlci50cmFja2luZ051bWJlcn1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZvb3RlciAqL31cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIHRleHRBbGlnbjogXCJjZW50ZXJcIixcbiAgICAgICAgICBmb250U2l6ZTogXCI4cHhcIixcbiAgICAgICAgICBjb2xvcjogXCIjNjY2XCIsXG4gICAgICAgICAgYm9yZGVyVG9wOiBcIjFweCBzb2xpZCAjY2NjXCIsXG4gICAgICAgICAgcGFkZGluZ1RvcDogXCI0cHhcIlxuICAgICAgICB9fT5cbiAgICAgICAgICA8ZGl2Pti02YPYsdin2Ysg2YTYp9iu2KrZitin2LHZg9mFINiu2K/Zhdin2KrZhtinPC9kaXY+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Ub3A6IFwiMnB4XCIgfX0+XG4gICAgICAgICAgICDYp9mE2KrYp9ix2YrYrjoge25ldyBEYXRlKCkudG9Mb2NhbGVEYXRlU3RyaW5nKCdhci1JUScpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cbik7XG5cblJlY2VpcHRUZW1wbGF0ZS5kaXNwbGF5TmFtZSA9IFwiUmVjZWlwdFRlbXBsYXRlXCI7XG5cbmV4cG9ydCBkZWZhdWx0IFJlY2VpcHRUZW1wbGF0ZTtcbiJdLCJuYW1lcyI6WyJmb3J3YXJkUmVmIiwiUmVjZWlwdFRlbXBsYXRlIiwib3JkZXIiLCJyZWYiLCJnZW5lcmF0ZUJhcmNvZGUiLCJ0cmFja2luZ051bWJlciIsImJhcmNvZGVQYXR0ZXJuIiwic3BsaXQiLCJtYXAiLCJqb2luIiwiZ2V0U3RhdHVzVGV4dCIsInN0YXR1cyIsInN0YXR1c01hcCIsInBlbmRpbmciLCJhc3NpZ25lZCIsImRlbGl2ZXJlZCIsInJldHVybmVkIiwiY2FuY2VsbGVkIiwicG9zdHBvbmVkIiwiZGl2IiwiY2xhc3NOYW1lIiwic3R5bGUiLCJ3aWR0aCIsImhlaWdodCIsInBhZGRpbmciLCJmb250RmFtaWx5IiwiZm9udFNpemUiLCJsaW5lSGVpZ2h0IiwiY29sb3IiLCJiYWNrZ3JvdW5kQ29sb3IiLCJib3JkZXIiLCJib3hTaXppbmciLCJkaXJlY3Rpb24iLCJ0ZXh0QWxpZ24iLCJtYXJnaW5Cb3R0b20iLCJib3JkZXJCb3R0b20iLCJwYWRkaW5nQm90dG9tIiwiaDEiLCJmb250V2VpZ2h0IiwibWFyZ2luIiwibGV0dGVyU3BhY2luZyIsInAiLCJkaXNwbGF5IiwianVzdGlmeUNvbnRlbnQiLCJhbGlnbkl0ZW1zIiwic3BhbiIsInJlY2lwaWVudFBob25lIiwiYm9yZGVyUmFkaXVzIiwiYXNzaWduZWRUbyIsImFtb3VudCIsInRvTG9jYWxlU3RyaW5nIiwibWFyZ2luVG9wIiwiYm9yZGVyVG9wIiwicGFkZGluZ1RvcCIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/receipt-template.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Input({ className, type, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        \"data-slot\": \"input\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\", \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLFNBQVNFLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxJQUFJLEVBQUUsR0FBR0MsT0FBc0M7SUFDekUscUJBQ0UsOERBQUNDO1FBQ0NGLE1BQU1BO1FBQ05HLGFBQVU7UUFDVkosV0FBV0YsOENBQUVBLENBQ1gsbWNBQ0EsaUZBQ0EsMEdBQ0FFO1FBRUQsR0FBR0UsS0FBSzs7Ozs7O0FBR2Y7QUFFZ0IiLCJzb3VyY2VzIjpbIkU6XFxNYXJzYWxcXG1hcnNhbFxcc3JjXFxjb21wb25lbnRzXFx1aVxcaW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZnVuY3Rpb24gSW5wdXQoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH06IFJlYWN0LkNvbXBvbmVudFByb3BzPFwiaW5wdXRcIj4pIHtcbiAgcmV0dXJuIChcbiAgICA8aW5wdXRcbiAgICAgIHR5cGU9e3R5cGV9XG4gICAgICBkYXRhLXNsb3Q9XCJpbnB1dFwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBzZWxlY3Rpb246YmctcHJpbWFyeSBzZWxlY3Rpb246dGV4dC1wcmltYXJ5LWZvcmVncm91bmQgZGFyazpiZy1pbnB1dC8zMCBib3JkZXItaW5wdXQgZmxleCBoLTkgdy1mdWxsIG1pbi13LTAgcm91bmRlZC1tZCBib3JkZXIgYmctdHJhbnNwYXJlbnQgcHgtMyBweS0xIHRleHQtYmFzZSBzaGFkb3cteHMgdHJhbnNpdGlvbi1bY29sb3IsYm94LXNoYWRvd10gb3V0bGluZS1ub25lIGZpbGU6aW5saW5lLWZsZXggZmlsZTpoLTcgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxuICAgICAgICBcImZvY3VzLXZpc2libGU6Ym9yZGVyLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcvNTAgZm9jdXMtdmlzaWJsZTpyaW5nLVszcHhdXCIsXG4gICAgICAgIFwiYXJpYS1pbnZhbGlkOnJpbmctZGVzdHJ1Y3RpdmUvMjAgZGFyazphcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS80MCBhcmlhLWludmFsaWQ6Ym9yZGVyLWRlc3RydWN0aXZlXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJpbnB1dCIsImRhdGEtc2xvdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types_roles__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/roles */ \"(ssr)/./src/types/roles.ts\");\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n// نظام المصادقة والصلاحيات\n\n\n\nclass AuthService {\n    // تسجيل الدخول\n    async login(credentials) {\n        try {\n            // محاولة البحث عن المستخدم في قاعدة البيانات\n            let user = null;\n            try {\n                user = await _supabase__WEBPACK_IMPORTED_MODULE_2__.userService.getUserByUsername(credentials.username);\n            } catch (dbError) {\n                console.warn('Database not available, using fallback auth:', dbError);\n                // في حالة عدم توفر قاعدة البيانات، استخدم المستخدمين التجريبيين\n                return this.fallbackLogin(credentials);\n            }\n            if (!user) {\n                // إذا لم يوجد المستخدم في قاعدة البيانات، جرب المستخدمين التجريبيين\n                return this.fallbackLogin(credentials);\n            }\n            if (!user.is_active) {\n                throw new Error('الحساب غير مفعل');\n            }\n            // في الوضع التجريبي، نقبل كلمة المرور 123456 لجميع المستخدمين\n            if (credentials.password !== '123456') {\n                throw new Error('كلمة المرور غير صحيحة');\n            }\n            // تحويل بيانات المستخدم من Supabase إلى AuthUser\n            const authUser = {\n                id: user.id,\n                username: user.username,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                permissions: [],\n                locationId: user.location_id || 'main_center',\n                location: user.location || {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: user.created_by,\n                createdAt: new Date(user.created_at),\n                isActive: user.is_active,\n                accessToken: 'supabase_access_token',\n                refreshToken: 'supabase_refresh_token'\n            };\n            this.currentUser = authUser;\n            this.notifyListeners();\n            // حفظ في localStorage\n            if (false) {}\n            return authUser;\n        } catch (error) {\n            console.error('Login error:', error);\n            if (error instanceof Error) {\n                throw error;\n            }\n            throw new Error('فشل في تسجيل الدخول');\n        }\n    }\n    // تسجيل الدخول الاحتياطي (في حالة عدم توفر قاعدة البيانات)\n    async fallbackLogin(credentials) {\n        // بيانات تجريبية للمستخدمين\n        const mockUsers = {\n            'manager': {\n                id: 'manager_1',\n                username: 'manager',\n                name: 'مدير النظام',\n                phone: '07901234567',\n                role: 'manager',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'system',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token',\n                refreshToken: 'fallback_refresh_token'\n            },\n            'supervisor': {\n                id: 'supervisor_1',\n                username: 'supervisor',\n                name: 'متابع النظام',\n                phone: '07901234568',\n                role: 'supervisor',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'manager_1',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token_supervisor',\n                refreshToken: 'fallback_refresh_token_supervisor'\n            },\n            'courier': {\n                id: 'courier_1',\n                username: 'courier',\n                name: 'مندوب التوصيل',\n                phone: '07901234570',\n                role: 'courier',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'manager_1',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token_courier',\n                refreshToken: 'fallback_refresh_token_courier'\n            }\n        };\n        const user = mockUsers[credentials.username];\n        if (!user || credentials.password !== '123456') {\n            throw new Error('بيانات الدخول غير صحيحة');\n        }\n        this.currentUser = user;\n        this.notifyListeners();\n        // حفظ في localStorage\n        if (false) {}\n        return user;\n    }\n    // تسجيل الخروج\n    async logout() {\n        this.currentUser = null;\n        if (false) {}\n        this.notifyListeners();\n    }\n    // الحصول على المستخدم الحالي\n    getCurrentUser() {\n        if (!this.currentUser && \"undefined\" !== 'undefined') {}\n        return this.currentUser;\n    }\n    // التحقق من الصلاحية\n    hasPermission(permission) {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        return (0,_types_roles__WEBPACK_IMPORTED_MODULE_1__.hasPermission)(user.role, permission);\n    }\n    // الحصول على الأقسام المتاحة\n    getAccessibleSections() {\n        const user = this.getCurrentUser();\n        if (!user) return [];\n        return (0,_types_roles__WEBPACK_IMPORTED_MODULE_1__.getAccessibleSections)(user.role);\n    }\n    // التحقق من إمكانية إنشاء دور معين\n    canCreateRole(targetRole) {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        const rolePermissions = {\n            'manager': [\n                'supervisor',\n                'courier'\n            ],\n            'supervisor': [\n                'courier'\n            ],\n            'courier': []\n        };\n        return rolePermissions[user.role]?.includes(targetRole) || false;\n    }\n    // إضافة مستمع للتغييرات\n    addListener(listener) {\n        this.listeners.push(listener);\n    }\n    // إزالة مستمع\n    removeListener(listener) {\n        this.listeners = this.listeners.filter((l)=>l !== listener);\n    }\n    notifyListeners() {\n        this.listeners.forEach((listener)=>listener(this.currentUser));\n    }\n    // تحديث بيانات المستخدم\n    async updateProfile(data) {\n        if (!this.currentUser) {\n            throw new Error('لم يتم تسجيل الدخول');\n        }\n        // محاكاة API call\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        this.currentUser = {\n            ...this.currentUser,\n            ...data\n        };\n        if (false) {}\n        this.notifyListeners();\n        return this.currentUser;\n    }\n    // تغيير كلمة المرور\n    async changePassword(currentPassword, newPassword) {\n        if (!this.currentUser) {\n            throw new Error('لم يتم تسجيل الدخول');\n        }\n        // محاكاة API call\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // في التطبيق الحقيقي، سيتم التحقق من كلمة المرور الحالية\n        if (currentPassword !== '123456') {\n            throw new Error('كلمة المرور الحالية غير صحيحة');\n        }\n        // تحديث كلمة المرور (في التطبيق الحقيقي)\n        console.log('تم تغيير كلمة المرور بنجاح');\n    }\n    // التحقق من صحة الجلسة\n    async validateSession() {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        try {\n            // محاكاة التحقق من صحة الجلسة\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            return true;\n        } catch (error) {\n            await this.logout();\n            return false;\n        }\n    }\n    constructor(){\n        this.currentUser = null;\n        this.listeners = [];\n    }\n}\nconst authService = new AuthService();\n// Hook للاستخدام في React\nfunction useAuth() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(authService.getCurrentUser());\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const listener = {\n                \"useAuth.useEffect.listener\": (newUser)=>setUser(newUser)\n            }[\"useAuth.useEffect.listener\"];\n            authService.addListener(listener);\n            return ({\n                \"useAuth.useEffect\": ()=>authService.removeListener(listener)\n            })[\"useAuth.useEffect\"];\n        }\n    }[\"useAuth.useEffect\"], []);\n    return {\n        user,\n        login: authService.login.bind(authService),\n        logout: authService.logout.bind(authService),\n        hasPermission: authService.hasPermission.bind(authService),\n        getAccessibleSections: authService.getAccessibleSections.bind(authService),\n        canCreateRole: authService.canCreateRole.bind(authService),\n        updateProfile: authService.updateProfile.bind(authService),\n        changePassword: authService.changePassword.bind(authService),\n        validateSession: authService.validateSession.bind(authService)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   STATUS_COLORS: () => (/* binding */ STATUS_COLORS),\n/* harmony export */   STATUS_LABELS: () => (/* binding */ STATUS_LABELS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES)\n/* harmony export */ });\n// Application configuration\nconst APP_CONFIG = {\n    name: \"مرسال\",\n    description: \"نظام إدارة عمليات التوصيل السريع\",\n    version: \"1.1.0\",\n    // Colors\n    colors: {\n        primary: \"#41a7ff\",\n        background: \"#f0f3f5\",\n        accent: \"#b19cd9\"\n    },\n    // Business rules\n    business: {\n        commissionPerOrder: 1000,\n        currency: \"د.ع\",\n        defaultOrderStatuses: [\n            \"pending\",\n            \"assigned\",\n            \"picked_up\",\n            \"in_transit\",\n            \"delivered\",\n            \"returned\",\n            \"cancelled\",\n            \"postponed\"\n        ]\n    },\n    // API endpoints (when backend is ready)\n    api: {\n        baseUrl: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\",\n        endpoints: {\n            orders: \"/api/orders\",\n            users: \"/api/users\",\n            couriers: \"/api/couriers\",\n            settlements: \"/api/settlements\"\n        }\n    },\n    // Firebase config keys (to be replaced with actual values)\n    firebase: {\n        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\n    },\n    // Features flags\n    features: {\n        enableNotifications: true,\n        enableReports: true,\n        enableBulkOperations: true,\n        enableImageUpload: true\n    },\n    // Demo mode settings - إعدادات الوضع التجريبي\n    demo: {\n        enabled: true,\n        autoLogin: true,\n        defaultUser: 'manager',\n        skipFirebase: true,\n        showDemoNotice: true // إظهار تنبيه الوضع التجريبي\n    },\n    // Company info\n    company: {\n        name: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        phone: '+*********** 4567',\n        address: 'بغداد، العراق'\n    },\n    // Receipt settings - إعدادات الوصل\n    receipt: {\n        dimensions: {\n            width: '110mm',\n            height: '130mm'\n        },\n        companyName: 'مكتب علي الشيباني للتوصيل السريع فرع الحي',\n        showBarcode: true,\n        showDate: true,\n        priceFormat: 'en-US',\n        currency: 'IQD',\n        fields: {\n            trackingNumber: true,\n            customerPhone: true,\n            status: true,\n            courierName: true,\n            amount: true,\n            barcode: true,\n            date: true\n        }\n    }\n};\n// Status labels in Arabic\nconst STATUS_LABELS = {\n    pending: \"في الانتظار\",\n    assigned: \"مسند\",\n    picked_up: \"تم الاستلام\",\n    in_transit: \"في الطريق\",\n    delivered: \"تم التسليم\",\n    returned: \"راجع\",\n    cancelled: \"ملغي\",\n    postponed: \"مؤجل\"\n};\n// Status colors for UI\nconst STATUS_COLORS = {\n    pending: \"text-yellow-600 bg-yellow-100\",\n    assigned: \"text-blue-600 bg-blue-100\",\n    picked_up: \"text-purple-600 bg-purple-100\",\n    in_transit: \"text-orange-600 bg-orange-100\",\n    delivered: \"text-green-600 bg-green-100\",\n    returned: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    postponed: \"text-gray-600 bg-gray-100\"\n};\n// User roles\nconst USER_ROLES = {\n    admin: \"مدير\",\n    manager: \"مدير فرع\",\n    dispatcher: \"موزع\",\n    courier: \"مندوب\",\n    accountant: \"محاسب\",\n    viewer: \"مشاهد\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firestore.ts":
/*!******************************!*\
  !*** ./src/lib/firestore.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ordersService: () => (/* binding */ ordersService),\n/* harmony export */   settlementsService: () => (/* binding */ settlementsService),\n/* harmony export */   usersService: () => (/* binding */ usersService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _mock_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mock-data */ \"(ssr)/./src/lib/mock-data.ts\");\n// Updated to use Supabase instead of Firebase\n\n\n\n// Orders Service - Updated to use Supabase\nconst ordersService = {\n    // Create new order\n    async create (orderData) {\n        const trackingNumber = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.generateTrackingNumber)();\n        const supabaseOrderData = {\n            tracking_number: trackingNumber,\n            customer_name: orderData.customerName,\n            customer_phone: orderData.customerPhone,\n            address: orderData.address,\n            amount: orderData.amount,\n            status: 'pending',\n            courier_name: orderData.courierName,\n            courier_id: orderData.courierId,\n            notes: orderData.notes\n        };\n        const order = await _supabase__WEBPACK_IMPORTED_MODULE_0__.orderService.createOrder(supabaseOrderData);\n        // Convert back to frontend format\n        return {\n            id: order.id,\n            trackingNumber: order.tracking_number,\n            customerName: order.customer_name,\n            customerPhone: order.customer_phone,\n            address: order.address,\n            amount: order.amount,\n            status: order.status,\n            courierName: order.courier_name,\n            courierId: order.courier_id,\n            notes: order.notes,\n            createdAt: new Date(order.created_at),\n            updatedAt: new Date(order.updated_at),\n            statusHistory: [\n                {\n                    status: 'pending',\n                    timestamp: new Date(order.created_at),\n                    updatedBy: 'system',\n                    notes: 'تم إنشاء الطلب'\n                }\n            ]\n        };\n    },\n    // Get all orders with pagination\n    async getAll (pageSize = 20, lastDoc) {\n        try {\n            const supabaseOrders = await _supabase__WEBPACK_IMPORTED_MODULE_0__.orderService.getAllOrders();\n            // Convert from Supabase format to frontend format\n            const orders = supabaseOrders.map((order)=>({\n                    id: order.id,\n                    trackingNumber: order.tracking_number,\n                    customerName: order.customer_name,\n                    customerPhone: order.customer_phone,\n                    address: order.address,\n                    amount: order.amount,\n                    status: order.status,\n                    courierName: order.courier_name,\n                    courierId: order.courier_id,\n                    notes: order.notes,\n                    createdAt: new Date(order.created_at),\n                    updatedAt: new Date(order.updated_at),\n                    deliveredAt: order.delivered_at ? new Date(order.delivered_at) : undefined,\n                    statusHistory: [\n                        {\n                            status: order.status,\n                            timestamp: new Date(order.updated_at),\n                            updatedBy: 'system',\n                            notes: order.notes || ''\n                        }\n                    ]\n                }));\n            // Simple pagination simulation\n            const startIndex = lastDoc ? parseInt(lastDoc) : 0;\n            const endIndex = startIndex + pageSize;\n            const paginatedOrders = orders.slice(startIndex, endIndex);\n            return {\n                orders: paginatedOrders,\n                lastDoc: endIndex.toString(),\n                hasMore: endIndex < orders.length\n            };\n        } catch (error) {\n            console.warn('Supabase not available, using mock data:', error);\n            return {\n                orders: _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockOrders,\n                lastDoc: null,\n                hasMore: false\n            };\n        }\n    },\n    // Get order by ID\n    async getById (id) {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('orders').select('*').eq('id', id).single();\n            if (error || !data) {\n                throw new Error('Order not found');\n            }\n            return {\n                id: data.id,\n                trackingNumber: data.tracking_number,\n                customerName: data.customer_name,\n                customerPhone: data.customer_phone,\n                address: data.address,\n                amount: data.amount,\n                status: data.status,\n                courierName: data.courier_name,\n                courierId: data.courier_id,\n                notes: data.notes,\n                createdAt: new Date(data.created_at),\n                updatedAt: new Date(data.updated_at),\n                deliveredAt: data.delivered_at ? new Date(data.delivered_at) : undefined,\n                statusHistory: [\n                    {\n                        status: data.status,\n                        timestamp: new Date(data.updated_at),\n                        updatedBy: 'system',\n                        notes: data.notes || ''\n                    }\n                ]\n            };\n        } catch (error) {\n            console.error('Error getting order by ID:', error);\n            throw new Error('Order not found');\n        }\n    },\n    // Search orders\n    async search (searchTerm) {\n        try {\n            const supabaseOrders = await _supabase__WEBPACK_IMPORTED_MODULE_0__.orderService.searchOrders(searchTerm);\n            return supabaseOrders.map((order)=>({\n                    id: order.id,\n                    trackingNumber: order.tracking_number,\n                    customerName: order.customer_name,\n                    customerPhone: order.customer_phone,\n                    address: order.address,\n                    amount: order.amount,\n                    status: order.status,\n                    courierName: order.courier_name,\n                    courierId: order.courier_id,\n                    notes: order.notes,\n                    createdAt: new Date(order.created_at),\n                    updatedAt: new Date(order.updated_at),\n                    deliveredAt: order.delivered_at ? new Date(order.delivered_at) : undefined,\n                    statusHistory: [\n                        {\n                            status: order.status,\n                            timestamp: new Date(order.updated_at),\n                            updatedBy: 'system',\n                            notes: order.notes || ''\n                        }\n                    ]\n                }));\n        } catch (error) {\n            console.warn('Search failed, using mock data:', error);\n            return _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockOrders.filter((order)=>order.trackingNumber.includes(searchTerm) || order.customerName.includes(searchTerm) || order.customerPhone.includes(searchTerm));\n        }\n        const orders = new Map();\n        results.forEach((snapshot)=>{\n            snapshot.docs.forEach((doc1)=>{\n                if (!orders.has(doc1.id)) {\n                    const data = doc1.data();\n                    orders.set(doc1.id, {\n                        id: doc1.id,\n                        ...data,\n                        createdAt: data.createdAt?.toDate(),\n                        updatedAt: data.updatedAt?.toDate(),\n                        deliveredAt: data.deliveredAt?.toDate(),\n                        returnedAt: data.returnedAt?.toDate(),\n                        statusHistory: data.statusHistory?.map((update)=>({\n                                ...update,\n                                timestamp: update.timestamp?.toDate()\n                            }))\n                    });\n                }\n            });\n        });\n        return Array.from(orders.values());\n    },\n    // Update order status\n    async updateStatus (orderId, status, notes, updatedBy, image) {\n        const orderRef = doc(db, COLLECTIONS.ORDERS, orderId);\n        const now = Timestamp.now();\n        const statusUpdate = {\n            status: status,\n            timestamp: now.toDate(),\n            updatedBy,\n            notes,\n            image\n        };\n        const updateData = {\n            status,\n            updatedAt: now,\n            [`statusHistory`]: [\n                ...(await this.getById(orderId)).statusHistory,\n                statusUpdate\n            ]\n        };\n        if (status === 'delivered') {\n            updateData.deliveredAt = now;\n        } else if (status === 'returned') {\n            updateData.returnedAt = now;\n        }\n        await updateDoc(orderRef, updateData);\n        return statusUpdate;\n    },\n    // Get orders by status\n    async getByStatus (status) {\n        const q = query(collection(db, COLLECTIONS.ORDERS), where('status', '==', status), orderBy('createdAt', 'desc'));\n        const snapshot = await getDocs(q);\n        return snapshot.docs.map((doc1)=>({\n                id: doc1.id,\n                ...doc1.data(),\n                createdAt: doc1.data().createdAt?.toDate(),\n                updatedAt: doc1.data().updatedAt?.toDate(),\n                deliveredAt: doc1.data().deliveredAt?.toDate(),\n                returnedAt: doc1.data().returnedAt?.toDate(),\n                statusHistory: doc1.data().statusHistory?.map((update)=>({\n                        ...update,\n                        timestamp: update.timestamp?.toDate()\n                    }))\n            }));\n    },\n    // Get orders by courier\n    async getByCourier (courierId) {\n        const q = query(collection(db, COLLECTIONS.ORDERS), where('assignedTo', '==', courierId), orderBy('createdAt', 'desc'));\n        const snapshot = await getDocs(q);\n        return snapshot.docs.map((doc1)=>({\n                id: doc1.id,\n                ...doc1.data(),\n                createdAt: doc1.data().createdAt?.toDate(),\n                updatedAt: doc1.data().updatedAt?.toDate(),\n                deliveredAt: doc1.data().deliveredAt?.toDate(),\n                returnedAt: doc1.data().returnedAt?.toDate(),\n                statusHistory: doc1.data().statusHistory?.map((update)=>({\n                        ...update,\n                        timestamp: update.timestamp?.toDate()\n                    }))\n            }));\n    },\n    // Assign orders to courier\n    async assignToCourier (orderIds, courierId, assignedBy) {\n        const batch = writeBatch(db);\n        const now = Timestamp.now();\n        for (const orderId of orderIds){\n            const orderRef = doc(db, COLLECTIONS.ORDERS, orderId);\n            const order = await this.getById(orderId);\n            const statusUpdate = {\n                status: 'assigned',\n                timestamp: now.toDate(),\n                updatedBy: assignedBy,\n                notes: `تم إسناد الطلب للمندوب`\n            };\n            batch.update(orderRef, {\n                assignedTo: courierId,\n                status: 'assigned',\n                updatedAt: now,\n                statusHistory: [\n                    ...order.statusHistory,\n                    statusUpdate\n                ]\n            });\n        }\n        await batch.commit();\n    },\n    // Delete order\n    async delete (id) {\n        await deleteDoc(doc(db, COLLECTIONS.ORDERS, id));\n    }\n};\n// Users Service - Updated to use Supabase\nconst usersService = {\n    // Create user\n    async create (userData) {\n        try {\n            const supabaseUserData = {\n                username: userData.username,\n                name: userData.name,\n                phone: userData.phone,\n                role: userData.role,\n                password_hash: '$2b$10$example_hash_for_123456',\n                is_active: true,\n                created_by: userData.createdBy || 'system'\n            };\n            const user = await _supabase__WEBPACK_IMPORTED_MODULE_0__.userService.createUser(supabaseUserData);\n            return {\n                id: user.id,\n                username: user.username,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                isActive: user.is_active,\n                createdAt: new Date(user.created_at),\n                updatedAt: new Date(user.created_at),\n                createdBy: user.created_by\n            };\n        } catch (error) {\n            console.error('Error creating user:', error);\n            throw error;\n        }\n    },\n    // Get all users\n    async getAll () {\n        try {\n            const supabaseUsers = await _supabase__WEBPACK_IMPORTED_MODULE_0__.userService.getAllUsers();\n            return supabaseUsers.map((user)=>({\n                    id: user.id,\n                    username: user.username,\n                    name: user.name,\n                    phone: user.phone,\n                    role: user.role,\n                    isActive: user.is_active,\n                    createdAt: new Date(user.created_at),\n                    updatedAt: new Date(user.created_at),\n                    createdBy: user.created_by\n                }));\n        } catch (error) {\n            console.warn('Error getting users, using mock data:', error);\n            return _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockUsers;\n        }\n    },\n    // Get couriers only\n    async getCouriers () {\n        try {\n            const couriers = await _supabase__WEBPACK_IMPORTED_MODULE_0__.userService.getUsersByRole('courier');\n            return couriers.filter((user)=>user.is_active).map((user)=>({\n                    id: user.id,\n                    username: user.username,\n                    name: user.name,\n                    phone: user.phone,\n                    role: user.role,\n                    isActive: user.is_active,\n                    createdAt: new Date(user.created_at),\n                    updatedAt: new Date(user.created_at),\n                    createdBy: user.created_by\n                }));\n        } catch (error) {\n            console.warn('Error getting couriers, using mock data:', error);\n            return _mock_data__WEBPACK_IMPORTED_MODULE_2__.mockUsers.filter((user)=>user.role === 'courier' && user.isActive);\n        }\n    },\n    // Get user by ID\n    async getById (id) {\n        const docRef = doc(db, COLLECTIONS.USERS, id);\n        const docSnap = await getDoc(docRef);\n        if (!docSnap.exists()) {\n            throw new Error('User not found');\n        }\n        const data = docSnap.data();\n        return {\n            id: docSnap.id,\n            ...data,\n            createdAt: data.createdAt?.toDate(),\n            updatedAt: data.updatedAt?.toDate()\n        };\n    },\n    // Update user\n    async update (id, userData) {\n        const userRef = doc(db, COLLECTIONS.USERS, id);\n        await updateDoc(userRef, {\n            ...userData,\n            updatedAt: Timestamp.now()\n        });\n    },\n    // Delete user\n    async delete (id) {\n        await deleteDoc(doc(db, COLLECTIONS.USERS, id));\n    }\n};\n// Settlements Service\nconst settlementsService = {\n    // Create settlement\n    async create (settlementData) {\n        const now = Timestamp.now();\n        const settlement = {\n            ...settlementData,\n            createdAt: now.toDate(),\n            isSettled: false\n        };\n        const docRef = await addDoc(collection(db, COLLECTIONS.SETTLEMENTS), settlement);\n        return {\n            id: docRef.id,\n            ...settlement\n        };\n    },\n    // Get all settlements\n    async getAll () {\n        const q = query(collection(db, COLLECTIONS.SETTLEMENTS), orderBy('createdAt', 'desc'));\n        const snapshot = await getDocs(q);\n        return snapshot.docs.map((doc1)=>({\n                id: doc1.id,\n                ...doc1.data(),\n                createdAt: doc1.data().createdAt?.toDate(),\n                settledAt: doc1.data().settledAt?.toDate()\n            }));\n    },\n    // Mark settlement as settled\n    async markAsSettled (id) {\n        const settlementRef = doc(db, COLLECTIONS.SETTLEMENTS, id);\n        await updateDoc(settlementRef, {\n            isSettled: true,\n            settledAt: Timestamp.now()\n        });\n    },\n    // Real-time subscription to orders\n    subscribeToOrders (callback) {\n        try {\n            const q = query(collection(db, COLLECTIONS.ORDERS), orderBy('createdAt', 'desc'));\n            return onSnapshot(q, (snapshot)=>{\n                const orders = snapshot.docs.map((doc1)=>({\n                        id: doc1.id,\n                        ...doc1.data()\n                    }));\n                callback(orders);\n            });\n        } catch (error) {\n            console.warn('Firebase subscription not available:', error);\n            // Return a dummy unsubscribe function\n            return ()=>{};\n        }\n    },\n    // Real-time subscription to user's orders\n    subscribeToUserOrders (userId, callback) {\n        try {\n            const q = query(collection(db, COLLECTIONS.ORDERS), where('courierId', '==', userId), orderBy('createdAt', 'desc'));\n            return onSnapshot(q, (snapshot)=>{\n                const orders = snapshot.docs.map((doc1)=>({\n                        id: doc1.id,\n                        ...doc1.data()\n                    }));\n                callback(orders);\n            });\n        } catch (error) {\n            console.warn('Firebase subscription not available:', error);\n            return ()=>{};\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firestore.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/mock-data.ts":
/*!******************************!*\
  !*** ./src/lib/mock-data.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockFirebaseService: () => (/* binding */ mockFirebaseService),\n/* harmony export */   mockOrders: () => (/* binding */ mockOrders),\n/* harmony export */   mockSettlements: () => (/* binding */ mockSettlements),\n/* harmony export */   mockUsers: () => (/* binding */ mockUsers)\n/* harmony export */ });\n// Mock data for development and testing\nconst mockOrders = [\n    {\n        id: \"1\",\n        trackingNumber: \"MRS001\",\n        senderName: \"أحمد محمد\",\n        senderPhone: \"07901234567\",\n        senderAddress: \"بغداد - الكرادة - شارع الرشيد\",\n        recipientName: \"فاطمة علي\",\n        recipientPhone: \"07801234567\",\n        recipientAddress: \"بغداد - الجادرية - قرب الجامعة\",\n        amount: 50000,\n        status: \"pending\",\n        notes: \"يرجى التسليم في المساء\",\n        createdAt: new Date(\"2024-01-15T10:30:00\"),\n        updatedAt: new Date(\"2024-01-15T10:30:00\"),\n        statusHistory: [\n            {\n                status: \"pending\",\n                timestamp: new Date(\"2024-01-15T10:30:00\"),\n                updatedBy: \"النظام\",\n                notes: \"تم إنشاء الطلب\"\n            }\n        ]\n    },\n    {\n        id: \"2\",\n        trackingNumber: \"MRS002\",\n        senderName: \"سارة أحمد\",\n        senderPhone: \"07701234567\",\n        senderAddress: \"بغداد - المنصور - شارع الأميرات\",\n        recipientName: \"محمد حسن\",\n        recipientPhone: \"07601234567\",\n        recipientAddress: \"بغداد - الكاظمية - شارع الإمام\",\n        amount: 75000,\n        status: \"assigned\",\n        assignedTo: \"courier1\",\n        createdAt: new Date(\"2024-01-15T09:15:00\"),\n        updatedAt: new Date(\"2024-01-15T11:00:00\"),\n        statusHistory: [\n            {\n                status: \"pending\",\n                timestamp: new Date(\"2024-01-15T09:15:00\"),\n                updatedBy: \"النظام\",\n                notes: \"تم إنشاء الطلب\"\n            },\n            {\n                status: \"assigned\",\n                timestamp: new Date(\"2024-01-15T11:00:00\"),\n                updatedBy: \"dispatcher1\",\n                notes: \"تم إسناد الطلب للمندوب\"\n            }\n        ]\n    },\n    {\n        id: \"3\",\n        trackingNumber: \"MRS003\",\n        senderName: \"خالد عبدالله\",\n        senderPhone: \"07501234567\",\n        senderAddress: \"بغداد - الدورة - شارع الصناعة\",\n        recipientName: \"زينب محمد\",\n        recipientPhone: \"07401234567\",\n        recipientAddress: \"بغداد - الشعلة - قرب المجمع\",\n        amount: 120000,\n        status: \"delivered\",\n        assignedTo: \"courier2\",\n        deliveredAt: new Date(\"2024-01-14T16:30:00\"),\n        createdAt: new Date(\"2024-01-14T08:00:00\"),\n        updatedAt: new Date(\"2024-01-14T16:30:00\"),\n        statusHistory: [\n            {\n                status: \"pending\",\n                timestamp: new Date(\"2024-01-14T08:00:00\"),\n                updatedBy: \"النظام\",\n                notes: \"تم إنشاء الطلب\"\n            },\n            {\n                status: \"assigned\",\n                timestamp: new Date(\"2024-01-14T09:00:00\"),\n                updatedBy: \"dispatcher1\",\n                notes: \"تم إسناد الطلب للمندوب\"\n            },\n            {\n                status: \"delivered\",\n                timestamp: new Date(\"2024-01-14T16:30:00\"),\n                updatedBy: \"courier2\",\n                notes: \"تم تسليم الطلب بنجاح\"\n            }\n        ]\n    }\n];\nconst mockUsers = [\n    {\n        id: \"admin1\",\n        email: \"<EMAIL>\",\n        name: \"أحمد المدير\",\n        username: \"admin\",\n        phone: \"07901234567\",\n        role: \"admin\",\n        isActive: true,\n        createdAt: new Date(\"2024-01-01T00:00:00\"),\n        updatedAt: new Date(\"2024-01-01T00:00:00\")\n    },\n    {\n        id: \"courier1\",\n        email: \"<EMAIL>\",\n        name: \"علي حسين\",\n        username: \"courier1\",\n        phone: \"07801234567\",\n        role: \"courier\",\n        isActive: true,\n        createdAt: new Date(\"2024-01-02T00:00:00\"),\n        updatedAt: new Date(\"2024-01-02T00:00:00\")\n    },\n    {\n        id: \"courier2\",\n        email: \"<EMAIL>\",\n        name: \"حسام محمد\",\n        username: \"courier2\",\n        phone: \"07701234567\",\n        role: \"courier\",\n        isActive: true,\n        createdAt: new Date(\"2024-01-03T00:00:00\"),\n        updatedAt: new Date(\"2024-01-03T00:00:00\")\n    },\n    {\n        id: \"dispatcher1\",\n        email: \"<EMAIL>\",\n        name: \"سارة الموزعة\",\n        username: \"dispatcher\",\n        phone: \"07601234567\",\n        role: \"dispatcher\",\n        isActive: true,\n        createdAt: new Date(\"2024-01-04T00:00:00\"),\n        updatedAt: new Date(\"2024-01-04T00:00:00\")\n    }\n];\nconst mockSettlements = [\n    {\n        id: \"settlement1\",\n        courierId: \"courier1\",\n        courierName: \"علي حسين\",\n        orders: [\n            \"3\"\n        ],\n        totalAmount: 120000,\n        commission: 1000,\n        netAmount: 119000,\n        createdAt: new Date(\"2024-01-14T18:00:00\"),\n        isSettled: true,\n        settledAt: new Date(\"2024-01-15T10:00:00\")\n    }\n];\n// Mock functions for when Firebase is not available\nconst mockFirebaseService = {\n    async getOrders () {\n        return mockOrders;\n    },\n    async getUsers () {\n        return mockUsers;\n    },\n    async getSettlements () {\n        return mockSettlements;\n    },\n    async getStatistics () {\n        return {\n            totalOrders: mockOrders.length,\n            deliveredOrders: mockOrders.filter((o)=>o.status === 'delivered').length,\n            returnedOrders: mockOrders.filter((o)=>o.status === 'returned').length,\n            pendingOrders: mockOrders.filter((o)=>o.status === 'pending').length,\n            totalAmount: mockOrders.filter((o)=>o.status === 'delivered').reduce((sum, o)=>sum + o.amount, 0),\n            totalCommission: mockOrders.filter((o)=>o.status === 'delivered').length * 1000\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL21vY2stZGF0YS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEsd0NBQXdDO0FBR2pDLE1BQU1BLGFBQXNCO0lBQ2pDO1FBQ0VDLElBQUk7UUFDSkMsZ0JBQWdCO1FBQ2hCQyxZQUFZO1FBQ1pDLGFBQWE7UUFDYkMsZUFBZTtRQUNmQyxlQUFlO1FBQ2ZDLGdCQUFnQjtRQUNoQkMsa0JBQWtCO1FBQ2xCQyxRQUFRO1FBQ1JDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxXQUFXLElBQUlDLEtBQUs7UUFDcEJDLFdBQVcsSUFBSUQsS0FBSztRQUNwQkUsZUFBZTtZQUNiO2dCQUNFTCxRQUFRO2dCQUNSTSxXQUFXLElBQUlILEtBQUs7Z0JBQ3BCSSxXQUFXO2dCQUNYTixPQUFPO1lBQ1Q7U0FDRDtJQUNIO0lBQ0E7UUFDRVYsSUFBSTtRQUNKQyxnQkFBZ0I7UUFDaEJDLFlBQVk7UUFDWkMsYUFBYTtRQUNiQyxlQUFlO1FBQ2ZDLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxrQkFBa0I7UUFDbEJDLFFBQVE7UUFDUkMsUUFBUTtRQUNSUSxZQUFZO1FBQ1pOLFdBQVcsSUFBSUMsS0FBSztRQUNwQkMsV0FBVyxJQUFJRCxLQUFLO1FBQ3BCRSxlQUFlO1lBQ2I7Z0JBQ0VMLFFBQVE7Z0JBQ1JNLFdBQVcsSUFBSUgsS0FBSztnQkFDcEJJLFdBQVc7Z0JBQ1hOLE9BQU87WUFDVDtZQUNBO2dCQUNFRCxRQUFRO2dCQUNSTSxXQUFXLElBQUlILEtBQUs7Z0JBQ3BCSSxXQUFXO2dCQUNYTixPQUFPO1lBQ1Q7U0FDRDtJQUNIO0lBQ0E7UUFDRVYsSUFBSTtRQUNKQyxnQkFBZ0I7UUFDaEJDLFlBQVk7UUFDWkMsYUFBYTtRQUNiQyxlQUFlO1FBQ2ZDLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxrQkFBa0I7UUFDbEJDLFFBQVE7UUFDUkMsUUFBUTtRQUNSUSxZQUFZO1FBQ1pDLGFBQWEsSUFBSU4sS0FBSztRQUN0QkQsV0FBVyxJQUFJQyxLQUFLO1FBQ3BCQyxXQUFXLElBQUlELEtBQUs7UUFDcEJFLGVBQWU7WUFDYjtnQkFDRUwsUUFBUTtnQkFDUk0sV0FBVyxJQUFJSCxLQUFLO2dCQUNwQkksV0FBVztnQkFDWE4sT0FBTztZQUNUO1lBQ0E7Z0JBQ0VELFFBQVE7Z0JBQ1JNLFdBQVcsSUFBSUgsS0FBSztnQkFDcEJJLFdBQVc7Z0JBQ1hOLE9BQU87WUFDVDtZQUNBO2dCQUNFRCxRQUFRO2dCQUNSTSxXQUFXLElBQUlILEtBQUs7Z0JBQ3BCSSxXQUFXO2dCQUNYTixPQUFPO1lBQ1Q7U0FDRDtJQUNIO0NBQ0QsQ0FBQztBQUVLLE1BQU1TLFlBQW9CO0lBQy9CO1FBQ0VuQixJQUFJO1FBQ0pvQixPQUFPO1FBQ1BDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsVUFBVTtRQUNWZCxXQUFXLElBQUlDLEtBQUs7UUFDcEJDLFdBQVcsSUFBSUQsS0FBSztJQUN0QjtJQUNBO1FBQ0VaLElBQUk7UUFDSm9CLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZkLFdBQVcsSUFBSUMsS0FBSztRQUNwQkMsV0FBVyxJQUFJRCxLQUFLO0lBQ3RCO0lBQ0E7UUFDRVosSUFBSTtRQUNKb0IsT0FBTztRQUNQQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLFVBQVU7UUFDVmQsV0FBVyxJQUFJQyxLQUFLO1FBQ3BCQyxXQUFXLElBQUlELEtBQUs7SUFDdEI7SUFDQTtRQUNFWixJQUFJO1FBQ0pvQixPQUFPO1FBQ1BDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsVUFBVTtRQUNWZCxXQUFXLElBQUlDLEtBQUs7UUFDcEJDLFdBQVcsSUFBSUQsS0FBSztJQUN0QjtDQUNELENBQUM7QUFFSyxNQUFNYyxrQkFBZ0M7SUFDM0M7UUFDRTFCLElBQUk7UUFDSjJCLFdBQVc7UUFDWEMsYUFBYTtRQUNiQyxRQUFRO1lBQUM7U0FBSTtRQUNiQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsV0FBVztRQUNYckIsV0FBVyxJQUFJQyxLQUFLO1FBQ3BCcUIsV0FBVztRQUNYQyxXQUFXLElBQUl0QixLQUFLO0lBQ3RCO0NBQ0QsQ0FBQztBQUVGLG9EQUFvRDtBQUM3QyxNQUFNdUIsc0JBQXNCO0lBQ2pDLE1BQU1DO1FBQ0osT0FBT3JDO0lBQ1Q7SUFFQSxNQUFNc0M7UUFDSixPQUFPbEI7SUFDVDtJQUVBLE1BQU1tQjtRQUNKLE9BQU9aO0lBQ1Q7SUFFQSxNQUFNYTtRQUNKLE9BQU87WUFDTEMsYUFBYXpDLFdBQVcwQyxNQUFNO1lBQzlCQyxpQkFBaUIzQyxXQUFXNEMsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFbkMsTUFBTSxLQUFLLGFBQWFnQyxNQUFNO1lBQ3hFSSxnQkFBZ0I5QyxXQUFXNEMsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFbkMsTUFBTSxLQUFLLFlBQVlnQyxNQUFNO1lBQ3RFSyxlQUFlL0MsV0FBVzRDLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRW5DLE1BQU0sS0FBSyxXQUFXZ0MsTUFBTTtZQUNwRVgsYUFBYS9CLFdBQ1Y0QyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVuQyxNQUFNLEtBQUssYUFDekJzQyxNQUFNLENBQUMsQ0FBQ0MsS0FBS0osSUFBTUksTUFBTUosRUFBRXBDLE1BQU0sRUFBRTtZQUN0Q3lDLGlCQUFpQmxELFdBQVc0QyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVuQyxNQUFNLEtBQUssYUFBYWdDLE1BQU0sR0FBRztRQUM3RTtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiRTpcXE1hcnNhbFxcbWFyc2FsXFxzcmNcXGxpYlxcbW9jay1kYXRhLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIE1vY2sgZGF0YSBmb3IgZGV2ZWxvcG1lbnQgYW5kIHRlc3RpbmdcbmltcG9ydCB7IE9yZGVyLCBVc2VyLCBTZXR0bGVtZW50IH0gZnJvbSAnQC90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBtb2NrT3JkZXJzOiBPcmRlcltdID0gW1xuICB7XG4gICAgaWQ6IFwiMVwiLFxuICAgIHRyYWNraW5nTnVtYmVyOiBcIk1SUzAwMVwiLFxuICAgIHNlbmRlck5hbWU6IFwi2KPYrdmF2K8g2YXYrdmF2K9cIixcbiAgICBzZW5kZXJQaG9uZTogXCIwNzkwMTIzNDU2N1wiLFxuICAgIHNlbmRlckFkZHJlc3M6IFwi2KjYutiv2KfYryAtINin2YTZg9ix2KfYr9ipIC0g2LTYp9ix2Lkg2KfZhNix2LTZitivXCIsXG4gICAgcmVjaXBpZW50TmFtZTogXCLZgdin2LfZhdipINi52YTZilwiLFxuICAgIHJlY2lwaWVudFBob25lOiBcIjA3ODAxMjM0NTY3XCIsXG4gICAgcmVjaXBpZW50QWRkcmVzczogXCLYqNi62K/Yp9ivIC0g2KfZhNis2KfYr9ix2YrYqSAtINmC2LHYqCDYp9mE2KzYp9mF2LnYqVwiLFxuICAgIGFtb3VudDogNTAwMDAsXG4gICAgc3RhdHVzOiBcInBlbmRpbmdcIixcbiAgICBub3RlczogXCLZitix2KzZiSDYp9mE2KrYs9mE2YrZhSDZgdmKINin2YTZhdiz2KfYoVwiLFxuICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoXCIyMDI0LTAxLTE1VDEwOjMwOjAwXCIpLFxuICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoXCIyMDI0LTAxLTE1VDEwOjMwOjAwXCIpLFxuICAgIHN0YXR1c0hpc3Rvcnk6IFtcbiAgICAgIHtcbiAgICAgICAgc3RhdHVzOiBcInBlbmRpbmdcIixcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZShcIjIwMjQtMDEtMTVUMTA6MzA6MDBcIiksXG4gICAgICAgIHVwZGF0ZWRCeTogXCLYp9mE2YbYuNin2YVcIixcbiAgICAgICAgbm90ZXM6IFwi2KrZhSDYpdmG2LTYp9ihINin2YTYt9mE2KhcIlxuICAgICAgfVxuICAgIF1cbiAgfSxcbiAge1xuICAgIGlkOiBcIjJcIixcbiAgICB0cmFja2luZ051bWJlcjogXCJNUlMwMDJcIixcbiAgICBzZW5kZXJOYW1lOiBcItiz2KfYsdipINij2K3ZhdivXCIsXG4gICAgc2VuZGVyUGhvbmU6IFwiMDc3MDEyMzQ1NjdcIixcbiAgICBzZW5kZXJBZGRyZXNzOiBcItio2LrYr9in2K8gLSDYp9mE2YXZhti12YjYsSAtINi02KfYsdi5INin2YTYo9mF2YrYsdin2KpcIixcbiAgICByZWNpcGllbnROYW1lOiBcItmF2K3ZhdivINit2LPZhlwiLFxuICAgIHJlY2lwaWVudFBob25lOiBcIjA3NjAxMjM0NTY3XCIsXG4gICAgcmVjaXBpZW50QWRkcmVzczogXCLYqNi62K/Yp9ivIC0g2KfZhNmD2KfYuNmF2YrYqSAtINi02KfYsdi5INin2YTYpdmF2KfZhVwiLFxuICAgIGFtb3VudDogNzUwMDAsXG4gICAgc3RhdHVzOiBcImFzc2lnbmVkXCIsXG4gICAgYXNzaWduZWRUbzogXCJjb3VyaWVyMVwiLFxuICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoXCIyMDI0LTAxLTE1VDA5OjE1OjAwXCIpLFxuICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoXCIyMDI0LTAxLTE1VDExOjAwOjAwXCIpLFxuICAgIHN0YXR1c0hpc3Rvcnk6IFtcbiAgICAgIHtcbiAgICAgICAgc3RhdHVzOiBcInBlbmRpbmdcIixcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZShcIjIwMjQtMDEtMTVUMDk6MTU6MDBcIiksXG4gICAgICAgIHVwZGF0ZWRCeTogXCLYp9mE2YbYuNin2YVcIixcbiAgICAgICAgbm90ZXM6IFwi2KrZhSDYpdmG2LTYp9ihINin2YTYt9mE2KhcIlxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgc3RhdHVzOiBcImFzc2lnbmVkXCIsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoXCIyMDI0LTAxLTE1VDExOjAwOjAwXCIpLFxuICAgICAgICB1cGRhdGVkQnk6IFwiZGlzcGF0Y2hlcjFcIixcbiAgICAgICAgbm90ZXM6IFwi2KrZhSDYpdiz2YbYp9ivINin2YTYt9mE2Kgg2YTZhNmF2YbYr9mI2KhcIlxuICAgICAgfVxuICAgIF1cbiAgfSxcbiAge1xuICAgIGlkOiBcIjNcIixcbiAgICB0cmFja2luZ051bWJlcjogXCJNUlMwMDNcIixcbiAgICBzZW5kZXJOYW1lOiBcItiu2KfZhNivINi52KjYr9in2YTZhNmHXCIsXG4gICAgc2VuZGVyUGhvbmU6IFwiMDc1MDEyMzQ1NjdcIixcbiAgICBzZW5kZXJBZGRyZXNzOiBcItio2LrYr9in2K8gLSDYp9mE2K/ZiNix2KkgLSDYtNin2LHYuSDYp9mE2LXZhtin2LnYqVwiLFxuICAgIHJlY2lwaWVudE5hbWU6IFwi2LLZitmG2Kgg2YXYrdmF2K9cIixcbiAgICByZWNpcGllbnRQaG9uZTogXCIwNzQwMTIzNDU2N1wiLFxuICAgIHJlY2lwaWVudEFkZHJlc3M6IFwi2KjYutiv2KfYryAtINin2YTYtNi52YTYqSAtINmC2LHYqCDYp9mE2YXYrNmF2LlcIixcbiAgICBhbW91bnQ6IDEyMDAwMCxcbiAgICBzdGF0dXM6IFwiZGVsaXZlcmVkXCIsXG4gICAgYXNzaWduZWRUbzogXCJjb3VyaWVyMlwiLFxuICAgIGRlbGl2ZXJlZEF0OiBuZXcgRGF0ZShcIjIwMjQtMDEtMTRUMTY6MzA6MDBcIiksXG4gICAgY3JlYXRlZEF0OiBuZXcgRGF0ZShcIjIwMjQtMDEtMTRUMDg6MDA6MDBcIiksXG4gICAgdXBkYXRlZEF0OiBuZXcgRGF0ZShcIjIwMjQtMDEtMTRUMTY6MzA6MDBcIiksXG4gICAgc3RhdHVzSGlzdG9yeTogW1xuICAgICAge1xuICAgICAgICBzdGF0dXM6IFwicGVuZGluZ1wiLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKFwiMjAyNC0wMS0xNFQwODowMDowMFwiKSxcbiAgICAgICAgdXBkYXRlZEJ5OiBcItin2YTZhti42KfZhVwiLFxuICAgICAgICBub3RlczogXCLYqtmFINil2YbYtNin2KEg2KfZhNi32YTYqFwiXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBzdGF0dXM6IFwiYXNzaWduZWRcIixcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZShcIjIwMjQtMDEtMTRUMDk6MDA6MDBcIiksXG4gICAgICAgIHVwZGF0ZWRCeTogXCJkaXNwYXRjaGVyMVwiLFxuICAgICAgICBub3RlczogXCLYqtmFINil2LPZhtin2K8g2KfZhNi32YTYqCDZhNmE2YXZhtiv2YjYqFwiXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBzdGF0dXM6IFwiZGVsaXZlcmVkXCIsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoXCIyMDI0LTAxLTE0VDE2OjMwOjAwXCIpLFxuICAgICAgICB1cGRhdGVkQnk6IFwiY291cmllcjJcIixcbiAgICAgICAgbm90ZXM6IFwi2KrZhSDYqtiz2YTZitmFINin2YTYt9mE2Kgg2KjZhtis2KfYrVwiXG4gICAgICB9XG4gICAgXVxuICB9XG5dO1xuXG5leHBvcnQgY29uc3QgbW9ja1VzZXJzOiBVc2VyW10gPSBbXG4gIHtcbiAgICBpZDogXCJhZG1pbjFcIixcbiAgICBlbWFpbDogXCJhZG1pbkBtYXJzYWwuY29tXCIsXG4gICAgbmFtZTogXCLYo9it2YXYryDYp9mE2YXYr9mK2LFcIixcbiAgICB1c2VybmFtZTogXCJhZG1pblwiLFxuICAgIHBob25lOiBcIjA3OTAxMjM0NTY3XCIsXG4gICAgcm9sZTogXCJhZG1pblwiLFxuICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoXCIyMDI0LTAxLTAxVDAwOjAwOjAwXCIpLFxuICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoXCIyMDI0LTAxLTAxVDAwOjAwOjAwXCIpXG4gIH0sXG4gIHtcbiAgICBpZDogXCJjb3VyaWVyMVwiLFxuICAgIGVtYWlsOiBcImNvdXJpZXIxQG1hcnNhbC5jb21cIixcbiAgICBuYW1lOiBcIti52YTZiiDYrdiz2YrZhlwiLFxuICAgIHVzZXJuYW1lOiBcImNvdXJpZXIxXCIsXG4gICAgcGhvbmU6IFwiMDc4MDEyMzQ1NjdcIixcbiAgICByb2xlOiBcImNvdXJpZXJcIixcbiAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKFwiMjAyNC0wMS0wMlQwMDowMDowMFwiKSxcbiAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKFwiMjAyNC0wMS0wMlQwMDowMDowMFwiKVxuICB9LFxuICB7XG4gICAgaWQ6IFwiY291cmllcjJcIixcbiAgICBlbWFpbDogXCJjb3VyaWVyMkBtYXJzYWwuY29tXCIsXG4gICAgbmFtZTogXCLYrdiz2KfZhSDZhdit2YXYr1wiLFxuICAgIHVzZXJuYW1lOiBcImNvdXJpZXIyXCIsXG4gICAgcGhvbmU6IFwiMDc3MDEyMzQ1NjdcIixcbiAgICByb2xlOiBcImNvdXJpZXJcIixcbiAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKFwiMjAyNC0wMS0wM1QwMDowMDowMFwiKSxcbiAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKFwiMjAyNC0wMS0wM1QwMDowMDowMFwiKVxuICB9LFxuICB7XG4gICAgaWQ6IFwiZGlzcGF0Y2hlcjFcIixcbiAgICBlbWFpbDogXCJkaXNwYXRjaGVyQG1hcnNhbC5jb21cIixcbiAgICBuYW1lOiBcItiz2KfYsdipINin2YTZhdmI2LLYudipXCIsXG4gICAgdXNlcm5hbWU6IFwiZGlzcGF0Y2hlclwiLFxuICAgIHBob25lOiBcIjA3NjAxMjM0NTY3XCIsXG4gICAgcm9sZTogXCJkaXNwYXRjaGVyXCIsXG4gICAgaXNBY3RpdmU6IHRydWUsXG4gICAgY3JlYXRlZEF0OiBuZXcgRGF0ZShcIjIwMjQtMDEtMDRUMDA6MDA6MDBcIiksXG4gICAgdXBkYXRlZEF0OiBuZXcgRGF0ZShcIjIwMjQtMDEtMDRUMDA6MDA6MDBcIilcbiAgfVxuXTtcblxuZXhwb3J0IGNvbnN0IG1vY2tTZXR0bGVtZW50czogU2V0dGxlbWVudFtdID0gW1xuICB7XG4gICAgaWQ6IFwic2V0dGxlbWVudDFcIixcbiAgICBjb3VyaWVySWQ6IFwiY291cmllcjFcIixcbiAgICBjb3VyaWVyTmFtZTogXCLYudmE2Yog2K3Ys9mK2YZcIixcbiAgICBvcmRlcnM6IFtcIjNcIl0sXG4gICAgdG90YWxBbW91bnQ6IDEyMDAwMCxcbiAgICBjb21taXNzaW9uOiAxMDAwLFxuICAgIG5ldEFtb3VudDogMTE5MDAwLFxuICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoXCIyMDI0LTAxLTE0VDE4OjAwOjAwXCIpLFxuICAgIGlzU2V0dGxlZDogdHJ1ZSxcbiAgICBzZXR0bGVkQXQ6IG5ldyBEYXRlKFwiMjAyNC0wMS0xNVQxMDowMDowMFwiKVxuICB9XG5dO1xuXG4vLyBNb2NrIGZ1bmN0aW9ucyBmb3Igd2hlbiBGaXJlYmFzZSBpcyBub3QgYXZhaWxhYmxlXG5leHBvcnQgY29uc3QgbW9ja0ZpcmViYXNlU2VydmljZSA9IHtcbiAgYXN5bmMgZ2V0T3JkZXJzKCkge1xuICAgIHJldHVybiBtb2NrT3JkZXJzO1xuICB9LFxuICBcbiAgYXN5bmMgZ2V0VXNlcnMoKSB7XG4gICAgcmV0dXJuIG1vY2tVc2VycztcbiAgfSxcbiAgXG4gIGFzeW5jIGdldFNldHRsZW1lbnRzKCkge1xuICAgIHJldHVybiBtb2NrU2V0dGxlbWVudHM7XG4gIH0sXG4gIFxuICBhc3luYyBnZXRTdGF0aXN0aWNzKCkge1xuICAgIHJldHVybiB7XG4gICAgICB0b3RhbE9yZGVyczogbW9ja09yZGVycy5sZW5ndGgsXG4gICAgICBkZWxpdmVyZWRPcmRlcnM6IG1vY2tPcmRlcnMuZmlsdGVyKG8gPT4gby5zdGF0dXMgPT09ICdkZWxpdmVyZWQnKS5sZW5ndGgsXG4gICAgICByZXR1cm5lZE9yZGVyczogbW9ja09yZGVycy5maWx0ZXIobyA9PiBvLnN0YXR1cyA9PT0gJ3JldHVybmVkJykubGVuZ3RoLFxuICAgICAgcGVuZGluZ09yZGVyczogbW9ja09yZGVycy5maWx0ZXIobyA9PiBvLnN0YXR1cyA9PT0gJ3BlbmRpbmcnKS5sZW5ndGgsXG4gICAgICB0b3RhbEFtb3VudDogbW9ja09yZGVyc1xuICAgICAgICAuZmlsdGVyKG8gPT4gby5zdGF0dXMgPT09ICdkZWxpdmVyZWQnKVxuICAgICAgICAucmVkdWNlKChzdW0sIG8pID0+IHN1bSArIG8uYW1vdW50LCAwKSxcbiAgICAgIHRvdGFsQ29tbWlzc2lvbjogbW9ja09yZGVycy5maWx0ZXIobyA9PiBvLnN0YXR1cyA9PT0gJ2RlbGl2ZXJlZCcpLmxlbmd0aCAqIDEwMDBcbiAgICB9O1xuICB9XG59O1xuIl0sIm5hbWVzIjpbIm1vY2tPcmRlcnMiLCJpZCIsInRyYWNraW5nTnVtYmVyIiwic2VuZGVyTmFtZSIsInNlbmRlclBob25lIiwic2VuZGVyQWRkcmVzcyIsInJlY2lwaWVudE5hbWUiLCJyZWNpcGllbnRQaG9uZSIsInJlY2lwaWVudEFkZHJlc3MiLCJhbW91bnQiLCJzdGF0dXMiLCJub3RlcyIsImNyZWF0ZWRBdCIsIkRhdGUiLCJ1cGRhdGVkQXQiLCJzdGF0dXNIaXN0b3J5IiwidGltZXN0YW1wIiwidXBkYXRlZEJ5IiwiYXNzaWduZWRUbyIsImRlbGl2ZXJlZEF0IiwibW9ja1VzZXJzIiwiZW1haWwiLCJuYW1lIiwidXNlcm5hbWUiLCJwaG9uZSIsInJvbGUiLCJpc0FjdGl2ZSIsIm1vY2tTZXR0bGVtZW50cyIsImNvdXJpZXJJZCIsImNvdXJpZXJOYW1lIiwib3JkZXJzIiwidG90YWxBbW91bnQiLCJjb21taXNzaW9uIiwibmV0QW1vdW50IiwiaXNTZXR0bGVkIiwic2V0dGxlZEF0IiwibW9ja0ZpcmViYXNlU2VydmljZSIsImdldE9yZGVycyIsImdldFVzZXJzIiwiZ2V0U2V0dGxlbWVudHMiLCJnZXRTdGF0aXN0aWNzIiwidG90YWxPcmRlcnMiLCJsZW5ndGgiLCJkZWxpdmVyZWRPcmRlcnMiLCJmaWx0ZXIiLCJvIiwicmV0dXJuZWRPcmRlcnMiLCJwZW5kaW5nT3JkZXJzIiwicmVkdWNlIiwic3VtIiwidG90YWxDb21taXNzaW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/mock-data.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseOrderService: () => (/* binding */ SupabaseOrderService),\n/* harmony export */   SupabaseUserService: () => (/* binding */ SupabaseUserService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   orderService: () => (/* binding */ orderService),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   testSupabaseConnection: () => (/* binding */ testSupabaseConnection),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Supabase configuration - إعدادات قاعدة البيانات السحابية\nconst supabaseUrl = \"https://ltxyomylyagbhueuyws.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0eHlvbXlseWFnYmh1ZXV1eXdzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MDMyNzQsImV4cCI6MjA2NzI3OTI3NH0.NeoJk-ReFqIy0QC8e-9lbg55tmu6snAmQOby0kgJDYo\" || 0;\n// Create Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Test connection function - اختبار الاتصال بقاعدة البيانات السحابية\nconst testSupabaseConnection = async ()=>{\n    try {\n        // Test connection by trying to fetch from a system table\n        const { data, error } = await supabase.from('users').select('count').limit(1);\n        if (error) {\n            console.error('Supabase connection test failed:', error);\n            return {\n                success: false,\n                message: `فشل في الاتصال بقاعدة البيانات السحابية: ${error.message}`\n            };\n        }\n        return {\n            success: true,\n            message: 'تم الاتصال بقاعدة البيانات السحابية بنجاح ✅'\n        };\n    } catch (error) {\n        console.error('Supabase connection test failed:', error);\n        let errorMessage = 'فشل في الاتصال بقاعدة البيانات السحابية';\n        if (error instanceof Error) {\n            if (error.message.includes('network')) {\n                errorMessage += ' - تحقق من الاتصال بالإنترنت';\n            } else if (error.message.includes('permission')) {\n                errorMessage += ' - مشكلة في الصلاحيات';\n            } else {\n                errorMessage += ': ' + error.message;\n            }\n        }\n        return {\n            success: false,\n            message: errorMessage\n        };\n    }\n};\n// User Service - خدمة المستخدمين\nclass SupabaseUserService {\n    async createUser(userData) {\n        const { data, error } = await supabase.from('users').insert([\n            userData\n        ]).select().single();\n        if (error) throw new Error(`Failed to create user: ${error.message}`);\n        return data;\n    }\n    async getUserByUsername(username) {\n        const { data, error } = await supabase.from('users').select('*').eq('username', username).single();\n        if (error && error.code !== 'PGRST116') {\n            throw new Error(`Failed to get user: ${error.message}`);\n        }\n        return data || null;\n    }\n    async getAllUsers() {\n        const { data, error } = await supabase.from('users').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get users: ${error.message}`);\n        return data || [];\n    }\n    async updateUser(id, updates) {\n        const { error } = await supabase.from('users').update(updates).eq('id', id);\n        if (error) throw new Error(`Failed to update user: ${error.message}`);\n    }\n    async deleteUser(id) {\n        const { error } = await supabase.from('users').delete().eq('id', id);\n        if (error) throw new Error(`Failed to delete user: ${error.message}`);\n    }\n    async getUsersByRole(role) {\n        const { data, error } = await supabase.from('users').select('*').eq('role', role).order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get users by role: ${error.message}`);\n        return data || [];\n    }\n}\n// Order Service - خدمة الطلبات\nclass SupabaseOrderService {\n    async createOrder(orderData) {\n        const { data, error } = await supabase.from('orders').insert([\n            {\n                ...orderData,\n                updated_at: new Date().toISOString()\n            }\n        ]).select().single();\n        if (error) throw new Error(`Failed to create order: ${error.message}`);\n        return data;\n    }\n    async getOrderByTrackingNumber(trackingNumber) {\n        const { data, error } = await supabase.from('orders').select('*').eq('tracking_number', trackingNumber).single();\n        if (error && error.code !== 'PGRST116') {\n            throw new Error(`Failed to get order: ${error.message}`);\n        }\n        return data || null;\n    }\n    async getAllOrders() {\n        const { data, error } = await supabase.from('orders').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get orders: ${error.message}`);\n        return data || [];\n    }\n    async getOrdersByStatus(status) {\n        const { data, error } = await supabase.from('orders').select('*').eq('status', status).order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get orders by status: ${error.message}`);\n        return data || [];\n    }\n    async getOrdersByCourier(courierId) {\n        const { data, error } = await supabase.from('orders').select('*').eq('courier_id', courierId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to get orders by courier: ${error.message}`);\n        return data || [];\n    }\n    async updateOrder(id, updates) {\n        const { error } = await supabase.from('orders').update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq('id', id);\n        if (error) throw new Error(`Failed to update order: ${error.message}`);\n    }\n    async deleteOrder(id) {\n        const { error } = await supabase.from('orders').delete().eq('id', id);\n        if (error) throw new Error(`Failed to delete order: ${error.message}`);\n    }\n    async searchOrders(query) {\n        const { data, error } = await supabase.from('orders').select('*').or(`tracking_number.ilike.%${query}%,customer_name.ilike.%${query}%,customer_phone.ilike.%${query}%,address.ilike.%${query}%`).order('created_at', {\n            ascending: false\n        });\n        if (error) throw new Error(`Failed to search orders: ${error.message}`);\n        return data || [];\n    }\n}\n// Create service instances\nconst userService = new SupabaseUserService();\nconst orderService = new SupabaseOrderService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateCommission: () => (/* binding */ calculateCommission),\n/* harmony export */   calculateNetAmount: () => (/* binding */ calculateNetAmount),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatPhone: () => (/* binding */ formatPhone),\n/* harmony export */   generateTrackingNumber: () => (/* binding */ generateTrackingNumber),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getStatusLabel: () => (/* binding */ getStatusLabel),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateIraqiPhone: () => (/* binding */ validateIraqiPhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Format currency in Iraqi Dinar\nfunction formatCurrency(amount) {\n    return `${amount.toLocaleString('ar-IQ')} د.ع`;\n}\n// Format date in Arabic\nfunction formatDate(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleDateString('ar-IQ', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n}\n// Format date and time in Arabic\nfunction formatDateTime(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleString('ar-IQ', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    });\n}\n// Generate tracking number\nfunction generateTrackingNumber() {\n    const prefix = 'MRS';\n    const timestamp = Date.now().toString().slice(-6);\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n    return `${prefix}${timestamp}${random}`;\n}\n// Validate Iraqi phone number\nfunction validateIraqiPhone(phone) {\n    const phoneRegex = /^(07[3-9]|075)\\d{8}$/;\n    return phoneRegex.test(phone.replace(/\\s+/g, ''));\n}\n// Format phone number\nfunction formatPhone(phone) {\n    const cleaned = phone.replace(/\\D/g, '');\n    if (cleaned.length === 11 && cleaned.startsWith('07')) {\n        return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;\n    }\n    return phone;\n}\n// Calculate commission\nfunction calculateCommission(orderCount, commissionPerOrder = 1000) {\n    return orderCount * commissionPerOrder;\n}\n// Calculate net amount after commission\nfunction calculateNetAmount(totalAmount, commission) {\n    return totalAmount - commission;\n}\n// Truncate text\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n}\n// Get status color class\nfunction getStatusColor(status) {\n    const colors = {\n        pending: \"text-yellow-600 bg-yellow-100\",\n        assigned: \"text-blue-600 bg-blue-100\",\n        picked_up: \"text-purple-600 bg-purple-100\",\n        in_transit: \"text-orange-600 bg-orange-100\",\n        delivered: \"text-green-600 bg-green-100\",\n        returned: \"text-red-600 bg-red-100\",\n        cancelled: \"text-gray-600 bg-gray-100\",\n        postponed: \"text-gray-600 bg-gray-100\"\n    };\n    return colors[status] || \"text-gray-600 bg-gray-100\";\n}\n// Get status label in Arabic\nfunction getStatusLabel(status) {\n    const labels = {\n        pending: \"في الانتظار\",\n        assigned: \"مسند\",\n        picked_up: \"تم الاستلام\",\n        in_transit: \"في الطريق\",\n        delivered: \"تم التسليم\",\n        returned: \"راجع\",\n        cancelled: \"ملغي\",\n        postponed: \"مؤجل\"\n    };\n    return labels[status] || status;\n}\n// Sleep function for demos\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n// Check if running on mobile\nfunction isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n// Copy text to clipboard\nasync function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (err) {\n        console.error('Failed to copy text: ', err);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/roles.ts":
/*!****************************!*\
  !*** ./src/types/roles.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ORDER_STATUSES: () => (/* binding */ ORDER_STATUSES),\n/* harmony export */   Permission: () => (/* binding */ Permission),\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   canCreateRole: () => (/* binding */ canCreateRole),\n/* harmony export */   getAccessibleSections: () => (/* binding */ getAccessibleSections),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\n// نظام الأدوار والصلاحيات الشامل\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    // الأدوار الأساسية فقط\n    UserRole[\"MANAGER\"] = \"manager\";\n    UserRole[\"COURIER\"] = \"courier\";\n    UserRole[\"SUPERVISOR\"] = \"supervisor\"; // متابع\n    return UserRole;\n}({});\nvar Permission = /*#__PURE__*/ function(Permission) {\n    // إدارة الطلبات\n    Permission[\"CREATE_ORDER\"] = \"create_order\";\n    Permission[\"VIEW_ORDER\"] = \"view_order\";\n    Permission[\"UPDATE_ORDER\"] = \"update_order\";\n    Permission[\"DELETE_ORDER\"] = \"delete_order\";\n    Permission[\"ASSIGN_ORDER\"] = \"assign_order\";\n    Permission[\"TRANSFER_ORDER\"] = \"transfer_order\";\n    // إدارة المستخدمين\n    Permission[\"CREATE_USER\"] = \"create_user\";\n    Permission[\"VIEW_USER\"] = \"view_user\";\n    Permission[\"UPDATE_USER\"] = \"update_user\";\n    Permission[\"DELETE_USER\"] = \"delete_user\";\n    // إدارة الفروع والمراكز\n    Permission[\"MANAGE_BRANCHES\"] = \"manage_branches\";\n    Permission[\"MANAGE_PROVINCES\"] = \"manage_provinces\";\n    // المحاسبة\n    Permission[\"VIEW_ACCOUNTING\"] = \"view_accounting\";\n    Permission[\"PROCESS_ACCOUNTING\"] = \"process_accounting\";\n    // الإحصائيات\n    Permission[\"VIEW_STATISTICS\"] = \"view_statistics\";\n    Permission[\"VIEW_ALL_STATISTICS\"] = \"view_all_statistics\";\n    // الأرشيف\n    Permission[\"VIEW_ARCHIVE\"] = \"view_archive\";\n    Permission[\"MANAGE_ARCHIVE\"] = \"manage_archive\";\n    // التذاكر\n    Permission[\"CREATE_TICKET\"] = \"create_ticket\";\n    Permission[\"MANAGE_TICKETS\"] = \"manage_tickets\";\n    // الإعدادات\n    Permission[\"MANAGE_SETTINGS\"] = \"manage_settings\";\n    // المخزن\n    Permission[\"MANAGE_WAREHOUSE\"] = \"manage_warehouse\";\n    // الاستيراد والتصدير\n    Permission[\"IMPORT_ORDERS\"] = \"import_orders\";\n    Permission[\"EXPORT_ORDERS\"] = \"export_orders\";\n    return Permission;\n}({});\nconst ROLE_PERMISSIONS = {\n    [\"manager\"]: {\n        role: \"manager\",\n        permissions: [\n            \"create_order\",\n            \"view_order\",\n            \"update_order\",\n            \"delete_order\",\n            \"assign_order\",\n            \"transfer_order\",\n            \"create_user\",\n            \"view_user\",\n            \"update_user\",\n            \"delete_user\",\n            \"manage_branches\",\n            \"manage_provinces\",\n            \"view_accounting\",\n            \"process_accounting\",\n            \"view_all_statistics\",\n            \"view_archive\",\n            \"manage_archive\",\n            \"manage_tickets\",\n            \"manage_settings\",\n            \"manage_warehouse\",\n            \"import_orders\",\n            \"export_orders\"\n        ],\n        canCreateRoles: [\n            \"courier\",\n            \"supervisor\"\n        ],\n        accessibleSections: [\n            'orders',\n            'dispatch',\n            'returns',\n            'accounting',\n            'statistics',\n            'archive',\n            'users',\n            'import-export',\n            'notifications',\n            'settings'\n        ]\n    },\n    [\"supervisor\"]: {\n        role: \"supervisor\",\n        permissions: [\n            \"view_order\",\n            \"update_order\",\n            \"assign_order\",\n            \"manage_tickets\",\n            \"view_statistics\",\n            \"view_archive\"\n        ],\n        canCreateRoles: [],\n        accessibleSections: [\n            'orders',\n            'statistics',\n            'archive',\n            'notifications',\n            'settings'\n        ]\n    },\n    [\"courier\"]: {\n        role: \"courier\",\n        permissions: [\n            \"view_order\",\n            \"update_order\"\n        ],\n        canCreateRoles: [],\n        accessibleSections: [\n            'orders',\n            'archive',\n            'notifications',\n            'settings'\n        ]\n    }\n};\nconst ORDER_STATUSES = [\n    {\n        id: 'delivered',\n        name: 'تم التسليم',\n        color: 'bg-green-600',\n        description: 'تم تسليم الطلب بنجاح',\n        requiresPhoto: true,\n        requiresReason: false,\n        isDelivered: true,\n        isReturned: false\n    },\n    {\n        id: 'returned_to_courier',\n        name: 'راجع عند المندوب',\n        color: 'bg-amber-600',\n        description: 'الطلب راجع عند المندوب',\n        requiresPhoto: true,\n        requiresReason: true,\n        isDelivered: false,\n        isReturned: true\n    },\n    {\n        id: 'partial_delivery',\n        name: 'تسليم جزئي',\n        color: 'bg-blue-500',\n        description: 'تم تسليم جزء من الطلب',\n        requiresPhoto: true,\n        requiresReason: false,\n        isDelivered: true,\n        isReturned: false\n    },\n    {\n        id: 'price_change',\n        name: 'تغيير السعر',\n        color: 'bg-purple-500',\n        description: 'تم تغيير سعر الطلب',\n        requiresPhoto: false,\n        requiresReason: true,\n        isDelivered: false,\n        isReturned: false\n    },\n    {\n        id: 'postponed',\n        name: 'مؤجل',\n        color: 'bg-gray-500',\n        description: 'تم تأجيل التسليم',\n        requiresPhoto: true,\n        requiresReason: true,\n        isDelivered: false,\n        isReturned: false\n    }\n];\nfunction hasPermission(userRole, permission) {\n    return ROLE_PERMISSIONS[userRole]?.permissions.includes(permission) || false;\n}\nfunction canCreateRole(userRole, targetRole) {\n    return ROLE_PERMISSIONS[userRole]?.canCreateRoles.includes(targetRole) || false;\n}\nfunction getAccessibleSections(userRole) {\n    return ROLE_PERMISSIONS[userRole]?.accessibleSections || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/roles.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Farchive%2Fpage&page=%2Farchive%2Fpage&appPaths=%2Farchive%2Fpage&pagePath=private-next-app-dir%2Farchive%2Fpage.tsx&appDir=E%3A%5CMarsal%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMarsal%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();