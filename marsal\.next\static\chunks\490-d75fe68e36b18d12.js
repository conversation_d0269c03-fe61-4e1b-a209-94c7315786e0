"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[490],{968:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(2115),o=n(3655),i=n(5155),l=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},1154:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2318:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},3655:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(2115),o=n(7650),i=n(9708),l=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},4616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},7830:(e,t,n)=>{n.d(t,{UC:()=>rg,YJ:()=>rw,In:()=>rv,q7:()=>rb,VF:()=>rS,p4:()=>rE,JU:()=>rx,ZL:()=>rm,bL:()=>rf,wn:()=>rR,PP:()=>rC,wv:()=>rT,l9:()=>rp,WT:()=>rh,LM:()=>ry});var r,o,i,l,a=n(2115),u=n.t(a,2),c=n(7650);function s(e,[t,n]){return Math.min(n,Math.max(t,e))}function d(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function f(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function p(e,t){var n=f(e,t,"get");return n.get?n.get.call(e):n.value}function h(e,t,n){var r=f(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var v=n(5155);function m(e,t=[]){let n=[],r=()=>{let t=n.map(e=>a.createContext(e));return function(n){let r=n?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=a.createContext(r),i=n.length;n=[...n,r];let l=t=>{let{scope:n,children:r,...l}=t,u=n?.[e]?.[i]||o,c=a.useMemo(()=>l,Object.values(l));return(0,v.jsx)(u.Provider,{value:c,children:r})};return l.displayName=t+"Provider",[l,function(n,l){let u=l?.[e]?.[i]||o,c=a.useContext(u);if(c)return c;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var g=n(6101),y=n(9708),w=new WeakMap;function x(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=b(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function b(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap;var E=a.createContext(void 0),S=n(3655);function C(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}var R="dismissableLayer.update",T=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),A=a.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:c,onInteractOutside:s,onDismiss:f,...p}=e,h=a.useContext(T),[m,y]=a.useState(null),w=null!=(r=null==m?void 0:m.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,x]=a.useState({}),b=(0,g.s)(t,e=>y(e)),E=Array.from(h.layers),[A]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),P=E.indexOf(A),M=m?E.indexOf(m):-1,N=h.layersWithOutsidePointerEventsDisabled.size>0,D=M>=P,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=C(e),o=a.useRef(!1),i=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){k("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...h.branches].some(e=>e.contains(t));D&&!n&&(null==u||u(e),null==s||s(e),e.defaultPrevented||null==f||f())},w),O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=C(e),o=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!o.current&&k("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...h.branches].some(e=>e.contains(t))&&(null==c||c(e),null==s||s(e),e.defaultPrevented||null==f||f())},w);return!function(e,t=globalThis?.document){let n=C(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{M===h.layers.size-1&&(null==l||l(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},w),a.useEffect(()=>{if(m)return o&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(i=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(m)),h.layers.add(m),L(),()=>{o&&1===h.layersWithOutsidePointerEventsDisabled.size&&(w.body.style.pointerEvents=i)}},[m,w,o,h]),a.useEffect(()=>()=>{m&&(h.layers.delete(m),h.layersWithOutsidePointerEventsDisabled.delete(m),L())},[m,h]),a.useEffect(()=>{let e=()=>x({});return document.addEventListener(R,e),()=>document.removeEventListener(R,e)},[]),(0,v.jsx)(S.sG.div,{...p,ref:b,style:{pointerEvents:N?D?"auto":"none":void 0,...e.style},onFocusCapture:d(e.onFocusCapture,O.onFocusCapture),onBlurCapture:d(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:d(e.onPointerDownCapture,j.onPointerDownCapture)})});function L(){let e=new CustomEvent(R);document.dispatchEvent(e)}function k(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,S.hO)(i,l):i.dispatchEvent(l)}A.displayName="DismissableLayer",a.forwardRef((e,t)=>{let n=a.useContext(T),r=a.useRef(null),o=(0,g.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,v.jsx)(S.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var P=0;function M(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var N="focusScope.autoFocusOnMount",D="focusScope.autoFocusOnUnmount",j={bubbles:!1,cancelable:!0},O=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[u,c]=a.useState(null),s=C(o),d=C(i),f=a.useRef(null),p=(0,g.s)(t,e=>c(e)),h=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let e=function(e){if(h.paused||!u)return;let t=e.target;u.contains(t)?f.current=t:F(f.current,{select:!0})},t=function(e){if(h.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||F(f.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&F(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,h.paused]),a.useEffect(()=>{if(u){H.add(h);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(N,j);u.addEventListener(N,s),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(F(r,{select:t}),document.activeElement!==n)return}(I(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&F(u))}return()=>{u.removeEventListener(N,s),setTimeout(()=>{let t=new CustomEvent(D,j);u.addEventListener(D,d),u.dispatchEvent(t),t.defaultPrevented||F(null!=e?e:document.body,{select:!0}),u.removeEventListener(D,d),H.remove(h)},0)}}},[u,s,d,h]);let m=a.useCallback(e=>{if(!n&&!r||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=I(e);return[W(t,e),W(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&F(i,{select:!0})):(e.preventDefault(),n&&F(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,h.paused]);return(0,v.jsx)(S.sG.div,{tabIndex:-1,...l,ref:p,onKeyDown:m})});function I(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function W(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function F(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}O.displayName="FocusScope";var H=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=B(e,t)).unshift(t)},remove(t){var n;null==(n=(e=B(e,t))[0])||n.resume()}}}();function B(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var _=globalThis?.document?a.useLayoutEffect:()=>{},z=u[" useId ".trim().toString()]||(()=>void 0),V=0;function G(e){let[t,n]=a.useState(z());return _(()=>{e||n(e=>e??String(V++))},[e]),e||(t?`radix-${t}`:"")}let K=["top","right","bottom","left"],Y=Math.min,X=Math.max,U=Math.round,$=Math.floor,q=e=>({x:e,y:e}),Z={left:"right",right:"left",bottom:"top",top:"bottom"},J={start:"end",end:"start"};function Q(e,t){return"function"==typeof e?e(t):e}function ee(e){return e.split("-")[0]}function et(e){return e.split("-")[1]}function en(e){return"x"===e?"y":"x"}function er(e){return"y"===e?"height":"width"}let eo=new Set(["top","bottom"]);function ei(e){return eo.has(ee(e))?"y":"x"}function el(e){return e.replace(/start|end/g,e=>J[e])}let ea=["left","right"],eu=["right","left"],ec=["top","bottom"],es=["bottom","top"];function ed(e){return e.replace(/left|right|bottom|top/g,e=>Z[e])}function ef(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ep(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function eh(e,t,n){let r,{reference:o,floating:i}=e,l=ei(t),a=en(ei(t)),u=er(a),c=ee(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(et(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let ev=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=eh(c,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:v}=a[n],{x:m,y:g,data:y,reset:w}=await v({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=eh(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function em(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=Q(t,e),h=ef(p),v=a[f?"floating"===d?"reference":"floating":d],m=ep(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=ep(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(m.top-x.top+h.top)/w.y,bottom:(x.bottom-m.bottom+h.bottom)/w.y,left:(m.left-x.left+h.left)/w.x,right:(x.right-m.right+h.right)/w.x}}function eg(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ey(e){return K.some(t=>e[t]>=0)}let ew=new Set(["left","top"]);async function ex(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=ee(n),a=et(n),u="y"===ei(n),c=ew.has(l)?-1:1,s=i&&u?-1:1,d=Q(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function eb(){return"undefined"!=typeof window}function eE(e){return eR(e)?(e.nodeName||"").toLowerCase():"#document"}function eS(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eC(e){var t;return null==(t=(eR(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eR(e){return!!eb()&&(e instanceof Node||e instanceof eS(e).Node)}function eT(e){return!!eb()&&(e instanceof Element||e instanceof eS(e).Element)}function eA(e){return!!eb()&&(e instanceof HTMLElement||e instanceof eS(e).HTMLElement)}function eL(e){return!!eb()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof eS(e).ShadowRoot)}let ek=new Set(["inline","contents"]);function eP(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=e_(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!ek.has(o)}let eM=new Set(["table","td","th"]),eN=[":popover-open",":modal"];function eD(e){return eN.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let ej=["transform","translate","scale","rotate","perspective"],eO=["transform","translate","scale","rotate","perspective","filter"],eI=["paint","layout","strict","content"];function eW(e){let t=eF(),n=eT(e)?e_(e):e;return ej.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||eO.some(e=>(n.willChange||"").includes(e))||eI.some(e=>(n.contain||"").includes(e))}function eF(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eH=new Set(["html","body","#document"]);function eB(e){return eH.has(eE(e))}function e_(e){return eS(e).getComputedStyle(e)}function ez(e){return eT(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eV(e){if("html"===eE(e))return e;let t=e.assignedSlot||e.parentNode||eL(e)&&e.host||eC(e);return eL(t)?t.host:t}function eG(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eV(t);return eB(n)?t.ownerDocument?t.ownerDocument.body:t.body:eA(n)&&eP(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=eS(o);if(i){let e=eK(l);return t.concat(l,l.visualViewport||[],eP(o)?o:[],e&&n?eG(e):[])}return t.concat(o,eG(o,[],n))}function eK(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eY(e){let t=e_(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eA(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=U(n)!==i||U(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eX(e){return eT(e)?e:e.contextElement}function eU(e){let t=eX(e);if(!eA(t))return q(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eY(t),l=(i?U(n.width):n.width)/r,a=(i?U(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let e$=q(0);function eq(e){let t=eS(e);return eF()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:e$}function eZ(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eX(e),a=q(1);t&&(r?eT(r)&&(a=eU(r)):a=eU(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===eS(l))&&o)?eq(l):q(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=eS(l),t=r&&eT(r)?eS(r):r,n=e,o=eK(n);for(;o&&r&&t!==n;){let e=eU(o),t=o.getBoundingClientRect(),r=e_(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=l,o=eK(n=eS(o))}}return ep({width:d,height:f,x:c,y:s})}function eJ(e,t){let n=ez(e).scrollLeft;return t?t.left+n:eZ(eC(e)).left+n}function eQ(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eJ(e,r)),y:r.top+t.scrollTop}}let e0=new Set(["absolute","fixed"]);function e1(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=eS(e),r=eC(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eF();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=eC(e),n=ez(e),r=e.ownerDocument.body,o=X(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=X(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eJ(e),a=-n.scrollTop;return"rtl"===e_(r).direction&&(l+=X(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(eC(e));else if(eT(t))r=function(e,t){let n=eZ(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=eA(e)?eU(e):q(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=eq(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ep(r)}function e2(e){return"static"===e_(e).position}function e5(e,t){if(!eA(e)||"fixed"===e_(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eC(e)===n&&(n=n.ownerDocument.body),n}function e9(e,t){var n;let r=eS(e);if(eD(e))return r;if(!eA(e)){let t=eV(e);for(;t&&!eB(t);){if(eT(t)&&!e2(t))return t;t=eV(t)}return r}let o=e5(e,t);for(;o&&(n=o,eM.has(eE(n)))&&e2(o);)o=e5(o,t);return o&&eB(o)&&e2(o)&&!eW(o)?r:o||function(e){let t=eV(e);for(;eA(t)&&!eB(t);){if(eW(t))return t;if(eD(t))break;t=eV(t)}return null}(e)||r}let e6=async function(e){let t=this.getOffsetParent||e9,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eA(t),o=eC(t),i="fixed"===n,l=eZ(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=q(0);if(r||!r&&!i)if(("body"!==eE(t)||eP(o))&&(a=ez(t)),r){let e=eZ(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eJ(o));i&&!r&&o&&(u.x=eJ(o));let c=!o||r||i?q(0):eQ(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},e4={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=eC(r),a=!!t&&eD(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=q(1),s=q(0),d=eA(r);if((d||!d&&!i)&&(("body"!==eE(r)||eP(l))&&(u=ez(r)),eA(r))){let e=eZ(r);c=eU(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!l||d||i?q(0):eQ(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:eC,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?eD(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eG(e,[],!1).filter(e=>eT(e)&&"body"!==eE(e)),o=null,i="fixed"===e_(e).position,l=i?eV(e):e;for(;eT(l)&&!eB(l);){let t=e_(l),n=eW(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&e0.has(o.position)||eP(l)&&!n&&function e(t,n){let r=eV(t);return!(r===n||!eT(r)||eB(r))&&("fixed"===e_(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eV(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=e1(t,n,o);return e.top=X(r.top,e.top),e.right=Y(r.right,e.right),e.bottom=Y(r.bottom,e.bottom),e.left=X(r.left,e.left),e},e1(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:e9,getElementRects:e6,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eY(e);return{width:t,height:n}},getScale:eU,isElement:eT,isRTL:function(e){return"rtl"===e_(e).direction}};function e3(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e8=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=Q(e,t)||{};if(null==c)return{};let d=ef(s),f={x:n,y:r},p=en(ei(o)),h=er(p),v=await l.getDimensions(c),m="y"===p,g=m?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-f[p]-i.floating[h],w=f[p]-i.reference[p],x=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),b=x?x[g]:0;b&&await (null==l.isElement?void 0:l.isElement(x))||(b=a.floating[g]||i.floating[h]);let E=b/2-v[h]/2-1,S=Y(d[m?"top":"left"],E),C=Y(d[m?"bottom":"right"],E),R=b-v[h]-C,T=b/2-v[h]/2+(y/2-w/2),A=X(S,Y(T,R)),L=!u.arrow&&null!=et(o)&&T!==A&&i.reference[h]/2-(T<S?S:C)-v[h]/2<0,k=L?T<S?T-S:T-R:0;return{[p]:f[p]+k,data:{[p]:A,centerOffset:T-A-k,...L&&{alignmentOffset:k}},reset:L}}}),e7=(e,t,n)=>{let r=new Map,o={platform:e4,...n},i={...o.platform,_c:r};return ev(e,t,{...o,platform:i})};var te="undefined"!=typeof document?a.useLayoutEffect:function(){};function tt(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!tt(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!tt(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function tn(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function tr(e,t){let n=tn(e);return Math.round(t*n)/n}function to(e){let t=a.useRef(e);return te(()=>{t.current=e}),t}let ti=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?e8({element:n.current,padding:r}).fn(t):{}:n?e8({element:n,padding:r}).fn(t):{}}}),tl=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await ex(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),ta=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=Q(e,t),c={x:n,y:r},s=await em(t,u),d=ei(ee(o)),f=en(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=X(n,Y(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=X(n,Y(h,r))}let v=a.fn({...t,[f]:p,[d]:h});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),tu=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=Q(e,t),s={x:n,y:r},d=ei(o),f=en(d),p=s[f],h=s[d],v=Q(a,t),m="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+m.mainAxis,n=i.reference[f]+i.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=ew.has(ee(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:m.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?m.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),tc=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:v,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=Q(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let x=ee(a),b=ei(s),E=ee(s)===s,S=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=v||(E||!y?[ed(s)]:function(e){let t=ed(e);return[el(e),t,el(t)]}(s)),R="none"!==g;!v&&R&&C.push(...function(e,t,n,r){let o=et(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?eu:ea;return t?ea:eu;case"left":case"right":return t?ec:es;default:return[]}}(ee(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(el)))),i}(s,y,g,S));let T=[s,...C],A=await em(t,w),L=[],k=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&L.push(A[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=et(e),o=en(ei(e)),i=er(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=ed(l)),[l,ed(l)]}(a,c,S);L.push(A[e[0]],A[e[1]])}if(k=[...k,{placement:a,overflows:L}],!L.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=T[e];if(t&&("alignment"!==h||b===ei(t)||k.every(e=>e.overflows[0]>0&&ei(e.placement)===b)))return{data:{index:e,overflows:k},reset:{placement:t}};let n=null==(i=k.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(m){case"bestFit":{let e=null==(l=k.filter(e=>{if(R){let t=ei(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ts=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:l,rects:a,platform:u,elements:c}=t,{apply:s=()=>{},...d}=Q(e,t),f=await em(t,d),p=ee(l),h=et(l),v="y"===ei(l),{width:m,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,w=m-f.left-f.right,x=Y(g-f[o],y),b=Y(m-f[i],w),E=!t.middlewareData.shift,S=x,C=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!h){let e=X(f.left,0),t=X(f.right,0),n=X(f.top,0),r=X(f.bottom,0);v?C=m-2*(0!==e||0!==t?e+t:X(f.left,f.right)):S=g-2*(0!==n||0!==r?n+r:X(f.top,f.bottom))}await s({...t,availableWidth:C,availableHeight:S});let R=await u.getDimensions(c.floating);return m!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),td=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=Q(e,t);switch(r){case"referenceHidden":{let e=eg(await em(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ey(e)}}}case"escaped":{let e=eg(await em(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:ey(e)}}}default:return{}}}}}(e),options:[e,t]}),tf=(e,t)=>({...ti(e),options:[e,t]});var tp=a.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,v.jsx)(S.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,v.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tp.displayName="Arrow";var th="Popper",[tv,tm]=m(th),[tg,ty]=tv(th),tw=e=>{let{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return(0,v.jsx)(tg,{scope:t,anchor:r,onAnchorChange:o,children:n})};tw.displayName=th;var tx="PopperAnchor",tb=a.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=ty(tx,n),l=a.useRef(null),u=(0,g.s)(t,l);return a.useEffect(()=>{i.onAnchorChange((null==r?void 0:r.current)||l.current)}),r?null:(0,v.jsx)(S.sG.div,{...o,ref:u})});tb.displayName=tx;var tE="PopperContent",[tS,tC]=tv(tE),tR=a.forwardRef((e,t)=>{var n,r,o,i,l,u,s,d;let{__scopePopper:f,side:p="bottom",sideOffset:h=0,align:m="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:T=!1,updatePositionStrategy:A="optimized",onPlaced:L,...k}=e,P=ty(tE,f),[M,N]=a.useState(null),D=(0,g.s)(t,e=>N(e)),[j,O]=a.useState(null),I=function(e){let[t,n]=a.useState(void 0);return _(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(j),W=null!=(s=null==I?void 0:I.width)?s:0,F=null!=(d=null==I?void 0:I.height)?d:0,H="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},B=Array.isArray(b)?b:[b],z=B.length>0,V={padding:H,boundary:B.filter(tk),altBoundary:z},{refs:G,floatingStyles:K,placement:U,isPositioned:q,middlewareData:Z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:u=!0,whileElementsMounted:s,open:d}=e,[f,p]=a.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,v]=a.useState(r);tt(h,r)||v(r);let[m,g]=a.useState(null),[y,w]=a.useState(null),x=a.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),b=a.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=i||m,S=l||y,C=a.useRef(null),R=a.useRef(null),T=a.useRef(f),A=null!=s,L=to(s),k=to(o),P=to(d),M=a.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:h};k.current&&(e.platform=k.current),e7(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};N.current&&!tt(T.current,t)&&(T.current=t,c.flushSync(()=>{p(t)}))})},[h,t,n,k,P]);te(()=>{!1===d&&T.current.isPositioned&&(T.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let N=a.useRef(!1);te(()=>(N.current=!0,()=>{N.current=!1}),[]),te(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(L.current)return L.current(E,S,M);M()}},[E,S,M,L,A]);let D=a.useMemo(()=>({reference:C,floating:R,setReference:x,setFloating:b}),[x,b]),j=a.useMemo(()=>({reference:E,floating:S}),[E,S]),O=a.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=tr(j.floating,f.x),r=tr(j.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...tn(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,j.floating,f.x,f.y]);return a.useMemo(()=>({...f,update:M,refs:D,elements:j,floatingStyles:O}),[f,M,D,j,O])}({strategy:"fixed",placement:p+("center"!==m?"-"+m:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eX(e),d=i||l?[...s?eG(s):[],...eG(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=eC(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let h=$(d),v=$(o.clientWidth-(s+f)),m={rootMargin:-h+"px "+-v+"px "+-$(o.clientHeight-(d+p))+"px "+-$(s)+"px",threshold:X(0,Y(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||e3(c,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(y,{...m,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,m)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let v=c?eZ(e):null;return c&&function t(){let r=eZ(e);v&&!e3(v,r)&&n(),v=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:P.anchor},middleware:[tl({mainAxis:h+F,alignmentAxis:y}),x&&ta({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?tu():void 0,...V}),x&&tc({...V}),ts({...V,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),j&&tf({element:j,padding:w}),tP({arrowWidth:W,arrowHeight:F}),T&&td({strategy:"referenceHidden",...V})]}),[J,Q]=tM(U),ee=C(L);_(()=>{q&&(null==ee||ee())},[q,ee]);let et=null==(n=Z.arrow)?void 0:n.x,en=null==(r=Z.arrow)?void 0:r.y,er=(null==(o=Z.arrow)?void 0:o.centerOffset)!==0,[eo,ei]=a.useState();return _(()=>{M&&ei(window.getComputedStyle(M).zIndex)},[M]),(0,v.jsx)("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:q?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eo,"--radix-popper-transform-origin":[null==(i=Z.transformOrigin)?void 0:i.x,null==(l=Z.transformOrigin)?void 0:l.y].join(" "),...(null==(u=Z.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,v.jsx)(tS,{scope:f,placedSide:J,onArrowChange:O,arrowX:et,arrowY:en,shouldHideArrow:er,children:(0,v.jsx)(S.sG.div,{"data-side":J,"data-align":Q,...k,ref:D,style:{...k.style,animation:q?void 0:"none"}})})})});tR.displayName=tE;var tT="PopperArrow",tA={top:"bottom",right:"left",bottom:"top",left:"right"},tL=a.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tC(tT,n),i=tA[o.placedSide];return(0,v.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,v.jsx)(tp,{...r,ref:t,style:{...r.style,display:"block"}})})});function tk(e){return null!==e}tL.displayName=tT;var tP=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,h]=tM(a),v={start:"0%",center:"50%",end:"100%"}[h],m=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(l=null==(o=c.arrow)?void 0:o.y)?l:0)+f/2,y="",w="";return"bottom"===p?(y=s?v:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=s?v:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?v:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?v:"".concat(g,"px")),{data:{x:y,y:w}}}});function tM(e){let[t,n="center"]=e.split("-");return[t,n]}var tN=a.forwardRef((e,t)=>{var n,r;let{container:o,...i}=e,[l,u]=a.useState(!1);_(()=>u(!0),[]);let s=o||l&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return s?c.createPortal((0,v.jsx)(S.sG.div,{...i,ref:t}),s):null});tN.displayName="Portal";var tD=u[" useInsertionEffect ".trim().toString()]||_;function tj({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,l]=function({defaultProp:e,onChange:t}){let[n,r]=a.useState(e),o=a.useRef(n),i=a.useRef(t);return tD(()=>{i.current=t},[t]),a.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:o;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,a.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else i(t)},[u,e,i,l])]}Symbol("RADIX:SYNC_STATE");var tO=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});a.forwardRef((e,t)=>(0,v.jsx)(S.sG.span,{...e,ref:t,style:{...tO,...e.style}})).displayName="VisuallyHidden";var tI=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tW=new WeakMap,tF=new WeakMap,tH={},tB=0,t_=function(e){return e&&(e.host||t_(e.parentNode))},tz=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=t_(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tH[n]||(tH[n]=new WeakMap);var i=tH[n],l=[],a=new Set,u=new Set(o),c=function(e){!e||a.has(e)||(a.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tW.get(e)||0)+1,c=(i.get(e)||0)+1;tW.set(e,u),i.set(e,c),l.push(e),1===u&&o&&tF.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),a.clear(),tB++,function(){l.forEach(function(e){var t=tW.get(e)-1,o=i.get(e)-1;tW.set(e,t),i.set(e,o),t||(tF.has(e)||e.removeAttribute(r),tF.delete(e)),o||e.removeAttribute(n)}),--tB||(tW=new WeakMap,tW=new WeakMap,tF=new WeakMap,tH={})}},tV=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||tI(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tz(r,o,n,"aria-hidden")):function(){return null}},tG=n(9249),tK="right-scroll-bar-position",tY="width-before-scroll-bar";function tX(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tU="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,t$=new WeakMap;function tq(e){return e}var tZ=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=tq),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return i.options=(0,tG.Cl)({async:!0,ssr:!1},e),i}(),tJ=function(){},tQ=a.forwardRef(function(e,t){var n,r,o,i,l=a.useRef(null),u=a.useState({onScrollCapture:tJ,onWheelCapture:tJ,onTouchMoveCapture:tJ}),c=u[0],s=u[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,v=e.enabled,m=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,x=e.inert,b=e.allowPinchZoom,E=e.as,S=e.gapMode,C=(0,tG.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[l,t],r=function(e){return n.forEach(function(t){return tX(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tU(function(){var e=t$.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tX(e,null)}),r.forEach(function(e){t.has(e)||tX(e,o)})}t$.set(i,n)},[n]),i),T=(0,tG.Cl)((0,tG.Cl)({},C),c);return a.createElement(a.Fragment,null,v&&a.createElement(g,{sideCar:tZ,removeScrollBar:h,shards:m,noRelative:y,noIsolation:w,inert:x,setCallbacks:s,allowPinchZoom:!!b,lockRef:l,gapMode:S}),d?a.cloneElement(a.Children.only(f),(0,tG.Cl)((0,tG.Cl)({},T),{ref:R})):a.createElement(void 0===E?"div":E,(0,tG.Cl)({},T,{className:p,ref:R}),f))});tQ.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tQ.classNames={fullWidth:tY,zeroRight:tK};var t0=function(e){var t=e.sideCar,n=(0,tG.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,(0,tG.Cl)({},n))};t0.isSideCarExport=!0;var t1=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=l||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},t2=function(){var e=t1();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},t5=function(){var e=t2();return function(t){return e(t.styles,t.dynamic),null}},t9={left:0,top:0,right:0,gap:0},t6=function(e){return parseInt(e||"",10)||0},t4=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[t6(n),t6(r),t6(o)]},t3=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t9;var t=t4(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},t8=t5(),t7="data-scroll-locked",ne=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(t7,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tK," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tY," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tK," .").concat(tK," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tY," .").concat(tY," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(t7,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},nt=function(){var e=parseInt(document.body.getAttribute(t7)||"0",10);return isFinite(e)?e:0},nn=function(){a.useEffect(function(){return document.body.setAttribute(t7,(nt()+1).toString()),function(){var e=nt()-1;e<=0?document.body.removeAttribute(t7):document.body.setAttribute(t7,e.toString())}},[])},nr=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;nn();var i=a.useMemo(function(){return t3(o)},[o]);return a.createElement(t8,{styles:ne(i,!t,o,n?"":"!important")})},no=!1;if("undefined"!=typeof window)try{var ni=Object.defineProperty({},"passive",{get:function(){return no=!0,!0}});window.addEventListener("test",ni,ni),window.removeEventListener("test",ni,ni)}catch(e){no=!1}var nl=!!no&&{passive:!1},na=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},nu=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),nc(e,r)){var o=ns(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},nc=function(e,t){return"v"===e?na(t,"overflowY"):na(t,"overflowX")},ns=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nd=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=ns(e,u),v=h[0],m=h[1]-h[2]-l*v;(v||m)&&nc(e,u)&&(f+=m,p+=v);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},nf=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},np=function(e){return[e.deltaX,e.deltaY]},nh=function(e){return e&&"current"in e?e.current:e},nv=0,nm=[];let ng=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(nv++)[0],i=a.useState(t5)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,tG.fX)([e.lockRef.current],(e.shards||[]).map(nh),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=nf(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=nu(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=nu(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return nd(p,t,e,"h"===p?u:c,!0)},[]),c=a.useCallback(function(e){if(nm.length&&nm[nm.length-1]===i){var n="deltaY"in e?np(e):nf(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(nh).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=nf(e),r.current=void 0},[]),f=a.useCallback(function(t){s(t.type,np(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,nf(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return nm.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,nl),document.addEventListener("touchmove",c,nl),document.addEventListener("touchstart",d,nl),function(){nm=nm.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,nl),document.removeEventListener("touchmove",c,nl),document.removeEventListener("touchstart",d,nl)}},[]);var h=e.removeScrollBar,v=e.inert;return a.createElement(a.Fragment,null,v?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(nr,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tZ.useMedium(r),t0);var ny=a.forwardRef(function(e,t){return a.createElement(tQ,(0,tG.Cl)({},e,{ref:t,sideCar:ng}))});ny.classNames=tQ.classNames;var nw=[" ","Enter","ArrowUp","ArrowDown"],nx=[" ","Enter"],nb="Select",[nE,nS,nC]=function(e){let t=e+"CollectionProvider",[n,r]=m(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=a.useRef(null),i=a.useRef(new Map).current;return(0,v.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};l.displayName=t;let u=e+"CollectionSlot",c=(0,y.TL)(u),s=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(u,n),l=(0,g.s)(t,o.collectionRef);return(0,v.jsx)(c,{ref:l,children:r})});s.displayName=u;let d=e+"CollectionItemSlot",f="data-radix-collection-item",p=(0,y.TL)(d),h=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,l=a.useRef(null),u=(0,g.s)(t,l),c=i(d,n);return a.useEffect(()=>(c.itemMap.set(l,{ref:l,...o}),()=>void c.itemMap.delete(l))),(0,v.jsx)(p,{...{[f]:""},ref:u,children:r})});return h.displayName=d,[{Provider:l,Slot:s,ItemSlot:h},function(t){let n=i(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(f,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(nb),[nR,nT]=m(nb,[nC,tm]),nA=tm(),[nL,nk]=nR(nb),[nP,nM]=nR(nb),nN=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:l,defaultValue:u,onValueChange:c,dir:s,name:d,autoComplete:f,disabled:p,required:h,form:m}=e,g=nA(t),[y,w]=a.useState(null),[x,b]=a.useState(null),[S,C]=a.useState(!1),R=function(e){let t=a.useContext(E);return e||t||"ltr"}(s),[T,A]=tj({prop:r,defaultProp:null!=o&&o,onChange:i,caller:nb}),[L,k]=tj({prop:l,defaultProp:u,onChange:c,caller:nb}),P=a.useRef(null),M=!y||m||!!y.closest("form"),[N,D]=a.useState(new Set),j=Array.from(N).map(e=>e.props.value).join(";");return(0,v.jsx)(tw,{...g,children:(0,v.jsxs)(nL,{required:h,scope:t,trigger:y,onTriggerChange:w,valueNode:x,onValueNodeChange:b,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:G(),value:L,onValueChange:k,open:T,onOpenChange:A,dir:R,triggerPointerDownPosRef:P,disabled:p,children:[(0,v.jsx)(nE.Provider,{scope:t,children:(0,v.jsx)(nP,{scope:e.__scopeSelect,onNativeOptionAdd:a.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:a.useCallback(e=>{D(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),M?(0,v.jsxs)(ru,{"aria-hidden":!0,required:h,tabIndex:-1,name:d,autoComplete:f,value:L,onChange:e=>k(e.target.value),disabled:p,form:m,children:[void 0===L?(0,v.jsx)("option",{value:""}):null,Array.from(N)]},j):null]})})};nN.displayName=nb;var nD="SelectTrigger",nj=a.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=nA(n),l=nk(nD,n),u=l.disabled||r,c=(0,g.s)(t,l.onTriggerChange),s=nS(n),f=a.useRef("touch"),[p,h,m]=rs(e=>{let t=s().filter(e=>!e.disabled),n=t.find(e=>e.value===l.value),r=rd(t,e,n);void 0!==r&&l.onValueChange(r.value)}),y=e=>{u||(l.onOpenChange(!0),m()),e&&(l.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,v.jsx)(tb,{asChild:!0,...i,children:(0,v.jsx)(S.sG.button,{type:"button",role:"combobox","aria-controls":l.contentId,"aria-expanded":l.open,"aria-required":l.required,"aria-autocomplete":"none",dir:l.dir,"data-state":l.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":rc(l.value)?"":void 0,...o,ref:c,onClick:d(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:d(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:d(o.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&nw.includes(e.key)&&(y(),e.preventDefault())})})})});nj.displayName=nD;var nO="SelectValue",nI=a.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=nk(nO,n),{onValueNodeHasChildrenChange:c}=u,s=void 0!==i,d=(0,g.s)(t,u.onValueNodeChange);return _(()=>{c(s)},[c,s]),(0,v.jsx)(S.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:rc(u.value)?(0,v.jsx)(v.Fragment,{children:l}):i})});nI.displayName=nO;var nW=a.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,v.jsx)(S.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nW.displayName="SelectIcon";var nF=e=>(0,v.jsx)(tN,{asChild:!0,...e});nF.displayName="SelectPortal";var nH="SelectContent",nB=a.forwardRef((e,t)=>{let n=nk(nH,e.__scopeSelect),[r,o]=a.useState();return(_(()=>{o(new DocumentFragment)},[]),n.open)?(0,v.jsx)(nG,{...e,ref:t}):r?c.createPortal((0,v.jsx)(n_,{scope:e.__scopeSelect,children:(0,v.jsx)(nE.Slot,{scope:e.__scopeSelect,children:(0,v.jsx)("div",{children:e.children})})}),r):null});nB.displayName=nH;var[n_,nz]=nR(nH),nV=(0,y.TL)("SelectContent.RemoveScroll"),nG=a.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:l,side:u,sideOffset:c,align:s,alignOffset:f,arrowPadding:p,collisionBoundary:h,collisionPadding:m,sticky:y,hideWhenDetached:w,avoidCollisions:x,...b}=e,E=nk(nH,n),[S,C]=a.useState(null),[R,T]=a.useState(null),L=(0,g.s)(t,e=>C(e)),[k,N]=a.useState(null),[D,j]=a.useState(null),I=nS(n),[W,F]=a.useState(!1),H=a.useRef(!1);a.useEffect(()=>{if(S)return tV(S)},[S]),a.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:M()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:M()),P++,()=>{1===P&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),P--}},[]);let B=a.useCallback(e=>{let[t,...n]=I().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&R&&(R.scrollTop=0),n===r&&R&&(R.scrollTop=R.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[I,R]),_=a.useCallback(()=>B([k,S]),[B,k,S]);a.useEffect(()=>{W&&_()},[W,_]);let{onOpenChange:z,triggerPointerDownPosRef:V}=E;a.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=V.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(i=null==(r=V.current)?void 0:r.y)?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||z(!1),document.removeEventListener("pointermove",t),V.current=null};return null!==V.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,z,V]),a.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[G,K]=rs(e=>{let t=I().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=rd(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),Y=a.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==E.value&&E.value===t||r)&&(N(e),r&&(H.current=!0))},[E.value]),X=a.useCallback(()=>null==S?void 0:S.focus(),[S]),U=a.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==E.value&&E.value===t||r)&&j(e)},[E.value]),$="popper"===r?nY:nK,q=$===nY?{side:u,sideOffset:c,align:s,alignOffset:f,arrowPadding:p,collisionBoundary:h,collisionPadding:m,sticky:y,hideWhenDetached:w,avoidCollisions:x}:{};return(0,v.jsx)(n_,{scope:n,content:S,viewport:R,onViewportChange:T,itemRefCallback:Y,selectedItem:k,onItemLeave:X,itemTextRefCallback:U,focusSelectedItem:_,selectedItemText:D,position:r,isPositioned:W,searchRef:G,children:(0,v.jsx)(ny,{as:nV,allowPinchZoom:!0,children:(0,v.jsx)(O,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:d(o,e=>{var t;null==(t=E.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,v.jsx)(A,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,v.jsx)($,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...b,...q,onPlaced:()=>F(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:d(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||K(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=I().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});nG.displayName="SelectContentImpl";var nK=a.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nk(nH,n),l=nz(nH,n),[u,c]=a.useState(null),[d,f]=a.useState(null),p=(0,g.s)(t,e=>f(e)),h=nS(n),m=a.useRef(!1),y=a.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:b,focusSelectedItem:E}=l,C=a.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&d&&w&&x&&b){let e=i.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=b.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,a=e.width+l,c=Math.max(a,t.width),d=s(i,[10,Math.max(10,window.innerWidth-10-c)]);u.style.minWidth=a+"px",u.style.left=d+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,a=e.width+l,c=Math.max(a,t.width),d=s(i,[10,Math.max(10,window.innerWidth-10-c)]);u.style.minWidth=a+"px",u.style.right=d+"px"}let l=h(),a=window.innerHeight-20,c=w.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),v=parseInt(f.paddingTop,10),g=parseInt(f.borderBottomWidth,10),y=p+v+c+parseInt(f.paddingBottom,10)+g,E=Math.min(5*x.offsetHeight,y),S=window.getComputedStyle(w),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),T=e.top+e.height/2-10,A=x.offsetHeight/2,L=p+v+(x.offsetTop+A);if(L<=T){let e=l.length>0&&x===l[l.length-1].ref.current;u.style.bottom="0px";let t=Math.max(a-T,A+(e?R:0)+(d.clientHeight-w.offsetTop-w.offsetHeight)+g);u.style.height=L+t+"px"}else{let e=l.length>0&&x===l[0].ref.current;u.style.top="0px";let t=Math.max(T,p+w.offsetTop+(e?C:0)+A);u.style.height=t+(y-L)+"px",w.scrollTop=L-T+w.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=E+"px",u.style.maxHeight=a+"px",null==r||r(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,u,d,w,x,b,i.dir,r]);_(()=>C(),[C]);let[R,T]=a.useState();_(()=>{d&&T(window.getComputedStyle(d).zIndex)},[d]);let A=a.useCallback(e=>{e&&!0===y.current&&(C(),null==E||E(),y.current=!1)},[C,E]);return(0,v.jsx)(nX,{scope:n,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:A,children:(0,v.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,v.jsx)(S.sG.div,{...o,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});nK.displayName="SelectItemAlignedPosition";var nY=a.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=nA(n);return(0,v.jsx)(tR,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nY.displayName="SelectPopperPosition";var[nX,nU]=nR(nH,{}),n$="SelectViewport",nq=a.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=nz(n$,n),l=nU(n$,n),u=(0,g.s)(t,i.onViewportChange),c=a.useRef(0);return(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,v.jsx)(nE.Slot,{scope:n,children:(0,v.jsx)(S.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:d(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=l;if((null==r?void 0:r.current)&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});nq.displayName=n$;var nZ="SelectGroup",[nJ,nQ]=nR(nZ),n0=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=G();return(0,v.jsx)(nJ,{scope:n,id:o,children:(0,v.jsx)(S.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});n0.displayName=nZ;var n1="SelectLabel",n2=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nQ(n1,n);return(0,v.jsx)(S.sG.div,{id:o.id,...r,ref:t})});n2.displayName=n1;var n5="SelectItem",[n9,n6]=nR(n5),n4=a.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...l}=e,u=nk(n5,n),c=nz(n5,n),s=u.value===r,[f,p]=a.useState(null!=i?i:""),[h,m]=a.useState(!1),y=(0,g.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,r,o)}),w=G(),x=a.useRef("touch"),b=()=>{o||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,v.jsx)(n9,{scope:n,value:r,disabled:o,textId:w,isSelected:s,onItemTextChange:a.useCallback(e=>{p(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,v.jsx)(nE.ItemSlot,{scope:n,value:r,disabled:o,textValue:f,children:(0,v.jsx)(S.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":h?"":void 0,"aria-selected":s&&h,"data-state":s?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...l,ref:y,onFocus:d(l.onFocus,()=>m(!0)),onBlur:d(l.onBlur,()=>m(!1)),onClick:d(l.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:d(l.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:d(l.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:d(l.onPointerMove,e=>{if(x.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:d(l.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:d(l.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(nx.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});n4.displayName=n5;var n3="SelectItemText",n8=a.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,l=nk(n3,n),u=nz(n3,n),s=n6(n3,n),d=nM(n3,n),[f,p]=a.useState(null),h=(0,g.s)(t,e=>p(e),s.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,s.value,s.disabled)}),m=null==f?void 0:f.textContent,y=a.useMemo(()=>(0,v.jsx)("option",{value:s.value,disabled:s.disabled,children:m},s.value),[s.disabled,s.value,m]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=d;return _(()=>(w(y),()=>x(y)),[w,x,y]),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(S.sG.span,{id:s.textId,...i,ref:h}),s.isSelected&&l.valueNode&&!l.valueNodeHasChildren?c.createPortal(i.children,l.valueNode):null]})});n8.displayName=n3;var n7="SelectItemIndicator",re=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return n6(n7,n).isSelected?(0,v.jsx)(S.sG.span,{"aria-hidden":!0,...r,ref:t}):null});re.displayName=n7;var rt="SelectScrollUpButton",rn=a.forwardRef((e,t)=>{let n=nz(rt,e.__scopeSelect),r=nU(rt,e.__scopeSelect),[o,i]=a.useState(!1),l=(0,g.s)(t,r.onScrollButtonChange);return _(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,v.jsx)(ri,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});rn.displayName=rt;var rr="SelectScrollDownButton",ro=a.forwardRef((e,t)=>{let n=nz(rr,e.__scopeSelect),r=nU(rr,e.__scopeSelect),[o,i]=a.useState(!1),l=(0,g.s)(t,r.onScrollButtonChange);return _(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,v.jsx)(ri,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ro.displayName=rr;var ri=a.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=nz("SelectScrollButton",n),l=a.useRef(null),u=nS(n),c=a.useCallback(()=>{null!==l.current&&(window.clearInterval(l.current),l.current=null)},[]);return a.useEffect(()=>()=>c(),[c]),_(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,v.jsx)(S.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:d(o.onPointerDown,()=>{null===l.current&&(l.current=window.setInterval(r,50))}),onPointerMove:d(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===l.current&&(l.current=window.setInterval(r,50))}),onPointerLeave:d(o.onPointerLeave,()=>{c()})})}),rl=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,v.jsx)(S.sG.div,{"aria-hidden":!0,...r,ref:t})});rl.displayName="SelectSeparator";var ra="SelectArrow";a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nA(n),i=nk(ra,n),l=nz(ra,n);return i.open&&"popper"===l.position?(0,v.jsx)(tL,{...o,...r,ref:t}):null}).displayName=ra;var ru=a.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...o}=e,i=a.useRef(null),l=(0,g.s)(t,i),u=function(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return a.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[u,r]),(0,v.jsx)(S.sG.select,{...o,style:{...tO,...o.style},ref:l,defaultValue:r})});function rc(e){return""===e||void 0===e}function rs(e){let t=C(e),n=a.useRef(""),r=a.useRef(0),o=a.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=a.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return a.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function rd(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}ru.displayName="SelectBubbleInput";var rf=nN,rp=nj,rh=nI,rv=nW,rm=nF,rg=nB,ry=nq,rw=n0,rx=n2,rb=n4,rE=n8,rS=re,rC=rn,rR=ro,rT=rl},7863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}}]);