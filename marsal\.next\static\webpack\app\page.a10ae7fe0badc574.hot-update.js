"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/header */ \"(app-pages-browser)/./src/components/header.tsx\");\n/* harmony import */ var _components_dashboard_stats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard-stats */ \"(app-pages-browser)/./src/components/dashboard-stats.tsx\");\n/* harmony import */ var _components_quick_search__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/quick-search */ \"(app-pages-browser)/./src/components/quick-search.tsx\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/auth-provider */ \"(app-pages-browser)/./src/components/auth-provider.tsx\");\n/* harmony import */ var _lib_statistics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/statistics */ \"(app-pages-browser)/./src/lib/statistics.ts\");\n/* harmony import */ var _lib_permissions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/permissions */ \"(app-pages-browser)/./src/lib/permissions.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Info,Package,RotateCcw,Settings,TruckIcon,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Icon mapping\nconst iconMap = {\n    Package: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    TruckIcon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    RotateCcw: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    Calculator: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    BarChart3: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    Archive: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    Users: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    Upload: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    Bell: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    Settings: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    Info: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"]\n};\nfunction Home() {\n    _s();\n    const { user } = (0,_components_auth_provider__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            loadStats();\n        }\n    }[\"Home.useEffect\"], []);\n    const loadStats = async ()=>{\n        try {\n            const overallStats = await _lib_statistics__WEBPACK_IMPORTED_MODULE_7__.statisticsService.getOverallStats();\n            const todayOrders = await _lib_statistics__WEBPACK_IMPORTED_MODULE_7__.statisticsService.getTodayStats();\n            setStats({\n                ...overallStats,\n                todayOrders,\n                activeCouriers: 12,\n                completionRate: overallStats.totalOrders > 0 ? Math.round(overallStats.deliveredOrders / overallStats.totalOrders * 100) : 0\n            });\n        } catch (error) {\n            console.error('Error loading stats:', error);\n            setStats({\n                totalOrders: 1234,\n                deliveredOrders: 987,\n                returnedOrders: 45,\n                pendingOrders: 156,\n                totalAmount: 125000000,\n                totalCommission: 987000,\n                todayOrders: 89,\n                activeCouriers: 12,\n                completionRate: 85\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const availableSections = user ? (0,_lib_permissions__WEBPACK_IMPORTED_MODULE_8__.getNavigationSections)(user.role) : [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 p-4 md:p-6 lg:p-8 animated-bg\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-6 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl blur-3xl\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl shadow-2xl mb-6 shadow-glow animate-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-12 w-12 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-4 animate-pulse\",\n                                                children: \"مرسال\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-300 text-xl max-w-3xl mx-auto leading-relaxed glass p-6 rounded-2xl\",\n                                                children: \"\\uD83D\\uDE80 نظام إدارة التوصيل السريع والموثوق - حلول متطورة لإدارة الطلبات والتوصيل بأحدث التقنيات\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_quick_search__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    _lib_config__WEBPACK_IMPORTED_MODULE_9__.APP_CONFIG.demo.enabled && _lib_config__WEBPACK_IMPORTED_MODULE_9__.APP_CONFIG.demo.showDemoNotice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Info_Package_RotateCcw_Settings_TruckIcon_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-blue-900 mb-2\",\n                                                    children: \"\\uD83C\\uDFAF الوضع التجريبي مُفعل\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-700 text-sm leading-relaxed\",\n                                                    children: [\n                                                        \"أنت تستخدم النسخة التجريبية من نظام مرسال. جميع البيانات تجريبية ولن يتم حفظها بشكل دائم.\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"بيانات الدخول:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" manager / 123456 أو supervisor / 123456 أو courier / 123456\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this),\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            stats: stats\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    user && availableSections.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-gray-800 dark:text-white mb-2\",\n                                        children: \"أقسام النظام\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-300\",\n                                        children: \"اختر القسم الذي تريد الوصول إليه\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                children: availableSections.map((section)=>{\n                                    const IconComponent = iconMap[section.icon];\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: section.href,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"group card-hover cursor-pointer border-2 hover:border-blue-300 glass gpu-accelerated\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    className: \"text-center pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r \".concat(section.color, \" shadow-lg mb-4 group-hover:scale-110 transition-transform duration-300 will-change-transform\"),\n                                                            children: IconComponent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"h-8 w-8 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg font-bold text-gray-800 dark:text-white group-hover:text-blue-600 transition-colors\",\n                                                            children: section.label\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    className: \"text-center pt-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                                        children: section.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, section.id, false, {\n                                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this) : null,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"max-w-2xl mx-auto glass border-2 border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-2xl text-blue-600\",\n                                            children: [\n                                                \"مرحباً، \",\n                                                (user === null || user === void 0 ? void 0 : user.name) || 'مستخدم'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            className: \"text-lg\",\n                                            children: (user === null || user === void 0 ? void 0 : user.role) === 'manager' ? 'مدير النظام' : (user === null || user === void 0 ? void 0 : user.role) === 'supervisor' ? 'متابع' : 'مندوب'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                            children: \"مرحباً بك في نظام مرسال لإدارة التوصيل. يمكنك استخدام القائمة الجانبية للتنقل بين أقسام النظام المختلفة.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-blue-800 dark:text-blue-200 mb-2\",\n                                                    children: \"معلومات سريعة:\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-700 dark:text-blue-300 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"\\uD83D\\uDCC5 التاريخ: \",\n                                                                new Date().toLocaleDateString('ar-SA')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"⏰ الوقت: \",\n                                                                new Date().toLocaleTimeString('ar-SA')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"\\uD83D\\uDC64 المستخدم: \",\n                                                                (user === null || user === void 0 ? void 0 : user.name) || 'غير محدد'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"\\uD83D\\uDD11 الدور: \",\n                                                                (user === null || user === void 0 ? void 0 : user.role) === 'manager' ? 'مدير' : (user === null || user === void 0 ? void 0 : user.role) === 'supervisor' ? 'متابع' : 'مندوب'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"5oDpyjbHAsuLdsZd4zVcW7uZ598=\", false, function() {\n    return [\n        _components_auth_provider__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});