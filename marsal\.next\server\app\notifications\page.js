(()=>{var e={};e.id=173,e.ids=[173],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>d,cn:()=>n,ps:()=>x,qY:()=>u,r6:()=>o,vv:()=>i,y7:()=>c,zC:()=>l});var s=r(49384),a=r(82348);function n(...e){return(0,a.QP)((0,s.$)(e))}function i(e){return`${e.toLocaleString("ar-IQ")} د.ع`}function d(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("ar-IQ",{year:"numeric",month:"long",day:"numeric"})}function o(e){return("string"==typeof e?new Date(e):e).toLocaleString("ar-IQ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function c(){let e=Date.now().toString().slice(-6),t=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return`MRS${e}${t}`}function l(e){return/^(07[3-9]|075)\d{8}$/.test(e.replace(/\s+/g,""))}function u(e){return({pending:"text-yellow-600 bg-yellow-100",assigned:"text-blue-600 bg-blue-100",picked_up:"text-purple-600 bg-purple-100",in_transit:"text-orange-600 bg-orange-100",delivered:"text-green-600 bg-green-100",returned:"text-red-600 bg-red-100",cancelled:"text-gray-600 bg-gray-100",postponed:"text-gray-600 bg-gray-100"})[e]||"text-gray-600 bg-gray-100"}function x(e){return({pending:"في الانتظار",assigned:"مسند",picked_up:"تم الاستلام",in_transit:"في الطريق",delivered:"تم التسليم",returned:"راجع",cancelled:"ملغي",postponed:"مؤجل"})[e]||e}},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13964:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var s=r(60687);r(43210);var a=r(8730),n=r(24224),i=r(4780);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:t,size:r,asChild:n=!1,...o}){let c=n?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:t,size:r,className:e})),...o})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35037:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(60687),a=r(43210),n=r(44493),i=r(29523),d=r(5336),o=r(93613),c=r(35071),l=r(96882),u=r(97051),x=r(13964),p=r(88233),m=r(48730),g=r(4780),h=r(67958);let f=[{id:"1",title:"طلب جديد مسند إليك",message:"تم إسناد طلب جديد برقم MRS001 من أحمد محمد إليك",type:"info",isRead:!1,createdAt:new Date(Date.now()-18e5),assignedTo:"courier",orderId:"MRS001"},{id:"2",title:"تم تسليم طلب",message:"تم تسليم الطلب MRS002 بنجاح للمستلم فاطمة علي",type:"success",isRead:!1,createdAt:new Date(Date.now()-72e5),assignedTo:"courier",orderId:"MRS002"},{id:"3",title:"طلب راجع",message:"الطلب MRS003 تم إرجاعه من قبل المندوب علي حسين",type:"warning",isRead:!0,createdAt:new Date(Date.now()-18e6),assignedTo:"supervisor",orderId:"MRS003"},{id:"4",title:"تذكرة جديدة",message:"تم إنشاء تذكرة جديدة للطلب MRS001 - مشكلة في العنوان",type:"warning",isRead:!1,createdAt:new Date(Date.now()-216e5),assignedTo:"courier",orderId:"MRS001"},{id:"5",title:"كشف تسوية جديد",message:"تم إنشاء كشف تسوية جديد للمندوب حسام محمد",type:"info",isRead:!1,createdAt:new Date(Date.now()-1728e5),assignedTo:"manager"}];function b(){let{user:e}=(0,h.A)(),[t,r]=(0,a.useState)(f),[b,v]=(0,a.useState)("all"),y=e=>{switch(e){case"success":return(0,s.jsx)(d.A,{className:"h-5 w-5 text-green-600"});case"warning":return(0,s.jsx)(o.A,{className:"h-5 w-5 text-orange-600"});case"error":return(0,s.jsx)(c.A,{className:"h-5 w-5 text-red-600"});default:return(0,s.jsx)(l.A,{className:"h-5 w-5 text-blue-600"})}},j=e=>{switch(e){case"success":return"border-l-green-500";case"warning":return"border-l-orange-500";case"error":return"border-l-red-500";default:return"border-l-blue-500"}},w=e=>{r(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t))},k=e=>{r(t=>t.filter(t=>t.id!==e))},N=(()=>{let r=t;switch(e?.role==="courier"&&(r=t.filter(t=>"courier"===t.assignedTo||t.orderId&&t.assignedTo===e.username)),b){case"unread":return r.filter(e=>!e.isRead);case"read":return r.filter(e=>e.isRead);default:return r}})(),A=t.filter(e=>!e.isRead).length;return(0,s.jsx)("div",{className:"min-h-screen bg-background p-4 md:p-6 lg:p-8",dir:"rtl",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-primary flex items-center gap-2",children:[(0,s.jsx)(u.A,{className:"h-8 w-8"}),"الإشعارات",A>0&&(0,s.jsx)("span",{className:"bg-red-500 text-white text-xs px-2 py-1 rounded-full",children:A})]}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:"التنبيهات والتحديثات الهامة"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[A>0&&(0,s.jsxs)(i.$,{variant:"outline",onClick:()=>{r(e=>e.map(e=>({...e,isRead:!0})))},children:[(0,s.jsx)(x.A,{className:"h-4 w-4 ml-2"}),"قراءة الكل"]}),t.length>0&&(0,s.jsxs)(i.$,{variant:"outline",onClick:()=>{confirm("هل أنت متأكد من حذف جميع الإشعارات؟")&&r([])},children:[(0,s.jsx)(p.A,{className:"h-4 w-4 ml-2"}),"حذف الكل"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,s.jsx)(n.Zp,{children:(0,s.jsxs)(n.Wu,{className:"p-6 text-center",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-blue-600 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:t.length}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"إجمالي الإشعارات"})]})}),(0,s.jsx)(n.Zp,{children:(0,s.jsxs)(n.Wu,{className:"p-6 text-center",children:[(0,s.jsx)(m.A,{className:"h-8 w-8 text-orange-600 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:A}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"غير مقروءة"})]})}),(0,s.jsx)(n.Zp,{children:(0,s.jsxs)(n.Wu,{className:"p-6 text-center",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 text-green-600 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:t.filter(e=>e.isRead).length}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"مقروءة"})]})}),(0,s.jsx)(n.Zp,{children:(0,s.jsxs)(n.Wu,{className:"p-6 text-center",children:[(0,s.jsx)(o.A,{className:"h-8 w-8 text-red-600 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:t.filter(e=>"error"===e.type).length}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"أخطاء"})]})})]}),(0,s.jsxs)("div",{className:"flex space-x-1 bg-muted p-1 rounded-lg w-fit",children:[(0,s.jsxs)("button",{onClick:()=>v("all"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"all"===b?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"}`,children:["الكل (",t.length,")"]}),(0,s.jsxs)("button",{onClick:()=>v("unread"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"unread"===b?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"}`,children:["غير مقروءة (",A,")"]}),(0,s.jsxs)("button",{onClick:()=>v("read"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"read"===b?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"}`,children:["مقروءة (",t.filter(e=>e.isRead).length,")"]})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"قائمة الإشعارات"}),(0,s.jsxs)(n.BT,{children:[N.length," إشعار"]})]}),(0,s.jsx)(n.Wu,{children:0===N.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)(u.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,s.jsx)("p",{children:"لا توجد إشعارات"})]}):(0,s.jsx)("div",{className:"space-y-4",children:N.map(e=>(0,s.jsx)("div",{className:`p-4 border-l-4 rounded-lg transition-colors ${j(e.type)} ${e.isRead?"bg-muted/30":"bg-background border border-border"}`,children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[y(e.type),(0,s.jsx)("span",{className:`font-medium ${!e.isRead?"text-foreground":"text-muted-foreground"}`,children:e.title}),!e.isRead&&(0,s.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full"})]}),(0,s.jsx)("p",{className:`text-sm ${!e.isRead?"text-foreground":"text-muted-foreground"}`,children:e.message}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-2",children:(0,g.r6)(e.createdAt)})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[!e.isRead&&(0,s.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>w(e.id),children:(0,s.jsx)(x.A,{className:"h-4 w-4"})}),(0,s.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>k(e.id),children:(0,s.jsx)(p.A,{className:"h-4 w-4"})})]})]})},e.id))})})]})]})})}},35071:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37366:e=>{"use strict";e.exports=require("dns")},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},51325:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>x,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),d=r(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);r.d(t,o);let c={children:["",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,63555)),"E:\\Marsal\\marsal\\src\\app\\notifications\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["E:\\Marsal\\marsal\\src\\app\\notifications\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63555:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\notifications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\notifications\\page.tsx","default")},69941:(e,t,r)=>{Promise.resolve().then(r.bind(r,63555))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83509:(e,t,r)=>{Promise.resolve().then(r.bind(r,35037))},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96882:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},97051:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,863,860,610],()=>r(51325));module.exports=s})();