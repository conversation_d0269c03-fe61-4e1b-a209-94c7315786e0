"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/statistics/page",{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types_roles__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/roles */ \"(app-pages-browser)/./src/types/roles.ts\");\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n// نظام المصادقة والصلاحيات\n\n\n\nclass AuthService {\n    // تسجيل الدخول مع ربط دائم بقاعدة البيانات السحابية\n    async login(credentials) {\n        try {\n            // محاولة البحث عن المستخدم في قاعدة البيانات\n            let user = null;\n            try {\n                user = await _supabase__WEBPACK_IMPORTED_MODULE_2__.userService.getUserByUsername(credentials.username);\n            } catch (dbError) {\n                console.warn('Database not available, using fallback auth:', dbError);\n                // في حالة عدم توفر قاعدة البيانات، استخدم المستخدمين التجريبيين\n                return this.fallbackLogin(credentials);\n            }\n            if (!user) {\n                // إذا لم يوجد المستخدم في قاعدة البيانات، جرب المستخدمين التجريبيين\n                return this.fallbackLogin(credentials);\n            }\n            if (!user.is_active) {\n                throw new Error('الحساب غير مفعل');\n            }\n            // في الوضع التجريبي، نقبل كلمة المرور 123456 لجميع المستخدمين\n            if (credentials.password !== '123456') {\n                throw new Error('كلمة المرور غير صحيحة');\n            }\n            // تحويل بيانات المستخدم من Supabase إلى AuthUser\n            const authUser = {\n                id: user.id,\n                username: user.username,\n                name: user.name,\n                phone: user.phone,\n                role: user.role,\n                permissions: [],\n                locationId: user.location_id || 'main_center',\n                location: user.location || {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: user.created_by,\n                createdAt: new Date(user.created_at),\n                isActive: user.is_active,\n                accessToken: 'supabase_access_token',\n                refreshToken: 'supabase_refresh_token'\n            };\n            this.currentUser = authUser;\n            this.notifyListeners();\n            // حفظ في localStorage\n            if (true) {\n                localStorage.setItem('auth_user', JSON.stringify(authUser));\n            }\n            return authUser;\n        } catch (error) {\n            console.error('Login error:', error);\n            if (error instanceof Error) {\n                throw error;\n            }\n            throw new Error('فشل في تسجيل الدخول');\n        }\n    }\n    // تسجيل الدخول الاحتياطي (في حالة عدم توفر قاعدة البيانات)\n    async fallbackLogin(credentials) {\n        // بيانات تجريبية للمستخدمين\n        const mockUsers = {\n            'manager': {\n                id: 'manager_1',\n                username: 'manager',\n                name: 'مدير النظام',\n                phone: '07901234567',\n                role: 'manager',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'system',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token',\n                refreshToken: 'fallback_refresh_token'\n            },\n            'supervisor': {\n                id: 'supervisor_1',\n                username: 'supervisor',\n                name: 'متابع النظام',\n                phone: '07901234568',\n                role: 'supervisor',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'manager_1',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token_supervisor',\n                refreshToken: 'fallback_refresh_token_supervisor'\n            },\n            'courier': {\n                id: 'courier_1',\n                username: 'courier',\n                name: 'مندوب التوصيل',\n                phone: '07901234570',\n                role: 'courier',\n                permissions: [],\n                locationId: 'main_center',\n                location: {\n                    id: 'main_center',\n                    name: 'المركز العام',\n                    type: 'company'\n                },\n                createdBy: 'manager_1',\n                createdAt: new Date(),\n                isActive: true,\n                accessToken: 'fallback_access_token_courier',\n                refreshToken: 'fallback_refresh_token_courier'\n            }\n        };\n        const user = mockUsers[credentials.username];\n        if (!user || credentials.password !== '123456') {\n            throw new Error('بيانات الدخول غير صحيحة');\n        }\n        this.currentUser = user;\n        this.notifyListeners();\n        // حفظ في localStorage\n        if (true) {\n            localStorage.setItem('auth_user', JSON.stringify(user));\n        }\n        return user;\n    }\n    // تسجيل الخروج\n    async logout() {\n        this.currentUser = null;\n        if (true) {\n            localStorage.removeItem('auth_user');\n        }\n        this.notifyListeners();\n    }\n    // الحصول على المستخدم الحالي\n    getCurrentUser() {\n        if (!this.currentUser && \"object\" !== 'undefined') {\n            const stored = localStorage.getItem('auth_user');\n            if (stored) {\n                try {\n                    this.currentUser = JSON.parse(stored);\n                } catch (error) {\n                    localStorage.removeItem('auth_user');\n                }\n            }\n        }\n        return this.currentUser;\n    }\n    // التحقق من الصلاحية\n    hasPermission(permission) {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        return (0,_types_roles__WEBPACK_IMPORTED_MODULE_1__.hasPermission)(user.role, permission);\n    }\n    // الحصول على الأقسام المتاحة\n    getAccessibleSections() {\n        const user = this.getCurrentUser();\n        if (!user) return [];\n        return (0,_types_roles__WEBPACK_IMPORTED_MODULE_1__.getAccessibleSections)(user.role);\n    }\n    // التحقق من إمكانية إنشاء دور معين\n    canCreateRole(targetRole) {\n        var _rolePermissions_user_role;\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        const rolePermissions = {\n            'manager': [\n                'supervisor',\n                'courier'\n            ],\n            'supervisor': [\n                'courier'\n            ],\n            'courier': []\n        };\n        return ((_rolePermissions_user_role = rolePermissions[user.role]) === null || _rolePermissions_user_role === void 0 ? void 0 : _rolePermissions_user_role.includes(targetRole)) || false;\n    }\n    // إضافة مستمع للتغييرات\n    addListener(listener) {\n        this.listeners.push(listener);\n    }\n    // إزالة مستمع\n    removeListener(listener) {\n        this.listeners = this.listeners.filter((l)=>l !== listener);\n    }\n    notifyListeners() {\n        this.listeners.forEach((listener)=>listener(this.currentUser));\n    }\n    // تحديث بيانات المستخدم\n    async updateProfile(data) {\n        if (!this.currentUser) {\n            throw new Error('لم يتم تسجيل الدخول');\n        }\n        // محاكاة API call\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        this.currentUser = {\n            ...this.currentUser,\n            ...data\n        };\n        if (true) {\n            localStorage.setItem('auth_user', JSON.stringify(this.currentUser));\n        }\n        this.notifyListeners();\n        return this.currentUser;\n    }\n    // تغيير كلمة المرور\n    async changePassword(currentPassword, newPassword) {\n        if (!this.currentUser) {\n            throw new Error('لم يتم تسجيل الدخول');\n        }\n        // محاكاة API call\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // في التطبيق الحقيقي، سيتم التحقق من كلمة المرور الحالية\n        if (currentPassword !== '123456') {\n            throw new Error('كلمة المرور الحالية غير صحيحة');\n        }\n        // تحديث كلمة المرور (في التطبيق الحقيقي)\n        console.log('تم تغيير كلمة المرور بنجاح');\n    }\n    // التحقق من صحة الجلسة\n    async validateSession() {\n        const user = this.getCurrentUser();\n        if (!user) return false;\n        try {\n            // محاكاة التحقق من صحة الجلسة\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            return true;\n        } catch (error) {\n            await this.logout();\n            return false;\n        }\n    }\n    constructor(){\n        this.currentUser = null;\n        this.listeners = [];\n    }\n}\nconst authService = new AuthService();\n// Hook للاستخدام في React\nfunction useAuth() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(authService.getCurrentUser());\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const listener = {\n                \"useAuth.useEffect.listener\": (newUser)=>setUser(newUser)\n            }[\"useAuth.useEffect.listener\"];\n            authService.addListener(listener);\n            return ({\n                \"useAuth.useEffect\": ()=>authService.removeListener(listener)\n            })[\"useAuth.useEffect\"];\n        }\n    }[\"useAuth.useEffect\"], []);\n    return {\n        user,\n        login: authService.login.bind(authService),\n        logout: authService.logout.bind(authService),\n        hasPermission: authService.hasPermission.bind(authService),\n        getAccessibleSections: authService.getAccessibleSections.bind(authService),\n        canCreateRole: authService.canCreateRole.bind(authService),\n        updateProfile: authService.updateProfile.bind(authService),\n        changePassword: authService.changePassword.bind(authService),\n        validateSession: authService.validateSession.bind(authService)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ })

});