(()=>{var e={};e.id=15,e.ids=[15],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7108:(e,r,t)=>{"use strict";t.d(r,{A:()=>m});var s=t(60687),a=t(67958),i=t(10957),l=t(16189);t(43210);var o=t(44493),n=t(29523),d=t(43649),c=t(70334);function m({children:e,requiredSection:r,fallbackPath:t="/"}){let{user:m,isAuthenticated:u}=(0,a.A)(),x=(0,l.useRouter)();return u&&m?(0,i._m)(m.role,r)?(0,s.jsx)(s.Fragment,{children:e}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center p-4",dir:"rtl",children:(0,s.jsxs)(o.Zp,{className:"max-w-md w-full shadow-xl border-2 border-red-200",children:[(0,s.jsxs)(o.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-full shadow-lg mb-4 mx-auto",children:(0,s.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,s.jsx)(o.ZB,{className:"text-2xl text-red-600",children:"غير مصرح بالدخول"}),(0,s.jsx)(o.BT,{className:"text-lg text-red-500",children:"ليس لديك صلاحية للوصول إلى هذا القسم"})]}),(0,s.jsxs)(o.Wu,{className:"text-center space-y-4",children:[(0,s.jsxs)("p",{className:"text-gray-600 leading-relaxed",children:["عذراً، دورك الحالي (","manager"===m.role?"مدير":"supervisor"===m.role?"متابع":"مندوب",") لا يسمح بالوصول إلى هذا القسم."]}),(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-sm text-yellow-800",children:"إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع المدير لمراجعة صلاحياتك."})}),(0,s.jsxs)(n.$,{onClick:()=>x.push(t),className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:["العودة إلى الصفحة الرئيسية",(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"})]})]})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-lg mb-4 animate-pulse",children:(0,s.jsx)("div",{className:"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"مرسال"}),(0,s.jsx)("p",{className:"text-gray-500",children:"جاري التحقق من الصلاحيات..."})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10957:(e,r,t)=>{"use strict";t.d(r,{BC:()=>l,_m:()=>a});let s={manager:["orders","dispatch","returns","accounting","statistics","archive","users","import-export","notifications","settings"],supervisor:["orders","statistics","archive","users","notifications","settings"],courier:["orders","archive","notifications","statistics"]};function a(e,r){return s[e]?.includes(r)||!1}let i=[{id:"orders",href:"/orders",label:"إدارة الطلبات",description:"عرض ومتابعة وتعديل الطلبات",icon:"Package",color:"from-blue-500 to-blue-600",roles:["manager","supervisor","courier"]},{id:"dispatch",href:"/dispatch",label:"إسناد الطلبات",description:"توزيع الطلبات على المندوبين",icon:"TruckIcon",color:"from-green-500 to-emerald-600",roles:["manager"]},{id:"returns",href:"/returns",label:"إدارة الرواجع",description:"استلام الطلبات الراجعة",icon:"RotateCcw",color:"from-orange-500 to-amber-600",roles:["manager"]},{id:"accounting",href:"/accounting",label:"المحاسبة",description:"التسويات المالية مع المندوبين",icon:"Calculator",color:"from-purple-500 to-violet-600",roles:["manager"]},{id:"statistics",href:"/statistics",label:"الإحصائيات",description:"التقارير والإحصائيات التفصيلية",icon:"BarChart3",color:"from-cyan-500 to-blue-600",roles:["manager","supervisor"]},{id:"archive",href:"/archive",label:"الأرشيف",description:"السجلات المنتهية",icon:"Archive",color:"from-gray-500 to-slate-600",roles:["manager","supervisor","courier"]},{id:"users",href:"/users",label:"إدارة الموظفين",description:"إدارة حسابات المستخدمين",icon:"Users",color:"from-indigo-500 to-purple-600",roles:["manager","supervisor"]},{id:"import-export",href:"/import-export",label:"استيراد وتصدير",description:"عمليات مجمعة على الطلبات",icon:"Upload",color:"from-teal-500 to-cyan-600",roles:["manager"]},{id:"notifications",href:"/notifications",label:"الإشعارات",description:"التنبيهات والتحديثات",icon:"Bell",color:"from-yellow-500 to-orange-600",roles:["manager","supervisor","courier"]},{id:"settings",href:"/settings",label:"الإعدادات",description:"تخصيص التطبيق",icon:"Settings",color:"from-pink-500 to-rose-600",roles:["manager","supervisor","courier"]}];function l(e){return i.filter(r=>r.roles.includes(e))}},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},39076:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j});var s=t(60687),a=t(43210),i=t(85814),l=t.n(i),o=t(44493),n=t(29523),d=t(89667),c=t(37730),m=t(7108);let u=(0,t(62688).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var x=t(32192),p=t(31158),h=t(16023),g=t(5336),b=t(88233),f=t(43649);let v=[{id:"albarq",name:"شركة البرق",color:"bg-blue-500",columns:{trackingNumber:"A",companyName:"C",customerPhone:"F",customerAddress:"G",amount:"H"}},{id:"almasro",name:"شركة المسرة",color:"bg-green-500",columns:{trackingNumber:"C",companyName:"D",customerPhone:"H",amount:"I"}}];function j(){let[e,r]=(0,a.useState)("import"),[t,i]=(0,a.useState)(""),[j,y]=(0,a.useState)(""),[N,w]=(0,a.useState)("all"),[k,A]=(0,a.useState)("all"),[q,M]=(0,a.useState)(""),[C,P]=(0,a.useState)(""),[S,E]=(0,a.useState)(""),[_,R]=(0,a.useState)(!1),[B,T]=(0,a.useState)(null),[Z,$]=(0,a.useState)(!1),[z,G]=(0,a.useState)(null),U=async()=>{if(!B)return void alert("يرجى اختيار ملف أولاً");if(!j)return void alert("يرجى اختيار الشركة أولاً");$(!0);try{let e={success:Math.floor(50*Math.random())+10,errors:["الصف 5: رقم الهاتف غير صحيح","الصف 12: المبلغ مطلوب","الصف 18: عنوان المستلم مطلوب"]};await new Promise(e=>setTimeout(e,2e3)),G(e),alert(`تم استيراد ${e.success} طلب بنجاح`)}catch(e){console.error("Error importing orders:",e),alert("حدث خطأ أثناء استيراد الطلبات")}finally{$(!1)}},H=async()=>{$(!0);try{await new Promise(e=>setTimeout(e,1e3));let e=`رقم الوصل,اسم المرسل,اسم المستلم,هاتف المستلم,المبلغ,الحالة,تاريخ الإنشاء
MRS001,أحمد محمد,فاطمة علي,07801234567,50000,تم التسليم,2024-01-15
MRS002,سارة أحمد,محمد حسن,07901234567,75000,في الانتظار,2024-01-15`,r=new Blob([e],{type:"text/csv;charset=utf-8;"}),t=document.createElement("a"),s=URL.createObjectURL(r);t.setAttribute("href",s),t.setAttribute("download",`orders_${new Date().toISOString().split("T")[0]}.csv`),t.style.visibility="hidden",document.body.appendChild(t),t.click(),document.body.removeChild(t),alert("تم تصدير الطلبات بنجاح")}catch(e){console.error("Error exporting orders:",e),alert("حدث خطأ أثناء تصدير الطلبات")}finally{$(!1)}},V=async()=>{if(confirm("هل أنت متأكد من حذف جميع الطلبات؟ هذا الإجراء لا يمكن التراجع عنه!")&&confirm("تأكيد نهائي: سيتم حذف جميع الطلبات نهائياً. هل تريد المتابعة؟")){$(!0);try{await new Promise(e=>setTimeout(e,2e3)),alert("تم حذف جميع الطلبات")}catch(e){console.error("Error deleting orders:",e),alert("حدث خطأ أثناء حذف الطلبات")}finally{$(!1)}}};return(0,s.jsxs)(m.A,{requiredSection:"import-export",children:[(0,s.jsx)(c.A,{}),(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 p-4 md:p-6 lg:p-8",dir:"rtl",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"text-center flex-1 space-y-4",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-lg",children:(0,s.jsx)(u,{className:"h-8 w-8 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"استيراد وتصدير البيانات"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2",children:"إدارة البيانات من وإلى شركات التوصيل المختلفة"})]})]}),(0,s.jsx)(l(),{href:"/",children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex items-center gap-2 bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-200",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),"الصفحة الرئيسية"]})})]}),(0,s.jsxs)("div",{className:"flex space-x-1 bg-white/80 backdrop-blur-sm p-1 rounded-xl shadow-lg w-fit",children:[(0,s.jsx)("button",{onClick:()=>r("import"),className:`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${"import"===e?"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:"استيراد طلبات"}),(0,s.jsx)("button",{onClick:()=>r("export"),className:`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${"export"===e?"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:"تصدير طلبات"}),(0,s.jsx)("button",{onClick:()=>r("delete"),className:`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${"delete"===e?"bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:"حذف مجمع"})]}),"import"===e&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(o.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-xl",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"flex items-center gap-2 text-gray-800",children:[(0,s.jsx)(u,{className:"h-5 w-5 text-blue-600"}),"تحميل النموذج"]}),(0,s.jsx)(o.BT,{className:"text-gray-600",children:"قم بتحميل نموذج Excel لتعبئة بيانات الطلبات"})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)(n.$,{onClick:()=>{let e=new Blob([`رقم الوصل,اسم المرسل,هاتف المرسل,عنوان المرسل,اسم المستلم,هاتف المستلم,عنوان المستلم,المبلغ,ملاحظات
MRS001,أحمد محمد,07901234567,بغداد - الكرادة,فاطمة علي,07801234567,بغداد - الجادرية,50000,مثال
MRS002,سارة أحمد,07701234567,بغداد - المنصور,محمد حسن,07601234567,بغداد - الكاظمية,75000,مثال آخر`],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a"),t=URL.createObjectURL(e);r.setAttribute("href",t),r.setAttribute("download","template_orders.csv"),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r)},className:"bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),"تحميل نموذج Excel"]})})]}),(0,s.jsxs)(o.Zp,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-xl",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"flex items-center gap-2 text-gray-800",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 text-blue-600"}),"استيراد الطلبات"]}),(0,s.jsx)(o.BT,{className:"text-gray-600",children:"اختر ملف Excel يحتوي على بيانات الطلبات"})]}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اختيار الشركة"}),(0,s.jsxs)("select",{value:j,onChange:e=>y(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"",children:"اختر الشركة"}),v.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),j&&(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,s.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"تحديد الأعمدة:"}),(0,s.jsx)("div",{className:"text-sm text-blue-700 space-y-1",children:(()=>{let e=v.find(e=>e.id===j);return e?(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,s.jsxs)("div",{children:["عمود ",e.columns.trackingNumber,": رقم الوصل"]}),(0,s.jsxs)("div",{children:["عمود ",e.columns.companyName,": اسم الشركة"]}),(0,s.jsxs)("div",{children:["عمود ",e.columns.customerPhone,": رقم الهاتف"]}),e.columns.customerAddress&&(0,s.jsxs)("div",{children:["عمود ",e.columns.customerAddress,": العنوان"]}),(0,s.jsxs)("div",{children:["عمود ",e.columns.amount,": المبلغ"]})]}):null})()})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"اختيار الملف"}),(0,s.jsx)(d.p,{type:"file",accept:".xlsx,.xls,.csv",onChange:e=>{e.target.files&&e.target.files[0]&&(T(e.target.files[0]),G(null))},className:"w-full",disabled:!j}),B&&(0,s.jsxs)("p",{className:"text-sm text-muted-foreground mt-2",children:["الملف المختار: ",B.name]})]}),(0,s.jsxs)(n.$,{onClick:U,disabled:!B||!j||Z,className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),Z?"جاري الاستيراد...":"استيراد الطلبات"]}),z&&(0,s.jsxs)("div",{className:"mt-4 p-4 border rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)(g.A,{className:"h-5 w-5 text-green-600"}),(0,s.jsx)("span",{className:"font-medium",children:"نتائج الاستيراد"})]}),(0,s.jsxs)("p",{className:"text-sm text-green-600 mb-2",children:["تم استيراد ",z.success," طلب بنجاح"]}),z.errors.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-red-600 mb-1",children:"أخطاء:"}),(0,s.jsx)("ul",{className:"text-xs text-red-600 space-y-1",children:z.errors.map((e,r)=>(0,s.jsxs)("li",{children:["• ",e]},r))})]})]})]})]})]}),"export"===e&&(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-5 w-5"}),"تصدير الطلبات"]}),(0,s.jsx)(o.BT,{children:"تصدير جميع الطلبات إلى ملف Excel"})]}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-sm text-blue-800",children:"سيتم تصدير جميع الطلبات الموجودة في النظام إلى ملف CSV يمكن فتحه في Excel"})}),(0,s.jsxs)(n.$,{onClick:H,disabled:Z,className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),Z?"جاري التصدير...":"تصدير جميع الطلبات"]})]})]}),"delete"===e&&(0,s.jsxs)(o.Zp,{className:"border-red-200",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,s.jsx)(b.A,{className:"h-5 w-5"}),"حذف جميع الطلبات"]}),(0,s.jsx)(o.BT,{children:"حذف جميع الطلبات من النظام نهائياً"})]}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)(f.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("span",{className:"font-medium text-red-800",children:"تحذير!"})]}),(0,s.jsx)("p",{className:"text-sm text-red-800",children:"هذا الإجراء سيحذف جميع الطلبات من النظام نهائياً ولا يمكن التراجع عنه. تأكد من عمل نسخة احتياطية قبل المتابعة."})]}),(0,s.jsxs)(n.$,{onClick:V,disabled:Z,variant:"destructive",className:"flex items-center gap-2",children:[(0,s.jsx)(b.A,{className:"h-4 w-4"}),Z?"جاري الحذف...":"حذف جميع الطلبات"]})]})]})]})})]})}},43649:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},48799:(e,r,t)=>{Promise.resolve().then(t.bind(t,64709))},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64709:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Marsal\\\\marsal\\\\src\\\\app\\\\import-export\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Marsal\\marsal\\src\\app\\import-export\\page.tsx","default")},70334:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},72849:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),l=t.n(i),o=t(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(r,n);let d={children:["",{children:["import-export",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,64709)),"E:\\Marsal\\marsal\\src\\app\\import-export\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\Marsal\\marsal\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\Marsal\\marsal\\src\\app\\import-export\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/import-export/page",pathname:"/import-export",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},77972:e=>{"use strict";e.exports=require("https")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85751:(e,r,t)=>{Promise.resolve().then(t.bind(t,39076))},88233:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687);t(43210);var a=t(4780);function i({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,437,839,814,451,690,809],()=>t(72849));module.exports=s})();